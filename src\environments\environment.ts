// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  name: "local",
  baseUrl: "https://credentiauatapi.examroom.ai/",
  redirectUrl: "http://localhost:8011/",
  redirectUrls: "http://localhost:",
  candidate: "8011",
  client: "8002",
  training: "/training",
  sponsor: "/voucher",
  state:"/loader/manage-applications",
  Employees:"/loader/manage-all-users",
  Evalutor:"https://credentiauat.examroom.ai/gis/validate-gisaccess",
  Finance:"/training",
  examroomapiUrl: "https://testapi.examroom.ai/api/",
  taotaker:'https://credentiataotest.examroom.ai/taoTestTaker/',
  PayPalConfigurationId:"AURSiJyrCRnfnU6pVeuu4IQhELaQVOTGC5JALwdCYx2AllFyOB1inK36PY8QY7rxfAiDUuKXtbJHxi3v",
  config: {
    iceServers: [{
      urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
    },
    {
      username: "brucewayne",
      credential: "1234567890",
      urls: [
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
      ]
    }
    ]
  }
};


/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
