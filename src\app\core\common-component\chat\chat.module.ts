import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { IconModule } from '@visurel/iconify-angular';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRippleModule } from '@angular/material/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ChatComponent } from './chat.component';
import { MatBadgeModule } from '@angular/material/badge';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CommonComponentModule } from '../common-component.module';
@NgModule({
  declarations: [
    ChatComponent
  ],
  imports: [
    CommonModule,
    FlexLayoutModule,
    PageLayoutModule,
    MatTableModule,
    MatPaginatorModule,
    IconModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRippleModule,
    ReactiveFormsModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    MatSnackBarModule,
    FormsModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatExpansionModule,
    MatBadgeModule,
    MatAutocompleteModule,
    CommonComponentModule
  ],
  exports: [
    ChatComponent
  ]
})
export class ChatModule { }
