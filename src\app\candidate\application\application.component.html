<div class="px-gutter pt-2" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>

    <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">

        <div class="pt-2 titleFont" fxLayout="column">
            <h5><strong>{{this.lngSrvc.curLangObj.value.selectApp}}</strong></h5>
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
    </div>

    <div class="py-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
        <!-- 1st Card Eligibility Route -->
        <div class="justify-start dashboard" gdColumn="1 / 4" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
            <div class="card shadow-none cardBorder h-full" fxFlex="auto">

                <div class=" pt-4 px-6 state eligibility-list " fxLayout="column">
                    <mat-label class="ml-1 my-2 font-bold text-xs fontColor2">State</mat-label>
                    <form [formGroup]="form">
                        <mat-form-field appearance='outline' fxFlex='auto' >
                            <input class="fontColor2 mb-1" matInput formControlName='stateName'  [ngModel]="state.state">
                            <mat-icon class="icon-eligible -pt-3" matSuffix matTooltip="State">error_outline
                            </mat-icon>
                            <mat-error>
                                <div class='invalid-feedback'>
                                    <div>{{this.lngSrvc.curLangObj.value.stateError}}</div>
                                </div>
                            </mat-error>
                        </mat-form-field>
                    </form>
                </div>
                <div class="px-6 pt-2" fxLayout="row" fxLayoutAlign="start">
                    <h6 class="mb-2 font-bold text-xs">{{this.lngSrvc.curLangObj.value.selectEligibility}}</h6>
                </div>
                <div class="touch-auto overflow-auto eligibility">

                    <div class="px-6 eligibility-list" fxLayout="column">
                        <!-- <button class="btn mb-2 flex t-xs justify-between" mat-stroked-button color="light" fxFlex='auto' *ngFor='let data of eligibilityRoutes$ | async; let routeIndex=index' [ngClass]="{'active' : (activeEligibilityRouteIndex$ | async)=== routeIndex}" (click)='onactive(data,routeIndex)'>
                            {{data.eligibilityName}}
                            <mat-icon class="text-end text-base flex items-center mt-px"
                                *ngIf='(activeEligibilityRouteIndex$ | async)=== routeIndex'>arrow_forward_ios
                            </mat-icon>
                        </button> -->
                        <button class="btn mb-2 flex t-xs justify-between" mat-stroked-button color="light" fxFlex='auto' *ngFor='let data of eligibilityRoutes$ | async; let routeIndex=index' [ngClass]="{'active' : (activeEligibilityRouteIndex$ | async)=== routeIndex}" (click)='onactive(data,routeIndex)'  [disabled]="disable(data)">
                            <span class="content-line" matTooltip="{{data.eligibilityName.length && data.eligibilityName !='M1 – FIRST TIME TEST TAKER' && data.eligibilityName !='M2- REPEAT TEST TAKER' && data.eligibilityName !='M3 – OUT OF STATE TRAINED' ?data.eligibilityName:data.eligibilityName =='M1 – FIRST TIME TEST TAKER'?M1_eligibilty:data.eligibilityName =='M2- REPEAT TEST TAKER'?M2_eligibilty:data.eligibilityName =='M3 – OUT OF STATE TRAINED'?M3_eligibilty:'' }}">{{data.eligibilityName}}</span>
                            <mat-icon class="text-end text-base flex items-center mt-px" 
                                *ngIf='(activeEligibilityRouteIndex$ | async)=== routeIndex'>arrow_forward_ios
                            </mat-icon>
                        </button> 
                    </div>

                </div>
            </div>

        </div>
        <!-- 1st Card Eligibility Route -->

        <!-- 2st Card Eligibility Description -->
        <div class="justify-start touch-auto overflow-auto dashboard" gdColumn="4/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1">
            <!-- Hide & Show -->
            <div *ngIf='routeDetails == null; else routeDetailsTemplate' class="card shadow-none cardBorder touch-auto overflow-auto h-full" fxFlex="auto">
                <div class="flex justify-center pt-4 h-full touch-auto overflow-auto" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr" gdRows="1fr 1fr 1fr 1fr" gdRows.lt-md="1fr 1fr 1fr" gdRows.lt-sm="1fr 1fr 1fr">
                    <div class="flex item-center" fxLayoutAlign="start center" gdColumn="4 / 6" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="2 / 3" gdRow.lt-md=" 2 / 3" gdRow.lt-sm="2 / 3" fxLayout="column">
                        <div class="" fxLayout="column">
                            <img src="assets/img/fill-application1.svg" alt="">
                        </div>
                    </div>
                    <div class="flex item-center" gdColumn="3 / 7" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="3 / 4" gdRow.lt-md=" 3 / 4" gdRow.lt-sm="3 / 6" fxLayout="column">
                        <div fxLayout="column">
                            <div class="text-center text-xs empty-eligible">
                                {{this.lngSrvc.curLangObj.value.eligibilityDesc}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ng-template #routeDetailsTemplate>
                <div class="card shadow-none cardBorder py-4 px-6" fxFlex="auto" style="overflow:hidden">
                    <div *ngFor="let item of routeDetails" class="eligibility-desc touch-auto overflow-auto pr-2" >

                        <div class="flex item-center font-bold elgibilityTitle mb-1 pt-2" fxLayoutAlign="start center">
                            <div class="text-xs" fxLayout="column">
                                {{item.title}} :
                            </div>
                        </div>
                        
                        <div class="flex item-center text-xs elgibilityDet mb-1" fxLayoutAlign="start center">
                            <div class="text-justify pb-3" fxLayout="column"  [innerHTML]='item.content'>
                             
                            </div>
                        </div>

                        <div class=" item-center text-xs elgibilityDet mb-1 " fxLayoutAlign="start center">
                            <div class="text-justify pb-3 " fxLayout="column">
                               <p class="pb-1 font-bold">{{confirmERDEtails?.title }} - </p><p class='text-xs' [innerHTML]='confirmERDEtails.description'></p>
                            </div>
                        </div>
                        <div class="flex item-center text-xs elgibilityDet mb-1 " fxLayoutAlign="start center" >
                            <div class="text-justify pb-3" fxLayout="column" >
                                <mat-checkbox  (change)='showOptions($event)' class="align-co" color='primary' >
                                    {{confirmERDEtails.acknowledgement}}
                                </mat-checkbox>
                            </div>
                        </div>
                        <div class="flex item-center text-xs elgibilityDet mb-1 " fxLayoutAlign="start center">
                            <div class="text-justify pb-3" fxLayout="column" *ngIf='confirmERDEtails.importantNote !="" &&confirmERDEtails.importantNote !=null && confirmERDEtails.importantNote !=undefined '>
                                <span  class="pb-1 font-bold"><strong >Important:</strong></span>  <p class='text-xs' [innerHTML]='confirmERDEtails.importantNote'></p>
                            </div>
                        </div>
                    </div>
                    <hr class="horzl mb-2">

                    <div class="flex justify-end " fxLayout="">
                        <button mat-button class="add-new text-xs" (click)="startApplication()" [disabled]="!checked" >
                            {{this.lngSrvc.curLangObj.value.start}}
                        </button>
                    </div>
                    <!-- </div> -->
                </div>
            </ng-template>
        </div>
        <!-- 2st Card Eligibility Description -->
    </div>
</div>