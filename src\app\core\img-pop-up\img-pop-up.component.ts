import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, Inject, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { LanguageService } from 'src/app/core/language.service';
import { jsPDF } from 'jspdf';
import domtoimage from 'dom-to-image';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { environment } from 'src/environments/environment';
import { NEXT_STEPSFAIL,NEXT_STEPSPASS ,NEXT_STEPSFAIL_WRIT_ORAL,NEXT_STEPSPASSSKILLS,NEXT_STEPSPASSMEDAID,NEXT_STEPSPASSMEDAIDFAIL,NEXT_STEPSPASSMEDAIDADULTPASS,NEXT_STEPSPASSMEDAIDADULTFAIL} from './tempdata';
import { Roles } from 'src/app/candidate/help/help.component';
import { StateList } from 'src/app/candidate/forms-wrapper/forms-wrapper.types';
@Component({
  selector: 'exai-img-pop-up',
  templateUrl: './img-pop-up.component.html',
  styleUrls: ['./img-pop-up.component.scss'],
})
export class ImgPopUpComponent implements OnInit {
  NEXT_STEPSFAIL_WRIT_ORAL=NEXT_STEPSFAIL_WRIT_ORAL;
  NEXT_STEPSPASSSKILLS=NEXT_STEPSPASSSKILLS
  NEXT_STEPSFAIL=NEXT_STEPSFAIL;
  NEXT_STEPSPASS=NEXT_STEPSPASS;
  NEXT_STEPSPASSMEDAID=NEXT_STEPSPASSMEDAID
  NEXT_STEPSPASSMEDAIDFAIL=NEXT_STEPSPASSMEDAIDFAIL
  NEXT_STEPSPASSMEDAIDADULTPASS=NEXT_STEPSPASSMEDAIDADULTPASS
  NEXT_STEPSPASSMEDAIDADULTFAIL=NEXT_STEPSPASSMEDAIDADULTFAIL
  scoresDetails :ScoresDetails;
  stateName: any;
  isOral: boolean=false;
  isWritten: boolean=false;
  isSkills: boolean = false;
  isMedication:boolean=false;
  isNNAP:boolean=true;
  isAdult: boolean=false;
  URL:string
  stateCode:string
  @ViewChild('pdfTable') pdfTable!: ElementRef;
  nextSteps;
  Exams:string
  ExamName:string
  Details :string;
  numeralCodes = [["","I","II","III","IV","V","VI","VII","VIII","IX"],
  ["","X","XX","XXX", "XL", "L", "LX", "LXX", "LXXX", "XC"]]; 
  isE12Route:boolean = false;
  
  constructor(
    private http : HttpClient,
    private dialogRef: MatDialogRef<ImgPopUpComponent>,
    public lngSrvc: LanguageService,
    public global: GlobalUserService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.Details = data.examName !='Medication Assistant MACE Examination'?'CANDIDATE DETAILS':"EXAM DETAILS"
    this.Exams= this.global.userDetails.getValue().stateId ==18?"Examination":"Evaluation"
    this.ExamName= this.global.userDetails.getValue().stateId ==18?"Examination":""
  
   }

  ngOnInit(): void {
    if(this.data.examName.includes('Oral')){
      this.isOral=true;
    }
    else if(this.data.examName.includes('Medication')){
      this.isMedication=true;
    }
    else if(this.data.examName.includes('Written')){
      this.isWritten=true;
    }
    else if(this.data.examName.includes('Skills')){
      this.isSkills=true;
    }
    // this.stateName = this.global.userDetails.value.stateName 
    this.http.get(environment.baseUrl+`candidate/api/Exam/score/summary?personEventId=${this.data.id}`).subscribe((res :ScoresDetails)=>{
      if(res){
      this.scoresDetails= res;
        this.stateName=this.scoresDetails["stateName"];
        this.stateCode = this.scoresDetails['stateCode']
   

        this.changeNextSteps();
        this.checkStates();
        if(this.scoresDetails["eligibilityRouteName"] === 'E-12 New Nurse Aide Trained at a facility that incorporates skill testing into the training program' || this.scoresDetails["eligibilityRouteName"] === 'E2 – Challenger Nurse Aide'){
          this.isE12Route = true;
        }
      }
    },error => {
    })

    setTimeout(()=>{
      this.URL= this.nextSteps[0].url
    },2000)
    
  }
  checkStates(){
    debugger
    if(this.scoresDetails && this.scoresDetails["stateCode"]=="DC" && this.scoresDetails["examCode"].charAt(0)=='H' ){
      this.isNNAP=false
      if(this.scoresDetails["examType"]=="Skill" && this.scoresDetails.isPassed){
          this.nextSteps[0].value = 'Congratulations! You have passed the Home Health Aide Skills Evaluation in the District of Columbia.To receive an overall passing result on the Home Health Aide Examination, you must also pass the Written Examination withinthe two-year period immediately following your nurse aide trainingcompletion date. Once you have passed both the Written Examination and the SkillsEvaluation, you may apply to the DC Board of Nursing forcertification as HHA. You may request an application by calling ************ or download the application from http://doh.dc.goc/bon. Once your application has been approved,you will receive a paper copy of the certification and the DC Health Professional Licensing Adm ';
      }
      else if(this.scoresDetails["examType"]=="Written" && this.scoresDetails.isPassed){
        this.nextSteps[0].value = 'Congratulations! You have successfully passed the District of Columbia Home Health Aide Examination.If you have additional questions, please contact your state agency.'
      }
      else if(this.scoresDetails["examType"]=="Oral" && this.scoresDetails.isPassed){
        this.nextSteps[0].value = 'Congratulations! You have successfully passed the District of Columbia Home Health Aide Examination.If you have additional questions, please contact your state agency.'
      }
    }
    // else if(this.scoresDetails && this.scoresDetails["stateCode"]=="NC" && this.scoresDetails["examCode"].charAt(0)=='M' ){
    //   ;
    //   this.nextSteps[0].value = 'Congratulations! You have successfully passed the District of Columbia Home Health Aide Examination.If you have additional questions, please contact your state agency.'
    // }
  }
  changeNextSteps(){
    if(this.isSkills  && !this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSFAIL.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isSkills  && this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSPASSSKILLS.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isWritten  && !this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSFAIL_WRIT_ORAL.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isOral  && !this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSFAIL_WRIT_ORAL.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isOral  && this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSPASS.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isMedication && this.scoresDetails.isPassed && this.scoresDetails.examName.includes("Adult")){
        this.isAdult=true;
       this.nextSteps=this.NEXT_STEPSPASSMEDAIDADULTPASS.filter(
         item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
       );
    }
    else if(this.isMedication && !this.scoresDetails.isPassed && this.scoresDetails.examName.includes("Adult")){
        this.isAdult=true;
       this.nextSteps=this.NEXT_STEPSPASSMEDAIDADULTFAIL.filter(
         item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
       );
    }
    else if(this.isMedication && this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSPASSMEDAID.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isMedication && !this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSPASSMEDAIDFAIL.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.isWritten && this.scoresDetails.isPassed){
      this.nextSteps=this.NEXT_STEPSPASS.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
    }
    else if(this.scoresDetails.isPassed){
      if(this.scoresDetails.examName==="North Carolina Adult Care Medication Aide Examination"){
        this.nextSteps[0].value = 'Your name will be submitted to the North Carolina Medication Aide Registry.Once listed,you may view your name on the registry by going www.ncnar.org. If you have any questions regarding your listing on the registry,you may contact the North Carolina Division of Health Service Regulation,Monday through Friday from 9:00 am to 3:00 pm (EST),at (919) 855-3969 (919) 715-0562. The re-take fee is $25.You Must have a credit card or pre-paid credit card to schedule an examination. For questions about ON-LINE Services contact customer service at ************'
      }
      //need to change based on examination code based on api
     else  if(this.scoresDetails.examName==="North Carolina Medication Aide Examination"){
        this.nextSteps[0].value = 'Your name will be submitted to the North Carolina Medication Aide Registry.Once listed,you may view your name on the registry by going www.ncnar.org. If you have any questions regarding your listing on the registry,you may contact the North Carolina Division of Health Service Regulation,Monday through Friday from 9:00 am to 3:00 pm (EST),at (919) 855-3969 (919) 715-0562. The re-take fee is $25.You Must have a credit card or pre-paid credit card to schedule an examination. For questions about ON-LINE Services contact customer service at ************'
      }
    }
    else if(!this.scoresDetails.isPassed){
     if(this.scoresDetails.examName.includes("Adult")){
       this.isAdult=true;
      this.nextSteps=this.NEXT_STEPSFAIL.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
     }
     else{
      this.nextSteps=this.NEXT_STEPSFAIL_WRIT_ORAL.filter(
        item => item.label.toLowerCase().includes(this.scoresDetails['stateCode'].toLowerCase())
      );
     }
    }
    
 
  }
  toPdf() {
    const dashboard = document.getElementById('fullDiv');
    const dashboardHeight = dashboard.clientHeight;
    const dashboardWidth = dashboard.clientWidth;
    const options = { background: 'white', width: dashboardWidth, height: dashboardHeight, allowTaint : true,
    useCORS: true
     };

    domtoimage.toPng(dashboard, options).then((imgData) => {
         const doc = new jsPDF(dashboardWidth > dashboardHeight ? 'l' : 'p', 'pc', [dashboardWidth, dashboardHeight],false);
         doc.internal.scaleFactor = 30;
         const imgProps = doc.getImageProperties(imgData);
         const pdfWidth = doc.internal.pageSize.getWidth();
         const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

         doc.addImage(imgData, 'jpeg', 0, 0, pdfWidth, pdfHeight,"NONE",);
         doc.save('Certificate.pdf');
    });
}

 convert(num) {
  var numeral = "";
  var digits = num.toString().split('').reverse();
  for (var i=0; i < digits.length; i++){
    numeral = this.numeralCodes[i][parseInt(digits[i])] + numeral;
  }
  return numeral;  
}
 
}




export interface SkillPerformance {
  question: string;
  result: string;
  steps: any[];
}

export interface ScoresDetails {
  skillPerformance: SkillPerformance[];
  candidateId: number;
  examinationDate: Date;
  dateOfBirth?: any;
  answerSheetNumber: string;
  isPassed: boolean;
  examName:string;
  eligibilityRouteDescription:string;
  passingScore:number;
  score:number;
  testCenterId:number;
  candidateCity:string;
  state:string;
  candidateState:string;
  candidateAddress:string;
  candidateName:string;
  candidateFirstName:string;
  candidateLastName:string;
  candidatePostalCode:string;
  paperTestId:any;
  stateId?:number
}

