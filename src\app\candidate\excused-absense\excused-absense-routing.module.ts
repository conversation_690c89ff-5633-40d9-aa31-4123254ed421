import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ScheduledComponent } from '../scheduled/scheduled.component';
import { ExcusedAbsenseComponent } from './excused-absense.component';


const routes: Routes = [
  {
    path: '', component: ExcusedAbsenseComponent,
    data: {
      title: 'Excused Absence',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Absence Form',
          url: '/absense-form'
        },
      ]
    },
  },
  {
    path: 'Absence-form-view',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    // canActivate: [AuthGuard],
    data: {
      title: 'Excused Absence',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Absence Form',
          url: '/absense-form'
        },
        {
          label: 'View Absence',
          url: ""
        }

      ]
    },
  },
  { path: 'examSchedule', component: ScheduledComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ExcusedAbsenseRoutingModule { }
