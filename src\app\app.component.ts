import { Component, Inject, LOCALE_ID, OnDestroy, OnInit, Renderer2 } from '@angular/core';
import { ConfigService } from '../@exai/services/config.service';
import { Settings } from 'luxon';
import { DOCUMENT } from '@angular/common';
import { Platform } from '@angular/cdk/platform';
import { ActivatedRoute } from '@angular/router';
import { filter, map, take } from 'rxjs/operators';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { Style, StyleService } from '../@exai/services/style.service';
import { ConfigName } from '../@exai/interfaces/config-name.model';
import { Subscription } from 'rxjs';
import { AppService } from './app.service';
import { SplashScreenService } from 'src/@exai/services/splash-screen.service';

import { GlobalUserService } from './core/global-user.service';
import { ReplaySessionService } from './core/openreplay.services';


@Component({
  selector: 'exai-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnDestroy {
  title = 'exai';
  querParams: Subscription[] = [];

  constructor(private configService: ConfigService,
    private styleService: StyleService,
    private renderer: Renderer2,
    private platform: Platform,
    @Inject(DOCUMENT) private document: Document,
    private route: ActivatedRoute,
    private _splashScreenService: SplashScreenService,
    private global:GlobalUserService,
    private openreplayservice:ReplaySessionService,
    private _appService: AppService /* Please do not remove this AppService dependency injection  */
  ) {
    setTimeout(()=>{
      // this.openreplayservice.openReplay(this.global.emailIdToStartExam)
    },2000)
 
    // console.log("relese date 18th April 2022 at 05:30 pm EST")
    if (this.platform.BLINK) {
      this.renderer.addClass(this.document.body, 'is-blink');
    }

    const a = this.route.queryParamMap.pipe(
      map(queryParamMap => queryParamMap.has('rtl') && coerceBooleanProperty(queryParamMap.get('rtl'))),
    ).subscribe(isRtl => {
      this.document.body.dir = isRtl ? 'rtl' : 'ltr';
      this.configService.updateConfig({
        rtl: isRtl
      });
    });   

    const b = this.route.queryParamMap.pipe(
      filter(queryParamMap => queryParamMap.has('layout'))
    ).subscribe(queryParamMap => this.configService.setConfig(queryParamMap.get('layout') as ConfigName));

    const c = this.route.queryParamMap.pipe(
      filter(queryParamMap => queryParamMap.has('style'))
    ).subscribe(queryParamMap => this.styleService.setStyle(queryParamMap.get('style') as Style));

    [a, b, c].forEach((n: Subscription) => n ? this.querParams.push(n) : null);
  }

  ngOnDestroy(): void {
    this.querParams.forEach((n: Subscription) => n ? n.unsubscribe() : null);
  }



}
