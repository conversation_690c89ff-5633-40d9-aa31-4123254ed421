import { HttpClient, HttpParams } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { jsPDF } from "jspdf";
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { SnackbarService } from 'src/app/snackbar.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'exai-test-directions-dialog',
  templateUrl: './test-directions-dialog.component.html',
  styleUrls: ['./test-directions-dialog.component.scss']
})
export class TestDirectionsDialogComponent implements OnInit {
  directions: any;

  constructor(
    public dialogRef: MatDialogRef<TestDirectionsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private http: HttpClient,
    private snackbar: SnackbarService,
  ) {}

  ngOnInit(): void {
    const { id } = this.data;

    if(!id ) return;

    let params = new HttpParams();
    params = params.set('personEventId', id);
    this.fetchData(params);     
  }

  isUrl(value: string): boolean {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }
  
  
  
  fetchData(params) {
    const url = `${environment.baseUrl}schedulemsvc/api/examdetail/test-center`;
    this.http.get(url, {params}).pipe(
      catchError((err) => {
        this.snackbar.callSnackbaronError("Something went wrong...");
        this.dialogRef.close()
        return of(null); 
      })
    ).subscribe(val => {
      this.directions = val;
    })
  }

  replaceTags(txt: string) {
    if(!txt) return '';
    const replaces = [
      { jsTag: '\\n', htmlTag: '<br>' }
    ];
    replaces.forEach(elem => {
      const rg = new RegExp(elem.jsTag, 'gmi');
      if(txt?.match(rg)) txt = txt.replace(rg, elem.htmlTag);
    });
    return txt;
  }

  download() {
    const doc = new jsPDF("p", "mm", "a4");
    const props = [
      {label: 'Adress', prop: 'testSiteAddress'},
      {label: 'City', prop: 'city'},
      {label: 'State', prop: 'state'},
      {label: 'Postal Code', prop: 'postalCode'},
      {label: 'Directions', prop: 'directions'},
    ];
    const headerX = 20;
    doc.setFont("helvetica", "bold");
    doc.setFontSize(22);
    doc.setTextColor(0, 118, 193);
    doc.text("Test Center Directions", 20, headerX);
    doc.setFont("helvetica", "normal");
    doc.setFontSize(12);
    let x = headerX + 20;
    let Props = props.filter(x=>this.directions[x.prop] !=null)
    Props.forEach((elem, index) => {
      doc.setTextColor(0, 118, 193);
      doc.text(elem.label, 20, x);
      x += 5;
      doc.setTextColor(0, 0, 0);
      doc.text(this.directions[elem.prop], 20, x , { maxWidth: 175, align: 'justify' });
      x += 10;
    });
    doc.save("test-directions.pdf");
    this.dialogRef.close()
  }
}
