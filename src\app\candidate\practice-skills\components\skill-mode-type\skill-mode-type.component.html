<div class="dialog-body rounded-md p-4 border cursor-pointer mb-2"
    [ngClass]="{ 'selected': selectedModeId === mode.id }" (click)="selectMode()">
    <div class="flex justify-between items-center">
        <div class="select_text text-sm font-semibold flex items-center gap-1">
            {{ mode.name }}
            <span *ngIf="tooltip" [matTooltip]="tooltip" matTooltipPosition="below"
                class="cursor-help text-base font-normal" style="font-size: 14px; line-height: 1;">
                ⓘ
            </span>
        </div>
        <input type="radio" name="mode" [checked]="selectedModeId === mode.id" />
    </div>

    <p class="mt-2 leading-relaxed">
        {{ mode.description }}
    </p>

    <div *ngIf="showPrice || extraInfo" class="flex justify-between items-center mt-2">
        <span *ngIf="showPrice">
            <span class="">Additional Price:</span>
            <span class="link-text">${{ mode.price }}</span>
        </span>

        <span *ngIf="extraInfo" class="whitespace-nowrap text-sm">
            <span [innerHTML]="extraInfo"></span>
        </span>
    </div>
</div>