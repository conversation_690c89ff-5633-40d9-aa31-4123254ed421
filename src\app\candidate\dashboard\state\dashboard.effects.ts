
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { createEffect, Actions, ofType } from '@ngrx/effects';
import { switchMap, map, catchError, tap, take } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';


import { cancelExam, Examcancelled, getCandidateLogin, getDashboardData, getForm, getPersonForms, getShowRegisterExam, getupcomingExam, gotCandidateLogin, gotDashboardData, gotForm, gotPersonForms, gotShowRegisterExam, gotupcomingExam } from './dashboard.actions';
import { FormModel } from './dashboard.models';
import { DashboardState } from './dashboard.state';
import { Store } from '@ngrx/store';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { URL } from 'src/app/core/url';
import { DashboardViewModel } from '../dashboard.viewmodel';
import data from '@iconify/icons-ic/twotone-menu';


@Injectable({
  providedIn: 'root',
})
export class DashboardEffects {
  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private router: Router,
    private store: Store<DashboardState>,
    private snackbar: SnackbarService,
    private viewModel: DashboardViewModel
  ) { }

  effectivelyGetDashboardData$ = createEffect(() => this.actions$.pipe(
    ofType(getDashboardData),
    switchMap((action) => {
      return this.viewModel.fetchDashboardData().pipe(
        map(form =>
          gotDashboardData({
            dashboardData: form
          })),
          take(1)
      );
    }),
  ));

  effectivelyGetForms$ = createEffect(() => this.actions$.pipe(
    ofType(getForm),
    switchMap((action) => {
      return this.httpClient
        .get<FormModel[]>(
          URL.BASE_URL +
          `dashboard/activeform?candidateId=${action.candidateId}&formTypeIds=${action.formTypeId1}&formTypeIds=${action.formTypeId2}`)
        .pipe(
          map(form =>
            gotForm({
              form: form
            })),
            take(1)
        );
    }),
  ));

  effectivelyGetupcomingExam$ = createEffect(() => this.actions$.pipe(
    ofType(getupcomingExam),
    switchMap((action) => {
      return this.httpClient
        .get<any>(
          URL.BASE_URL +
          `exam/upcomingexam?candidateId=${action.candidateId}`)
        .pipe(
          map(upcomingExam =>
            gotupcomingExam({
              upcomingExam: upcomingExam
            })),
            take(1)
        );
    }),
  ));

  effectivelyGetPersonForm$ = createEffect(() => this.actions$.pipe(
    ofType(getPersonForms),
    switchMap((action) => {
      let formTypeids: number[] = [action.formTypeId1, action.formTypeId2];
      return this.viewModel.getPersonForms(action.candidateId, formTypeids).pipe(
        map(personForms =>
          gotPersonForms({
            personForms: personForms
          })),
        take(1)
      );
    }),
  ));

  effectivelyCancelExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(cancelExam),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL + `exam/cancel?examScheduleId=${action.examScheduleId}&candidateId=${action.candidateId}`
          )
          .pipe(
            map((isCancelled) => {
              if (isCancelled) {
                this.snackbar.callSnackbaronSuccess("Successfully cancelled")
              }
              return Examcancelled({ isCancelled: isCancelled })
            }
            ),
            take(1)
          );
      })
    )
  );

  effectivelyCandidateLogin$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getCandidateLogin),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.V1_BaseUrl + `candidateLogin`,
            action.candidateLoginInputs
          )
          .pipe(
            map((res) =>
              gotCandidateLogin({
                Result: res,
              })
            ),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyShowRegisterExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getShowRegisterExam),
      switchMap((action) => {
        return this.viewModel.getshowregisterExam(action.personTenantRoleId).pipe(
          map(data =>
            gotShowRegisterExam({
              data: data
            })),
            take(1)
        );
      })
    )
  );

}
