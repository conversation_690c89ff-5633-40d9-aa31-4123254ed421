import { createAction, props } from "@ngrx/store";
import { eligibilityRoute, eligibilityRouteDetails, PersonForm, PersonFormLog, ShowRegisterExamModel } from '../application.types';

export const getEligibilityRoutes = createAction('[EligibilityRoutes] GET All Eligibility Routes',
  props<{ stateId: number,candidateId:number}>())

export const gotEligibilityRoutes = createAction('[EligibilityRoutes] GOT All Eligibility Routes',
  props<{ eligibilityRoutes: Array<eligibilityRoute> }>());

export const getEligibilityRouteDetails = createAction('[EligibilityRoute] GET Eligibility Route Details',
  props<{
    eligibilityRoute: eligibilityRoute,
    routeIndex: number
  }>());

export const gotEligibilityRouteDetails = createAction('[EligibilityRoute] GOT Eligibility Route Details',
  props<{
    routeIndex: number;
    eligibilityRoute: eligibilityRoute,
    eligibilityRouteDetails: eligibilityRouteDetails,
    isFormSubmissionAllowed?:boolean
  }>());

export const getPersonForms = createAction('[personforms] GET personforms',
  props<{ candidateId: number, formTypeId1: number, formTypeId2: number }>());

export const gotPersonForms = createAction('[personforms] GOT personforms',
  props<{ personForms: PersonForm[] }>())

  export const getShowRegisterExam = createAction('[GET SHOW REGISTERED EXAM]', props<{ personTenantRoleId: number }>())

export const gotShowRegisterExam = createAction('[GOT SHOW REGISTERED EXAM]', props<{ data: ShowRegisterExamModel }>())


export const clearApplicationState = createAction('[CLEAR APPLICATION STATE] Cleared Application State');





