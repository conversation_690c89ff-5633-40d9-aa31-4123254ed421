<div class="w-full">
    <div class="mb-2">
        <div class="flex justify-between items-center px-4 py-2 dialog-body">
            <h2 class="text-sm font-semibold">{{ skill?.skillTitle }}</h2>

            <div class="flex items-center gap-1">
                <exai-menu [menuItems]="menuItems" (itemClicked)="handleMenuAction($event, skill)"></exai-menu>
                <button class="close-btn" (click)="closeDialog()">✕</button>
            </div>
        </div>
    </div>

    <div class="px-2 mb-2 space-y-4">

        <div class="dialog-body rounded-md p-4 ">
            <h3 class="text-sm font-semibold mb-2"></h3>
            <p class="description_text text-xs leading-relaxed tracking-wide text-justify mb-4">
                {{ skill?.description }}
            </p>

            <div class="flex flex-wrap justify-between text-center text-xs mt-2 px-3">
                <div class="w-1/5">
                    <div class="meta-label">Duration</div>
                    <div class="meta-value">{{ skill?.duration }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Steps</div>
                    <div class="meta-value">{{ skill?.skillStepCount }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Attempts</div>
                    <div class="meta-value">{{ skill?.defaultAttempt }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Validity</div>
                    <div class="meta-value">{{ skill?.validity }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Price</div>
                    <div class="meta-value1">${{ skill?.priceUsd }}</div>
                </div>
            </div>
        </div>
    </div>
    <div class="px-3 mt-auto flex justify-end">
        <button class="try-now-btn font-semibold flex items-center justify-center mb-2 mr-2"
            (click)="openAddAttemptDialog(skill)">
            Try Now →
        </button>
    </div>
</div>