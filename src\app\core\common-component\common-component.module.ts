import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FlexLayoutModule } from "@angular/flex-layout";
import { PageLayoutModule } from "src/@exai/components/page-layout/page-layout.module";
import { MatTableModule } from "@angular/material/table";
import { MatPaginatorModule } from "@angular/material/paginator";
import { IconModule } from "@visurel/iconify-angular";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatInputModule } from "@angular/material/input";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatRippleModule } from "@angular/material/core";
import { ReactiveFormsModule } from "@angular/forms";
import { MatMenuModule } from "@angular/material/menu";
import { MatSortModule } from "@angular/material/sort";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSelectModule } from "@angular/material/select";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatSnackBarModule } from "@angular/material/snack-bar";
// import { TableComponent } from "./table/table.component";
import { SnackbarComponent } from "./snackbar/snackbar.component";
import { FreeDragDirective } from "./free-drag.directive";
import { NgxEditorModule } from "ngx-editor";
import { SafeHmtlPipe } from "./safe-hmtl.pipe";
import { MenuComponent } from "./menu/menu.component";
@NgModule({
  declarations: [
    SnackbarComponent,
    FreeDragDirective,
    SafeHmtlPipe,
    MenuComponent,
  ],
  imports: [
    CommonModule,
    FlexLayoutModule,
    PageLayoutModule,
    MatTableModule,
    MatPaginatorModule,
    IconModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRippleModule,
    ReactiveFormsModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatIconModule,
    NgxEditorModule,
  ],
  exports: [
    SnackbarComponent,
    MenuComponent,
    FreeDragDirective,
    NgxEditorModule,
    SafeHmtlPipe,
  ],
})
export class CommonComponentModule {}
