export class PersonFormDetailsModel{
    public constructor(init?:Partial<PersonFormDetailsModel>) {
        Object.assign(this, init);
    }

    comment : string = '';
    fields : string = '';
    formId : number = 0;
    formTypeId : number = 0;
    iconUrl : string = '';
    name : string = '';
    personFormId: number = 0;
    status : string = '';
    statusId: number = 0;
    submittedDate : Date = null;
}