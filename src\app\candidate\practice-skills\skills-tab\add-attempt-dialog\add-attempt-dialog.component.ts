import { Component, Inject, inject, OnInit } from "@angular/core";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { Router } from "@angular/router";
import { CartItem } from "src/app/candidate/scheduled/state/models/cartItem";
import { SnackbarService } from "src/app/core/snackbar.service";
import { SelectModeDialogComponent } from "../select-mode-dialog/select-mode-dialog.component";
import { PracticeSkillModeDetail } from "../../state/models/practice-skill-mode.model";

@Component({
  selector: "exai-add-attempt-dialog",
  templateUrl: "./add-attempt-dialog.component.html",
  styleUrls: ["./add-attempt-dialog.component.scss"],
})
export class AddAttemptDialogComponent implements OnInit {
  skill: any;
  skillModeList: PracticeSkillModeDetail[] = [];
  quantity = 0;
  subtotal: number = 0;
  cart: CartItem[];
  selectedMode: string = "";
  constructor(
    private router: Router,
    private dialog: MatDialog,
    private snackbar: SnackbarService,
    private _dialogRef: MatDialogRef<AddAttemptDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.skill = data.skill;
    this.skillModeList = data.skillsMode || [];
  }

  ngOnInit(): void {
    this.calculateSubtotal();
  }

  calculateSubtotal() {
    if (this.skill) {
      this.subtotal =
        this.quantity > 0
          ? this.quantity * this.skill.additionalAttemptsPrice +
            this.skill.priceUsd
          : this.skill.priceUsd;
    }
  }
  /**
   * closeDialog method close
   */
  closeDialog() {
    this._dialogRef.close();
  }

  /**
   * increaseAttempts by clicking ADD
   */
  increaseAttempts() {
    this.quantity++;
    this.calculateSubtotal();
  }

  /**
   * decreaseAttempts by clicking SUB
   */
  decreaseAttempts() {
    if (this.quantity > 0) {
      this.quantity--;
      this.calculateSubtotal();
    }
  }

  /**
   *
   */
  openSelectModeDialog(): void {
    const dialogRef = this.dialog.open(SelectModeDialogComponent, {
      width: "600px",
      data: {
        skill: this.skill,
        quantity: this.quantity,
        skillModeList: this.skillModeList,
        subtotal: this.subtotal,
      },
      panelClass: "no-padding-dialog",
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result: string) => {
      if (result === "back") {
      } else if (result) {
        this.selectedMode = result;
        //this._dialogRef.close();

        // this.router.navigate(
        //   ["/practice-skills/payment", this.skill.practiceSkillGuid],
        //   {
        //     state: {
        //       type: "practice-skill",
        //       skillItem: this.skill,
        //       mode: this.selectedMode,
        //       quantity: this.quantity,
        //     },
        //   }
        // );
      }
    });
  }
}
