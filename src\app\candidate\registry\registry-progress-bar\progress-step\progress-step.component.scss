.dot {
    color: var(--status-color);
}

.link {
    color: var(--button-background);
}

.selected-dot {
    color: var(--change-request);
}

.selected-link {
    background-color: var(--button-background);
}

.incomplete-dot {
    color: var(--sing-out);
}

.incomplete-link {
    background-color: var(--button-background);
}
.stepper-date {
    font-size: 0.75rem;
}

.line-box {
    min-width: 1.5rem;
    min-height: 4rem;
    display: flex;
    justify-content: space-around;
}

.line {
    border: 0.3px dashed var(--text-color1) !important;
    text-align: center;
    // margin-top: 1px;
}

.content-container {
    text-align: center;
}

.mat-body-1 {
    display: flex;
    align-items: left;
    color: var(--text-color5);
    font-style: italic;
    font-size: .65rem;

    
}

.mat-body-2 {
    display: flex;
    color: var(--text-color4);
    font-size: .65rem;
    line-height: initial;
}

.mat-body-3 {
    display: flex;
    align-items: end;
    color: var(--text-color1);
    font-size: .65rem;
}

.mat-body-4 {
    display: flex;
    color: var(--text-dropdown);
    font-size: .65rem;
}

.limitTextHeight {
    overflow: hidden;
}

.onhover {
    cursor: pointer;
}

.name-container {
    color: #27262C;
        font-weight: 500;
        font-size: .75rem;
        
}
.mat-icon{
    font-size: 1.2rem;
    display:flex;
    flex-direction:row;
    justify-content:center; 
    height: auto;
}
