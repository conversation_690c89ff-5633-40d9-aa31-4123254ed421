import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CrCardsComponent } from '../../registry/cr-cards/cr-cards.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { StoreModule } from '@ngrx/store';

@NgModule({
  declarations: [
    CrCardsComponent
  ],
  imports: [
    CommonModule,
    FlexLayoutModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatTooltipModule,
    RouterModule,
    StoreModule
  ],
  exports: [
    CrCardsComponent
  ]
})
export class CrCardsSharedModule { }
