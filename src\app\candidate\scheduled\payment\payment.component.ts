import { DatePipe } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import {
  AfterViewInit,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from "@angular/core";
import {
  AbstractControl,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { MatAccordion, MatExpansionPanel } from "@angular/material/expansion";
import { ActivatedRoute, Params, Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import IMask from "imask";
import moment from "moment";
import { BehaviorSubject, Observable } from "rxjs";
import { FormTypes } from "src/app/core/Dto/enum";
import { VoucherCartDetailsModel, VoucherItemModel } from "src/app/core/Dto/VocherCartDetails";
import { VoucherModel } from "src/app/core/Dto/VoucherModel";
import { GlobalUserService } from "src/app/core/global-user.service";
import { LanguageService } from "src/app/core/language.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { DecodedIdentityToken } from "../../candiate.types";
import { FormsWrapperComponent } from "../../forms-wrapper/forms-wrapper.component";
import { FormsWrapperService } from "../../forms-wrapper/forms-wrapper.service";
import { FormCartItemId } from "../../forms-wrapper/forms-wrapper.types";
import { PopUpComponent } from "../../forms-wrapper/pop-up/pop-up.component";
import { loadScript } from "@paypal/paypal-js";
import {
  deleteCartItem,
  getCartItems,
  getCartPracticeItems,
} from "../../state/shared/shared.actions";
import {
  getLoading,
  get_PracticecartItems,
  get_cartItems,
  get_userDetails,
} from "../../state/shared/shared.selectors";
import { ReschudelPaymentComponent } from "../reschudel-payment/reschudel-payment.component";
import { ScheduledService } from "../scheduled.service";
import { CartItem } from "../state/models/cartItem";
import { chargeBody } from "../state/models/charge";
import { customerId } from "../state/models/customerId";
import {
  CreatePaymentCustomerIdBody,
  CreatePaymentMethod,
  paymentMethod,
} from "../state/models/paymentMethods";
import { VocherUpdate } from "../state/models/vocher";
import {
  clearChargeResponseState,
  clearVocherResponse,
  createPaymentCustomerId,
  createPaymentMethod,
  createupdate,
  deleteCard,
  getPaymentCustomerId,
  getPaymentMethod,
  getPracticeRegisteredExam,
  getRegisteredExam,
  getScheduled,
  getVoucher,
  getVoucher_validate_apply,
  getVoucherApply,
  getVoucherAssign,
  makeCharge,
  makePracticeExamCharge,
  removeCartItem,
  removePracticeCartItem,
} from "../state/scheduled.actions";
import {
  selectorGetChargeResources,
  selectorGetCustomerId,
  selectorGetPaymentMethods,
  selectorGetScheduledExam,
  selectorGetCartDeleteStatus,
  selectorGetVoucher,
  selectorGetVoucherAssigns,
  selectorGetVouchersApply,
  createPaymentMethodResponse$,
  createPaymentCustomerId$,
  selectorGetUpdateVocher,
  selectorPaymentError,
  selectorGetPracticeChargeResources,
  selectorGetCartPracticeDeleteStatus,
  selectorGetRegisteredexam,
  selectorGetVoucher_validate_apply,
} from "../state/scheduled.selectors";
import { ScheduledState } from "../state/scheduled.state";
import { PaymentTranscationDetailsComponent } from "./payment-transcation-details/payment-transcation-details.component";
import { NgDynamicBreadcrumbService } from "ng-dynamic-breadcrumb";
@Component({
  selector: "exai-payment",
  templateUrl: "./payment.component.html",
  styleUrls: ["./payment.component.scss"],
})
export class PaymentComponent implements OnInit, OnDestroy {
  @ViewChild(MatAccordion) accordion: MatAccordion;
  apllyVoucherConfirmation: BehaviorSubject<number> = new BehaviorSubject<number>(null);
  @ViewChild("disabledPanel", { static: false })
  disabledPanel: MatExpansionPanel;
  static enteredValue;
  VocherCodes: string
  voucher: boolean = false
  itemCode = ItemCode
  VoucherCode: string
  event: any = null;
  openpopOnlyOnce: boolean = true;
  form: FormGroup;
  steps: number
  voucherId
  ACHform: FormGroup;
  cardnumber: HTMLInputElement;
  expirationdate: HTMLInputElement;
  securitycode: HTMLInputElement;
  first_last_name: HTMLInputElement;
  ExamCombined = ItemCodeExams
  subtotal: number;
  message: boolean = false;
  closePanel: boolean = true
  total: any;
  totalAmount: any;
  disablePaybutton: boolean = false;
  responseData: any;
  enableRenewalApply: boolean = false
  vouchercount: number = 0;
  voucherApplyInc: number = 0
  Validator: FormGroup;
  Vocher: any;
  TrainingCode: any
  vochercode: any
  paymentOptions: Array<object> = [{ id: 1, name: "Credits/Debit Cards" }]
  // VocherList: any;
  examName = "Applied Code SuccessFully";
  examId: any;
  ExamTotal: number
  amount: number;
  Sponsor: CartItem[];
  paymentMethods: paymentMethod[];
  cvv: FormControl;
  cvvForm: FormGroup;
  lineItems: any[] = [];
  noCartItemds: boolean;
  step2: number;
  customerIdObj: customerId = new customerId();
  personalInfo: PersonalInfo = new PersonalInfo();
  userData: DecodedIdentityToken;
  step = 0;
  listExam
  PracticeListExam
  cart: CartItem[];
  VocherList: VoucherModel[] = [];
  VoucherDataList: any[] = [];
  vochercodes: string[] = [];
  mask = "S* S*";
  vocherApplied: boolean = false;
  VocherApply: any;
  Applycode: boolean = true
  SuccessCode: boolean = false
  VocherValidation: Array<string> = []
  hideScreen: boolean = false;
  registeredExams: any;


  bankType = ['checking', 'savings'];
  bank_holder_type = ['personal', 'business']
  roles: number;
  PracticeItems: number
  constructor(
    private dialog: MatDialog,
    private store: Store,
    private router: Router,
    private global: GlobalUserService,
    private services: SnackbarService,
    private datepipe: DatePipe,
    private formwrapper: FormsWrapperService,
    private lngSrvc: LanguageService,
    private scheduleService: ScheduledService,
    private http: HttpClient, private activate: ActivatedRoute,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService
  ) {
    this.activate.paramMap.subscribe((params: Params) => {
      if (params) {
        this.PracticeItems = Number(params.params.id)
      }
    });
    this.store.select(selectorGetRegisteredexam).subscribe((exams) => {
      this.registeredExams = exams;
    })
  }
  async ngOnInit(): Promise<void> {
    this.userData = this.global.getUserData();
    this.personalInfo.firstname = this.userData.given_name;
    this.personalInfo.lastname = this.userData.family_name;
    this.personalInfo.email = this.userData.email;

    this.cvvForm = new FormGroup({
      cvv: new FormControl("", [
        Validators.required,
        Validators.maxLength(3),
        Validators.pattern("^[0-9]*$"),
      ]),
    });
    const alphabetRegexWithSpace = /^[a-zA-Z]+\s[a-zA-Z]+\s?[a-zA-Z]*\s?[a-zA-Z]*\s?[a-zA-Z]*$/;
    this.form = new FormGroup({
      CardNumber: new FormControl("", Validators.compose([Validators.required])),
      CardName: new FormControl("", Validators.compose([Validators.required, Validators.pattern(alphabetRegexWithSpace), Validators.minLength(3)])),
      Year: new FormControl("", [Validators.required]),
      CVV: new FormControl("", [
        Validators.required,
        Validators.maxLength(4),
        Validators.pattern("^[0-9]*$"),
      ]),
    });
    this.ACHform = new FormGroup({
      AccountNumber: new FormControl("", [Validators.required]),
      AccountHolderName: new FormControl("", [Validators.required]),
      RoutingNumber: new FormControl("", [Validators.required]),
      bankType: new FormControl("", [Validators.required]),
      bankHoldeType: new FormControl("", [Validators.required]),
    });

    this.Validator = new FormGroup({
      code: new FormControl("", [Validators.required]),
    });

    const breadcrumb = this.PracticeItems == 1 ? [
      {
        label: 'Home',
        url: '/dashboard'
      },
      {
        label: 'Exam Schedule',
        url: '/exam-scheduled'
      },
      {
        label: 'Register For Exam',
        url: '/exam-scheduled/register'
      },
      {
        label: 'Payment',
        url: ''
      },

    ] : [
      {
        label: 'Home',
        url: '/dashboard'
      },
      {
        label: 'Practice Exam',
        url: '/practice_exam'
      },
      {
        label: 'Register For Practice Exam',
        url: '/practice_exam/register_practice'
      },
      {
        label: 'Payment',
        url: ''
      },
    ]
    this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);

    this.store.dispatch<Action>(clearChargeResponseState());

    // this.store.dispatch<Action>(
    //   getPaymentCustomerId({ PersonTenantRoleId: this.global.candidateId })
    // );  
    this.selectrangeactive({ id: 1 })


    this.store.select(get_cartItems).subscribe((cartItems) => {
      if (cartItems != null) {
        this.cart = cartItems;
        this.scheduleService.addedToCartInfo = cartItems
        this.listExam = JSON.parse(JSON.stringify(cartItems));
        let n = Intl.DateTimeFormat().resolvedOptions();
        this.listExam = this.listExam.map((ele) =>
          Object.assign(
            {},
            ele,
            { eventDateN: moment(ele.eventDate).format("MM/DD/YYYY") },
            {
              eventDateandTime: moment(ele.eventDate)
                .tz(n.timeZone)
                .format("h:mm a z"),
            },

            {
              examDatetime: this.datepipe.transform((ele.eventDateUtc), "MM/dd/YYYY", "+0000",)
            }
          )
        );
        this.ExamTotal = this.listExam.reduce(
          (acc, val) => (acc += val.amount),
          0
        );
        this.total = this.listExam.reduce((acc, val) => (acc += val.amount), 0);
        this.Vocher = this.listExam.reduce((acc, val) => (acc += val.voucherAmount), 0);
        this.VocherCodes = this.listExam.reduce((acc, val) => (acc += val.voucherCode), 0);
        this.lineItems = []
        cartItems.forEach((dat) => {
          this.lineItems.push({
            id: dat.personEventId.toString(),
            item:
              Number(dat.cartItemTypeId) == FormCartItemId.Renewal
                ? RenewelStateList.Renewal_FormCode
                : Number(dat.cartItemTypeId) == FormCartItemId.Reciprocity
                  ? RenewelStateList.Reciprocity_FormCode
                  : Number(dat.cartItemTypeId) ==
                    FormCartItemId["V2-reciporating-scmae"]
                    ? RenewelStateList.MACE_Recipocity_FormCode
                    : Number(dat.cartItemTypeId) ==
                      FormCartItemId["V2-renewal-scmae"]
                      ? RenewelStateList.MACE_Renewal_FormCode
                      :
                      Number(dat.cartItemTypeId) ==
                        FormCartItemId.V2_greivance_code
                        ? RenewelStateList.Grievance_Reviewer
                        :
                        Number(dat.cartItemTypeId) ==
                          FormCartItemId.SC_reinstate_cartypeId
                          ? RenewelStateList.Reinstate_renewal
                          : dat.examCode,

            details: dat.cartId.toString(),
            quantity: 1,
            price: dat.amount,
          });
        });
      }
    })



    this.store.select(selectorGetUpdateVocher).subscribe((data) => {
      if (data && data.isPayment != null && data.isPayment == true) {
        setTimeout(
          () =>
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            ),
          1000
        );
        this.services.callSnackbaronSuccess(`Payment Successful`);


        this.router.navigateByUrl("exam-scheduled");



        this.store.dispatch<Action>(clearChargeResponseState);
      }
    });

    this.store.select(get_userDetails).subscribe((data) => {
      if (!data) return;
      this.personalInfo.personId = data.personId.toString();
      this.personalInfo.state = data.state;

      this.store.dispatch(getVoucherAssign({ personId: data.personId }));
    });
    this.store
      .select(selectorGetVoucherAssigns)
      .subscribe((data: VoucherModel[]) => {
        if (data && data.length > 0) {
          this.VocherList = data;



        }
        let a;
        this.VocherList.forEach((x) => {
          a = this.listExam.filter((y) =>
            x.examTypeId.some((examId) => examId === y.examTypeId)
          );
        });
        let b = this.listExam.find(
          (y) =>
            y.examTypeId == 2 ||
            y.examTypeId == 5 ||
            y.examTypeId == 4 ||
            y.examTypeId == 1 ||
            y.examTypeId == 6 ||
            y.examTypeId == 3 ||
            y.examTypeId == 9 ||
            y.examTypeId == 8 ||
            y.examTypeId == 10 ||
            y.examTypeId == 12
        );
        this.voucherId =
          a?.length > 0 ? a[0].examId : b.examTypeId == 8 ? 0 : b.examId;
      });

    this.store.select(selectorGetVouchersApply).subscribe((data) => {
      if (data != null) {
        this.Validator.reset();
      }
    });

    this.store.select(selectorPaymentError).subscribe(data => {

      this.event?.unsubscribe()
    })

    this.store.select(createPaymentCustomerId$).subscribe((data) => {
      if (data) {
        this.customerIdObj.fattmerchantCustomerId = data.id;
      }
    });

    this.store.select(selectorGetCustomerId).subscribe((obj) => {
      if (obj) {
        if (obj.fattmerchantCustomerId) {
          this.customerIdObj = obj;
          this.personalInfo.personId = this.customerIdObj.personId;
          if (this.customerIdObj.fattmerchantCustomerId) {
            this.store.dispatch(getPaymentMethod({ customerId: this.customerIdObj.fattmerchantCustomerId }));
          }

        } else {
          var body = new CreatePaymentCustomerIdBody();
          body.personid = this.personalInfo.personId;
          body.firstname = this.personalInfo.firstname;
          body.email = this.personalInfo.email;
          body.lastname = this.personalInfo.lastname;
          //this.store.dispatch<Action>(createPaymentCustomerId({ body: body }));
        }
      }
    });
    this.store.select(createPaymentMethodResponse$).subscribe((res) => {
      if (res != null) {
        this.services.callSnackbaronSuccess(`Card Saved Successfully`);
        this.form.reset();
        if (this.customerIdObj.fattmerchantCustomerId) {
          this.store.dispatch(getPaymentMethod({ customerId: this.customerIdObj.fattmerchantCustomerId }));
        }
      }
    });

    this.store.select(selectorGetPaymentMethods).subscribe((data) => {
      if (data) {
        this.paymentMethods = [...data.filter(x => x.method == 'card')];
        if (data.length > 0) {
          this.setStep(6);
        }
      }
    });

    this.store.select(selectorGetChargeResources).subscribe((response: any) => {
      if (response) {
        if (response.cartItemStatus?.find(x => x.isPreflightCompleted == false && x.preFlightCheckResponses != null)) {
          //Get List of Failed Exam
          let PerfilghtFailedExam = [];
          let isItemDeletedFromCart = []
          PerfilghtFailedExam = response.cartItemStatus?.filter(x => x.isPreflightCompleted == false);
          PerfilghtFailedExam.forEach(element => {
            PerfilghtFailedExam.push(element.preFlightCheckResponses.filter(x => x.isPreFlightCheckPassed == false))
            isItemDeletedFromCart.push(element.preFlightCheckResponses.filter(x => x.isItemDeletedFromCart == true))
          })
          //get  the failed exam in cart
          PerfilghtFailedExam = this.scheduleService.addedToCartInfo.filter(y => PerfilghtFailedExam[1].find(z => y.personEventId == z.personEventId))
          if (response.isPaymentCompleted == false) {
            if (PerfilghtFailedExam.length > 0 && isItemDeletedFromCart.length == 0)
              for (let i = 0; i < PerfilghtFailedExam.length; i++) {
                this.http
                  .delete<any>(
                    environment.baseUrl +
                    `candidate/api/exam/cart-item?personTenantRoleId=${this.global.candidateId}&cartItemId=${Number(PerfilghtFailedExam[i].personEventCartId)}`
                  ).subscribe((data) => {
                    if (data) {
                      setTimeout(() => {
                        this.store.dispatch<Action>(
                          getCartItems({ personTenantRoleId: this.global.candidateId })
                        )
                      }, 2000)
                    }
                  }, (err) => {

                  })
                let a = this.store.select(get_cartItems).subscribe(data => {
                  if (data.length == 0) {
                    this.router.navigateByUrl('/exam-scheduled');
                    a.unsubscribe();
                  }
                })
              }


          }
          if (this.openpopOnlyOnce && this.dialog.openDialogs.length == 0) {
            const dialogRef = this.dialog.open(ReschudelPaymentComponent, {
              width: '1000px',
              height: '500px',
              disableClose: true,
              data: { List: PerfilghtFailedExam, Id: this.PracticeItems }


            })
              .afterClosed().subscribe(data => {
                if (data || data.confirmed == true) {
                  this.openpopOnlyOnce = true
                }
              })
            this.openpopOnlyOnce = false
          }
        }
        this.event?.unsubscribe();
        this.listExam.forEach(x => {
          if (this.VocherList.length > 0) {
            let vouchercode = this.VocherList.find(y => y.voucherCode === x.voucherCode);
            if (vouchercode) {
              this.vocherApplied = true;
              this.vochercodes.push(vouchercode.voucherCode);
            }
            else {
              let Vocherapply = this.listExam.find(x => this.VocherList.some(y => x.voucherCode !== y.voucherCode))
              if (Vocherapply && Vocherapply.voucherCode) {
                this.vocherApplied = true;
                this.vochercodes.push(Vocherapply.voucherCode);
              }
            }
          } else {
            let Vocherapplied = this.listExam.find(x => x.voucherCode)
            if (Vocherapplied) {
              this.vocherApplied = true;
              this.vochercodes.push(Vocherapplied.voucherCode);
            }
          }



        });
        let vocherUpdateDetails = new VocherUpdate();
        vocherUpdateDetails.persontenantRoleId = this.global.candidateId
        this.vochercodes = this.vochercodes.filter((el, i, a) => i === a.indexOf(el))
        if (this.vochercodes) {
          vocherUpdateDetails.voucherCodes = this.vochercodes;
        }
        // if(response?.isPaymentCompleted && response.isRenewalStatus) {
        // this.enableRenewalApply==true?this.formwrapper.applyrenewelForm(this.enableRenewalApply,this.cart):null
        // this.enableRenewalApply=false;
        // }

        this.vocherApplied
          ? this.store.dispatch(createupdate({ VocherUpdateDetails: vocherUpdateDetails }))
          : null;
        setTimeout(
          () =>
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            ),

          1000
        );
        if (response.success == false && response.message != null) {
          this.services.callSnackbaronError(response.message);
          // this.router.navigateByUrl('exam-scheduled')
        }
        else {
          this.form.reset();
          if (response.isPaymentCompleted) {
            // this.services.callSnackbaronSuccess('Payment Successfull');
            try {
              response.cartItemStatus?.forEach((element, i) => {
                setTimeout(() => {
                  if (element.isScheduled) {
                    element.scheduledMessage != null ? this.callsnackbarMessage(element.scheduledMessage) : this.callsnackbarMessage('Exam Scheduled Sucessfully')
                  } else {
                    this.services.callSnackbaronError(element.scheduledMessage)
                  }
                }, i * 5000)
              })
              let a = this.listExam.filter(x => x.examTypeId != FormTypes.CertificateReciprocity && x.examTypeId != FormTypes.CertificateRenewal && x.examTypeId != FormTypes.V2_Reciporating_MACE && x.examTypeId != FormTypes.V2_Renewal_MACE && x.examTypeId != FormTypes.Grievance_add)
              let Grievance = this.listExam.filter(x => x.examTypeId === FormTypes.Grievance_add)
              if (a.length > 0) {
                this.router.navigateByUrl("/exam-scheduled");
              } else if (Grievance.length > 0) {
                this.router.navigateByUrl('/grievance-form')
                this.services.callSnackbaronSuccess(`${response.paymentMessage}`)
              }
              else {
                this.router.navigateByUrl("/registry")
                this.services.callSnackbaronSuccess(`${response.paymentMessage}`)

              }
            }
            catch (e) {
              this.router.navigateByUrl('/exam-scheduled')
            }
          }
          else if (response.isPaymentCompleted == false) {
            try {
              if (response.paymentMessage) {
                let placeValue = response.paymentMessage?.search("Content");
                placeValue == -1 ? this.services.callSnackbaronWarning(`${response.paymentMessage}`) : this.services.callSnackbaronWarning(`${response.paymentMessage.slice(placeValue, -1)}`)
              }
              else {
                this.services.callSnackbaronError(`${response.cartItemStatus[0]?.preflightMessage}`)
              }
            }
            catch (e) {
              this.services.callSnackbaronWarning(`Payment Unsuccessful`)
            }
          }
          this.store.dispatch<Action>(clearChargeResponseState());
          // this.store.dispatch<Action>(
          //   getPaymentCustomerId({ PersonTenantRoleId: this.global.candidateId })
          // );
        }
      }
    })


    this.store.select(selectorGetVoucher).subscribe((data) => {
      if (data != null && data.response[0].isValidExam == true) {
        let examsTypes = this.listExam.filter(x => (data.allowedTypes.find(y => y === x.examTypeId)));
        let VocherDetails = new VoucherCartDetailsModel();
        VocherDetails.cartId = this.listExam[0].cartId;
        VocherDetails.voucherCode = this.VoucherCode.replace(/\s+/g, "");
        VocherDetails.userId = this.global.userId;
        let voucherItems: VoucherItemModel[] = [];
        for (let i = 0; i < examsTypes.length; i++) {
          if (examsTypes.length > 0) {
            voucherItems.push(new VoucherItemModel({
              personEventCartId: parseInt(examsTypes[i].personEventCartId),
              voucherAmount: parseInt(data.voucherValue)
            })
            );
          }
        }
        VocherDetails.voucherItems = voucherItems;
        this.store.dispatch(getVoucherApply({ VocherDetails }));
        this.store.dispatch(clearVocherResponse());
        this.Validator.reset()
        // this.store.select(selectorGetVouchersApply).subscribe((data) => {
        //   if (data != null && data == 0) {
        setTimeout(
          () =>
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            ),
          2000
        );
        this.store.dispatch(clearVocherResponse());
        this.services.callSnackbaronSuccess("Applied Code Successfully");
      }
    });

    await this.payPalCall();

    //check if userResponse exist
    // this.checkRegistryUserResponse()
  }


  callsnackbarMessage(message) {
    this.services.callSnackbaronSuccess(message)
  }

  // checkRegistryUserResponse(){  
  //   
  //   if(this.formwrapper.userResponse==null && this.cart.find(item=>item.cartItemTypeId== CartItemId.Renewal || item.cartItemTypeId== CartItemId.Reciprocity)){
  //     let registryDetails=[];
  //     registryDetails=this.cart.filter(item=>item.cartItemTypeId==CartItemId.Renewal|| item.cartItemTypeId==CartItemId.Reciprocity)
  //     if(registryDetails.length>1){
  //       registryDetails.forEach((element,i)=>{
  //         setTimeout(()=>{
  //         this.deleteCartItems(element);
  //         },i*1000);
  //       })
  //     }
  //     else if(registryDetails.length==1){
  //       this.store.dispatch<Action>(
  //         removeCartItem({ tetantId: this.global.candidateId, cartItemsId: Number(registryDetails[0].personEventCartId) })
  //       );
  //       this.store.select(selectorGetCartDeleteStatus).subscribe((data: any) => {
  //         if (data) {
  //           this.store.dispatch<Action>(
  //             getCartItems({ personTenantRoleId: this.global.candidateId })
  //           )
  //         }
  //       });
  //     }
  //     setTimeout(()=>{
  //       this.store.dispatch<Action>(
  //         getCartItems({ personTenantRoleId: this.global.candidateId })
  //       )
  //     },3000)

  //     let a=this.store.select(get_cartItems).subscribe(data=>{
  //       if(data.length==0){
  //         this.router.navigateByUrl('/exam-scheduled');
  //         a.unsubscribe();
  //       }
  //     })
  //   }

  //   else{
  //     this.router.navigateByUrl('/exam-scheduled');
  // }
  // }



  cardnumber_mask: any;
  securitycode_mask: any;
  expirationdate_mask: any;

  public ngAfterViewInit(): void {



  }

  // public onlyNumberKey(event: KeyboardEvent): boolean {
  //   PaymentComponent.enteredValue=event.key;
  //   if (event.charCode !== undefined) {
  //     return event.charCode == 8 || event.charCode == 0
  //       ? null
  //       : event.charCode >= 48 && event.charCode <= 57;
  //   } else {
  //     return event.keyCode == 8 || event.keyCode == 0
  //       ? null
  //       : event.keyCode >= 48 && event.keyCode <= 57;
  //   }
  // }
  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }



  public setStep(index: number): void {
    this.step = index;
  }

  public setStep2(index: number): void {
    this.step2 = index == this.step2 ? -1 : index;
  }

  public nextStep(): void {
    this.step++;
  }

  public prevStep(): void {
    this.step--;
  }
  cancel() {


    let a = this.listExam.filter(x => x.examTypeId != FormTypes.CertificateReciprocity && x.examTypeId != FormTypes.CertificateRenewal)
    if (a.length > 0) {
      this.router.navigateByUrl("exam-scheduled");
    }
    else {
      this.router.navigateByUrl("registry")

    }




  }


  payment(card: paymentMethod, method: string) {

    if (card == null) {
      let Voucher = this.PracticeItems == 1 ? this.listExam.filter(x => this.VocherList.find(y => y.examTypeId == x.examTypeId)) : []
      //update pop code
      if (Voucher.length > 0) {
        this.dialog
          .open(PopUpComponent, {
            data: {
              title: this.lngSrvc.curLangObj.value.voucherConfirmation,
              message: this.lngSrvc.curLangObj.value.voucherMessage,
              cancelButton: this.lngSrvc.curLangObj.value.no,
              OkButton: this.lngSrvc.curLangObj.value.yes,
            },
          })
          .afterClosed()
          .subscribe((confirmed: any) => {
            if (confirmed == true || confirmed.confirmed == true) {
              if (this.VocherList.find(item => item.examTypeId.length >= 2)) {
                this.VocherCode(this.VocherList[this.vouchercount].voucherCode);
                this.apllyVoucherConfirmation.subscribe(data => {
                  if (data) {
                    if (this.VocherList[data].examTypeId.length >= 2) {
                      this.VocherCode(this.VocherList[data].voucherCode);
                    }
                  }
                })
              }
              else {
                this.VocherApplyCode(this.VocherList[0], 0);
                this.VocherApplyCode(this.VocherList[0], 0);
                this.apllyVoucherConfirmation.subscribe(data => {
                  if (data) {
                    if (this.VocherList.length > 1) {
                      this.VocherApplyCode(this.VocherList[data], data);
                    }
                  }
                })
              }
            }
            else if ((confirmed == false || confirmed.confirmed == false) && (confirmed !== "")) {
              //end pop code
              this.paywithoutApplyvoucher()
            }
          })
      }
      else if (Voucher.length == 0) {
        this.paywithoutApplyvoucher()
      }

    }
    else if (card == null && method == "bank") {
      var meta = {
        subtotal: this.subtotal,
        lineItems: this.lineItems,
        reference: this.personalInfo.state,
      };
      var body: chargeBody = {
        cartId: this.listExam[0].cartId.toString(),
        personId: "",
        firstname: this.ACHform.value.AccountHolderName,
        lastname: "",
        email: "",
        method: "bank",
        card_number: "",
        card_cvv: "",
        card_exp: "",
        payment_method_id: "",
        bank_account: this.ACHform.value.AccountNumber,
        bank_routing: this.ACHform.value.RoutingNumber,
        bank_name: "HGFC",
        bank_type: this.ACHform.value.bankType,
        bank_holder_type: this.ACHform.value.bankHoldeType,
        customer_id: this.customerIdObj.fattmerchantCustomerId,
        meta: meta,
        total: this.total,
        pre_auth: 0,
      };
      this.store.dispatch<Action>(makeCharge({ chargeBodu: body }));
    }
    this.enableRenewalApply = this.lineItems.find(element => element.item == (RenewelStateList.Renewal_FormCode)) ? true : this.lineItems.find(element => element.item == (RenewelStateList.Reciprocity_FormCode)) ? true : false
  }




  async payPalCall() {
    let paypal;

    try {
      paypal = await loadScript({
        clientId: environment.PayPalConfigurationId,
      });
    } catch (error) {
      console.error("failed to load the PayPal JS SDK script", error);
    }

    if (paypal) {
      try {
        const meta = {
          subtotal: this.PracticeItems != 2 ? this.ExamTotal : this.subtotal,
          lineItems: this.lineItems,
          reference: this.personalInfo.state,
        };
        const body = {
          meta: meta,
          personId: this.global.personId.toString(),
          personTenantRoleId: this.global.candidateId,
        };

        await paypal
          .Buttons({
            style: {
              layout: "horizontal",
              height: 35,
              color: "silver"
            },
            onClick() {
              let Values: any = document.getElementById('payPalButton').children[0]
              Values.style.border = "1px solid #0076c1"
              Values.style.borderRadius = "5px"
            },
            createOrder: () => {
              return this.PracticeItems != 2 ? this.scheduleService.getPayPalPayment(body).toPromise().then((res: paypal) => res.id) : this.scheduleService.getPayPalPracticePayment(body).toPromise().then((res: paypal) => res.id);
            },
            onApprove: (data) => {
              return this.PracticeItems != 2 ? this.scheduleService
                .confirmPayPalPayment(data.orderID, data.payerID, {})
                .toPromise()
                .then(_ => {
                  this.router.navigateByUrl('exam-scheduled');
                  this.services.callSnackbaronSuccess('Payment completed successfully');
                  this.store.dispatch(getCartItems({ personTenantRoleId: this.global.candidateId }));
                }) : this.scheduleService
                  .confirmPracticePayPalPayment(data.orderID, data.payerID, {})
                  .toPromise()
                  .then(_ => {
                    this.router.navigateByUrl('/practice_exam');
                    this.services.callSnackbaronSuccess('Payment completed successfully');
                    this.store.dispatch<Action>(
                      getCartPracticeItems({ personTenantRoleId: this.global.candidateId })
                    )
                  });
            },
            onCancel: (data) => {
              let Values: any = document.getElementById('payPalButton').children[0]
              Values.style.border = "none"
              Values.style.borderRadius = "none"
              return this.scheduleService.cancelPayPalPayment(data.orderID, {}).toPromise();
            },
            onError: (error) => {
              this.services.callSnackbaronError(error.message);
            }
          })
          .render("#payPalButton");
      } catch (error) {
        console.error("failed to render the PayPal Buttons", error);
      }
    }
  }


  private paywithoutApplyvoucher() {
    let firstName = this.form.value.CardName.split(" ");
    let cardNumber = this.form.value.CardNumber.includes(" ") ? this.form.value.CardNumber.replace(/\s+/g, '') : this.form.value.CardNumber
    var date = this.datepipe.transform(this.expirationdate_mask.value, "MMyy");
    var meta = {
      subtotal: this.ExamTotal,
      lineItems: this.lineItems,
      reference: this.personalInfo.state,
    };
    var body: chargeBody = {
      cartId: this.listExam[0].cartId.toString(),
      personId: this.global.personId.toString(),
      personTenantRoleId: this.global.candidateId,
      firstname: firstName[0],
      lastname: firstName.slice(1, 3).join(" "),
      email: "",
      method: "",
      card_number: cardNumber,
      card_cvv: this.form.value.CVV,
      card_exp: date,
      customer_id: '',
      payment_method_id: '',
      meta: meta,
      bank_account: "",
      bank_routing: "",
      bank_name: "",
      bank_type: "",
      bank_holder_type: "",
      total: this.total,
      pre_auth: 0,
    };
    this.store.dispatch<Action>(makeCharge({ chargeBodu: body }))
    this.event = this.store.select(getLoading).subscribe(data => {
      this.disablePaybutton = data
    })
  }

  public deleteItem(id: any, i: number): void {
    if (id) {
      !examName.includes(id.examName) ? this.store.dispatch<Action>(
        removeCartItem({ tetantId: this.global.candidateId, cartItemsId: id.personEventCartId })
      ) :
        this.store.dispatch<Action>(
          removePracticeCartItem({ personTenantRoleId: this.global.candidateId, cartItemsId: id.personEventCartId })
        );

      !examName.includes(id.examName) ?
        this.store.select(selectorGetCartDeleteStatus).subscribe((data) => {

          if (data) {

            this.store.dispatch<Action>(deleteCartItem({ index: i }));

            setTimeout(() => {

              this.store.dispatch<Action>(

                getRegisteredExam({ candidateId: this.global.candidateId })

              );
            }, 2000);

            setTimeout(() => {
              this.store.dispatch<Action>(
                getCartItems({ personTenantRoleId: this.global.candidateId })
              )
            }, 2000)
          }

        }) : this.store.select(selectorGetCartPracticeDeleteStatus).subscribe((data: any) => {
          if (data) {
            // this.store.dispatch<Action>(deleteCartItem({ index: i }));
            setTimeout(() => {
              this.store.dispatch<Action>(
                getCartPracticeItems({ personTenantRoleId: this.global.candidateId })
              )
              this.store.dispatch<Action>(
                getPracticeRegisteredExam({ candidateId: this.global.candidateId })
              );
              this.store.dispatch<Action>(
                getCartItems({ personTenantRoleId: this.global.candidateId })
              )
            }, 2000)


          }
        });



    }
    setTimeout(() => {
      !examName.includes(id.examName) ? this.router.navigateByUrl('exam-scheduled') : this.router.navigateByUrl('/practice_exam')

    }, 2000);



  }

  private getCartItemsExams(items: CartItem[]): number[] {
    var result = items.map(function (a) { return a.examId; });
    return result;
  }
  private getCartItemsAmount(items: CartItem[]): number {
    var result = items.map(a => a.amount).reduce(function (a, b) {
      return a + b;
    });
    return result;
  }
  VocherApplyCode(i, item) {
    this.vocherApplied = true;
    this.examId = i;
    var VocherValidator = {
      validateVoucher: {
        personTenantRoleId: this.global.candidateId,
        examTypeId: [this.listExam[item].examTypeId],
        examId: [this.listExam[item].examId],
        formCode: this.listExam[item].examTypeId == FormTypes.CertificateRenewal
          ? RenewelStateList.Renewal_FormCode
          : this.listExam[item].examTypeId == FormTypes.CertificateReciprocity
            ? RenewelStateList.Reciprocity_FormCode
            : this.listExam[item].examTypeId == FormTypes.V2_Renewal_MACE
              ? RenewelStateList.MACE_Renewal_FormCode
              : this.listExam[item].examTypeId == FormTypes.V2_Reciporating_MACE
                ? RenewelStateList.MACE_Recipocity_FormCode
                : this.listExam[item].examTypeId == FormTypes.V2_Reciporating_MACE
                  ? RenewelStateList.Reinstate_renewal
                  : null,
      },
      applyVoucher: {
        cartId: this.listExam[item].cartId,
        voucherCode: this.examId.voucherCode,
        userId: this.global.userDetails.value.personId,
        voucherItems: [
          {
            personEventCartId: parseInt(
              this.listExam[item].personEventCartId
            ),
            voucherAmount: parseInt(this.listExam[item].amount),
            examId: this.listExam[item].examId
          },
        ],
      }

    };
    this.event = this.store.select(getLoading).subscribe((data) => {
      this.disablePaybutton = data;
    });
    this.store.dispatch(getVoucher_validate_apply({ VocherValidator }));

    this.store.select(selectorGetVoucher_validate_apply).subscribe((data) => {
      if (data == 0) {
        setTimeout(() => {
          this.store.dispatch<Action>(
            getCartItems({ personTenantRoleId: this.global.candidateId })
          )
        }, 1000)

        this.store.dispatch(clearVocherResponse());
        setTimeout(() => {
          this.voucherApplyInc += 1
          this.services.callSnackbaronSuccess("Applied Code Successfully");
          this.apllyVoucherConfirmation.next(this.voucherApplyInc);
        }, 1000);
      }
      //   this.event = this.store.select(getLoading).subscribe((data) => {
      //     this.disablePaybutton = data;
      //   });
      //   if (data != null && data.response[0].isValidExam == true) {
      //     this.VocherApply = data;
      //     // var VocherDetails = {
      //     //   cartId: this.listExam[item].cartId,
      //     //   voucherCode: this.examId.voucherCode,
      //     //   userId: this.global.userDetails.value.personId,
      //     //   voucherItems: [
      //     //     {
      //     //       personEventCartId: parseInt(
      //     //         this.listExam[item].personEventCartId
      //     //       ),
      //     //       voucherAmount: parseInt(data.voucherValue),
      //     //     },
      //     //   ],
      //     // };

      //     // this.store.dispatch(getVoucherApply({ VocherDetails }));
      //     // this.store.dispatch(clearVocherResponse());
      //     // this.store.select(selectorGetVouchersApply).subscribe((data) => {
      //     //   if (data != null && data == 0) {
      //
      //     //   }
      //     // });
      //   }
    });
  }


  public SponsorVocherCode(): void {

    let voucherCode = this.Validator.value.code.includes(" ") ? this.Validator.value.code.replace(/\s+/g, '') : this.Validator.value.code
    let voucherItems = [];

    for (let item = 0; item < this.cart.length; item++) {
      voucherItems.push({
        personEventCartId: parseInt(this.cart[item].personEventCartId),
        voucherAmount: this.cart[item].amount,
        examId: this.cart[item].examId,
      });




      // }
      // });
    }

    var VocherValidator = {
      validateVoucher: {
        personTenantRoleId: this.global.candidateId,
        examTypeId: this.listExam.map(x => x.examTypeId),
        examId: this.listExam.map(x => x.examId),
        formCode: this.listExam.map((x) => x.examTypeId) == FormTypes.CertificateRenewal
          ? RenewelStateList.Renewal_FormCode
          : this.listExam.map((x) => x.examTypeId) ==
            FormTypes.CertificateReciprocity
            ? RenewelStateList.Reciprocity_FormCode
            : this.listExam.map((x) => x.examTypeId) == FormTypes.V2_Renewal_MACE
              ? RenewelStateList.MACE_Renewal_FormCode
              : this.listExam.map((x) => x.examTypeId) ==
                FormTypes.V2_Reciporating_MACE
                ? RenewelStateList.MACE_Recipocity_FormCode
                : this.listExam.map((x) => x.examTypeId) ==
                  FormTypes.SC_reinstate_id
                  ? RenewelStateList.Reinstate_renewal

                  : null,
      },
      applyVoucher: {
        cartId: this.listExam[0].cartId,
        voucherCode: voucherCode,
        userId: this.global.userDetails.value.personId,
        voucherItems: voucherItems,
      }

    };
    this.store.dispatch(getVoucher_validate_apply({ VocherValidator }));
    this.store.select(selectorGetVoucher_validate_apply).subscribe((data) => {
      if (data == 0) {
        setTimeout(() => {
          this.store.dispatch<Action>(
            getCartItems({ personTenantRoleId: this.global.candidateId })
          )
        }, 1000)

        this.store.dispatch(clearVocherResponse());
        setTimeout(() => {
          // this.voucherApplyInc += 1
          this.services.callSnackbaronSuccess("Applied Code Successfully");
          this.Validator.reset()
          // this.apllyVoucherConfirmation.next(this.voucherApplyInc);
        }, 1000);
      }
      //   this.event = this.store.select(getLoading).subscribe((data) => {
      //     this.disablePaybutton = data;
      //   });
      //   if (data != null && data.response[0].isValidExam == true) {
      //     this.VocherApply = data;
      //     // var VocherDetails = {
      //     //   cartId: this.listExam[item].cartId,
      //     //   voucherCode: this.examId.voucherCode,
      //     //   userId: this.global.userDetails.value.personId,
      //     //   voucherItems: [
      //     //     {
      //     //       personEventCartId: parseInt(
      //     //         this.listExam[item].personEventCartId
      //     //       ),
      //     //       voucherAmount: parseInt(data.voucherValue),
      //     //     },
      //     //   ],
      //     // };

      //     // this.store.dispatch(getVoucherApply({ VocherDetails }));
      //     // this.store.dispatch(clearVocherResponse());
      //     // this.store.select(selectorGetVouchersApply).subscribe((data) => {
      //     //   if (data != null && data == 0) {
      //
      //     //   }
      //     // });
      //   }
    });
  }

  VocherCode(code: string): void {
    this.vocherApplied = true
    let voucher = this.VocherList.find(x => x.voucherCode === code);
    let exams = this.listExam.filter(x => voucher.examTypeId.find(y => y === x.examTypeId));
    if (exams.length > 0) {
      let VocherValidator = {
        personTenantRoleId: this.global.candidateId,
        voucherCode: code,
        examTypeId: voucher.examTypeId,
        examId: this.getCartItemsExams(exams),
        examPrice: this.getCartItemsAmount(exams)
      }
      this.vochercode = code
      this.store.dispatch(getVoucher({ VocherValidator }));
      this.store.select(selectorGetVoucher).subscribe((data) => {
        if (data != null && data.response[0].isValidExam == true) {
          let VocherDetails = new VoucherCartDetailsModel();
          VocherDetails.cartId = this.listExam[0].cartId;
          VocherDetails.voucherCode = code;
          VocherDetails.userId = this.global.userId;
          let voucherItems: VoucherItemModel[] = [];
          for (let i = 0; i < exams.length; i++) {
            voucherItems.push(new VoucherItemModel({
              personEventCartId: parseInt(exams[i].personEventCartId),
              voucherAmount: parseInt(data.voucherValue)
            })
            );
          }
          VocherDetails.voucherItems = voucherItems;
          this.store.dispatch(getVoucherApply({ VocherDetails }));
          this.store.dispatch(clearVocherResponse());
          setTimeout(
            () =>
              this.store.dispatch<Action>(
                getCartItems({ personTenantRoleId: this.global.candidateId })
              ),
            1000
          );
          this.services.callSnackbaronSuccess("Applied Code Successfully");
          this.vouchercount += 1
          this.apllyVoucherConfirmation.next(this.vouchercount);
        }
      });
    } else {
      this.services.callSnackbaronError("Combo voucher cannot be applied here as only one exam type is added to the cart.")
    }

  }

  async selectrangeactive(event) {
    this.steps = event.id;
    if (event.id === 2) {
      this.form.reset();
    } else if (event.id === 1) {
      this.loadValidations();
    }
  }

  loadValidations() {
    setTimeout(() => {
      this.cardnumber = <HTMLInputElement>document.getElementById("cardnumber");
      this.expirationdate = <HTMLInputElement>(
        document.getElementById("expirationdate")
      );
      this.securitycode = <HTMLInputElement>(
        document.getElementById("securitycode")
      );
      this.cardnumber_mask = IMask(this.cardnumber, {
        mask: [
          {
            mask: '000000000000000',
            regex: '^3[47]\\d{0,13}',
            cardtype: 'american express'
          },
          {
            mask: '0000000000000000',
            regex: '^(?:6011|65\\d{0,2}|64[4-9]\\d?)\\d{0,12}',
            cardtype: 'discover'
          },
          {
            mask: '00000000000000',
            regex: '^3(?:0([0-5]|9)|[689]\\d?)\\d{0,11}',
            cardtype: 'diners'
          },
          {
            mask: '0000000000000000',
            regex: '^(5[1-5]\\d{0,2}|22[2-9]\\d{0,1}|2[3-7]\\d{0,2})\\d{0,12}',
            cardtype: 'mastercard'
          },
          {
            mask: '000000000000000',
            regex: '^(?:2131|1800)\\d{0,11}',
            cardtype: 'jcb15'
          },
          {
            mask: '0000000000000000',
            regex: '^(?:35\\d{0,2})\\d{0,12}',
            cardtype: 'jcb'
          },
          {
            mask: '0000000000000000',
            regex: '^(?:5[0678]\\d{0,2}|6304|67\\d{0,2})\\d{0,12}',
            cardtype: 'maestro'
          },
          {
            mask: '0000000000000000',
            regex: '^4\\d{0,15}',
            cardtype: 'visa'
          },
          {
            mask: '0000000000000000',
            regex: '^62\\d{0,14}',
            cardtype: 'unionpay'
          },
          {
            mask: '0000000000000000',
            cardtype: 'Unknown'
          }
        ],
        // dispatch: function (appended, dynamicMasked): any {
        //   if(appended==PaymentComponent.enteredValue||appended==''){
        //   var number = (dynamicMasked.value + appended).replace(/\D/g, "");
        //   for (var i = 0; i < dynamicMasked.compiledMasks.length; i++) {
        //     let re = new RegExp(dynamicMasked.compiledMasks[i].regex);
        //     if (number.match(re) != null) {
        //       return dynamicMasked.compiledMasks[i];
        //     }
        //   }
        // }
        // },
        dispatch: function (appended, dynamicMasked) {
          var number = (dynamicMasked.value + appended).replace(/\D/g, '');
          for (var i = 0; i < dynamicMasked.compiledMasks.length; i++) {
            let re = new RegExp(dynamicMasked.compiledMasks[i].regex);
            if (number.match(re) != null) {
              return dynamicMasked.compiledMasks[i];
            }
          }
        }
      });

      this.expirationdate_mask = IMask(this.expirationdate, {
        mask: "YYYY{-}MM",
        blocks: {
          YYYY: {
            mask: IMask.MaskedRange,
            from: new Date().getFullYear(),
            to: 2099,
          },
          MM: {
            mask: IMask.MaskedRange,
            from: 1,
            to: 12,
          },
        },
      });
      this.securitycode_mask = IMask(this.securitycode, {
        mask: "0000",
      });
    })
  }

  public schedule(): void {
    this.vochercodes = [];
    this.store.dispatch(
      getScheduled({
        personTentantRole: this.global.candidateId,
        PersonID: this.global.userDetails.getValue().personId,
        cartId: this.listExam[0].cartId,

      })
    );
    this.event = this.store.select(getLoading).subscribe(data => {
      this.disablePaybutton = data
    })
    this.enableRenewalApply = this.lineItems.find(element => element.item == (RenewelStateList.Renewal_FormCode)) ? true : this.lineItems.find(element => element.item == (RenewelStateList.Reciprocity_FormCode)) ? true : false
    this.store.select(selectorGetScheduledExam).subscribe((data) => {
      if (data) {
        if (data.cartItemStatus?.find(x => x.isPreflightCompleted == false && x.preFlightCheckResponses != null)) {

          //Get List of Failed Exam
          let PerfilghtFailedExam = [];
          let isItemDeletedFromCart = []
          PerfilghtFailedExam = data.cartItemStatus?.filter(x => x.isPreflightCompleted == false);
          PerfilghtFailedExam.forEach(element => {
            PerfilghtFailedExam.push(element.preFlightCheckResponses.filter(x => x.isPreFlightCheckPassed == false))
            isItemDeletedFromCart.push(element.preFlightCheckResponses.filter(x => x.isItemDeletedFromCart == true))
          })
          //get  the failed exam in cart
          PerfilghtFailedExam = this.scheduleService.addedToCartInfo.filter(y => PerfilghtFailedExam[1].find(z => y.personEventId == z.personEventId))
          if (PerfilghtFailedExam.length > 0 && isItemDeletedFromCart.length == 0)
            for (let i = 0; i < PerfilghtFailedExam.length; i++) {
              this.http
                .delete<any>(
                  environment.baseUrl +
                  `candidate/api/exam/cart-item?personTenantRoleId=${this.global.candidateId}&cartItemId=${Number(PerfilghtFailedExam[i].personEventCartId)}`
                ).subscribe((data) => {
                  if (data) {
                    setTimeout(() => {
                      this.store.dispatch<Action>(
                        getCartItems({ personTenantRoleId: this.global.candidateId })
                      )
                    }, 2000)
                  }
                })
              let a = this.store.select(get_cartItems).subscribe(data => {
                if (data.length == 0) {
                  this.router.navigateByUrl('/exam-scheduled');
                  a.unsubscribe();
                }
              })
            }
          if (this.openpopOnlyOnce && PerfilghtFailedExam != null && PerfilghtFailedExam.length > 0) {
            const dialogRef = this.dialog.open(ReschudelPaymentComponent, {
              width: '1000px',
              height: '500px',
              disableClose: true,
              data: PerfilghtFailedExam
            }).afterClosed().subscribe(data => {
              if (data || data.confirmed == true) {
                this.openpopOnlyOnce = true
              }
            })
            this.openpopOnlyOnce = false
          }
          this.event?.unsubscribe();
        }
        this.listExam.forEach(x => {
          if (this.VocherList.length > 0) {
            let vouchercode = this.VocherList.find(y => y.voucherCode === x.voucherCode);
            if (vouchercode) {
              this.vochercodes.push(vouchercode.voucherCode.replace(/\s+/g, ""));
            } else {
              let Vocherapply = this.listExam.find((x) =>
                this.VocherList.some((y) => x.voucherCode !== y.voucherCode)
              );
              if (Vocherapply) {
                this.vochercodes.push(Vocherapply.voucherCode.replace(/\s+/g, ""));
              }
            }
          }
          else {
            let Vocherapplied = this.listExam.find(x => x.voucherCode)
            if (Vocherapplied) {
              this.vochercodes.push(Vocherapplied.voucherCode.replace(/\s+/g, ""));
            }
          }

        });
        this.vochercodes = this.vochercodes.filter((el, i, a) => i === a.indexOf(el))
        let vocherUpdateDetails = new VocherUpdate();
        vocherUpdateDetails.persontenantRoleId = this.global.candidateId
        if (this.vochercodes) {
          vocherUpdateDetails.voucherCodes = this.vochercodes;

        }
        // added by Bruno - May-11-2022
        if (data.isPaymentCompleted) {
          let isScheduled = data.cartItemStatus.every(x => x.isScheduled);
          if (isScheduled) {
            this.store.dispatch(createupdate({ VocherUpdateDetails: vocherUpdateDetails }));
          }
        }
        if (data.isPaymentCompleted) {
          try {
            data.cartItemStatus?.forEach((element, i) => {
              let ScheduleExamMessage = element.scheduledMessage != null ? element.scheduledMessage : "Exam Scheduled Sucessfully"
              setTimeout(() => {
                element.isScheduled ? this.callsnackbarMessage(ScheduleExamMessage) : element.isScheduled == false ? this.services.callSnackbaronError(element.preflightMessage) : null
              }, i * 5000)

            })
            let a = this.listExam.filter(x => x.examTypeId != FormTypes.CertificateReciprocity && x.examTypeId != FormTypes.CertificateRenewal && x.examTypeId != FormTypes.V2_Reciporating_MACE && x.examTypeId != FormTypes.V2_Renewal_MACE)
            let Grievance = this.listExam.filter(x => x.examTypeId === FormTypes.Grievance_add)
            if (a.length > 0) {
              this.router.navigateByUrl("/exam-scheduled");
            } else if (Grievance.length > 0) {
              this.router.navigateByUrl('/grievance')
              this.services.callSnackbaronSuccess(`${data.paymentMessage}`)
            }
            else {
              this.router.navigateByUrl("/registry")
              this.services.callSnackbaronSuccess(`${data.paymentMessage}`)

            }



          }
          catch (e) {



            this.router.navigateByUrl('exam-scheduled')




          }
        }

        else if (data.isPaymentCompleted == false) {
          try {
            if (data.paymentMessage) {
              let placeValue = data.paymentMessage.search("Content");
              placeValue == -1 ? this.services.callSnackbaronWarning(`${data.paymentMessage}`) : this.services.callSnackbaronWarning(`${data.paymentMessage.slice(placeValue, -1)}`)
            }
            else {
              this.services.callSnackbaronError(`${data.cartItemStatus[0].preflightMessage}`)
            }
          }
          catch (e) {
            this.services.callSnackbaronWarning('Payment Unsuccessful')
          }
        }
        else {
          this.services.callSnackbaronWarning(`Payment Unsuccessful`)
        }
        //    if(data.isPaymentCompleted  && data.isRenewalStatus){
        // this.enableRenewalApply==true?this.formwrapper.applyrenewelForm(this.enableRenewalApply,this.cart):null
        // this.enableRenewalApply=false;
        //    }
        setTimeout(
          () =>
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            ),

          1000
        );
        this.store.dispatch(getRegisteredExam({ candidateId: this.global.candidateId }))
      }
    });
    this.store.dispatch<Action>(clearChargeResponseState());
  }
  deleteCard(card) {
    this.store.dispatch<Action>(deleteCard({ id: card.id }))
    setTimeout(() => {
      this.store.dispatch(getPaymentMethod({ customerId: this.customerIdObj.fattmerchantCustomerId }));
    }, 500);
  }

  untilPaymentProcess(item) {
    if (item == 0) {
      if (this.disablePaybutton) {
        this.hideScreen = true
        return false;
      }
      else {
        return true;
      }

    }
    else if (item == 1) {
      if (this.disablePaybutton) {
        return this.disablePaybutton;
      }
      else {
        return false;
      }
      //   // return true
    }
  }

  addExam() {
    this.router.navigateByUrl("/exam-scheduled/register");
  }

  closeExapanel() {
    if ((this.ExamTotal == 0 && this.PracticeItems == 1) || (this.subtotal == 0 && this.PracticeItems == 2)) {
      this.closePanel = false
      return true
    }
  }

  public ngOnDestroy(): void { }
}

export class PersonalInfo {
  firstname: string = "";
  lastname: string = "";
  email: string = "";
  method: string = "card";
  personId: string = "";
  state: string = "";
}

export function ValidateLastName(control: AbstractControl) {
  if (control.value.length <= 3) {
    return { validLname: true };
  }
  return null;
}
export enum RenewelStateList {
  "Renewal_FormCode" = "NA-REN",
  "Reciprocity_FormCode" = "MS-NA-REC",
  "MACE_Recipocity_FormCode" = "V2-SC-NA-REC",
  "MACE_Renewal_FormCode" = "V2-SC-NA-REN",
  "Grievance_Reviewer" = "GR_WS_Code",
  "Reinstate_renewal" = "NA-RIN"
}

export enum CartItemId {
  "Renewal" = "3",
  "Reciprocity" = "4"
}
export class paypal {
  id: string;
}
export const examName = ['Nurse Aide Bundle II', 'Nurse Aide Bundle I', 'Nurse Aide Bundle III']

export const ItemCode = ['NA-WR-PPD', 'NA-PWR', 'NA-PWR2', 'NA-PWR3']
export const ItemCodeExams = [{ ItemCode: 'NA-WR-PPD', ExamName: 'Nurse Aide Written Exam' }, { ItemCode: 'NA-PWR', ExamName: 'Nurse Aide Bundle I' }, { ItemCode: 'NA-PWR2', ExamName: 'Nurse Aide Bundle II' }, { ItemCode: 'NA-PWR3', ExamName: 'Nurse Aide Bundle III' }]



