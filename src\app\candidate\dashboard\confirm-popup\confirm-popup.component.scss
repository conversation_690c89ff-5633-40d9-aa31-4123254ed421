.submit {
    color: var(--text-color2);
}

.confirm {
    color: var(--text-popup);
    text-align: center;
}

.terms {
    font-size: 10px;
    color: var(--card-border);
    a {
        color: #0076c1;
        cursor: pointer;
    }
}

.loginBtn {
    font-weight: 500;
    font-size: 14px;
    margin-top: 1.2rem;
    padding-right: 100px;
    padding-left: 100px;
}

.cardBrd {
    border: var(--save-draft-border);
    border-radius: var(--border-radius);
}

.formcard {
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 85px);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 85px);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 85px);
    }
}

.custom{
    padding: 30px;
}

.custom1{
    display: flex;
    justify-content: center;
}