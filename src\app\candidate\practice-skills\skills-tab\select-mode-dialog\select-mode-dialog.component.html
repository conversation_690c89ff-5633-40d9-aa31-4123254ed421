<div>
    <div class="dialog-body flex justify-between items-center px-4 py-2">
        <h2 class="text-sm font-semibold">Select Mode</h2>
        <button class="close-btn" (click)="closeDialog()">✕</button>
    </div>
</div>
<div class="p-4 space-y-1 rounded-md">

    <div class="text-xs font font-semibold">
        <p>Please choose your preferred mode of practice to proceed</p>
        <p>Note:<span class="italic per_attempt">The pricing for each mode varies based on the level of interactivity
                and experience offered.</span> </p>
    </div>
    <div class="h-4"></div>
</div>
<div class="px-2 mb-2 space-y-4">
    <ng-container *ngFor="let mode of skillModeList">
        <exai-skill-mode-type [mode]="mode" [selectedModeId]="selectedMode?.id" (modeSelected)="selectMode($event)">
        </exai-skill-mode-type>
    </ng-container>


    <!-- Go Back -->
    <div class="mt-2">
        <button mat-button (click)="goBack()" class="go-back-btn  font-medium font-semibold">&larr; Go
            Back</button>
    </div>
</div>


<!-- Subtotal & Actions -->
<div class="dialog-body p-4 flex flex-wrap justify-between items-center">
    <div class="text-sm font-semibold">
        Subtotal:
        <span class="subtotal-text select_text">
            ${{ total }}
        </span>
    </div>
    <div class="space-x-2 flex flex-wrap justify-between">
        <button mat-button (click)="closeDialog()" class="cancel-btn">Cancel</button>
        <button mat-button [disabled]="!selectedMode" (click)="proceedNow()" class="proceed-btn">
            Proceed
        </button>
    </div>
</div>