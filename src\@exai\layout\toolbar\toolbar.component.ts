import {
  Component,
  ElementRef,
  HostBinding,
  Inject,
  Input,
  OnInit,
} from '@angular/core';
import { LayoutService } from '../../services/layout.service';
import icBookmarks from '@iconify/icons-ic/twotone-bookmarks';
import emojioneUS from '@iconify/icons-emojione/flag-for-flag-united-states';
import emojioneDE from '@iconify/icons-emojione/flag-for-flag-germany';
import icMenu from '@iconify/icons-ic/twotone-menu';
import { ConfigService } from '../../services/config.service';
import { map } from 'rxjs/operators';
import icPersonAdd from '@iconify/icons-ic/twotone-person-add';
import icAssignmentTurnedIn from '@iconify/icons-ic/twotone-assignment-turned-in';
import icBallot from '@iconify/icons-ic/twotone-ballot';
import icDescription from '@iconify/icons-ic/twotone-description';
import icAssignment from '@iconify/icons-ic/twotone-assignment';
import icReceipt from '@iconify/icons-ic/twotone-receipt';
import icDoneAll from '@iconify/icons-ic/twotone-done-all';
import { NavigationService } from '../../services/navigation.service';
import icArrowDropDown from '@iconify/icons-ic/twotone-arrow-drop-down';
import { PopoverService } from '../../components/popover/popover.service';
import { MegaMenuComponent } from '../../components/mega-menu/mega-menu.component';
import icSearch from '@iconify/icons-ic/twotone-search';
import { DOCUMENT } from '@angular/common';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';

import { CartItem } from 'src/app/candidate/scheduled/state/models/cartItem';
import { getCartItems, getCartPracticeItems } from 'src/app/candidate/state/shared/shared.actions';
import { get_PracticecartItems, get_cartItems } from 'src/app/candidate/state/shared/shared.selectors';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { CartSummaryPopupComponent } from 'src/app/candidate/scheduled/cart-summary-popup/cart-summary-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { PersonalInfo } from 'src/app/candidate/scheduled/payment/payment.component';
import { RolesList } from 'src/app/core/examroom-formbuilder/form-builder.types';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'exai-toolbar',
  templateUrl: './toolbar.component.html',
  styleUrls: ['./toolbar.component.scss'],
})
export class ToolbarComponent implements OnInit {
  @Input() mobileQuery: boolean;
 isButtonOpen = false;
  @Input()
  @HostBinding('class.shadow-b')
  hasShadow: boolean;
Roles:Array<{roleId:number,roleName:string
}>=[]
  navigationItems = this.navigationService.items;

  isHorizontalLayout$ = this.configService.config$.pipe(
    map((config) => config.layout === 'horizontal')
  );
  isVerticalLayout$ = this.configService.config$.pipe(
    map((config) => config.layout === 'vertical')
  );
  isNavbarInToolbar$ = this.configService.config$.pipe(
    map((config) => config.navbar.position === 'in-toolbar')
  );
  isNavbarBelowToolbar$ = this.configService.config$.pipe(
    map((config) => config.navbar.position === 'below-toolbar')
  );

  icSearch = icSearch;
  icBookmarks = icBookmarks;
  emojioneUS = emojioneUS;
  emojioneDE = emojioneDE;
  icMenu = icMenu;
  icPersonAdd = icPersonAdd;
  icAssignmentTurnedIn = icAssignmentTurnedIn;
  icBallot = icBallot;
  icDescription = icDescription;
  icAssignment = icAssignment;
  icReceipt = icReceipt;
  icDoneAll = icDoneAll;
  icArrowDropDown = icArrowDropDown;
  elem: any;
  cart: Array<any> = [];
  Practicecart: Array<any> = [];
  screenShow: boolean = false;
  subtotal: any
  total: any
  candidateId:any;
  usersData:any;
  personalInfo: PersonalInfo = new PersonalInfo();
  roleId:number;
  roleName:string
  isMobile:boolean;

  constructor(
    private layoutService: LayoutService,
    private dialog: MatDialog,
    private configService: ConfigService,
    private navigationService: NavigationService,
    private popoverService: PopoverService,
    private store: Store,
    public global: GlobalUserService,
    private http:HttpClient,
    @Inject(DOCUMENT) private document: any
  ) { }
  redirectToCredentialTicketForm(){
    const formValue = {firstName:this.personalInfo.firstname,lastName:this.personalInfo.lastname,email:this.personalInfo.email,candidateId:this.candidateId};
    window.location.href = `assets/img/credentia-ticket-form.html?formValue=${formValue.firstName}&lastName=${formValue.lastName}&email=${formValue.email}&candidateId=${formValue.candidateId}`;
  }

 async userData(): Promise<void>{
  this.usersData = this.global.getUserData();
    this.personalInfo.firstname = this.usersData.given_name;
    this.personalInfo.lastname = this.usersData.family_name;
    this.personalInfo.email = this.usersData.email;
 }
 
  ngOnInit() {
    this.userData()
    this.getRoles()
    this.elem = document.documentElement;
   
    this.global.userDetails.subscribe((data: any) => {
      if(data != null){
        this.candidateId=this.global.personId;
        // Use the active role name from the modified data object
        this.roleName = data._activeRoleName || this.global.roleName;

        this.store.dispatch<Action>(
          getCartItems({ personTenantRoleId: this.global.candidateId })
        );
      }
      if (!data) return;
      this.store.dispatch<Action>(
        getCartItems({ personTenantRoleId: this.global.candidateId })
      );
      this.store.dispatch<Action>(
        getCartPracticeItems({ personTenantRoleId: this.global.candidateId })
      );



    });
    this.store.select(get_cartItems).subscribe((cart) => {
      this.cart = cart;
      this.subtotal = this.cart.reduce((acc, val) => acc += val.amount, 0)
      this.total = this.cart.reduce((acc, val) => acc += val.amount, 1);
    });

    this.store.select(get_PracticecartItems).subscribe(cartItems => {
      if (cartItems != null) {
        this.Practicecart = cartItems
        this.subtotal = this.Practicecart.reduce((acc, val) => acc += val.amount, 0)
        this.total = this.Practicecart.reduce((acc, val) => acc += val.amount, 1);
      } else {
      }

    })
    this.layoutService.isMobile$.subscribe(res => {
      this.isMobile = res;
    });
  }

  openQuickpanel() {
    this.layoutService.openQuickpanel();
  }

  openSidenav() {
    this.layoutService.openSidenav();
  }

  openMegaMenu(origin: ElementRef | HTMLElement) {
    this.popoverService.open({
      content: MegaMenuComponent,
      origin,
      position: [
        {
          originX: 'start',
          originY: 'bottom',
          overlayX: 'start',
          overlayY: 'top',
        },
        {
          originX: 'end',
          originY: 'bottom',
          overlayX: 'end',
          overlayY: 'top',
        },
      ],
    });
  }

  openSearch() {
    this.layoutService.openSearch();
  }

  openFullscreen() {
    this.screenShow = true;
    if (this.elem.requestFullscreen) {
      this.elem.requestFullscreen();
    } else if (this.elem.mozRequestFullScreen) {
      /* Firefox */
      this.elem.mozRequestFullScreen();
    } else if (this.elem.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      this.elem.webkitRequestFullscreen();
    } else if (this.elem.msRequestFullscreen) {
      /* IE/Edge */
      this.elem.msRequestFullscreen();
    }
  }

  closeFullscreen() {
    this.screenShow = false;
    if (document.exitFullscreen) {

      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      /* Firefox */
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      /* IE/Edge */
      this.document.msExitFullscreen();
    }
  }

  toggleButton(){
    if (window.productFruits.api.inAppCenter.open()) {
      window.productFruits.api.inAppCenter.close();
    } else {
      window.productFruits.api.inAppCenter.open();
    }
}

getRoles(){
   this.http.get(`${environment.baseUrl}login/get-role-assign?emailId=${this.global.emailIdToStartExam}`).subscribe((data:Array<{roleId:number,roleName:string
   }>)=>{
       if(data.length > 0){
        // Filter out current role and deduplicate by role name
        const filteredRoles = data.filter((x)=>x.roleName !=this.roleName);

        // Remove duplicates by role name, keeping the first occurrence
        const uniqueRoles = filteredRoles.filter((role, index, self) =>
          index === self.findIndex(r => r.roleName === role.roleName)
        );

        this.Roles = uniqueRoles;
       }
   })
}

SelectRole(role, idToken, stateId): void {
  this.global.SelectRole(role,idToken,stateId)
}

  cartItems(message:string) {
    if (this.cart.length > 0 || this.Practicecart.length > 0) {
      const dialogRef = this.dialog.open(CartSummaryPopupComponent, {
        width: '360px',
        height: '550px',
        data: { message:message },
        // position: {c
        //   top: '57px',
        //   right: '190px',
        // }

      });
    }
  }
}
