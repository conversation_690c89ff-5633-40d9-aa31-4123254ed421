
<div class="bg-white">
    <div class="px-gutter pt-2 pb-2 bg-white" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
        <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
            <div class="pt-2 titleFont" fxLayout="column">
                <h5><strong>{{ this.lngSrvc.curLangObj.value.view_bundle }}</strong></h5>
                <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
                    [fontSize]="'0.65rem'">
                </app-ng-dynamic-breadcrumb>
            </div>
        </div>

        <h2 class="text-sm mt-4 font-semibold">{{ bundleData?.title }}</h2>
        <p class="bg_color p-2 leading-relaxed tracking-wide text-justify">{{ bundleData?.description }}
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Nulla vitae eros nec justo tempor egestas. Curabitur nec lacinia eros.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br> <br>
            Nulla vitae eros nec justo tempor egestas. Curabitur nec lacinia eros.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit
            Nulla vitae eros nec justo tempor egestas. Curabitur nec lacinia eros.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Nulla vitae eros nec justo tempor egestas. Curabitur nec lacinia eros.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
        </p>

        <div class="mt-4">
            <h2 class="text-sm font-semibold mt-4">Other Information</h2>

            <div class="info-grid p-2">
                <label>Bundle ID</label>
                <div>: {{ bundleData?.id }}</div>

                <label>Program</label>
                <div>: NNAAP Practice Skills 3D </div>

                <label>Created Date</label>
                <div>: Feb 20, 2024 | 12:25</div>

                <label>No. of Skills</label>
                <div>: 02 </div>

                <label>Default Attempts</label>
                <div>: 1 per each skill </div>

                <label>Expiry</label>
                <div>: 60 days (after purchase)</div>
            </div>
        </div>
    </div>
</div>