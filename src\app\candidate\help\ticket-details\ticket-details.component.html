
<div  class="shadow-none card cardBorder justify-start dashboard px-2 py-4 h-full" gdColumn="2/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1">
    <div class="eligibility-desc mb-3">
        <div class="eligibility1 touch-auto overflow-auto">
        <div class="exam ">
          <h2 class="px-5 py-1 flex items-center text-xs font-bold fontColor1">
              <button (click)="goBack()" class="flex items-center">
                  <mat-icon class="mr-4 icons">
                      arrow_backwards_ios
                  </mat-icon>
              </button>
            {{ selectedCategory.name }}
          </h2>
        <!-- ===========  CARDS START -->
        <ul>
            <li class="card cardBorder shadow-none m-4" *ngFor="let ticket of selectedTicket$ | async">
                <exai-card *ngIf="selectedCategory.name=== 'Form'"
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
                [ticketRaised]="ticket.ticketRaised"
                [name]="ticket.formName"
                [eligibilityRouteName]="ticket.eligibilityRouteName"
                [id]="ticket.code"
                [applicationId]="ticket.personFormId"
                [createdDate]="ticket.submittedDateTime"
                [status]="ticket.status"
                [changedDate]="ticket.statusSinceDate"></exai-card>

               <exai-card *ngIf="selectedCategory.name=== 'Exam'"
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
                [ticketRaised]="ticket.ticketRaised"
                [name]="ticket.examName"
                [eligibilityRouteName]="ticket.eligibilityRouteName"
                [id]="ticket.id"
                [applicationId]="ticket.examId"
                [createdDate]="ticket.registeredDateTime"
                [status]="ticket.examStatusType"
                [changedDate]="ticket.statusSinceDate"></exai-card>

               <exai-card *ngIf="selectedCategory.name=== 'Certificate'"
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
                [ticketRaised]="ticket.ticketRaised"
                [name]="ticket.RegistryName"
                [eligibilityRouteName]="ticket.eligibilityRoute"
                [id]="ticket.Id"
                [applicationId]="ticket.personEventId"
                [createdDate]="ticket.submittedDateTime"
                [status]="ticket.Status"
                [changedDate]="ticket.statusSinceDate"></exai-card>
            </li>
        </ul>
        <!-- <div class="questions">
            <ul>
                <li class="px-4 mt-4  w-full " *ngFor="let question of selectedCategory.dataDetail.FAQ" (click)="toggleAnswer(question)">
                    <div class="flex items-center justify-between questions py-2">
                        <p class="text-xs">{{ question.Question }}</p>
                        <mat-icon *ngIf="!question.ShowAnswer" class="ml-auto -mx-3 icons">
                            keyboard_arrow_down
                        </mat-icon>
                        <mat-icon *ngIf="question.ShowAnswer" class="ml-auto -mx-3 icons">
                            keyboard_arrow_up
                        </mat-icon>
                    </div>    
                    <div class="flex flex-col items-start justify-start p-4" *ngIf="question.ShowAnswer">
                        <div class="w-full">
                            <p class="text-xs light-txt">{{ question.Answer }}</p>
                        </div>    
                    </div>
                </li>
            </ul>
        </div> -->
        <!-- =========== CARDS END -->
      </div>
  </div>
</div>
