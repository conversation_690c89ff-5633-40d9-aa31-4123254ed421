import { Location } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable, Subscription } from 'rxjs';
import { tap } from 'rxjs/operators';
import { HelpService } from '../help.service';
import { Category } from '../interfaces/category';

@Component({
  selector: 'exai-ticket-details',
  templateUrl: './ticket-details.component.html',
  styleUrls: ['./ticket-details.component.scss']
})
export class TicketDetailsComponent implements OnInit, OnDestroy {

  constructor(
    private helpService: HelpService,
    private route: ActivatedRoute,
    private location: Location
  ) { }

  selectedCategorySub: Subscription
  selectedCategory: Category
  selectedTicket$: Observable<any[]>

  ngOnInit(): void {
    const id = this.route.snapshot.params.id   
    this.helpService.selectedTicketId.next(id)
    this.selectedCategorySub = this.helpService.selectedCategory.pipe(tap(data => {
      data.dataDetail.FAQ.forEach(el => {
        el.ShowAnswer = false
      })
    })).subscribe(data => this.selectedCategory = data)
    
    this.selectedTicket$ = this.helpService.getTicket(id).pipe(
      tap(data => {})
    )
  }

  ngOnDestroy() {
    this.selectedCategorySub.unsubscribe()
  }

  toggleAnswer(question) {
    question.ShowAnswer = !question.ShowAnswer
  }

  goBack() {
    this.location.back()
  }
}
