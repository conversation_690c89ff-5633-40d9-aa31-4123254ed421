import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormBuilderService } from '../form-builder.service';

@Component({
  selector: 'exai-form-json-output',
  templateUrl: './form-json-output.component.html',
  styleUrls: ['./form-json-output.component.scss']
})
export class FormJsonOutputComponent implements OnInit {

  constructor(public formBuilderService: FormBuilderService) { }

  ngOnInit(): void {
  }
  JSONified(object:any) {
    return JSON.stringify(object.map((x: FormGroup) => {
      return x.value;
    }))
  }
  
}
