import { Component, Inject, inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'exai-add-attempt-dialog',
  templateUrl: './add-attempt-dialog.component.html',
  styleUrls: ['./add-attempt-dialog.component.scss']
})
export class AddAttemptDialogComponent implements OnInit {

  skill: any;
  quantity = 1;


  constructor(
    private _dialogRef : MatDialogRef<AddAttemptDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data : any
  ) { 
    this.skill = data.skill;
  }

  ngOnInit(): void {
  }

  /**
   * closeDialog method close
   */
  closeDialog(){
    this._dialogRef.close();
  }

  /**
   * increaseAttempts by clicking ADD
   */
  increaseAttempts(){
    this.quantity++;
  }

  /**
   * decreaseAttempts by clicking SUB
   */
  decreaseAttempts(){
    if (this.quantity > 1) this.quantity--;
  }
}
