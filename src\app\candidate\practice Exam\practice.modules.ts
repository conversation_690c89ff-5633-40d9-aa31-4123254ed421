import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatExpansionModule} from '@angular/material/expansion';

import { ContainerModule } from 'src/@exai/directives/container/container.module';

import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';

import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

import { NgxMaskModule, IConfig } from 'ngx-mask';
import {IMaskModule} from 'angular-imask';
import {MatTabsModule} from '@angular/material/tabs';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { ImgPopUpComponent } from 'src/app/core/img-pop-up/img-pop-up.component';
import { MatProgressBarModule } from '@angular/material/progress-bar';

import { CommonComponentModule } from 'src/app/core/common-component/common-component.module';
import { PracticeExamComponent } from './practice.component';
import { PracticeExamRoutingModule } from './practice-routing.module';
import { RegisterExamComponent } from './register_practice_exam/register-practice.component';
import { ShowPracticeExamResult } from '../examination-result/service';
// import { NgxSpinnerModule } from "ngx-spinner";
export const options: Partial<IConfig> | (() => Partial<IConfig>) = null;

@NgModule({
  declarations: [
     PracticeExamComponent,
     RegisterExamComponent
  ],
  imports: [
    CommonModule,
    PracticeExamRoutingModule,
    NgDynamicBreadcrumbModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatRadioModule,
    MatExpansionModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTabsModule,
    MatSelectModule,
    MatOptionModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    FlexLayoutModule,
    ContainerModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatMenuModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    NgxMaskModule.forRoot(),
    IMaskModule,
    // NgxSpinnerModule,
   FormsModule,
   MatProgressBarModule,
   CommonComponentModule,
    HttpClientModule

  ],
  providers:[{provide: MatDialogRef,
    useValue: {}},
    DatePipe, 
    
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
    ShowPracticeExamResult
  ]
})
export class PracticeExamModule { }
