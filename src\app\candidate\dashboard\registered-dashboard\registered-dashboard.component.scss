.dashboard {
  height: calc(
    100vh - var(--toolbar-height) - var(--navigation-height) -
      var(--footer-height)
  );
}

.db {
  justify-content: center !important;
}

.welc {
  color: var(--text-color2);
}

.welc-note {
  color: var(--text-color1);
}

.title-hed {
  color: var(--text-dropdown);
}

.state-elig {
  color: var(--text-color1);
}

.bg-color {
  background-color: var(--background-base2);
}

.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: 0.1rem 0.5rem;
}

.arrow {
  justify-content: center !important;
}

.iconSize {
  width: 12px;
  height: 12px;
}

.status {
  color: var(--text-profile);
}

.status1 {
  color: var(--text-color1);
}

.active {
  color: var(--text-color2);
}

.active2 {
  color: var(--text-color1);
}

.content {
  padding-top: 16px;
  padding-bottom: 16px;
  padding-right: 16px;
}

.content > mat-card {
  margin-bottom: 10px;
}

mat-card {
  max-width: 230px;
  max-height: 150px;
  padding: 0px;
}

.update {
  color: var(--text-color1);
  font-size: 0.6rem;
}

.content1 {
  color: var(--text-color4);
}

.active2 {
  color: var(--text-color2);
}

// System Check
.imgSize {
  width: 8em;
}

// System Check
// Timer
.time-card {
  flex-direction: column;
}

.mat-card-title {
  font-style: normal;
  font-size: 1rem !important;
  font-weight: 600;
}

.mat-card-subtitle {
  font-style: normal;
  font-size: 0.65rem !important;
  font-weight: normal;
}

.mat-card {
  background: white;
  box-shadow: none;
}

.system-title{
  // font-weight: 500;
}

::ng-deep .mat-dialog-container {
  padding: 12px!important; 
}