.card-borders {
  border: var(--border);
  border-radius: var(--border-radius);
}

.title {
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0px;
  color: #333;

  width: 286px;
  height: 50px;

  display: -webkit-box;
  // -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 16px;
  padding-top: 8px;
}

.description-text {
  overflow-y: auto;
  // font-weight: 400;
  // font-size: 14px;
  // line-height: 1;
  // letter-spacing: 0px;
  color: #7d7d7d;
  margin: 0;

  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
  font-family: "Roboto", sans-serif;
  padding-left: 16px;
  margin-top: 12px !important;
  max-height: none;
  overflow: hidden;
  transition: max-height 0.3s ease;
  margin-bottom: 10px;
}

.viewmore {
  color: #0076c1;
}

.description-text.collapsed {
  max-height: 80px;
}

.description-text::-webkit-scrollbar {
  width: 4px;
}

.description-text::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 8px;
}

.meta {
  padding-bottom: 23.86px;
  padding-right: 44px;
}

.meta-label {
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0;
  color: #c4c4c4;
  padding-left: 16px;
  width: 42px;
  height: 16px;
}

.meta-value {
  color: #0b132a;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0px;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  width: 42px;
  height: 16px;
  padding-left: 16px;
  padding-top: 7px;
}

.meta-value1 {
  color: #1b75bb;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0px;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  width: 42px;
  height: 16px;
  padding-left: 16px;
  padding-top: 7px;
}

.try-now-btn {
  color: var(--text-color2);
  background-color: #e5f1f9;
  border-radius: 4px;
  padding: 8px 14px 8px 14px;
}

.try-now-btn:hover {
  opacity: 0.9;
}

.try-now {
  color: #1b75bb;
}
