.header{
  height: 40px;
  display: flex!important;

  .title{
      color: #000000;
      font-size:14px;
  }
}
.font{
color: #11263C;

}
.active{
  color: var(--text-color2);
}
.add {
  border: 1px solid var(--text-color2);
  color: var(--text-color2);
  width: 84px;
}

.paymemntoptions{
  line-height: 9px !important;
  min-width: unset !important;
  //font-size: 10px;
}
.dashboard {
  @screen xl {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 80px
    );
  }
  @screen lg {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 80px
    );
  }
  @screen md {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 80px
    );
  }
}

// .payment-card {
//   width: min-content;
// }

.bg-color{
  background-color: #FAFAFA;
}
.content {
  padding: 16px;
}
.total{
  color: #11263C;

}

.content1 > mat-card {
  margin: 12px;
}
.status1{
  color: #7d7d7d;
}
.status{
  color: 
   #C4C4C4;

};

.btn1{
  border: none !important;
}


.buuton2 {
  line-height: 32px !important;
  min-width: unset !important;
  font-size: 12px;
  background-color: var(--button-background) !important;
  color: #ffff;
}

.buuton1{
line-height: 26px !important;
min-width: unset !important;
font-size: 12px; 
color: var(--button-background) !important;
border:1px solid var(--button-background) !important;
}
.Apply{
  color: var(--button-background) !important;
}

.button{
color: var(--button-background) !important;
// margin-top: -1.5rem;
}

::ng-deep .mat-input-element { width: 40% ; }

::ng-deep .mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay, .mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay {
  opacity: 0 !important;
}
::ng-deep .mat-icon{
  font-size: 17px !important;
  cursor: pointer !important ;
}
::ng-deep button.mat-accent.mat-button-disabled, .mat-flat-button.mat-warn.mat-button-disabled, .mat-flat-button.mat-button-disabled.mat-button-disabled, .mat-raised-button.mat-primary.mat-button-disabled, .mat-raised-button.mat-accent.mat-button-disabled, .mat-raised-button.mat-warn.mat-button-disabled, .mat-raised-button.mat-button-disabled.mat-button-disabled, .mat-fab.mat-primary.mat-button-disabled, .mat-fab.mat-accent.mat-button-disabled, .mat-fab.mat-warn.mat-button-disabled, .mat-fab.mat-button-disabled.mat-button-disabled, .mat-mini-fab.mat-primary.mat-button-disabled, .mat-mini-fab.mat-accent.mat-button-disabled, .mat-mini-fab.mat-warn.mat-button-disabled, .mat-mini-fab.mat-button-disabled.mat-button-disabled {
  // background-color: rgba(0, 0, 0, 0.12);
  color: #fff !important;
}

.delete{
  cursor: pointer;
}

.height {
  height: 28px;
}

::ng-deep{
  .mat-content {
    overflow: inherit!important;
  }
}

.registerCard {
  z-index: 1 !important;
}

.text-active-color{
  color: #ff0000b5;
  font-size: 0.75rem;
}


::ng-deep .mat-radio-container {
  width: 15px!important;
  height: 15px;
}

.rowSpace {
  white-space:normal!important;
}
// .invalid-cvv{
//   padding-top: 1em !important;
// }

// ::ng-deep .mat-error {
//     margin-top: 1rem !important;
//   }

.content2 {
  overflow: auto;
  @screen xl {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 15rem
    );
  }
  @screen lg {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 15rem
    );
  }
  @screen md {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 15rem
    );
  }
}

.scheduled {
  overflow: auto;
  @screen xl {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 8rem
    );
  }
  @screen lg {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 8rem
    );
  }
  @screen md {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 8rem
    );
  }
}

::ng-deep .mat-expansion-panel-body {
  padding: 0px!important;
}

.block-1 {
  display: block!important;
}

// .form-bank {
//   @screen xl {
//     width: 30%;
//   }
//   @screen lg {
//     width: 30%;
//   }
//   @screen md {
//     width: 30%;
//   }
// }

.promo-code.mat-input-element{
  width: 56% !important;
}

.hide{
  background: #e9e9e9;
  display: block !important;
  filter: blur(10px) !important;
  pointer-events: none !important;
  user-select: none !important;
  opacity: 0.5;
}

.hide1{
  // width: 100vw !important;
  // height: 100vh !important;
  display: none !important;
}

.spinner {
  position: absolute;
  top: 30vh;
  left: 35vw;
}

.loading {
  position: absolute;
  top: 50vh;
  left: 28vw;
}

::ng-deep .paypal-button-tagline{
  display: none;
}
