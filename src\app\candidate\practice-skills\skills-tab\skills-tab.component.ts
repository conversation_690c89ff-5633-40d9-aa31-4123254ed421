import {
  AfterViewChecked,
  After<PERSON><PERSON>wInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  ViewChild,
  ViewChildren,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { ActivatedRoute, Router } from "@angular/router";
import { skillsList } from "../practice-skills.data";
import { PracticeSkill } from "../state/models/practice-skill.model";
import { Observable } from "rxjs";
import { BreakpointObserver, Breakpoints } from "@angular/cdk/layout";
import { SkillViewMoreDialogComponent } from "./skill-view-more-dialog/skill-view-more-dialog.component";
import { AddAttemptDialogComponent } from "./add-attempt-dialog/add-attempt-dialog.component";
import { DynamicMenuItem } from "src/app/core/common-component/menu/model/dynamic-menu-item";
import {
  PracticeSkillMode,
  PracticeSkillModeDetail,
} from "../state/models/practice-skill-mode.model";

interface SkillViewModel extends PracticeSkill {
  isExpanded: boolean;
  showToggle: boolean;
}
@Component({
  selector: "exai-skills-tab",
  templateUrl: "./skills-tab.component.html",
  styleUrls: ["./skills-tab.component.scss"],
})
export class SkillsTabComponent
  implements OnInit, AfterViewChecked, OnChanges, OnDestroy
{
  @Input() skills: PracticeSkill[] = [];
  @Input() skillsMode: PracticeSkillMode;
  @Output() addToCartEvent = new EventEmitter<PracticeSkill>();

  selectedSkill: PracticeSkill | null = null;
  skillViewModels: SkillViewModel[] = [];
  @ViewChildren("desc") descriptionEls!: QueryList<ElementRef>;

  private hasCheckedHeight = false;
  isMobileOrTablet = false;
  menuItems: DynamicMenuItem[] = [
    { label: "View", icon: "visibility", value: "view" },
    { label: "Add to Cart", icon: "shopping_cart", value: "add_to_cart" },
  ];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private _dialog: MatDialog,
    private breakpointObserver: BreakpointObserver,
    private cdRef: ChangeDetectorRef
  ) {}
  ngOnDestroy(): void {
    this._dialog.closeAll();
  }

  ngOnInit(): void {
    this.breakpointObserver
      .observe([Breakpoints.XSmall, Breakpoints.Small, Breakpoints.Medium])
      .subscribe((result) => {
        this.isMobileOrTablet = result.matches;
      });
  }

  /**
   *
   * @param event
   */
  @HostListener("document:click", ["$event"])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest(".menu_pop") && !target.closest(".menu-toggle")) {
      this.selectedSkill = null;
    }
  }
  /**
   *
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes["skills"] && this.skills) {
      this.skillViewModels = this.skills.map((skill) => ({
        ...skill,
        isExpanded: false,
        showToggle: false,
      }));

      this.hasCheckedHeight = false;
    }
  }
  /**
   *
   */
  ngAfterViewChecked(): void {
    if (!this.hasCheckedHeight && this.descriptionEls?.length) {
      setTimeout(() => {
        this.descriptionEls.forEach((elRef, index) => {
          const el = elRef.nativeElement;
          if (el.scrollHeight > 60) {
            this.skillViewModels[index].showToggle = true;
          }
        });
        this.hasCheckedHeight = true;
        this.cdRef.detectChanges(); //  Trigger manual change detection
      });
    }
  }
  /**
   *
   */
  ngAfterViewInit(): void {
    if (this.descriptionEls?.length) {
      this.descriptionEls.forEach((elRef, index) => {
        const el = elRef.nativeElement;
        if (el.scrollHeight > 60) {
          this.skillViewModels[index].showToggle = true;
        }
      });
      this.cdRef.detectChanges();
    }
  }
  /**
   *
   * @param skill
   */
  openViewPage(skill: SkillViewModel): void {
    if (this.isMobileOrTablet) {
      this.router.navigate(["/skills", skill.practiceSkillGuid]);
    } else {
      skill.isExpanded = !skill.isExpanded;
    }
  }
  /**
   *
   * @param skill
   */
  openMenu(skill: PracticeSkill) {
    this.selectedSkill = this.selectedSkill === skill ? null : skill;
  }

  /**
   *
   */
  viewSkill(skill: PracticeSkill) {
    this.router.navigate(["/practice-skills/skills", skill.practiceSkillGuid], {
      relativeTo: this.route,
      state: { skillData: skill },
    });
  }
  /**
   *
   * @param skill
   */
  viewSkillDialog(skill: PracticeSkill): void {
    const dialogRef = this._dialog.open(SkillViewMoreDialogComponent, {
      data: skill,
      width: "600px",
      panelClass: "no-padding-dialog",
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === "try") {
        this.tryNow(skill);
      }
    });
  }
  /**
   *
   * @param skill
   */
  tryNow(skill: PracticeSkill): void {
    console.log("Trying skill:", skill);
  }

  addToCart(skill: PracticeSkill) {
    this.addToCartEvent.emit(skill);
    this.selectedSkill = null;
  }

  /**
   *
   * @param skill
   */
  openAddAttemptDialog(skill: PracticeSkill) {
    this._dialog.open(AddAttemptDialogComponent, {
      width: "600px",
      data: { skill, skillsMode: this.skillsMode.data },
      panelClass: "no-padding-dialog",
    });
  }

  toggleMenu(skill: any) {
    this.selectedSkill = this.selectedSkill === skill ? null : skill;
  }

  handleMenuAction(item: DynamicMenuItem, skill: any) {
    switch (item.value) {
      case "view":
        this.viewSkill(skill);
        break;
      case "add_to_cart":
        this.addToCart(skill);
        break;
    }

    this.selectedSkill = null; // close menu after action
  }
}
