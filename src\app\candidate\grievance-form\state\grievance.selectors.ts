import { createFeatureSelector, createSelector } from "@ngrx/store";
import { GrevienceModuleState } from "./grievance.model";
import { DashboardState } from "./grievance.store";
// import {getDashboardState}from "../../dashboard/state/dashboard.selectors";
// import {DashboardState} from "../../dashboard/state/dashboard.state";



export const GRIEVANCE_MODULE_STATE = "GRIEVANCE_MODULE_STATE";
const grevienceModuleState = createFeatureSelector<GrevienceModuleState>(GRIEVANCE_MODULE_STATE);

export const DASHBOARD_STATE_NAME = 'DashboardModuleState';
export const APPLICATION_STATE_NAME = "ApplicationModuleState";
export const getDashboardState = createFeatureSelector<DashboardState>(DASHBOARD_STATE_NAME);

export const selectorLoadAllSuccess = createSelector(
  grevienceModuleState,
  (state) => {
    return state.grevienceFormList;
  }
);

export const selectorLoadReportGrievance = createSelector(
  grevienceModuleState,
  (state) => {
    return state.reportGrievance
  }
);

export const selectorLoadSavedReportGrievance = createSelector(
  grevienceModuleState,
  (state) => {
    return state.saveGrievance
  }
)

export const selectorLoadSaveDraftReportGrievance = createSelector(
  grevienceModuleState,
  (state) => {
    return state.saveDraftGrievance
  }
)


export const selectUpcommmingExam = createSelector(
  grevienceModuleState, (state) =>{
    return state.upcomingExam
})
export const selectorViewGrievance = createSelector(
  grevienceModuleState,
  (state) => {
    return state.viewFormStatus
  }
)

export const selectorViewGrievanceProgress = createSelector(
  grevienceModuleState,
  (state) => {
    return state.viewFormProgress
  }
)

export const get_isCancelled = createSelector(grevienceModuleState, (state)=>{
  return state.deletedGrievanceForm;
});