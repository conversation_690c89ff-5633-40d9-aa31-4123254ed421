import { createFeatureSelector, createSelector } from "@ngrx/store";
import {
  PracticeFeatureState,
  PracticeSkillsState,
  PracticeSkillModeState,
} from "./practice-skills.state";

export const PRACTICE_FEATURE_KEY = "practice";

export const selectPracticeFeatureState =
  createFeatureSelector<PracticeFeatureState>(PRACTICE_FEATURE_KEY);

// Select Skills Mode
export const selectPracticeSkillsModeState = createSelector(
  selectPracticeFeatureState,
  (state) => state.skillModeState
);

export const selectPracticeSkillsModes = createSelector(
  selectPracticeSkillsModeState,
  (state) => state.modeResponse
);

export const selectPracticeSkillsModesLoading = createSelector(
  selectPracticeSkillsModeState,
  (state) => state.loading
);

export const selectPracticeSkillsModesError = createSelector(
  selectPracticeSkillsModeState,
  (state) => state.error
);

// Select Skills State
export const selectPracticeSkillsState = createSelector(
  selectPracticeFeatureState,
  (state) => state.skillsState
);

// Practice Skills Selectors
export const selectPracticeSkills = createSelector(
  selectPracticeSkillsState,
  (state) => state.skills
);

export const selectPracticeSkillsLoading = createSelector(
  selectPracticeSkillsState,
  (state) => state.loading
);

// Selected Skill by GUID (single skill detail)
export const selectPracticeSkillByGuidState = createSelector(
  selectPracticeFeatureState,
  (state) => state.skillByGuidState
);

export const selectPracticeSkillByGuidResponse = createSelector(
  selectPracticeSkillByGuidState,
  (state) => state.skillResponse
);

export const selectPracticeSkillByGuidLoading = createSelector(
  selectPracticeSkillByGuidState,
  (state) => state.loading
);

export const selectPracticeSkillByGuidError = createSelector(
  selectPracticeSkillByGuidState,
  (state) => state.error
);

// Select Bundles State
export const selectPracticeSkillBundlesState = createSelector(
  selectPracticeFeatureState,
  (state) => state.bundlesState
);
// Practice Bundles Selectors
export const selectPracticeSkillBundles = createSelector(
  selectPracticeSkillBundlesState,
  (state) => state.bundles
);

export const selectPracticeSkillBundlesLoading = createSelector(
  selectPracticeSkillBundlesState,
  (state) => state.loading
);

// Optionally, add pagination
export const selectBundlesPageSize = createSelector(
  selectPracticeSkillBundlesState,
  (state) => state.pageSize
);

export const selectBundlesPageNo = createSelector(
  selectPracticeSkillBundlesState,
  (state) => state.pageNo
);

export const selectBundlesTotalRecords = createSelector(
  selectPracticeSkillBundlesState,
  (state) => state.totalRecords
);
