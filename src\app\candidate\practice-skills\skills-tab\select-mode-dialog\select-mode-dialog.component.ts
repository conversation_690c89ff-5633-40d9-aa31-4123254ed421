import { Component, Inject, OnInit } from "@angular/core";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { PracticeSkill } from "../../state/models/practice-skill.model";
import { Router } from "@angular/router";
import { PracticeSkillModeDetail } from "../../state/models/practice-skill-mode.model";
import { SkillsModeTypeEnum } from "src/app/core/Dto/enum";
import { GlobalUserService } from "src/app/core/global-user.service";
import { PracticeSkillService } from "../../state/service/practice-skill.service";
import { Action, Store } from "@ngrx/store";
import { getCartItems } from "src/app/candidate/state/shared/shared.actions";

@Component({
  selector: "exai-select-mode-dialog",
  templateUrl: "./select-mode-dialog.component.html",
  styleUrls: ["./select-mode-dialog.component.scss"],
})
export class SelectModeDialogComponent implements OnInit {
  quantity = 1;
  skillModeList: PracticeSkillModeDetail[] = [];
  subtotal: number = 0;
  skill: PracticeSkill | null = null;

  selectedMode: PracticeSkillModeDetail | null = null;
  basePrice = 20;
  total: number = 0;
  constructor(
    public daialogRef: MatDialogRef<SelectModeDialogComponent>,
    private _dialog: MatDialog,
    private router: Router,
    private global: GlobalUserService,
    private practiceSkillService: PracticeSkillService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private store: Store
  ) {
    this.skillModeList = data.skillModeList || [];
    this.subtotal = data.subtotal || 0;
    this.skill = data.skill || null;
    this.quantity = data.quantity || 1;
  }

  ngOnInit(): void {
    this.total = this.subtotal == 0 ? this.basePrice : this.subtotal;
  }

  closeDialog(): void {
    this.daialogRef.close();
  }
  goBack(): void {
    this.daialogRef.close("back");
  }

  selectMode(mode: PracticeSkillModeDetail) {
    this.selectedMode = mode;
    this.calculateTotal();
  }

  confirm(): void {
    this.daialogRef.close(this.selectedMode);
  }

  tryNow(): void {
    this.daialogRef.close("try");
  }

  calculateTotal() {
    if (this.selectedMode) {
      this.total = this.subtotal + this.selectedMode.price;
    }
  }
  /**
   *
   */
  proceedNow(): void {
    if (this.selectedMode) {
      const user = this.global.candidateId;
      this.global.SkillsItems.push({
        skillItem: this.skill,
        mode: this.selectedMode,
        quantity: this.quantity,
        price: this.subtotal,
      });

      this.practiceSkillService
        .AddPracticeSkillToCart({
          personTenantRoleId: user,
          skillTypeId: 1,
          createdBy: user,
          practiceSkill: [
            {
              skillId: this.skill.practiceSkillGuid,
              additionalAttempt: this.quantity,
              practiceModeId: Number(this.selectedMode.id),
            },
          ],
        })
        .subscribe((response) => {
          if (response.success) {
            this.router.navigate(["/practice-skills/payment"], {
              state: {
                type: "practice-skill",
                skillItem: this.skill,
                mode: this.selectedMode,
                quantity: this.quantity,
              },
            });
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            );
            this.daialogRef.close(this.selectedMode);
            this.store;
          }
        });
    }
  }
}
