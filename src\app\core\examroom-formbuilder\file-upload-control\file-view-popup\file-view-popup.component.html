<ng-container>
    <div class="text-base flex  justify-end py-2 dynamicPopUp touch-auto overflow-auto" #PopupHeader>
        <div class="flex justify-end cursor-pointer">
            <mat-icon class="text-base flex" mat-dialog-close>close</mat-icon>
        </div>
    </div>
    <hr class="popUpCard">
    <ng-container *ngIf="(viewUrl$ | async) != null && ['png','jpg','jpeg','PNG','JPG','JPEG'].includes(this.fileExtension)" class="h-full cont">
        <div class="img-container">
            <img [src]="(viewUrl$ | async)" alt="uploaded img file" class="h-full view-image">
        </div>
    </ng-container>
    <ng-container *ngIf="(viewUrl$ | async) != null && !['png','jpg','jpeg','PNG','JPG','JPEG'].includes(this.fileExtension)" class="h-full">
        <ngx-doc-viewer [url]="(viewUrl$ | async)" [viewer]="'google'" class="w-full h-full view-document">
        </ngx-doc-viewer>
    </ng-container>
</ng-container>