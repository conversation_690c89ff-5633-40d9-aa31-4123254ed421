<div class="p-4 cardBrd touch-auto overflow-auto flex flex-col" style="min-height: 30vh;max-height:60vh;min-width:300px;"  exaiContainer>
    <div class="w-full" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div class="flex justify-end cursor-pointer" fxLayout="row">
                <mat-icon class="text-sm flex justify-end" mat-dialog-close>close</mat-icon>
            </div>
        </div>
    </div>
    <!-- <div class="w-full" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div class="flex justify-center submit" fxLayout="row">
                <h3> <b>{{examName}}</b></h3> 
            </div>
        </div>
    </div> -->
    <div class="w-full h-full max-h-full touch-auto overflow-auto">
        <div class="pb-4 flex justify-center t-xs confirm" fxLayout="row">
            <mat-selection-list #states [multiple]="false" [(ngModel)]="selectedState" class="w-full">
                <mat-list-option *ngFor="let state of allStatesList" [value]="state" class="state_option">
                    <p class="state_name">{{state.name}}</p>
                </mat-list-option>
            </mat-selection-list>
        </div>
    </div>
    <div class="flex" >
        <div class="w-full flex items-end justify-end">
            <div class="" fxLayout="column">
                <button class="btn-1" (click)="selectedStateEvent()" mat-button>Accept</button>

            </div>
        </div>
    </div>
</div>