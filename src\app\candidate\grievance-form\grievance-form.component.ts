import {
  Component,
  OnInit,
} from '@angular/core';
import { Router } from '@angular/router';
import { HttpService } from 'src/app/core/http-services/http.service';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { Action, Store } from '@ngrx/store';
import {
  GrevienceModuleState,
  GrievanceFormsList,
  upcomingExam,
} from './state/grievance.model';
import {
  clearGrievanceState,
  getupcomingExam,
  loadAll,
} from './state/grievance.actions';
import { selectorLoadAllSuccess } from './state/grievance.selectors';
import { selectUpcommmingExam } from './state/grievance.selectors';
import { Observable } from 'rxjs';
import { GrievanceFormService } from './grievance-form.service';
import * as moment from 'moment/moment';
import 'moment-timezone';
import { ScheduledService } from '../scheduled/scheduled.service';
import { FormTypes } from '../forms-wrapper/forms-wrapper.types';
import { getShowRegisterExam } from '../dashboard/state/dashboard.actions';
import { selectorShowRegisterExam$ } from '../dashboard/state/dashboard.selectors';
import { selectorGetRegisteredexam } from '../scheduled/state/scheduled.selectors';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'exai-grievance-form',
  templateUrl: './grievance-form.component.html',
  styleUrls: ['./grievance-form.component.scss'],
})
export class GrievanceFormComponent implements OnInit {
  hideGrievanceForm: boolean = false;
  examDetails:any;
  statusIcon = statusIcon;
  showRegisterExam:any;
  gridColumns = 4;
  register: boolean = false;
  listExam: any;
  errors: any;
  exam: boolean = false;
  details: boolean = false;
  isExamCompleted: boolean=false;
  matchedExams: any[] = [];
  // PersonGrevianceForm: Grievenceform;
  PersonGrievanceForm: GrievanceFormsList[];
  grivieanceForm$: Observable<Array<GrievanceFormsList>>;
  constructor(
    private router: Router,
    private http: HttpService,
    private global: GlobalUserService,
    private store: Store<GrevienceModuleState>,
    private _service: GrievanceFormService,
    private services:ScheduledService,
    public dialog: MatDialog
  ) {
    this.global.userDetails.subscribe((data: any) => {
      if (data) this.subscriptions();
    });

    this.store.select(selectorGetRegisteredexam).subscribe((result) => {
      this.examDetails = result;
    })
  }

  ngOnInit() {}
  subscriptions(): void {
    this.store.dispatch<Action>(getShowRegisterExam({ personTenantRoleId: this.global.candidateId }));
    this.store.dispatch<Action>(getupcomingExam({ candidateId: this.global.candidateId }));
    this.store.select(selectUpcommmingExam).subscribe((upcomingExam: upcomingExam[]) => {
        for (let i = 0; i < upcomingExam.length; i++) {
          if ((upcomingExam.length>0)) {
            this.isExamCompleted = true;
          }
        }
      });
      let n = Intl.DateTimeFormat().resolvedOptions()
    this.store.dispatch(clearGrievanceState());
    this.store.dispatch<Action>(loadAll());
    this.grivieanceForm$ = this.store.select(selectorLoadAllSuccess);
    this.grivieanceForm$.subscribe((data: Array<GrievanceFormsList>) => {
      if (data.length) {
        data = data.map((ele) =>
          Object.assign(
            {},
            ele,
            {
              submittedDateN: moment(ele.submittedDate).format(
                'MMMM Do, YYYY / h:mm A'
              ),
            },
            { examDateN: moment(ele.examDate).format('MM/DD/YYYY') },
            {
              examTimePDT: moment(ele.examDate)
                .tz(n.timeZone)
                .format('h:mm a z'),
            }
          )
        );
        this.PersonGrievanceForm = data;
        this.matchExamsWithForms();

        //this.grievance = true
        this.hideGrievanceForm = false;
        this.register = true;
        for (let i = 0; i < data.length; i++) {
          this.global.personFormId = data[i].personFormId;
        }
      } else if (data.length == 0) {
        this.PersonGrievanceForm = [];
        //this.grievance = false
        this.hideGrievanceForm = true;
        // this.register = false
      }
    });
    this.store.select(selectorShowRegisterExam$).subscribe((data) => {
      if (data) {
        this.showRegisterExam = data;
      }
      });
  }
  clickRoute() {
    this.router.navigateByUrl('exam-scheduled');
  }
  reportGrievance() {
    this.router.navigate(['grievance-form', 'report-grievance', FormTypes.Grievance, this.global.candidateId, this.global.eligibilityRouteId, this.global.stateId, this._service.viewGrievance.examId ? this._service.viewGrievance.examId : this._service.viewGrievance.id ]);
  }
  editGrievance(item: GrievanceFormsList): void {
    this.router.navigate(['grievance-form', 'report-grievance', FormTypes.Grievance, this.global.candidateId, item.eligiblityRoute, this.global.stateId, item.id, item.personFormId, 0]);
  }
  viewGrievance(item: any): void {
   item.statusId == 3?this.router.navigate(['grievance-form', 'report-grievance', FormTypes.Appeal, this.global.candidateId, item.eligiblityRoute, this.global.stateId,item.id,0]): this.router.navigate(['grievance-form', 'report-grievance', FormTypes.Grievance, this.global.candidateId, item.eligiblityRoute, this.global.stateId, item.id, item.personFormId, 0]);
  }
  reschedule(event: any): void {
    this.services.rescheduleInformation = event;
    this.router.navigateByUrl("exam-scheduled/register");
  }

  viewForm(item:any){
    item.statusId == 12?this.router.navigate(['grievance-form', 'report-grievance', FormTypes.Grievance, this.global.candidateId, item.eligiblityRoute, this.global.stateId, item.id, item.personFormId,0, item.statusId]): this.router.navigate(['grievance-form', 'report-grievance', FormTypes.Grievance, this.global.candidateId, item.eligiblityRoute, this.global.stateId, item.id, item.personFormId, 0]);
  }

  matchExamsWithForms(): void {
    this.matchedExams = [];
    this.PersonGrievanceForm.forEach(form => {
      this.examDetails.forEach(exam => {
        if (form.examName === exam.examName) {
          this.matchedExams.push(exam);
        }
      });
    });
  }
}

export enum statusIcon {
  'Approved' = 'assets/img/Group 354.svg',
  'Rejected' = 'assets/img/rejection.svg',
  'Nodata' = 'assets/img/grivence-null.svg',
  'Pending' = 'assets/img/Group 355.svg',
}
