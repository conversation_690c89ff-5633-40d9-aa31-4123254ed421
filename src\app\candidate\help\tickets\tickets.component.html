<div class="shadow-none card cardBorder justify-start dashboard px-2 pt-2 h-full" gdColumn="2/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1">
      <div class="eligibility-desc mb-3">
          <div class="eligibility1 touch-auto overflow-auto">
          <div class="exam">
            <h2 class="px-2 text-xs font-bold fontColor1">
              {{ selectedCategory.name }}
            </h2>
          <!-- ===========     CARD START -->
          <ul>
              <li *ngIf="selectedCategory.name=== 'General Licensure Questions'">
                <exai-support-ticket  
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
              ></exai-support-ticket >
              </li>
              <li *ngIf="selectedCategory.name=== 'CNA365 System Questions'">
                <exai-support-ticket 
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
              ></exai-support-ticket>
              </li>
              <li *ngIf="selectedCategory.name=== 'Other Questions'">
                <exai-support-ticket 
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
                ></exai-support-ticket>
              </li>
              <ng-container *ngIf= "!(selectedCategory.id > 3) "> 
                <li *ngFor="let ticket of tickets$ | async"  class="card cardBorder m-2 shadow-none">
                    <exai-card *ngIf="selectedCategory.name=== 'Form'"
                    [selectedCategoryName]="selectedCategory.name"
                    [getHelp]="false"
                    [ticketRaised]="ticket.ticketRaised"
                    [name]="ticket.formTypeName"
                    [eligibilityRouteName]="ticket.eligibilityRouteName"
                    [id]="ticket.code"
                    [applicationId]="ticket.personFormId"
                    [createdDate]="ticket.submittedDateTime"
                    [status]="ticket.status"
                    [changedDate]="ticket.statusSinceDate"></exai-card>
 
                   <exai-card *ngIf="selectedCategory.name=== 'Exam'"
                    [selectedCategoryName]="selectedCategory.name"
                    [getHelp]="false"
                    [ticketRaised]="ticket.ticketRaised"
                    [name]="ticket.examName"
                    [eligibilityRouteName]="ticket.eligibilityRouteName"
                    [id]="ticket.id"
                    [applicationId]="ticket.examId"
                    [createdDate]="ticket.registeredDateTime"
                    [status]="ticket.examStatusType"
                    [changedDate]="ticket.statusSinceDate"></exai-card>
 
                   <exai-card *ngIf="selectedCategory.name=== 'Certificate'"
                    [selectedCategoryName]="selectedCategory.name"
                    [getHelp]="false"
                    [ticketRaised]="ticket.ticketRaised"
                    [name]="ticket.RegistryName"
                    [eligibilityRouteName]="ticket.eligibilityRoute"
                    [id]="ticket.Id"
                    [applicationId]="ticket.personEventId"
                    [createdDate]="ticket.submittedDateTime"
                    [status]="ticket.Status"
                    [changedDate]="ticket.statusSinceDate"></exai-card>

                    
              </li>
                                </ng-container>
          </ul>
          <!-- =========== CARD END -->
        </div>
    </div>
</div>
