import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { HelpService } from '../help.service';
import { Category } from '../interfaces/category';
import { TicketCertificate } from '../interfaces/ticket-certificate';
import { TicketExam } from '../interfaces/ticket-exam';
import { TicketForm } from '../interfaces/ticketForm';

@Component({
  selector: 'exai-tickets',
  templateUrl: './tickets.component.html',
  styleUrls: ['./tickets.component.scss']
})
export class TicketsComponent implements OnInit, OnDestroy {

  constructor(
    private helpService: HelpService,
  ) { }

  selectedCategory: Category
  tickets$: Observable<TicketForm[] | TicketCertificate[] | TicketExam[]>
  selectedCategorySub: Subscription

  ngOnInit(): void {
    this.selectedCategorySub = this.helpService.selectedCategory.subscribe(data => {
      this.selectedCategory = data
    })
    this.tickets$ = this.helpService.getTickets()
  }

  ngOnDestroy() {
    this.selectedCategorySub.unsubscribe()
  }
  
}
