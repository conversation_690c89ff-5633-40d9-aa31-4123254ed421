export interface AbsenseModuleState {
    AbsenseFormList: Excusedlist[];
    excusedAbsense: ExcusedAbsense;
    saveGrievance: any;
    saveDraftGrievance: number;
    viewFormStatus: Object;
    viewFormProgress: Array<StatusLog>;
    deletedGrievanceForm:any;
    // upcomingExam: upcomingExam[];
    upcomingExam:upcomingExam[],
}

export class Excusedlist {
    formTypeId: number;
    personFormId: number;
    formId: number;
    examName: string;
    name: string;
    state: string;
    eligiblityRoute: string;
    stateName: string;
    eligiblityRouteName: string;
    iconUrl: string;
    comment: string | null;
    examDate: Date | string;
    submittedDate: Date | string;
    status: string;
    statusId: number;
    mode: string | null;
    examId: number;
    isExcuseAbsenceSubmitted: boolean;
    id: number = 0;
}

export class ExcusedAbsense {
    public constructor(init?:Partial<ExcusedAbsense>) {
        Object.assign(this, init);
    }
    id: number;
    tenantId: number;
    formUrl: string;
    formJson: string;
    dataDetail: string;
    formTypeId: number;
    name: string;
    personTenantRoleId: number;    
}

export declare interface SaveGrievnaceForm {
    personTenantRoleId: number;
    formId: number | string;
    personEventId: number;
    dataDetail: string;
    isSubmit: boolean;
}

export declare interface ViewGrievanceForm {
    personFormId: number;
}

export class StatusLog {
    personFormId: number;
    comment: string;
    name: any;
    actionOn: any;
    reviewer: string;
}

export  declare interface upcomingExam {
    id: number;
    examId: number;
    candidateId: number;
    examName: string;
    candidateEmailId: string;
    candidateName: string;
    examDateTime: Date;
    examDateTimeUtc: Date;
    timeZoneOffset?: any;
    examStatusId: number;
    examStatus: string;
    stateCode: string;
    eligibilityRouteCode: string;
    eligibilityRouteName: string;
    iconUrl: string;
    registeredDateTime: Date;
    timezone: string;
    timezoneCode: string;
    examMode: string;
    allowReschedule: string;
    allowCancel: string;
    isPrimary: boolean;
    reschedule:boolean;
    cancel:boolean
  }

  export interface Form {
    personFormId: number;
    formTypeId: number;
    formId: number;
    name: string;
    examName?: any;
    comment?: any;
    stateName: string;
    eligiblityRouteName: string;
    eligibilityRouteId: number;
    stateCode: string;
    eligiblityRouteCode: string;
    submittedDate: Date;
    examDate?: any;
    lastUpdatedDate: Date;
    iconUrl: string;
    status: string;
    waitingTime: string;
}