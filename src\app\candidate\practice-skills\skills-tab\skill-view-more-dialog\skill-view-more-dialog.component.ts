import { Component, Inject, inject, OnInit } from "@angular/core";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { PracticeSkill } from "../../state/models/practice-skill.model";
import { AddAttemptDialogComponent } from "../add-attempt-dialog/add-attempt-dialog.component";
import { DynamicMenuItem } from "src/app/core/common-component/menu/model/dynamic-menu-item";
import { ActivatedRoute, Router } from "@angular/router";

@Component({
  selector: "exai-skill-view-dialog",
  templateUrl: "./skill-view-more-dialog.component.html",
  styleUrls: ["./skill-view-more-dialog.component.scss"],
})
export class SkillViewMoreDialogComponent implements OnInit {
  menuItems: DynamicMenuItem[] = [
    { label: "View", icon: "visibility", value: "view" },
    { label: "Add to Cart", icon: "shopping_cart", value: "add_to_cart" },
  ];
  constructor(
    public daialogRef: MatDialogRef<SkillViewMoreDialogComponent>,
    private _dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(MAT_DIALOG_DATA) public skill: PracticeSkill
  ) {}

  ngOnInit(): void {}

  closeDialog(): void {
    this.daialogRef.close();
  }

  tryNow(): void {
    this.daialogRef.close("try");
  }

  handleMenuAction(item: DynamicMenuItem, skill: any) {
    switch (item.value) {
      case "view":
        this.viewSkill(skill);
        break;
    }
  }

  viewSkill(skill: PracticeSkill) {
    this.router.navigate(["/practice-skills/skills", skill.practiceSkillGuid], {
      relativeTo: this.route,
      state: { skillData: skill },
    });
  }
  /**
   *
   * @param skill
   */
  openAddAttemptDialog(skill: PracticeSkill) {
    this._dialog.open(AddAttemptDialogComponent, {
      width: "600px",
      data: { skill },
      panelClass: "custom-dialog-container",
    });
  }
}
