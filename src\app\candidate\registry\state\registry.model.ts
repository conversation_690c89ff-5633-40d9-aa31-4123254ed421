import { PersonFormLogModel } from "src/app/core/Dto/personform-log.model";
import { Form } from "../../application/application.types";

export interface RegistryModuleState {

    certificates: CertificateModel[];
    requests: RequestModel[];
    data:boolean
    certificatePath: string;
    form: Form;
    draftedForm: any;
    selectedCertificate: CertificateModel;
    selectedRequest: RequestModel;
    selectedState: StateModel;
    logs: PersonFormLogModel[];
    states: StateModel[];
    deletedRenewedForm: boolean;
    makenull:any;
}
export interface RequestModel {
    personFormId: number;
    formTypeId: number;
    formId: number;
    name: string;
    examName: string;
    stateId: number;
    candidateId: number;
    formCode: string;
    code: string;
    eligibilityRouteName: string;
    eligibilityRouteId: number;
    stateCode: string;
    eligibilityRouteCode: string;
    submittedDate: Date;
    examDate: Date;
    lastUpdatedDate: Date;
    iconUrl: string;
    status: string;
    statusId: number;
    waitingTime: Date;
    allowApplyAgain: boolean;
    certificateId: number;
    certificateName: string;
    certificateNumber: string;
    comment: string;
}

export interface CertificateModel {
    Id: number;
    AllowRenewal:boolean,
    PersonId: number;
    PersonTenantRoleId: number;
    PersonEventId: number;
    RegistryStatusId: number;
    RegistryStatus: string;
    RegistryName: string;
    EffectiveDate: string;
    ExpirationDate: string;
    StatusSinceDate: string;
    CertNumber: string;
    Certpath: string;
    WorkedBy: number;
    StateName: string;
    StateId: string;
    ExamId: number;
    ExamName: string;
    CertType: number;
    ActionOn: string;
    ActionBy: number;
    Comment: string;
    EligibilityRouteId: number;
    EligibilityRoute: string;
    FirstName: string;
    MiddleName: string;
    LastName: string;
    State: string;
    ZipCode: string;
    CertificateType?:string
    AllowReinstatement?:boolean
}

export class StateModel {
    id: number = 0;
    code: string = '';
    label: string = '';
    name: string = '';
    value: string = '';
}

export const RegistryStatus=[6,7,8]