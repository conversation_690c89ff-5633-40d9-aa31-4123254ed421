import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PracticeSkillsComponent } from "./practice-skills.component";
import { SkillDetailComponent } from "./skill-detail/skill-detail.component";
import { PaymentComponent } from "../scheduled/payment/payment.component";
import { ViewBundleComponent } from "./view-bundle/view-bundle.component";

const routes: Routes = [
  { path: "", component: PracticeSkillsComponent },
  {
    path: "skills/:id",
    component: SkillDetailComponent,
  },
  {
    path: "payment/:id/:name",
    component: PaymentComponent,
  },
  {
    path: "payment",
    component: PaymentComponent,
  },
  {
    path: "bundles/:id",
    component: ViewBundleComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PracticeSkillsRoutingModule {}
