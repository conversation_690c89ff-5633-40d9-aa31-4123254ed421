import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { GlobalUserService } from "../global-user.service";
import { environment } from "src/environments/environment";
import { BehaviorSubject, Observable } from "rxjs";
import { PersonDetails } from "../Dto/persondetail";
import { GrievenceFormModel } from "../Dto/grievanceform.model";
import { FormTypes } from "../Dto/enum";
import { URL } from 'src/app/core/url';
import { PersonForm } from "src/app/candidate/application/application.types";
import { PersonFormModel } from "../Dto/personform.model";
import { RegisteredExamsModel } from "../Dto/registered-exams.model";
import { ReportGrievance } from "src/app/candidate/grievance-form/state/grievance.model";
import { ShowRegisterExamModel } from "../Dto/show-register-exam.model";
import moment from "moment";
import { productFruits } from "product-fruits";

@Injectable({
  providedIn: "root",
})
export class HttpService {  
  constructor(private http: HttpClient,
    private global: GlobalUserService) {       
    }

  getExamList() {
    var url = `${URL.BASE_URL}Exam/timezones`;
    return this.http.get(url);
  }
  getRoute() {
    var url = `${URL.BASE_URL}eligibilityroute/active?candidateId=${this.global.candidateId}`;
    return this.http.get(url);
  }
  getAccountPersonDetails() {
    return this.http.get(`${URL.BASE_URL_SHORTER}candidate/api/account/get?personTenantRoleId=${this.global.candidateId}`);
  }
  editProfile(personDetails: PersonDetails) {
    return this.http.post<PersonDetails>(URL.BASE_URL_SHORTER + "candidate/api/account/updateuser", personDetails);
  }
  
  public scheduledExam(candidateId: number): Observable<RegisteredExamsModel[]> {
    var url = `${URL.BASE_URL}Exam/registeredexams?candidateId=${candidateId}`;
    return this.http.get<RegisteredExamsModel[]>(url);
  }
  public scheduledPracticeExam(candidateId: number): Observable<RegisteredExamsModel[]> {
    var url = `${URL.BASE_URL}Exam/Practiceregisteredexams?candidateId=${candidateId}`;
    return this.http.get<RegisteredExamsModel[]>(url);
  }
  getPersonGrievanceForm(): Observable<GrievenceFormModel> {
    var url = `${URL.BASE_URL}PersonForm/list?candidateid=${this.global.candidateId}&formTypeId=${FormTypes.Grievance}`;
    return this.http.get<GrievenceFormModel>(url);
  }
  getStatusLog() {
    var url = `${URL.BASE_URL}PersonForm/logs?personFormId=${this.global}`;
    return this.http.get(url);
  }
  uploadProfilePic(formData: FormData, userId: number): Observable<any> {
    var url = `${URL.BASE_URL_SHORTER}candidate/api/account/uploadprofilepic/${userId}`;
    return this.http.post<any>(url, formData);
  }
  getPersonGrievanceFormList() {
    var url = `${URL.BASE_URL}PersonForm/list?candidateId=${this.global.candidateId}&formTypeId=${FormTypes.Grievance}`;
    return this.http.get(url);
  }
  submitForm(body) {
    var url = `${URL.BASE_URL}PersonForm/save?candidateId=${this.global.candidateId}`;
    return this.http.post(url, body);
  }
  getformbyFormtypeId() {
    var url = `${URL.BASE_URL}Form/getformbyformtypeid?formTypeId=${FormTypes.Grievance}&stateId=${this.global.stateId}`;
    return this.http.get(url);
  }

  getForms(candidateId){
    var url = `${environment.baseUrl}candidate/api/form/personform?candidateId=${candidateId}&formTypeId=1&formTypeId=3`
    return this.http.get(url);

  }
  getExamId(id: number) {
    var url = `${URL.BASE_URL}exam/exams?eligibilityRouteId=${id}`;
    return this.http.get(url);
  }
  getSlots(time: string, date: any) {
    var url = `${URL.BASE_URL
      }exam/slots?timezoneId=${time}&startDate=${new Date(
        date
      ).toLocaleDateString()}`;
    return this.http.get(url);
  }
  cancelForm(body) {
    var url = `${URL.BASE_URL}PersonForm/delete?canddateId=${this.global.candidateId}&personFormId=${this.global.personFormId}`;
    return this.http.delete(url, body);
  }
  personFormView() {
    var url = `${URL.BASE_URL}PersonForm/view?personFormId=${this.global.personFormId}`;
    return this.http.get(url);
  }
  getMonthlySlots(date, year, timezone) {
    var url = `${URL.BASE_URL}exam/monthlySlots?month=${date}&year=${year}&timeZone=${timezone}`;
    return this.http.get(url);
  }
  getCartItems() {
    var url = `${URL.BASE_URL}exam/cartitems?personTenantRoleId=${this.global.candidateId}`;
    return this.http.get(url);
  }

  demographicForm() {
    var url = `${URL.BASE_URL}form/formsbyformtypeid?formTypeId=${FormTypes.Demographic}&clientId=${this.global.clientId}&stateId=${this.global.stateId}`;
    return this.http.get(url);
  }
  
  public loadReportGrievanceForms(): Observable<ReportGrievance> {
    var url = `${URL.BASE_URL}form/formsbyformtypeid?formTypeId=${FormTypes.Grievance}&clientId=${this.global.clientId}&stateId=${this.global.stateId}`;
    return this.http.get<ReportGrievance>(url);
  } 
  public loadAbsenseform(): Observable<ReportGrievance> {
    var url = `${URL.BASE_URL}form/formsbyformtypeid?formTypeId=${FormTypes.ExcusedAbsense}&clientId=${this.global.clientId}&stateId=${this.global.stateId}`;
    return this.http.get<ReportGrievance>(url);
  } 

  saveDemographic(data) {
    var url = `${URL.BASE_URL}form/savepersonform`;
    return this.http.post(url, data);
  }
  public getPersonForms() : Observable<PersonForm[]> {
    var url = `${URL.BASE_URL}form/personform?candidateId=${this.global.candidateId}&formTypeId=${FormTypes.Demographic}`;
    return this.http.get<PersonForm[]>(url);
  }
  public acessCorrectionFormValue(personFormId : number) : Observable<PersonFormModel[]>{
    var url = (`${URL.BASE_URL}form/personform/list?personFormId=${personFormId}`);
    return this.http.get<PersonFormModel[]>(url);
  }

  deleteDemographicForm(personFormId) {
    var url = (`${URL.BASE_URL}Form/personform?candidateId${this.global.candidateId}&personFormId=${personFormId}`);
    return this.http.delete(url);
  }

  public deletePersonForm(personFormId: number): Observable<number> {
    var url = `${URL.BASE_URL}Form/personform?candidateId${this.global.candidateId}&personFormId=${personFormId}`;
    return this.http.delete<number>(url);
  }

  getCorrectionlogs(personFormId) {
    var url = (`${URL.BASE_URL}Form/personformlogs?personFormId=${personFormId}`);
    return this.http.get(url);
  }
  // getDisableProfile(roleID: number, formTypeId: number):Observable<any> {
  //   var url = (`${URL.EditProfile_URL}form/any-active-form?personTenantRoleId=${roleID}&formTypeId=${formTypeId}`);
  //   return this.http.get(url);
  // }
  getUpcomingExams(candidateId: number): Observable<any> {
    return this.http.get<any>(URL.BASE_URL + `exam/upcomingexam?candidateId=${candidateId}`)
  }

  scheduleStartExam(inputDetails) {
    var url = `${URL.V1_scheduleStartExam_Url}`;
    return this.http.post(url, inputDetails);
  }

  Schedule(body){
    var url = `${URL.BASE_URL+'Exam/retry-schedule'}`;
    return this.http.post(url, body);
  }
  getAddnotes(body){
    var url = `${environment.baseUrl}client/api/form/AddNote`;
    return this.http.post(url, body);
  }

  upadateOnlySSN(personDetails) {
    return this.http.post<PersonDetails>(URL.BASE_URL_SHORTER + "candidate/api/account/updateuser", personDetails);
  }
  removecart(tetantId:number,cartItemId:number){
    var url = `${environment.baseUrl}candidate/api/exam/cart-item?personTenantRoleId=${tetantId}&cartItemId=${cartItemId}`;
    return this.http.delete(url);
  }
  getCertificates(candidateId:number){
    var url = `${environment.baseUrl}Formmsvc/api/form/personform?candidateId=${candidateId}&formTypeId=5&formTypeId=6&formTypeId=7&formTypeId=14&formtypeId=15&formtypeId=17`;
    return this.http.get(url);
  }

  getRegistryId(registryId:number){
    var url = `${URL.BASE_URL_SHORTER1}registry/get-details?registryId=${registryId}`;
    return this.http.get(url);

  }
  getAllData(personId){
    var url=`${URL.BASE_URL_SHORTER1}registry/get-all?pageNumber=1&pageSize=10&personId=${personId}`
    return this.http.get(url);
  }

  getPayPalPayment(body){
     const url =`${environment.baseUrl}candidate/api/Exam/paypal/initiate-payment`
     return this.http.post(url,body)
  }

  confirmPayPalPayment(orderId, payerId, body){
    const url =`${environment.baseUrl}candidate/api/Exam/paypal/confirm-payment?orderId=${orderId}&payerId=${payerId}`;
    return this.http.post(url,body)
 }

 cancelPayPalPayment(orderId, body){
  const url =`${environment.baseUrl}candidate/api/Exam/paypal/cancel-payment?orderId=${orderId}`;

  return this.http.post(url,body)
}
  getIncidentStatus(personId:number){
     var url =`${environment.baseUrl}client/api/api/Tracking/GetMisconductForPersonId?PersonId=${personId}`
     return this.http.post(url,{});
  }


  getProductsLoad(status:number){
    let userInfo={
      username:`${this.global.family_name} ${this.global.given_name}`,
      email: this.global.emailIdToStartExam,
      firstname: this.global.family_name,
      lastname: this.global.given_name,
      signUpAt: moment(new Date()).format('YYYY-MM-DDTHH:mm:SS'),
      role: this.global.roleName,
      props:{
        statusApplication:status
      }
    }
    productFruits.init('WfaLmLiswBa9gYKj', 'en',userInfo );
  }

  getPayPalPracticePayment(body){
    const url =`${environment.baseUrl}candidate/api/Exam/paypal/initiate-practice-payment`
    return this.http.post(url,body)
 }

confirmPracticePayPalPayment(orderId, payerId, body){
    const url =`${environment.baseUrl}candidate/api/Exam/paypal/confirm-practice-payment?orderId=${orderId}&payerId=${payerId}`;
    return this.http.post(url,body)
 }

  updatePersonform(personFormId:number ,eligiblityRouteId:number,personTenantRoleId:number ,body){
    const url = `${environment.baseUrl}formmsvc/api/form/updatePersonform?personformId=${personFormId}&status=12&persontenantroleId=${personTenantRoleId}&eligibilityRouteId=${eligiblityRouteId}`;
    return this.http.post(url,body)
  }

  addAppealNote(body){
    var url = `${environment.baseUrl}formmsvc/api/form/addAppealnote`;
    return this.http.post(url, body);
  }

  getNotes(personFormId ,candidateId,noteTypeId){
    var url =`${environment.baseUrl}Formmsvc/api/form/getnotes?personFormId=${personFormId}&candidateId=${candidateId}&noteTypeId=${noteTypeId}&showAll=false`
    return this.http.get(url);
  }
}
