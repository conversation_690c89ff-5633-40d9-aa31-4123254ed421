
<div   class="flex justify-end px-4">
    <button mat-icon-button mat-dialog-close>
        <mat-icon>close</mat-icon>
      </button>
</div>
<ng-container *ngIf="data[2]!=sectionName">

    <exai-form-builder [existingForm]="formJson" [showToolbar]="false" [showOnlyForm]="true" [disabled]="disable" [submitButtonRef]="submitButton" [element]="{id:selectedfromDetails.id}" [existingUserData]="userResponse">
</exai-form-builder>
</ng-container>

<ng-container *ngIf="data[2]==sectionName">
    <exai-form-builder  *ngIf="userResponse==undefined" [existingForm]="formJson" [showToolbar]="false" [showOnlyForm]="true" 
        (formSubmitEvent)="SaveactionForm($event)" [submitButtonRef]="submitButton" [disabled]="!data[3]">
    </exai-form-builder>
    <exai-form-builder  *ngIf="userResponse!=undefined" [existingForm]="formJson" [showToolbar]="false" [showOnlyForm]="true" [existingUserData]="userResponse"
    (formSubmitEvent)="SaveactionForm($event)" [submitButtonRef]="submitButton" [disabled]="!data[3]">
</exai-form-builder>
<div   class="flex justify-center p-8">
    <button   mat-button [ngClass]="data[3]?'btn-1 text-xs ml-2':''" (click)="isSubmit = false;permanentlySaveResponse = true;" #submitButton id="submitButton" [disabled]="!data[3]">
        <span *ngIf="data[3]">save </span>
    </button>
</div>
</ng-container>
