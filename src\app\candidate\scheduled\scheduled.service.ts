import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root'
})
export class ScheduledService {
    rescheduleInformation: any;
    PracticeInformation:any
    addedToCartInfo:any;

    constructor(private http:HttpClient) { }

    getPayPalPayment(body){
        const url =`${environment.baseUrl}candidate/api/Exam/paypal/initiate-payment`
        return this.http.post(url,body)
     }

     getPayPalPracticePayment(body){
        const url =`${environment.baseUrl}candidate/api/Exam/paypal/initiate-practice-payment`
        return this.http.post(url,body)
     }
   
     confirmPayPalPayment(orderId, payerId, body){
       const url =`${environment.baseUrl}candidate/api/Exam/paypal/confirm-payment?orderId=${orderId}&payerId=${payerId}`;
       return this.http.post(url,body)
    }

    confirmPracticePayPalPayment(orderId, payerId, body){
        const url =`${environment.baseUrl}candidate/api/Exam/paypal/confirm-practice-payment?orderId=${orderId}&payerId=${payerId}`;
        return this.http.post(url,body)
     }
   
    cancelPayPalPayment(orderId, body){
     const url =`${environment.baseUrl}candidate/api/Exam/paypal/cancel-payment?orderId=${orderId}`;
   
     return this.http.post(url,body)
   }
   
}


export interface cartInfo{
    date:string;
    timezone:string
}