.addressDetails {
  height: calc(80vh - 55vh);
  width: calc(150vh - 55vh);
}

.custom {
  padding: 20px 0px 20px 10px;
}

.addressDetails1 {
  width: calc(148vh - 55vh);
}

.custom1 {
  padding-top: 30px;
  border: 2px solid black;
}

.my-12 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.fb-container {
  // padding-left: 0.5rem!important;
  // padding-right: 0.5rem!important;
}

.fb-ht-fixed {
  // height: 80vh;
  // overflow: auto;
  // @screen xl {
  //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.5rem);
  // }
  // @screen lg {
  //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.5rem);
  // }
  // @screen md {
  //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.5rem);
  // }
}

.fb-ht-fixed-sm {
  height: 15vh;
  overflow: auto;
}

#wMsg {
  display: none;
}

.add-icon {
  position: absolute;
  bottom: 5px;
  right: 5px;
  font-size: 1.7rem !important;
  cursor: pointer;
}

.editor {
  width: 92% !important;
  margin: 0.5rem;
}

.basic-card {
  margin: 1rem 1rem;
  margin-bottom: 4rem;
  max-width: 100%;
  min-height: fit-content;
  min-width: fit-content;
  border: 1px solid rgba(49, 48, 48, 0.829);
  border-radius: 12px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding: 0.92rem;
}

.basic-card-children {
  margin: 0.5rem 0.5rem;
  width: stretch;
  height: stretch;
  min-height: fit-content;
  min-width: fit-content;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding: 0.9rem 0rem;
}

.genesis-form-feild {
  width: 27%;
  margin: 0.7rem;
}

.form-card {
  // margin-bottom: 4rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  padding-top: 0.25rem;
}

.bg-red-900 {
  background: red;
}

.header {
  color: #7d7d7d;
  line-height: 1.1;
  font-weight: 400;
  size: 16px;
  // margin-left: -59rem;
  // margin-top: -32px;
}

.formName {
  color: var(--text-color2);
  line-height: 1.1;
  font: Roboto;
  font-weight: 700;
  size: 20px;
  // margin-right: 2px;
}

// .Section{
//     color: #A7A8AC;
//     font: Roboto;
//     font-weight: 500;
//     size: 18px;
//     background-color: #F9F9F9;
// }
.expansion {
  height: 42px !important;
  background-color: #f9f9f9;
  color: #3d3d3d;
  font: Roboto;
  font-weight: 500;
  size: 18px;
  // background-color: #F9F9F9;
  //     border: 0.23px solid lightgray;
  //     box-shadow: none;
  // border-bottom: 0.5px darkgrey solid;
  // border-top: 0.5px darkgrey solid;
  // border-left: 0.5px darkgrey solid;
  // border-right: 0.5px darkgrey solid;
}

.exapansionBody {
  border-top: 0.13px solid lightgray;
  border-bottom: 0.13px lightgray solid;
  // border-top: 0.23px darkgrey solid;
  border-left: 0.13px lightgray solid;
  border-right: 0.13px lightgray solid;
  box-shadow: none;
}

.formCard {
  // border: 0.23px solid lightgray;
  // border-top: 0.23px solid lightgray;
  box-shadow: none;
  padding-bottom: 0px;
}

.mat-accordion .mat-expansion-panel:not(.mat-expanded),
.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing) {
  margin-bottom: 8px;
}

.button {
  background-color: var(--text-color2);
  color: #ffffff;
}

.info-content {
  background: #f9f9f9;
  // border: 0.2px solid #A7A8AC;
  box-sizing: border-box;
  // box-shadow: 0px 4px 4px rgb(0 0 0 / 15%);
  border-radius: 4px;
  //margin-top: 16px;
  justify-content: normal;
  text-align: justify;
}

.text-color {
  color: #a7a8ac;
}

.t-xs {
  font-size: 0.65rem;
}

.f-medium {
  color: #11263c;
}

.t-gray {
  color: #7d7d7d;
}

.disabled {
  pointer-events: none !important;
  opacity: 0.68 !important;
}

::ng-deep .mat-dialog-container {
  padding: 35px !important;
}

.addressDetails {
  height: calc(100vh - 55vh);
}

::ng-deep {
  mat-horizontal-stepper.stepperHzl {
    .mat-step-text-label {
      white-space: normal;
    }
    .mat-step-header:hover {
      background-color: transparent !important;
    }
    .mat-horizontal-content-container {
      overflow: hidden;
      padding: 12px 12px 12px 12px;
    }
    .mat-horizontal-stepper-header {
      padding: 0 12px;
    }
    .mat-form-field-wrapper {
      padding-bottom: 1em;
    }
  }
}

.delete-icon {
  font-size: 1rem;
  margin-top: 0.5rem;
  color: var(--text-color1);
}

::ng-deep {
  .delete-cell {
    text-align: center !important;
  }
}

.add-table {
  border: var(--border);
  border-radius: 4px !important;
}
.table_column {
  display: inline-block;
  height: 100px;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}

.comment-column {
  width: 200px;
}

.expand-comment {
  color: var(--text-color2);
}
