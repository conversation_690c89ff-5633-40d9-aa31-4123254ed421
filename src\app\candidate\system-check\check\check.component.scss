@import "../system-check.component";

.top-header {
  > h4 {
    text-align: center;
    margin-bottom: 1rem;
    text-transform: capitalize;
    font-weight: 600;
  }
}

.bottomCard{
  margin-bottom: 0;
}

.card-icon {
  max-height: 85px;
}

.card-min-height {
  // min-height: 320px; /* 300px */
  min-height: 271px;
}

@media (max-width: 767px) {
  .card-min-height {
    min-height: auto;
  }
}

td > i {
  margin-right: 6px;
}

.fail {
  color: #d60e0e;
  font-weight: 500;
}

.success {
  color: #12d12c;
}

.successSysCheck{
  color: #209e91;
}

.cam-mic {
  width: 90px;
  height: 90px;
}

.pids-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}

.pid {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
  margin: 0 5px;
}

.title {
  // font-weight: 600;
  font-size: 16px;
}

.material-icons-outlined {
  color: #323232;
  font-size: 19px;
}

.descColor {
  color: var(--text-color1);
}

.material-icons-outlined {
  color: var(--sidenav-item-icon-color);
  font-size: var(--sidenav-item-icon-size);
}

