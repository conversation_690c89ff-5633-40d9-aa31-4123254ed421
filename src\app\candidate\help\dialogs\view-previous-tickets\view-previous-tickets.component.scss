mat-dialog-content {
    @apply relative;
    width: 70vw;
}
mat-dialog-content {
    position: relative;
    overflow: auto;
    padding: 0px;
    margin: 0px;
}

.text_size {
    font-size: 12px;
    color: var(--dropdown-color);
}
// .mat-raised-button{
//     @apply absolute right-0 top-0 shadow-none p-0 m-0 flex items-start justify-center;
//     line-height: 18px;
//     min-width: 4rem;
// }
// .dialog-content{
//     @apply flex w-full;
//     @screen xs{
//         @apply flex-col;
//     }
//     @screen md{
//         @apply flex-row;
//     }
// }
// .dialog-content_block{
//     @apply flex-grow;
//     &:first-child {
//         border-right: 1px solid var(--foreground-divider);
//         @screen xs{
//             @apply max-w-full;
//         }
//         @screen md{
//             @apply max-w-1/4;
//         }
//     }
//     &:last-child{
//         @screen xs{
//             @apply max-w-full;
//         }
//         @screen md{
//             @apply max-w-3/4;
//         }
//     }
// }
.title {
    background-color: var(--background-base2);
}

// .card-body_block{
//     &:first-child{
//         @apply flex-grow max-w-3/4;
//     }
//     &:last-child{
//         @apply flex-grow max-w-1/4 pl-4;
//     }

// }
.wrap-test {
    overflow-wrap: break-word;
}

.item-label1 {
    flex: 1;
    // white-space: inherit;
    // overflow: hidden;
    // text-overflow: ellipsis;
    font-size: var(--sidenav-item-label-size);
    font-weight: 500;
    color: var(--text-color1);
}
.active {
    color: var(--button-background) !important;
}

::ng-deep {
    .mat-dialog-container {
        overflow: hidden !important;
    }
}

.popUp-height {
    @screen xl {
        height: 52.5vh;
        overflow: auto;
    }
    @screen lg {
        height: 52.5vh;
        overflow: auto;
    }
    @screen md {
        height: 52.5vh;
        overflow: auto;
    }
}

.icons {
    font-size: 12px;
    margin-top: 0.5rem !important;
}
.mat-stroked-button:not(.mat-button-disabled) {
    border-color: var(--theader);
}

.mat-icon {
    height: 16px;
    width: 16px;
}

.fontColor1 {
    color: var(--dropdown-color);
}

.status {
    color: var(--text-profile);
}
