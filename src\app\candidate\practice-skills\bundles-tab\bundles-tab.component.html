
<div class="bundle-grid">
    <mat-card *ngFor="let bundle of bundles"
        class="card_border flex flex-col justify-between p-2">
        <div class="card-content p-1 flex flex-col flex-1 ">

            <div class="p-2 flex justify-between items-center">
                <span class="title text-sm font-semibold truncate w-[90%]">
                    {{ bundle.title }}
                </span>
                <div class="relative">
                    <span class="menu-toggle text-xl cursor-pointer" (click)="openMenu(bundle)">⋮</span>
                    <div *ngIf="selectedBundle === bundle" class="menu_pop absolute top-6 right-0 w-32 z-50">
                        <div class="menu_pop_item p-2 cursor-pointer flex items-center gap-2"
                            (click)="viewBundle(bundle)">
                            <mat-icon class="icon mt-2">visibility</mat-icon>
                            <span>View</span>
                        </div>
                        <div class="menu_pop_item p-2 cursor-pointer flex items-center gap-2"
                            (click)="addToCart(bundle)">
                            <mat-icon class="icon mt-2">shopping_cart</mat-icon>
                            <span>Add to Cart</span>
                        </div>
                    </div>
                </div>
            </div>

            <mat-card-content class="p-3 flex-1">
                <p class="text-xs leading-relaxed tracking-wide text-justify">
                    {{ bundle.description }}
                </p>
            </mat-card-content>
            <div *ngIf="bundle.includes && bundle.includes.length > 0" class="bundle-includes">
            <p class="font-medium  font-semibold text-sm">Includes:</p>
                <ul class="includes-list">
                    <li *ngFor="let item of bundle.includes">{{ item }}</li>
                </ul>
            </div>


            <div class="flex flex-wrap justify-between text-center text-xs mt-2 px-3">
                <div class="w-1/4">
                    <div class="meta-label">Total Skills</div>
                    <div class="meta-value">{{ bundle.totalSkills }}</div>
                </div>
                <div class="w-1/4">
                    <div class="meta-label">Attempts</div>
                    <div class="meta-value">{{ bundle.attemptsPerSkill }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Validity</div>
                    <div class="meta-value">{{ bundle.validity }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Price</div>
                    <div class="meta-value">{{ bundle.price }}</div>
                </div>
            </div>

        </div>
    <mat-card-actions class="px-3 mt-auto flex justify-end">
        <button  class="try-now-btn font-semibold flex items-center justify-center mb-4 mt-3 mr-2"
            (click)="openAddAttemptDialog(bundle)">
            Try Now →
        </button>
    </mat-card-actions>
    </mat-card>
</div>