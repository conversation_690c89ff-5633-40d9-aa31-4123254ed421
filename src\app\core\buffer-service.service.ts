import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpResponse,
} from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Action, Store } from '@ngrx/store';
import {
  setErrorMessage,
  setLoadingSpinner,
} from '../candidate/state/shared/shared.actions';
@Injectable({
  providedIn: 'root',
})
export class BufferInterceptor implements HttpInterceptor {
  constructor(private store: Store) { }
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    this.store.dispatch<Action>(setLoadingSpinner({ status: true }));
    return next
      .handle(request)
      .pipe(
        catchError((err) => {
          this.store.dispatch<Action>(setErrorMessage({ message: err }));
          this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
          return err;
        })
      )
      .pipe(
        map<HttpEvent<any>, any>((evt: HttpEvent<any>) => {
          if (evt instanceof HttpResponse) {
            this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
          }
          return evt;
        })
      );
  }
}
