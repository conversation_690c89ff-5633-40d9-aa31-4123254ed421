export interface MakePaymentResponse {
    transactionResponse: TransactionResponse
    messages: Messages
  }
  
  export interface TransactionResponse {
    responseCode: string
    authCode: string
    transId: string
    refTransID: string
    accountNumber: string
    accountType: string
    avsResultCode: string
    cvvResultCode: string
    cavvResultCode: string
    transHash: string
    transHashSha2: string
    messages: Message[]
  }
  
  export interface Message {
    code: string
    description: string
  }
  
  export interface Messages {
    resultCode: string
    message: Message2[]
  }
  
  export interface Message2 {
    code: string
    text: string
  }


  export interface MakePaymentBody {
    cartId: number;
    currencyCode: string;
    payment: Payment;
    // firstname: string;
    // lastname: string;
    // email: string;

  }
  
  export interface Payment {
    creditCard: CreditCard
  }
  
  export interface CreditCard {
    cardNumber: string
    expirationDate: string
    cardCode: string
  }

  export interface Reschedule {
    candidateId: number,
    examId: number,
    examModeId: number,
    slotId:number,
    timeZone: string,
    offSet?: string,
    scheduleId: number
    personTenantRoleId:number
    ExamDateTime:string
    testCenterName: string,
    testCenterAddress:string ,
   testCenterCity:string,
   testCenterState:string ,
    testCenterPostalCode:string,
    testCenterId:string
    isGrievanceFilled:boolean,
    Slotime?:string,
    testSiteId?:string
    TestCenterDirections?:string,
    testCenterCode?:string,
    examClientEventId?:number
    examCode?:string
  }



  export interface Vocher {
    personTenantRoleId: number,
    voucherCode: string,
    examTypeId: number[],
    examId: number[],
    examPrice: number;
  }

  export interface Voucher_validate_apply{
    validateVoucher:validate
    applyVoucher:applyvoucher
  }
  export interface VocherResponse {
    allowedTypes:[number],
    response:Response[],
    voucherValue: string
  }

  export interface validate{
    personTenantRoleId: number,
    examTypeId: Array<number>
    examId:Array<number>
    formCode: string
  }

  export interface applyvoucher{
    cartId: number,
    voucherCode: string,
    userId: number,
    voucherItems: Array<{personEventCartId:number,voucherAmount:number}>
  }

  export interface Response {
    examId: number
    examType: number
   isValidExam: boolean
   voucherAppliedStatus: boolean
   
  }