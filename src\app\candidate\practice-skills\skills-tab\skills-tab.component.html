<div class="grid-layout">

    <mat-card *ngFor="let skill of skillViewModels" class="card-borders">
        <div class="card-content">

            <div class="flex justify-between items-center pr-2">
                <span class="title">
                    {{ skill.skillTitle }}
                </span>
                <exai-menu [menuItems]="menuItems" (itemClicked)="handleMenuAction($event, skill)"></exai-menu>
            </div>
            <!-- <mat-card-content class="flex-1">
                        <p class="description-text relative leading-relaxed tracking-wide text-justify text-xs pr-4"
                            [class.collapsed]="!skill.isExpanded" [class.expanded]="skill.isExpanded" #desc>
                            {{ skill.description }}
                        </p>
        
                        <button *ngIf="skill.showToggle" (click)="toggleExpanded(skill)"
                            class="viewmore pl-4 text-xs  underline focus:outline-none">
                            {{ skill.isExpanded ? 'View Less' : 'View More' }}
                        </button>
                    </mat-card-content> -->
            <mat-card-content class="flex-1">
                <p class="description-text relative leading-relaxed tracking-wide text-justify text-xs pr-4"
                    [class.collapsed]="!skill.isExpanded" [class.expanded]="skill.isExpanded" #desc>
                    {{ skill.description }}
                </p>


                <button *ngIf="skill.showToggle && isMobileOrTablet" (click)="openViewPage(skill)"
                    class="viewmore pl-4 text-xs underline focus:outline-none">
                    {{ skill.isExpanded ? 'View Less' : 'View More' }}
                </button>


                <button *ngIf="skill.showToggle && !isMobileOrTablet" (click)="viewSkillDialog(skill)"
                    class="viewmore pl-4 text-xs underline focus:outline-none">
                    View More
                </button>
            </mat-card-content>


            <div class="meta flex flex-wrap justify-between text-center mt-2">
                <div class="w-1/5">
                    <div class="meta-label">Duration</div>
                    <div class="meta-value">{{ skill.duration }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Steps</div>
                    <div class="meta-value">{{ skill.skillStepCount }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Attempts</div>
                    <div class="meta-value">{{ skill.defaultAttempt }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Validity</div>
                    <div class="meta-value">{{ skill.validity }}d</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Price</div>
                    <div class="meta-value1">${{ skill.priceUsd }}</div>
                </div>
            </div>
        </div>
        <mat-card-actions class="px-3 mt-auto flex justify-end">
            <button class="try-now-btn font-semibold flex items-center justify-center mb-4 mt-3 mr-2"
                (click)="openAddAttemptDialog(skill)">
                Try Now →
            </button>
        </mat-card-actions>
    </mat-card>
</div>