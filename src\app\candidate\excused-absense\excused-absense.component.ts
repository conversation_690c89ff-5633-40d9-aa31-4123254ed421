import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { Observable } from 'rxjs';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http-services/http.service';
import { getShowRegisterExam } from '../dashboard/state/dashboard.actions';
import { selectorShowRegisterExam$, selectUpcommmingExam } from '../dashboard/state/dashboard.selectors';
import { FormTypes } from '../forms-wrapper/forms-wrapper.types';
import { GrievanceFormService } from '../grievance-form/grievance-form.service';
import { clearGrievanceState, getupcomingExam, loadAll } from '../excused-absense/state/excused.action';
import { AbsenseModuleState,  Excusedlist } from '../excused-absense/state/excused.model';
import { selectorLoadAllSuccess } from '../excused-absense/state/excused.selectors';
import { ScheduledService } from '../scheduled/scheduled.service';

@Component({
  selector: 'exai-excused-absense',
  templateUrl: './excused-absense.component.html',
  styleUrls: ['./excused-absense.component.scss']
})
export class ExcusedAbsenseComponent implements OnInit {
  hideGrievanceForm: boolean = false;
  statusIcon = statusIcon;
  showRegisterExam:any;
  gridColumns = 4;
  register: boolean = false;
  listExam: any;
  errors: any;
  exam: boolean = false;
  details: boolean = false;
  isExamCompleted: boolean=false;
  // PersonGrevianceForm: Grievenceform;
  PersonGrievanceForm: Excusedlist[];
  absenseForm$: Observable<Array<Excusedlist>>;

  constructor(
    private router: Router,
    private http: HttpService,
    private global: GlobalUserService,
    private store: Store<AbsenseModuleState>,
    private _service: GrievanceFormService,
    private services:ScheduledService
  ) { 
    this.global.userDetails.subscribe((data: any) => {
      if (data) this.subscriptions();
    });
  }

  ngOnInit(): void {
  }
  subscriptions(): void {
    this.store.dispatch<Action>(getShowRegisterExam({ personTenantRoleId: this.global.candidateId }));
    this.store.dispatch<Action>(getupcomingExam({ candidateId: this.global.candidateId }));
    this.store.select(selectUpcommmingExam).subscribe((upcomingExam) => {
        for (let i = 0; i < upcomingExam.length; i++) {
          if ((upcomingExam.length>0)) {
            this.isExamCompleted = true;
          }
        }
      });
      let n = Intl.DateTimeFormat().resolvedOptions()
    this.store.dispatch(clearGrievanceState());
    this.store.dispatch<Action>(loadAll());
    this.absenseForm$ = this.store.select(selectorLoadAllSuccess);
    this.absenseForm$.subscribe((data: Array<Excusedlist>) => {
      if (data.length) {
        data = data.map((ele) =>
          Object.assign(
            {},
            ele,
            {
              submittedDateN: moment(ele.submittedDate).format(
                'MMMM Do, YYYY / h:mm A'
              ),
            },
            { examDateN: moment(ele.examDate).format('MM/DD/YYYY') },
            {
              examTimePDT: moment(ele.examDate)
                .tz(n.timeZone)
                .format('h:mm a z'),
            }
          )
        );
        this.PersonGrievanceForm = data;
        //this.grievance = true
        this.hideGrievanceForm = false;
        this.register = true;
        for (let i = 0; i < data.length; i++) {
          this.global.personFormId = data[i].personFormId;
        }
      } else if (data.length == 0) {
        this.PersonGrievanceForm = [];
        //this.grievance = false
        this.hideGrievanceForm = true;
        // this.register = false
      }
    });
    this.store.select(selectorShowRegisterExam$).subscribe((data) => {
      if (data) {
        this.showRegisterExam = data;
      }
      });
  }
  clickRoute() {
    this.router.navigateByUrl('exam-scheduled');
  }
  
  editAbsense(item: Excusedlist): void {
    
    {
      this.router.navigate(['absense-form', 'Absence-form-view', FormTypes.ExcusedAbsense, this.global.candidateId, item.eligiblityRoute, this.global.stateId, item.id, item.personFormId, 0]);
    }
   
   
  }
  viewAbsense(item: any): void {
    this.router.navigate(['absense-form', 'Absence-form-view', FormTypes.ExcusedAbsense, this.global.candidateId, item.eligiblityRoute, this.global.stateId, item.id, item.personFormId, 0]);
  }
  reschedule(event: any): void {
    this.services.rescheduleInformation = event;
    this.router.navigateByUrl("exam-scheduled/register");
  }
}
export enum statusIcon {
  'Approved' = 'assets/img/Group 354.svg',
  'Rejected' = 'assets/img/rejection.svg',
  'Nodata' = 'assets/img/grivence-null.svg',
  'Pending' = 'assets/img/Group 355.svg',
}

