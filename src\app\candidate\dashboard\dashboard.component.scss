.dashboard {
  height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height));
}

.db {
  justify-content: center !important;
}

.welc {
  color: var(--text-color2);
  font-size: 16px;
  line-height: 22px;
}
.onhover{
  cursor: pointer;
}
.minimise{
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow: hidden;
}

.welc-note {
  color: var(--text-color1);
}

.title-hed {
  color: var(--text-dropdown);
}

.state-elig {
  color: var(--text-color1);
}

.bg-color {
  background-color: var(--background-base2);
}

.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: -0.9rem 0.5rem;
}

.arrow {
  justify-content: center !important;
}

.iconSize {
  width: 12px;
  height: 12px;
}

.status {
  color: var(--text-profile);
}
.status1 {
  color: var(--text-color1);
}

.active {
  color: var(--text-color2);
}

// .content {
//     // padding-top: 16px;
//     // padding-bottom: 16px;
//     // padding-right: 16px;
// }

.content > mat-card {
  margin-bottom: 10px;
}

mat-card {
  max-width: 410px;
}

.update {
  color: var(--text-color1);
}

.content1 {
  color: var(--text-color4);
}

.active2 {
  color: var(--text-color2);
}
.space {
  height: 30px;
}

::ng-deep .mat-button.mat-primary.mat-button-disabled, .mat-button.mat-accent.mat-button-disabled, .mat-button.mat-warn.mat-button-disabled, .mat-button.mat-button-disabled.mat-button-disabled, .mat-icon-button.mat-primary.mat-button-disabled, .mat-icon-button.mat-accent.mat-button-disabled, .mat-icon-button.mat-warn.mat-button-disabled, .mat-icon-button.mat-button-disabled.mat-button-disabled, .mat-stroked-button.mat-primary.mat-button-disabled, .mat-stroked-button.mat-accent.mat-button-disabled, .mat-stroked-button.mat-warn.mat-button-disabled, .mat-stroked-button.mat-button-disabled.mat-button-disabled {
  /* color: rgba(0, 0, 0, 0.26); */
  background: #4444 !important;
}

.system-title {
  align-items: center;
  display: flex;
}

.button-disabled {
  opacity: 0.4;
  pointer-events: none;
}

.topmargin{
  margin-top: 1rem;
}