import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { PersonForm } from "../../application/application.types";
import { FormModel } from "./dashboard.models";
import { upcomingExam } from "./dashboard.models/Upcomingexam";
import { candidateLoginResponse } from "./dashboard.models/v1candidate";

export interface DashboardState{
    form :FormModel[];
    upcomingExam:upcomingExam[],
    personForms: PersonForm[],
    isCancelled:boolean,
    LoginResponse: candidateLoginResponse,
    loading: boolean;
    showRegisterExamStatus: ShowRegisterExamModel;
}


export const initDashboardState:DashboardState={
    form: null,
    upcomingExam:null,
    personForms:null,
    isCancelled:null,
    LoginResponse: null,
    loading: false,
    showRegisterExamStatus: null
};

export class DashboardData{
    form: FormModel[]=[];
    upcomingExam: upcomingExam[]=[];
    personForms: PersonForm[]=[];
    isCancelled: null;
    LoginResponse: null;    
};