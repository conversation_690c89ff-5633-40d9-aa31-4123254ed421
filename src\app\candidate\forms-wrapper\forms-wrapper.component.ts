import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewChild,
  ViewChildren,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import { DynamicFormControlEvent, DynamicFormService } from "@ng-dynamic-forms/core";
import { Action, Store } from "@ngrx/store";
import { BehaviorSubject, Observable, Subject, Subscription } from "rxjs";
import {
  sectionCompleteEvent,
  validatedSubmission,
} from "src/app/core/examroom-formbuilder/form-builder.types";
import { LanguageService } from "src/app/core/language.service";
import { AllowPayment, AllowPaymentReciporating, Form, FormCartItemId, FormTypes, generateRegistry, ReciporatingSCMA, ReciprocityStateList, registryCerttype, RenewelStateList, RenewelStateLists, response, stateAllowstosavedata, StateList, States } from "./forms-wrapper.types";
import { renewelCart, selectCandidateId, selectForm, selectLatestPersonFormId, selectPersonEventId, selectPersonFormLogs, selectUserResponse } from "./state/application.selectors";
import { ApplicationState } from "./state/application.state";
import { PopUpComponent } from "./pop-up/pop-up.component";
import { v4 as uuidv4 } from "uuid";
import { GlobalUserService } from "src/app/core/global-user.service";
import { getFormJson, setBasicDetails, saveUserResponse, deleteUserResponse, getUserResponse, clearApplicationState, getPersonFormLogs, downloadAccTypeForm, settingEligilityRouteOnLoad } from './state/application.actions';
import lodash from "lodash";
import { ChangeDetectionStrategy } from '@angular/core';
import { debounceTime, map, startWith, takeUntil } from "rxjs/operators";
import { PersonFormLog } from "src/app/core/common-component/progress-bar/progress-bar.types";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";
import { FormStatuses } from "src/app/core/Dto/enum";
import { SnackbarService } from "src/app/core/snackbar.service";
import { FormTypesToNames } from "src/app/core/Dto/enum";
import { AccomodationFormAccTypeFieldIds, AccomodationFormsSectionOneIds, AccomodationYesOrNoFieldIds, CorrectionFormFieldIds, correctionSectionValue, GrievanceFormFieldIds } from "./form-wrapper-static-data";
import { FormsWrapperService } from "./forms-wrapper.service";
import { deleteCartItem, getCartItems } from "../state/shared/shared.actions";
import { get_cartItems } from "../state/shared/shared.selectors";
import { selectorGetCartDeleteStatus } from "../scheduled/state/scheduled.selectors";
import { getSelectedRequest } from "../registry/state/registry.selectors";
import { removeCartItem } from "../scheduled/state/scheduled.actions";
import { FormBuilderComponent } from "src/app/core/examroom-formbuilder/form-builder.component";
import { HttpService } from "src/app/core/http-services/http.service";
import { FormBuilderService } from "src/app/core/examroom-formbuilder/form-builder.service";
import { AbstractControl, FormBuilder, FormControl, FormGroup, ValidatorFn, Validators } from "@angular/forms";

export function noWhitespaceValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': 'value is only whitespace' };
  };
}

@Component({
  selector: "exai-application",
  templateUrl: "./forms-wrapper.component.html",
  styleUrls: ["./forms-wrapper.component.scss"],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class FormsWrapperComponent implements OnInit, OnDestroy, OnChanges {
  registryId:number
  TestReferenceId:number
  TestRefrenceName:string
  options:Array<any>=[]
  
  NonEditData:boolean 
  formTypeId:number
  AcceptanceName:boolean
  radioselect = new FormControl("");
  radioselectAcceptance = new FormControl("");
  AcceptanceValue:boolean 
  GreivanceResponse: Array<{ selectedValue: string, answer: string }> = []
  GrievanceId: string
 GrievanceStatus: string = ''
  validation_messages = {
    testId: [{ type: "pattern", message: this.global.INF_Validation }],
  };
  @ViewChild("formBuilder") formBuilder: FormBuilderComponent;
  examTypeModels=[{id: 1, name: 'INF (In-facility)',checked: false},{id: 2, name: 'RTS (Regional Testing Center)',checked: false,}]
  Acceptance=[{id: 1, name: 'Yes',checked: false}]
   INFSelected :boolean = false
  testreference:boolean= false
  showdownload:boolean =false
  FormTypesToNames = FormTypesToNames;
  downloadAccTypeForm = downloadAccTypeForm;
  FormTypes = FormTypes;
  generateRegistry:boolean = false;
  GreivanceValue:string
  withdraw:boolean = false
  checkNoRadio:boolean=true;
  stateId:number
  statusId:number
  private unsubscribe: Subject<any> = new Subject();
  status: any;
  certNumber:number;
  formGroup: FormGroup;
  INFValue:FormGroup
  
  constructor(
    private dialog: MatDialog,
    public lngSrvc: LanguageService,
    public store: Store<ApplicationState>,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    public globalUserService: GlobalUserService,
    private cdr: ChangeDetectorRef,
    private http: HttpClient,
    private snackbar: SnackbarService,
    private formService:FormsWrapperService, 
    public global: GlobalUserService,
    private https:HttpService,
    private formservices:FormBuilderService,
    public formServicees: DynamicFormService,
    private fb: FormBuilder,
  ) { }
  
  ngOnChanges(changes: SimpleChanges): void {

    this.checkProgressOfSectionsfn()

  }

  submitAppeal(){
    var params;
    this.activatedRoute.paramMap.subscribe(res => {
      params = res;
      
    })


    if(!this.formGroup.valid) return;
      
      this.https.addAppealNote({
      id:Number(params.params.formTypeId),
      title: "",
      body:this.formGroup.get('description').value,
      files:[],
      noteTypeid:10,
      userId:this.globalUserService.userDetails.value.roles[0].personTenantRoleId,
      userName:this.globalUserService.family_name + " " +this.globalUserService.given_name,
      candidateId:this.globalUserService.candidateId,
      personFormId:Number(params.params.personFormId)
      }).subscribe(res => {
         
      });
    
    this.https.updatePersonform(params.params.personFormId,params.params.eligibilityRouteId , this.globalUserService.userDetails.value.roles[0].personTenantRoleId ,{}).subscribe(res => {
      if(res){
        this.snackbar.callSnackbaronSuccess("Submitted successfully");
          window.location.reload();
      }
    });
  
}

  correctionFormChoices: Array<boolean> = [false, false, false];
  queryParams;
  sectionsForProgress: Array<sectionCompleteEvent> = [];
  checkProgressOfSections = false;


  checkProgressOfSectionsfn(): void {
    let FormResponse;
    this.userResponse.subscribe(data=>{
      if(data?.formTypeId[0]==FormTypes.Demographic && this.checkNoRadio ){
      FormResponse=data;
      this.correctionFormChoices[0]=FormResponse.response[0]?.demographic_demographic_form_s1q2[correctionSectionValue.Section1]=="yes"?true:false;
      this.correctionFormChoices[1]=FormResponse.response[1]?.demographic_date_of_birth_s2q1[correctionSectionValue.Section2]=="yes"?true:false;
      this.correctionFormChoices[2]=FormResponse.response[2]?.demographic_social_security_number_s3q1[correctionSectionValue.Section3]=="yes"?true:false;
      }
    })
    if (this.form) {
      switch (this.form.formTypeID[0]) {
        case FormTypes.Application:
        case FormTypes.Certificate_Renewal:
        case FormTypes.Certificate_Duplicate:
        case FormTypes.Certificate_Reciprocity:
        case FormTypes.Accomodation:

          const a = !!this.sectionsForProgress.find((x: sectionCompleteEvent) => {
            return this.form.formTypeID[0] ==FormTypes.Application &&  ![15,23564,23975,23557].includes(this.global.userDetails.value.stateId) && !['117','118','63','64','22331'].includes(this.StateDetails.eligibilityRouteId)?  (x.status == "INVALID" || x.status == 'UNTOUCHED' || this.INFValue.get('infcode')?.value.length < 8 || !this.radioselectAcceptance.value) : (x.status == "INVALID" || x.status == 'UNTOUCHED') 
          });
        
          this.checkProgressOfSections = a 
  
          break;
        case FormTypes.Grievance:
          this.GreivanceResponse = []
          if (this.GrievanceStatus == undefined || this.GrievanceStatus == null || this.GrievanceStatus == '') {
            debugger
            this.GreivanceResponse.push({ selectedValue: 'testing', answer: 'tesinh' })
            this.checkProgressOfSections = false

          } else if (this.GrievanceId && this.GrievanceStatus != 'tester') {
            this.GreivanceResponse.push({ selectedValue: 'testing', answer: 'tesinh' })
            this.checkProgressOfSections = false

          }
          else {
            this.checkProgressOfSections = !!this.sectionsForProgress.find((x: sectionCompleteEvent) => {
              return x.status == "INVALID" || x.status == 'UNTOUCHED';
            });
            if (this.checkProgressOfSections == false) {
              this.GreivanceResponse.push({ selectedValue: 'testing', answer: 'tesinh' })

            } else {
              this.checkProgressOfSections == true
              this.GreivanceResponse = []
            }

          }

          break;
          case FormTypes.ExcusedAbsense:
            this.checkProgressOfSections = !!this.sectionsForProgress.find((x: sectionCompleteEvent) => {
              return x.status == "INVALID" || x.status == 'UNTOUCHED';
            });
            break;
        case FormTypes.Demographic:
          if(this.checkNoRadio){
              if(FormResponse){
              if((FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1]=="no" && FormResponse.response[1].demographic_date_of_birth_s2q1[correctionSectionValue.Section2]=="no" && FormResponse.response[2].demographic_social_security_number_s3q1[correctionSectionValue.Section3]=="no")){
                this.checkProgressOfSections = true;
              }
              else if( 
                //to check vlidation for each section need to make dynamic validation 
                (FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1]=="yes"   &&
                (FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1_Feild1]==null|| FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1_Feild2]==null ||FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1_Feild3]==null)||
                (FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1_Feild1]==""|| FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1_Feild2]=="" ||FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1_Feild3]=="")) ||
                 (FormResponse.response[1].demographic_date_of_birth_s2q1[correctionSectionValue.Section2]=="yes"  &&  (FormResponse.response[1].demographic_date_of_birth_s2q1[correctionSectionValue.Section2_Feild1]==null|| FormResponse.response[1].demographic_date_of_birth_s2q1[correctionSectionValue.Section2_Feild2]==null))||
                 (FormResponse.response[2].demographic_social_security_number_s3q1[correctionSectionValue.Section3]=="yes"   && (FormResponse.response[2].demographic_social_security_number_s3q1[correctionSectionValue.Section3_Feild1]==null || FormResponse.response[2].demographic_social_security_number_s3q1[correctionSectionValue.Section3_Feild2]==null))){
                  this.checkProgressOfSections = true;
              }
              else if( FormResponse.response[0].demographic_demographic_form_s1q2[correctionSectionValue.Section1]=="yes" || FormResponse.response[1].demographic_date_of_birth_s2q1[correctionSectionValue.Section2]=="yes" || FormResponse.response[2].demographic_social_security_number_s3q1[correctionSectionValue.Section3]=="yes"){
                this.checkProgressOfSections = false;
              }
          }
        }
          else if(!this.checkNoRadio){
            if(this.correctionFormChoices.every(x=>x==false)){
              this.checkProgressOfSections = true
            }
            else{
              this.checkProgressOfSections = this.sectionsForProgress.findIndex((x: sectionCompleteEvent) => {
                return x.status == "INVALID" || x.status == 'UNTOUCHED';
              }) > -1 
              || !(this.correctionFormChoices.findIndex((x:boolean)=>{
                return x == true;
              }) > -1);
            }
          }
          break;
          case FormTypes.ReciporatingSCMA:
            this.checkProgressOfSections = !!this.sectionsForProgress.find((x: sectionCompleteEvent) => {
              return x.status == "INVALID" || x.status == 'UNTOUCHED';
            });
            break;
            case FormTypes.RenewalSCMA:
              this.checkProgressOfSections = !!this.sectionsForProgress.find((x: sectionCompleteEvent) => {
                return x.status == "INVALID" || x.status == 'UNTOUCHED';
              });
              break;
              case FormTypes.sc_reinstate:
                this.checkProgressOfSections = !!this.sectionsForProgress.find((x: sectionCompleteEvent) => {
                  return x.status == "INVALID" || x.status == 'UNTOUCHED';
                });
      }
    }
  }
  form: Form = null;
  userResponse: BehaviorSubject<response> = new BehaviorSubject<response>(null);
  userResponse$: Observable<response> = this.userResponse.asObservable();
  fetchedUserResponse: any = null;
  breadCrumbsArray: any[] = null;
  userDetailsSub: Subscription;
  isAccomodationSelected: boolean = false;
  isSubmit: boolean = false; 
  permanentlySaveResponse: boolean = false;
  candidateId: number = null;
  personEventId: number = null;
  showRenewelPayment: boolean = false;
  showReciprocityPayment: boolean = false;
  enablePayment: boolean = false;
  AppealstatusId:number
  cartItem: any = null;
  enableResPayment: boolean = false;
  code: string = null;
  certStatusId:string=null;
  formSubmit:any;
  renewelPaymentResponse:any;
  StateDetails:any
  isAddToCart=false;
  AccomodationValues
  AccomodationlistedValues
  showGrievancePayment:boolean = false
  formResponse:any;
  ApplicationPerformLogs: PersonFormLog[] = [];
  AccomodationPerformLogs: PersonFormLog[] = [];
  accTypeFormSysFileName: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  paidCartItems;
  pageLoaded: boolean = false;
  @ViewChildren("submitButton") submitButtonRef: Array<ElementRef>;
  @ViewChild("accFormDownloadButton", { static: false }) accFormDownloadButton: ElementRef<HTMLDivElement>;
  description = '';
  value:any;
  filteredOptions:Array<any> =[];
  ngOnInit(): void {

    // THIS PREVENT THE SUBMIT BUTTON FROM VALIDATE IN THE WRONG TIME, DONT DELETE PLEASE!
    // setTimeout(() => {
    //   this.pageLoaded = true;
    //  }, 3000)
  

    this.formGroup = this.fb.group({
      description: ['', [Validators.required , noWhitespaceValidator()]]
    });

    this.INFValue = new FormGroup({
      infcode:new FormControl("", [Validators.required,Validators.maxLength(8)]),
   })


    


    this.store.dispatch(clearApplicationState());
    this.activatedRoute.paramMap.pipe(takeUntil(this.unsubscribe)).subscribe((paramMap: any) => {
      this.certStatusId = paramMap.params.certStatusId ? paramMap.params.certStatusId : "0"
      var params = paramMap.params;
      this.value = paramMap.params;
      this.StateDetails = paramMap.params;
      this.queryParams=params;
      this.AppealstatusId =(params.certStatusId !=undefined && params.certStatusId !='' && params.certStatusId !=null )? Number(params?.certStatusId):0
      if (params.formTypeId && params.candidateId && params.eligibilityRouteId && params.stateId) {
        this.stateId =Number(params.stateId)
        this.registryId =Number(params.personEventId) ==0?Number(params.certStatusId):Number(params.personEventId)
        this.formTypeId=Number(params.formTypeId)
        this.store.dispatch(setBasicDetails({
          formTypeId: Number(params.formTypeId),
          candidateId: Number(params.candidateId),
          eligibilityRouteId: Number(params.eligibilityRouteId),
          stateId: Number(params.stateId),
          personFormId: params.personFormId ? Number(params.personFormId) : null,
          code: params.code ? params.code : null,
          personEventId: params.personEventId ? Number(params.personEventId) : null
        }));
        this.showRenewelPayment=(this.globalUserService.stateId in RenewelStateList && parseInt(this.certStatusId) != 4) ?!this.showRenewelPayment:this.showRenewelPayment;
        this.showReciprocityPayment=((this.globalUserService.stateId in ReciprocityStateList)&&params.certStatusId!="4")?!this.showReciprocityPayment:this.showReciprocityPayment;
        this.showReciprocityPayment=((this.globalUserService.stateId in ReciporatingSCMA )&& params.certStatusId!="4" && (Number(params.formTypeId) == FormTypes.ReciporatingSCMA))?!this.showReciprocityPayment:this.showReciprocityPayment;
        this.showGrievancePayment=((States.includes(this.globalUserService.stateId) )&& params.certStatusId!="4" && ( Number(params.formTypeId) == FormTypes.Grievance))?!this.showReciprocityPayment:this.showReciprocityPayment;


       

        this.getpaymentDetails();
        this.formService.userResponse=true;  
        this.formService.renewalPayment.subscribe(data=>{
            if(data[0]==true){
                this.conformApplyRenewal(data);
            }
        })
        if (params.code || params.personFormId) {
          // this area handles editing of an existing form
          this.code = params.code;
          this.store.dispatch(getUserResponse({ code: this.code, personFormId: params.personFormId }));
          this.store.dispatch<Action>(getPersonFormLogs({ code: paramMap.params.code, personFormId: params.personFormId }));
        }
        else if (params.formTypeId == FormTypes.Application) {
      

          // this call will only be made when the candidate comes to fill the form for the first time.
  
        }
      }
    })


    if(this.AppealstatusId == 12){
      this.https.getNotes(this.value.personFormId ,this.globalUserService.userDetails.value.roles[0].personTenantRoleId,10).subscribe(res => {
        this.description = res[0].body;
      })
    }

    this.store.select(selectPersonFormLogs).subscribe((performlogs: any) => {
      if (performlogs && performlogs.length > 0) {
      
        performlogs.forEach((ele: PersonFormLog) => {
          if (ele.formTypeId == FormTypes.Accomodation) {
            this.AccomodationPerformLogs.push(ele)
          }
          else {
            this.ApplicationPerformLogs.push(ele)
          }
        });
        if (!(performlogs.some(item => ((item.formTypeId == FormTypes.Certificate_Renewal && this.globalUserService.stateId in RenewelStateList) || (item.formTypeId == FormTypes.Certificate_Reciprocity && this.globalUserService.stateId in ReciprocityStateList))))) {
          if (performlogs.some(x => (x.formTypeId == FormTypes.Application || x.formTypeId == FormTypes.Certificate_Reciprocity || x.formTypeId == FormTypes.Certificate_Renewal || x.formTypeId == FormTypes.ReciporatingSCMA || x.formTypeId ==FormTypes.RenewalSCMA))) {
            let status = performlogs.some(x => ((x.name == "Approved" || x.name == "Rejected" || x.name == "Change Request" || x.name =='')));
            this.withdraw = !status
          }
        }
                             // SHOWING accomodation download //
             let a = performlogs.filter(x=>x.formTypeId == 1)
             setTimeout(()=>{
              let b=this.userResponse?.value?.response[4]?.accomodation_accomodation_q1?.["98d0f83a61a64b14a3e4f5ffc7b3609c"]
              this.AccomodationlistedValues =this.AccomodationValues.filter(x=>b == x.value)
                this.showdownload = a.length && b && (this.AccomodationlistedValues[0].systemFileName !='' && this.AccomodationlistedValues[0].systemFileName !=null)?true:false
              
             },2000)
             
      }
  
    });

    this.store.select(selectForm).pipe(takeUntil(this.unsubscribe)).subscribe((x: Form) => {
      if (x) {
        if (!this.form || !this.form.formJSON) {
          this.form = x;
          if (!this.form.formJSON) {
            this.userDetailsSub = this.globalUserService.userDetails.subscribe((value: any) => {
              if (value) {
                this.store.dispatch(getFormJson(this.form));
              }
            })
          }
        }
        else if (x.formJSON && this.form.formID != x.formID) {
          if (this.userResponse.value == null || this.userResponse.value.response.length == this.form.formJSON.sections.length)
            (document.querySelectorAll('#submitButton')[0] as any).click();
          this.modifyFormJson(null, x); 
        }
      }
    });
    this.store.select(selectPersonEventId).subscribe((x: number) => this.personEventId = x);
    this.store.select(selectCandidateId).pipe(takeUntil(this.unsubscribe)).subscribe((x: number) => this.candidateId = x);
    this.store.select(selectLatestPersonFormId).pipe(takeUntil(this.unsubscribe)).subscribe((x: number) => {
      if (x) { 
        //saving personformid to apply for renewal and resproctiy after payment
        //the below condition is to check if the formtype is renewal or recirocity
        if((Number(this.queryParams.formTypeId)==FormTypes.Certificate_Reciprocity || Number(this.queryParams.formTypeId)==FormTypes.Certificate_Renewal)){
          //the below condition is to check if the cart item does not consist of renewal and recirocity 
          //inorder to avoid overwritting the savepersonFormId in servrice injector
          this.cartItem?.some(item=>item.examTypeId ==FormTypes.Certificate_Renewal || item.examTypeId ==FormTypes.Certificate_Reciprocity)?
          0:this.formService.savedResponseId=x
        }
        this.userResponse.next({
        formTypeId: this.userResponse.value.formTypeId,
        personFormId: this.getUnique([...this.userResponse.value.personFormId, x]),
        response: this.userResponse.value.response,
        disabled: this.userResponse.value.disabled,
        code: this.code
      })
        this.navigate(this.form.formTypeID[0])
    }
    });
     if(FormTypes.Certificate_Renewal == this.formTypeId){
      this.https.getRegistryId(this.registryId).subscribe((data:any)=>{
        if(data !=null){
          this.certNumber = Number(data.CertNumber)
        }
     })
     }
   

    this.store.select(renewelCart).subscribe((x: any) => {
      if (x?.isSubmit.isSubmit) {
        if(this.generateRegistry && this.formService.genrateScCertificate){
          this.formService.genrateScCertificate=false;
          let renewalItems=this.paidCartItems.find(x=>Number(x.cartItemTypeId)==FormCartItemId.Renewal)
          // renewalItems=Number(renewalItems?.certnumber)
        this.activatedRoute.params.subscribe(paramsId => {
          if(this.globalUserService.stateId==RenewelStateList.SC){
          const generateCertDetails:generateRegistry={
            certNumber:this.certNumber,
            certType:registryCerttype.RenewalCertTpe,
            actionBy:this.globalUserService.candidateId,
            stateId:this.globalUserService.stateId,
            personId: this.globalUserService.personId
          }
          this.http.post(`${environment.baseUrl}registry/generate-registry`,generateCertDetails).subscribe(data=>{
            if(data){
              this.snackbar.callSnackbaronSuccess("Certificate Generated Succesfully")
              this.generateRegistry=false;
            }
          })
      
        }
      })
    }
  };
})


    this.store.select(selectUserResponse).pipe(takeUntil(this.unsubscribe)).subscribe((x: Array<any>) => {
      if (x) {
        this.code = x[0].code;
        this.fetchedUserResponse = x;
        if (x.findIndex((x: any) => { return x.formTypeId == FormTypes.Accomodation }) > -1) {
          this.isAccomodationSelected = true;
          this.store.dispatch(getFormJson({
            formTypeID: [FormTypes.Accomodation],
            eligibilityID: this.form.eligibilityID,
            stateID: this.form.stateID,
            isSubmitAllowed: [true],
          }))
        }
        if (x[0].personEventId) this.personEventId = x[0].personEventId;
        this.userResponse.next({
          formTypeId: [...x.map((x: any) => { return x.formTypeId })],
          code: this.code,
          personFormId: [...x.map((x: any) => { return x.id })],
          disabled: this.getDisabled(x),
          response: this.parseResponse(x)
        })
      }
    });
    this.accTypeFormSysFileName.pipe(debounceTime(0), takeUntil(this.unsubscribe)).subscribe((value: string) => {
      if (value) this.accFormDownloadButton.nativeElement.scrollIntoView({ behavior: 'smooth' });
    })

    this.http.get(environment.baseUrl + "candidate/api/Form/accommodationtype").subscribe((response: any) => {
      this.AccomodationValues =response
    })



    this.global.userstatus.subscribe(data => {
      if(data !=null){
        this.status = data.status
        if(data.status !='Drafted' && data.status !='Change Request' && data.status !='Training Program Change Request'){
          this.radioselect.setValue(data.testingPreferenceName)
          let isTestCenterAcknowledge = data.isTestCenterAcknowledge == false?"No":'Yes'
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
          this.getSelectedRoute(data.testingPreferenceId,isTestCenterAcknowledge,data.testCenterId)
          this.INFValue = this.fb.group({
            infcode:[data.testCenterId?data.testCenterId:'',[Validators.required , noWhitespaceValidator(),Validators.maxLength(8)]],
         })
          this.NonEditData = true
          this.INFValue.get('infcode').disable()
        }else{
          debugger
          this.NonEditData = false
          this.radioselect.setValue(data.testingPreferenceName)
          let isTestCenterAcknowledge = data.isTestCenterAcknowledge == false && data.isTestCenterAcknowledge !=null?"No":data.isTestCenterAcknowledge !=null?'Yes':null
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
          this.getSelectedRoute(data.testingPreferenceId,isTestCenterAcknowledge,data.testCenterId)
        }
      }else{
        this.INFValue = new FormGroup({
          infcode:new FormControl("INF", [Validators.required,Validators.maxLength(8)]),
       })
      }
 
  
      // this.checkedValue(data.testingPreferenceId)
      // this.getSelectedRoute(true,data.testingPreferenceName,data.status,data.testCenterId)
   
      // this.INFValue.patchValue({infcode:data.testCenterId,testreference:true,acceptance:data.isTestCenterAcknowledge})
      this.cdr.markForCheck()
    })


   
  // grievance revaluator ///

  this.globalUserService.GrievamceEvaluator.subscribe((data) => {
    if (data) {
      this.GrievanceStatus = data.value
      this.GrievanceId = data.id
      this.checkProgressOfSectionsfn()
    } else {
      this.checkProgressOfSectionsfn()
    }
  })

    // this.global.GrievamceEvaluator.subscribe((data)=>{
    //       if(data){
    //         this.GrievanceStatus = data.model.value
    //       }
    // })

    // this.global.GrievamceEvaluatorresponse.subscribe((data)=>{
    //      if(data.value !=null){
    //       setTimeout(()=>{
    //         this.GreivanceValue = data.value
    //         this.checkProgressOfSections = false
    //       },2000)
           
    //      }
    // })

 

   

  



  }

  cleanINFString(str) {
    const match = str.match(/^INF(\d+)$/);
    return match ? match[1] : null;
  }

  submit(submit: boolean, savePermanently: boolean, addToCart: boolean) {
    
    this.isSubmit = submit;
    this.permanentlySaveResponse = savePermanently;
    this.isAddToCart = addToCart;

    if (this.checkProgressOfSections){
      
      alert('Some step at the process are missing, please verify and try again.');
    }      
    else
      this.forceSubmitValidation();
  }

  forceSubmitValidation() {
    this.formBuilder.forceSubmitWithValidation();
  }

  


  getpaymentDetails(){
    this.store.dispatch(getCartItems({ personTenantRoleId: this.globalUserService.candidateId }))
    this.store.select(get_cartItems).subscribe(data => {
      if (data.length > 0) {
        this.enablePayment = data.some(item =>AllowPayment.includes(item.examTypeId))
        this.cartItem = data;
        this.enableResPayment = data.some(item => AllowPaymentReciporating.includes(item.examTypeId))
      }
    })
  }

  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }

  getDisabled(x: Array<any>) {
    if (x.length == 1) {
      // only one form is there
      var parsedRes = JSON.parse(x[0].formResponse);
      return Array.isArray(parsedRes) ? getMappedDisabled(x[0], parsedRes) : getMappedDisabled(x[0], parsedRes.formValue);
    }
    else {
      var parsedRes1 = JSON.parse(x[0].formResponse);
      var retValue = getMappedDisabled(x[0], parsedRes1);
      if (x[1].formTypeId == FormTypes.Accomodation) {
        retValue[retValue.length - 1] = !(x[1].statusId == FormStatuses.Drafted || x[1].statusId == FormStatuses.ChangeRequest ||x[1].statusId == FormStatuses.withdraw_request )
      }
      return retValue;
    }
    function getMappedDisabled(parent: any, response: Array<any>) {
      return response.map((x: any) => { return !(parent.statusId == FormStatuses.Drafted || parent.statusId == FormStatuses.ChangeRequest || parent.statusId == FormStatuses.withdraw_request ) });
    }
  }
  
  get getWhetherAtleastOneEnabled() {
    if (this.userResponse.value)
      return this.userResponse.value.disabled.findIndex((x: boolean) => { return !x }) > -1;
    else 
      return true
  }
  parseResponse(x: Array<any>) {
    if (x.length == 1) {
      // only one form is there
      var parsedRes = JSON.parse(x[0].formResponse);
      return Array.isArray(parsedRes) ? parsedRes : parsedRes.formValue;
    }
    else {
      // we have both application and accomodation
      var parsedRes1 = JSON.parse(x[0].formResponse);
      var parsedRes2 = JSON.parse(x[1].formResponse);
      if (lodash.isEqual(parsedRes1, parsedRes2)) {
        // this means the form was filled by operation staff
        return parsedRes1;
      }
      else {
        // change request problem
        // parsedRes2[parsedRes.length-1] contains the latest accomodation from
        // and the parsedRes1[0:parsedRes1.length-2] contains the latest application form
        parsedRes1[parsedRes1.length-1] = parsedRes2[parsedRes2.length-1];
        return parsedRes1;
      }
    }

  }
  deleteForm() {
    
    this.dialog
      .open(PopUpComponent, {
        data: {
          title: this.lngSrvc.curLangObj.value.deletePage,
          message: this.lngSrvc.curLangObj.value.deleteMsg,
          cancelButton: this.lngSrvc.curLangObj.value.cancel,
          OkButton: this.lngSrvc.curLangObj.value.delete,
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        if (confirmed == true || confirmed.confirmed == true) {
          this.code && this.userResponse.value.personFormId[0] !=null  ?
            this.store.dispatch(deleteUserResponse({
              candidateId: this.candidateId,
              personFormId: this.userResponse.value.personFormId[0],
              route: true,
              formTypeID: this.userResponse.value.formTypeId[0],
            }))
            :
            this.navigate(this.form.formTypeID[0]);

          //delete renewel once application delete
          let cartItemIndex: number;
          cartItemIndex = this.cartItem.findIndex(item => item.cartItemTypeId == FormCartItemId.Renewal || item.cartItemTypeId == FormCartItemId.Reciprocity || item.cartItemTypeId == FormCartItemId["V2-reciporating-scmae"])
          this.cartItem = this.cartItem.filter(item => item.cartItemTypeId == FormCartItemId.Renewal || item.cartItemTypeId == FormCartItemId.Reciprocity || item.cartItemTypeId == FormCartItemId["V2-reciporating-scmae"])
          if (this.cartItem.length >= 1) {
            this.store.dispatch<Action>(
              removeCartItem({ tetantId: this.global.candidateId, cartItemsId: this.cartItem[0].personEventCartId }))
            this.store.select(selectorGetCartDeleteStatus).subscribe((data: any) => {
              if (data) {
                this.store.dispatch<Action>(deleteCartItem({ index: cartItemIndex }));
              }
            });
          }

        }
      })
  }

  navigate(formTypeID: FormTypes) {
    switch (formTypeID) {
      case FormTypes.Application:
      case FormTypes.Accomodation: this.router.navigate(['application']);
        break;
      case FormTypes.Grievance:this.globalUserService.Grievanceroute =="Evaluator"?null: this.router.navigate(['grievance-form']);
        break;
        case FormTypes.ExcusedAbsense: this.router.navigate(['absense-form']);
        break;
      case FormTypes.Demographic: this.router.navigate(['manage-profile']);
        break;
      case FormTypes.Certificate_Duplicate:
        case FormTypes.RenewalSCMA:formTypeID ==FormTypes.RenewalSCMA && RenewelStateLists.includes(this.globalUserService.stateId)?null: this.router.navigate(['registry']);
        break;
        case FormTypes.Certificate_Renewal: formTypeID ==FormTypes.Certificate_Renewal && RenewelStateLists.includes(this.globalUserService.stateId)?null: this.router.navigate(['registry']);
         break;
        case FormTypes.ReciporatingSCMA: formTypeID == FormTypes.ReciporatingSCMA && RenewelStateLists.includes(this.globalUserService.stateId)?null: this.router.navigate(['registry']);
        break;
      case FormTypes.Certificate_Reciprocity: formTypeID == FormTypes.Certificate_Reciprocity && this.globalUserService.stateId in ReciprocityStateList?null: this.router.navigate(['registry']);
        break;
      default: this.router.navigate(['dashboard']);
    }
  }

  onKeydownMain(event){
    if (event !== 'I' && event !== 'N' && event !== 'IN'  && event != '' && (event.match(/[A-Za-z]+/g) !='IN') && (event.match(/[A-Za-z]+/g) !='IF') && (event.match(/[A-Za-z]+/g) !='F') && (event.match(/[A-Za-z]+/g) !='NF')) {
       event =''
       this.checkProgressOfSectionsfn()
    }else{
      this.INFValue.setValue({infcode:"INF"})
      this.checkProgressOfSectionsfn()
    }

    this._filter(this.INFValue.value.infcode)
    
  
  }

  paymentPage() {
    setTimeout(() => {
      this.router.navigateByUrl('/exam-scheduled/payment/1');
    }, 1000)

  }
  
  conformApplyRenewal(result:any){
    if (this.form.isSubmitAllowed[0] && this.formService.alreadySubscribed) {
      this.formService.alreadySubscribed=false;
      this.store.dispatch(saveUserResponse({
        formTypeID: this.formService.savedResponse.formTypeID,
        userResponse: {
          id: this.formService.savedResponseId,
          personTenantRoleId: this.formService.savedResponse.userResponse.personTenantRoleId,
          formResponse: this.formService.savedResponse.userResponse.formResponse,
          isSubmit: true,
          formId: this.formService.savedResponse.userResponse.formId,
          code: this.formService.savedResponse.userResponse.code,
          personEventId: this.formService.savedResponse.userResponse.personEventId,
          actionBy: this.formService.savedResponse.userResponse.actionBy,
     
        }
      }));
    this.paidCartItems=result[1]
    this.generateRegistry=result[0];
    }
  }

  saveResponse($event: validatedSubmission) {
    let FormValueValid =$event
   if (!this.code) this.code = uuidv4();
   
   this.userResponse.next({
     formTypeId: this.form.formTypeID,
     personFormId: this.userResponse.value && this.userResponse.value.personFormId ? this.userResponse.value.personFormId : [],
     disabled: this.userResponse.value && this.userResponse.value.disabled ? this.userResponse.value.disabled : [false, false],
     response: $event.formValue,
     code: this.code
   })
   if (this.permanentlySaveResponse) {
     if (this.isSubmit) {
       if ((!this.checkProgressOfSections || this.checkValidity($event))) {
        if (FormValueValid.formValue.find(x =>x.hasOwnProperty("accomodation_accomodation_dates")) && (this.status !="Change Request" && this.statusId !=14) ||  (States.includes(this.stateId) && (this.status !="Change Request" && this.statusId !=14))){
        this.form.formTypeID[0]===2 && this.GreivanceResponse.length > 0?this.saveData() : (FormValueValid.valid !=false && FormValueValid.formValue.filter(x => x != null && x != false) || FormValueValid.valid) ? this.saveData() : this.snackbar.callSnackbaronError('Please fill all required fields and submit.');
        }
         else {
           this.saveData();
         }
       }
       else this.snackbar.callSnackbaronError('Please fill all required fields and submit.')
     }
     else if(this.isAddToCart){
       //only for renewel form and specified state to submit renewel form
       if(this.form.formTypeID[0]==FormTypes.Certificate_Renewal && (this.globalUserService.stateId in RenewelStateList) && this.certStatusId !='4' ){
         if(this.cartItem?.some(item=>(item.examTypeId ==FormTypes.Certificate_Renewal))){
          this.snackbar.callSnackbaronWarning("Cannot add to cart as renewal form exist in cart")
         }
         else{
         this.SaveRenewalandResprocity()
         const userResponse=this.userResponse.subscribe(data=>{
           if(data.personFormId.length!=0){
             this.formService.addTocartRenewlFee(this.form,this.queryParams);
             userResponse.unsubscribe();
           }
         })
       }
       }
       else if(this.form.formTypeID[0]==FormTypes.RenewalSCMA && (this.globalUserService.stateId in RenewelStateList) && this.certStatusId !='4' ){
        if(this.cartItem?.some(item=>(item.examTypeId ==FormTypes["V2-renewal-SCMAE"]))){
          this.snackbar.callSnackbaronWarning("Cannot add to cart as renewal form exist in cart")
        
        }
        else{
        if((FormValueValid.valid !=false)){
         this.SaveRenewalandResprocity()
         const userResponse=this.userResponse.subscribe(data=>{
           if(data.personFormId.length!=0){
             this.formService.addTocartRenewlFee(this.form,this.queryParams);;
             userResponse.unsubscribe();
           }
         })
       }else{
         this.snackbar.callSnackbaronError("Please fill all required fields and Add to cart.")
       }
      }
      }
       else if((this.form.formTypeID[0]==FormTypes.Certificate_Reciprocity) && (this.globalUserService.stateId in ReciprocityStateList) && this.certStatusId !='4'){
         if(this.cartItem?.some(item=>item.examTypeId ==FormTypes.Certificate_Reciprocity)){
          this.snackbar.callSnackbaronWarning("Cannot add to cart as reciproctiy form exist in cart")
         }
         else{
           this.SaveRenewalandResprocity()
         const userResponse=this.userResponse.subscribe(data=>{
           if(data.personFormId.length!=0){
             this.formService.addTocartReciprocityFee(this.form);
             userResponse.unsubscribe();
           }
         })
         }
         
       }
       else if((this.form.formTypeID[0]==FormTypes.ReciporatingSCMA) && (this.globalUserService.stateId in ReciporatingSCMA)){
        if(this.cartItem?.some(item=>item.examTypeId ==FormTypes["V2-reciporating-SCMAE"])){
          this.snackbar.callSnackbaronWarning("Cannot add to cart as reciproctiy form exist in cart")
        }
        else{
         if((FormValueValid.valid !=false)){
           this.SaveRenewalandResprocity()
           const userResponse=this.userResponse.subscribe(data=>{
             if(data.personFormId.length!=0){
               this.formService.addTocartReciprocityFee(this.form);
               userResponse.unsubscribe();
             }
           })
         }else{
           this.snackbar.callSnackbaronError("Please fill all required fields and Add to cart.")
         }
        }
        
      } 
      else if((this.form.formTypeID[0]==FormTypes.sc_reinstate) && (this.globalUserService.stateId in ReciporatingSCMA)){
        if(this.cartItem?.some(item=>item.examTypeId ==FormTypes["SC_Reinstate_Renewal"])){
          this.snackbar.callSnackbaronWarning("Cannot add to cart as reciproctiy form exist in cart")
        }
        else{
         if((FormValueValid.valid !=false)){
           this.SaveRenewalandResprocity()
           const userResponse=this.userResponse.subscribe(data=>{
             if(data.personFormId.length!=0){
               this.formService.addTocartRenewlFee(this.form,this.queryParams);
               userResponse.unsubscribe();
             }
           })
         }else{
           this.snackbar.callSnackbaronError("Please fill all required fields and Add to cart.")
         }
        }
        
      }
      if((this.form.formTypeID[0]==FormTypes.Grievance)){
        if(this.cartItem?.some(item=>item.examTypeId ==FormTypes["GrievanceEvaluator"])){
          this.snackbar.callSnackbaronWarning("Cannot add to cart as reciproctiy form exist in cart")
        }
        else{
         if((FormValueValid.valid !=false || (this.GreivanceValue !='' && this.GreivanceValue !=null && this.GreivanceValue !=undefined))){
           this.SaveRenewalandResprocity()
           const userResponse=this.userResponse.subscribe(data=>{
             if(data.personFormId.length!=0){
               this.formService.addTocartReciprocityFee(this.form);
               userResponse.unsubscribe();
             }
           })
         }else{
           this.snackbar.callSnackbaronError("Please fill all required fields and Add to cart.")
         }
        }
        
      }
       this.isAddToCart=false
     }
     else  {
       this.dispatchSaveResponseActions();
     }
   }
 }

  saveData() {
    if(this.StateDetails.formTypeId =="5" && this.StateDetails.stateId =="4" && (this.formservices.SearchValue !=null && this.formservices.SearchValue !='' )){
      this.dialog
      .open(PopUpComponent, {
        data: {
          title: this.form.formTypeID[0] == FormTypes.Demographic ? this.lngSrvc.curLangObj.value.correctionProcess : this.lngSrvc.curLangObj.value.leavePage,
          message: this.form.formTypeID[0] == FormTypes.Demographic ? this.lngSrvc.curLangObj.value.correctionProcessMsg : this.lngSrvc.curLangObj.value.leavePageMsg,
          cancelButton: this.lngSrvc.curLangObj.value.cancel,
          OkButton: this.lngSrvc.curLangObj.value.save,
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        // setTimeout(() => {
        //   if(confirmed==false){
        //     this.router.navigateByUrl("application/application-form")
        //   }

        // }, 2000);

        if (confirmed == true || confirmed.confirmed == true) {

          this.dispatchSaveResponseActions();
        }
      })
    }else if(this.StateDetails.formTypeId !="5"){
     this.formservices.RemoveGetuserDataafterpopupopen = this.form.formTypeID[0] == FormTypes.Demographic?"RemovedApi":null
      this.dialog
      .open(PopUpComponent, {
        data: {
          title: this.form.formTypeID[0] == FormTypes.Demographic ? this.lngSrvc.curLangObj.value.correctionProcess :this.form.formTypeID.includes(FormTypes.Grievance)?this.lngSrvc.curLangObj.value.GreivanceMsg: this.lngSrvc.curLangObj.value.leavePage,
          message: this.form.formTypeID[0] == FormTypes.Demographic ? this.lngSrvc.curLangObj.value.correctionProcessMsg : this.lngSrvc.curLangObj.value.leavePageMsg,
          cancelButton: this.lngSrvc.curLangObj.value.cancel,
          OkButton: this.lngSrvc.curLangObj.value.save,
          formType:this.form.formTypeID[0]
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        // setTimeout(() => {
        //   if(confirmed==false){
        //     this.router.navigateByUrl("application/application-form")
        //   }

        // }, 2000);

        if (confirmed == true || confirmed.confirmed == true) {

          this.dispatchSaveResponseActions();
        }
      })
    }else if(this.StateDetails.formTypeId =="5" && stateAllowstosavedata.includes(Number(this.StateDetails.stateId))){
      this.dialog
      .open(PopUpComponent, {
        data: {
          title: this.form.formTypeID[0] == FormTypes.Demographic ? this.lngSrvc.curLangObj.value.correctionProcess : this.lngSrvc.curLangObj.value.leavePage,
          message: this.form.formTypeID[0] == FormTypes.Demographic ? this.lngSrvc.curLangObj.value.correctionProcessMsg : this.lngSrvc.curLangObj.value.leavePageMsg,
          cancelButton: this.lngSrvc.curLangObj.value.cancel,

          OkButton: this.lngSrvc.curLangObj.value.save,
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        // setTimeout(() => {
        //   if(confirmed==false){
        //     this.router.navigateByUrl("application/application-form")
        //   }

        // }, 2000);

        if (confirmed == true || confirmed.confirmed == true) {

          this.dispatchSaveResponseActions();
        }
      })
    }
    
    else{
       this.snackbar.callSnackbaronError('Please fill all required fields and submit.')
    }

  }
  handleChange($event: DynamicFormControlEvent) {
                      // SHOWING accomodation download //
      this.showdownload= this.AccomodationlistedValues?.systemFileName ==undefined?false:true

    this.checkNoRadio=false;
    // the below comparision has to be changed to using identifiers
    // for Shreya, the array which was being updated earlier is now in the file forms-wrapper-static-data.ts 
    if (AccomodationYesOrNoFieldIds.includes($event.model.id)) {
      
      if (["true", "yes"].includes(($event.control.value as string).toLowerCase())) {
                // SHOWING accomodation download //
          this.showdownload =((this.accTypeFormSysFileName[0]?.systemFileName !='' &&this.accTypeFormSysFileName[0]?.systemFileName !=undefined) || (this.accTypeFormSysFileName.value !="" && this.accTypeFormSysFileName.value !=null) )?true:false
        this.isAccomodationSelected = true;
        if (!this.form.formTypeID.includes(FormTypes.Accomodation))
          this.store.dispatch(
            getFormJson({
              formTypeID: [FormTypes.Accomodation],
              stateID: this.form.stateID,
              eligibilityID: this.form.eligibilityID,
              isSubmitAllowed: [true]
            })
          );
      } else {
        // i.e accomodation form is also saved
        this.isAccomodationSelected = false;
        

        // this.accTypeFormSysFileName.next(false);
        let accIndex = this.form.formJSON.sections.findIndex((x: any) => {
          return AccomodationFormsSectionOneIds.includes(x.id)
        })
        if (accIndex > 0) {
          (document.querySelectorAll('#submitButton')[0] as any).click();
          this.form.formTypeID = this.form.formTypeID.filter((x: any) => { return x != FormTypes.Accomodation });
          this.modifyFormJson(accIndex);
        }
      }
    }
    // the below is the id for AccomodationTypeField
    if (AccomodationFormAccTypeFieldIds.includes($event.model.id)) {
      this.http.get(environment.baseUrl + "candidate/api/Form/accommodationtype").subscribe((response: any) => {
        this.accTypeFormSysFileName.next(response.find((x: any) => { return x.value == $event.control.value }).systemFileName);
      })
    }
    if(CorrectionFormFieldIds.includes($event.model.id)){
      this.correctionFormChoices[CorrectionFormFieldIds.findIndex((x)=> x == $event.model.id)] 
      = ["true", "yes"].includes(($event.control.value as string).toLowerCase()); 
    }
    this.checkProgressOfSectionsfn()
  }
  
  modifyFormJson(indexToRemove: number = null, extraForm = null) {
    var modifiedFormJson = lodash.cloneDeep(this.form.formJSON);
    if (indexToRemove != null)
      modifiedFormJson.sections.splice(indexToRemove, 1);
    if (extraForm != null) {
      modifiedFormJson.sections = [...modifiedFormJson.sections, ...extraForm.formJSON.sections];
      modifiedFormJson.formLayout = { ...modifiedFormJson.formLayout, ...extraForm.formJSON.formLayout };
    }
    var modifiedFormClone = {
      formID: [...this.form.formID],
      formTypeID: [...this.form.formTypeID],
      eligibilityID: this.form.eligibilityID,
      stateID: this.form.stateID,
      formJSON: modifiedFormJson,
      isSubmitAllowed: this.fetchedUserResponse ? this.fetchedUserResponse.map((res: any) => { return (res.statusId == FormStatuses.Drafted || res.statusId == FormStatuses.ChangeRequest || res.statusId === FormStatuses.withdraw_request) }) : [...this.form.isSubmitAllowed]    }
    if (extraForm != null) {
      modifiedFormClone.formTypeID = this.getUnique<number>([...this.form.formTypeID, ...extraForm.formTypeID]);
      modifiedFormClone.formID = this.getUnique<number>([...this.form.formID, ...extraForm.formID]);
      if (!this.fetchedUserResponse || (this.fetchedUserResponse && this.fetchedUserResponse.length < 2))
        modifiedFormClone.isSubmitAllowed = [...modifiedFormClone.isSubmitAllowed, ...extraForm.isSubmitAllowed];
    }
    // this definitely needs to be fixed , a lot of changes required in formBuilder itself
    // probably ChangeDetectionStrategy.OnPush needs to be used or something like that
    this.form = null;
    setTimeout(() => {
      this.form = modifiedFormClone;
    })
  }

  getUnique<T>(arr: Array<T>): Array<T> {
    return arr.filter((value, index, self) => {
      return self.indexOf(value) === index
    })
  }

  private _filter(value: string) {
    const filterValue = value.toLowerCase();
     this.filteredOptions = this.options.filter(option => option.testCenterId.toLowerCase().includes(filterValue));
  }

  checkValidity($event: validatedSubmission) {
    if (this.form.formTypeID.includes(FormTypes.Grievance) && $event.formValue) {
      let parentResponse = $event.formValue[0]["grievance__grievance_form_q2"]['c8e558db99684e9ebb6d061b6ad28b10'];
      for (let id of GrievanceFormFieldIds[parentResponse]) {
        let curVal = $event.formValue[0]["grievance__grievance_form_q2"][id];
        if (curVal == null || curVal == "" || curVal == undefined) {
          return false;
        }
      }
      return true;
    }
    return $event.valid;
  }

  dispatchSaveResponseActions() {
    
    if (this.form.isSubmitAllowed[0]) {
     
      this.store.dispatch(saveUserResponse({
        formTypeID: this.userResponse.value.formTypeId[0],
        userResponse: {
          id: this.userResponse.value.personFormId.length > 0 ? this.userResponse.value.personFormId[0] : 0,
          personTenantRoleId: this.candidateId,
          formResponse: JSON.stringify(this.userResponse.value.response),
          isSubmit: this.isSubmit,
          formId: this.form.formID[0],
          code: this.code,
          version:1,
          personEventRegistryId:this.queryParams.personEventId?Number(this.queryParams.personEventId):0,
          personEventId: this.personEventId ? this.personEventId : 0,
          actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
          eligibilityRouteId:this.queryParams.eligibilityRouteId?Number(this.queryParams.eligibilityRouteId):0,
          StateCode:this.globalUserService.userDetails.value.stateCode,
          testingPreferenceId: this.TestReferenceId,
          testCenterId:this.TestReferenceId == 1?`${this.INFValue.value.infcode}`:null,
          isTestCenterAcknowledge:this.TestReferenceId == 1?this.AcceptanceName:null
        }
      }));
     
    }
    if (this.isAccomodationSelected && this.form.isSubmitAllowed[1]) {
      
      this.store.dispatch(saveUserResponse({
        formTypeID: this.userResponse.value.formTypeId[1],
        userResponse: {
          id: this.userResponse.value.personFormId.length > 1 ? this.userResponse.value.personFormId[1] : 0,
          personTenantRoleId: this.candidateId,
          formResponse: JSON.stringify(this.userResponse.value.response),
          isSubmit: this.isSubmit,
          formId: this.form.formID[1],
          code: this.code,
          version:1,
          personEventRegistryId:this.queryParams.personEventId?Number(this.queryParams.personEventId):0,
          personEventId: this.personEventId ? this.personEventId : 0,
          actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
          testingPreferenceId: this.TestReferenceId,
          testCenterId:this.TestReferenceId == 1?`${this.INFValue.value.infcode}`:null,
          isTestCenterAcknowledge:this.TestReferenceId == 1?this.AcceptanceName:null
        }
      }));
    }
    else if (!this.isAccomodationSelected && this.userResponse.value.personFormId.length == 2) {
      this.store.dispatch(deleteUserResponse({
        candidateId: this.candidateId,
        personFormId: this.userResponse.value.personFormId[1],
        route: false,
        formTypeID: FormTypes.Accomodation,
      }))
    }
    
  }

  SaveRenewalandResprocity(){
    if (this.form.isSubmitAllowed[0]) {
      this.store.dispatch(saveUserResponse({
        formTypeID: this.userResponse.value.formTypeId[0],
        userResponse: {
          id: this.userResponse.value.personFormId.length > 0 ? this.userResponse.value.personFormId[0] : 0,
          personTenantRoleId: this.candidateId,
          formResponse: JSON.stringify(this.userResponse.value.response),
          isSubmit: this.isSubmit,
          formId: this.form.formID[0],
          code: this.code,
          personEventRegistryId:this.personEventId ? this.personEventId : 0,
          personEventId: this.personEventId ? this.personEventId : 0,
          actionBy: this.globalUserService.userDetails.value.personTenantRoleId,


        }
      }));
      (Number(this.queryParams.formTypeId) == FormTypes.Certificate_Reciprocity || Number(this.queryParams.formTypeId) == FormTypes.Certificate_Renewal || Number(this.queryParams.formTypeId) == FormTypes.ReciporatingSCMA || Number(this.queryParams.formTypeId) == FormTypes.RenewalSCMA  ) ?
        this.formService.savedResponse = {
          formTypeID: this.userResponse.value.formTypeId[0],
          userResponse: {
            personTenantRoleId: this.candidateId,
            formResponse: JSON.stringify(this.userResponse.value.response),
            formId: this.form.formID[0],
            code: this.code,
            personEventId: this.personEventId ? this.personEventId : 0,
            actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
          }
        } : null

    }
  }

  initialSectionValidationEvent(event: any) {
    setTimeout(() => {
      this.sectionsForProgress = event
      this.checkProgressOfSectionsfn()
    }, 1200);
  }

  changeSectionValidationEvent(event: any) {
    this.sectionsForProgress.map(elem => {
      if (elem.sectionName === event.sectionName)
        return event

      return elem
    })
    
    this.checkProgressOfSectionsfn()
  }

getINFcode(){
    this.http.get(`${environment.baseUrl}candidate/api/Form/GetTestCenterDetailsByStateForCredentia?stateCode=${this.globalUserService.userDetails.getValue().stateCode}`).subscribe((data:Array<any>)=>{
       if(data){
         this.options = data
              this.filteredOptions = data

         
       }
    })
}

  getSelectedRoute(event,isTestCenterAcknowledge?:string,testCenterId?:string) {
    if (event == 1) {
      debugger
      this.INFSelected = true
      this.TestReferenceId = event
     isTestCenterAcknowledge !=null?this.radioselectAcceptance.setValue(isTestCenterAcknowledge):this.radioselectAcceptance.setValue(null)
     testCenterId == null?  this.INFValue = new FormGroup({
      infcode:new FormControl("INF", [Validators.required,Validators.maxLength(8)]),
   }):this.INFValue = new FormGroup({
    infcode:new FormControl(testCenterId, [Validators.required,Validators.maxLength(8)]),
 })
         this.checkProgressOfSectionsfn()
    } else if (event == 2) {
      this.INFSelected = false
      this.TestReferenceId = event
      this.INFValue.setValue({infcode:'INF77373'})
      this.radioselectAcceptance.setValue('No')
      this.checkProgressOfSectionsfn()
    }
  }

  getSelectedAccept(event){
    this.AcceptanceName = event ==1?true:false
    event ===1?this.radioselectAcceptance.setValue('Yes'):this.radioselectAcceptance.setValue('No')
    this.checkProgressOfSectionsfn()

  }

  paste(event: any) {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData("text/plain") || "";
    const onlyNumbers = this.extractNumbersFromString(pastedData);
    this.INFValue.setValue({infcode:`${onlyNumbers}`})
    this.checkProgressOfSectionsfn()
  }

   extractNumbersFromString(input: string): string {
    const prefix = "INF";
  
    if (input.includes(prefix)) {
      // Keep the prefix and extract only digits after the prefix
      const index = input.indexOf(prefix);
      const afterPrefix = input.slice(index + prefix.length);
      const digitsOnly = afterPrefix.replace(/\D/g, "");
      return prefix + digitsOnly;
    }
  
    // If INF is not present, keep the digits and add INF prefix
    const digitsOnly = input.replace(/\D/g, "");
    return prefix + digitsOnly;
  }
  
  


  ngOnDestroy() {
    this.unsubscribe.next();
    this.unsubscribe.complete();
    this.global.userstatus.next(null)
  }

  
}



