// .cdk-drop-list {
//     display: flex;
//     flex-direction: row;
//     flex-wrap: wrap;
//     padding-right: 10px;
//     padding-bottom: 10px;
// }
.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
    opacity: 0.3;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.add-new {
    background-color: var(--button-background);
    color: #fff;
    width: 210px;
    height: 40px;
}

.add {
    background-color: var(--text-color2);
    color: #fff;
    width: 208px;
    height: 33px;
    font-size: 12px;
}

.buttonMA{
    width:262px !important
}

.backGround_color {
    background-color: var(--background-base2);
}

.textColor {
    color: var(--text-color4);
    font-family: Roboto;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    text-align: center;

    align-items: center;
}

.welc-note {
    color: var(--text-color1);
}

.size {
    height: 70px;
    width: 70px;
}
