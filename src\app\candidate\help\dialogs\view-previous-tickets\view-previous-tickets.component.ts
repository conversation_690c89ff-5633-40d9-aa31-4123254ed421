import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BehaviorSubject, Observable } from 'rxjs';
import { HelpService } from '../../help.service';
import { TicketForm } from '../../interfaces/ticketForm';
import { TicketDetail } from '../../interfaces/ticket-detail';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { BreakpointObserver } from '@angular/cdk/layout';
import { HttpClient } from '@angular/common/http';
import { URL } from 'src/app/core/url';
import { FileViewPopupComponent } from 'src/app/core/examroom-formbuilder/file-upload-control/file-view-popup/file-view-popup.component';
import { DynamicFileUploadModel, DynamicFormHook, DynamicFormModel, DynamicFormService } from '@ng-dynamic-forms/core';

@Component({
  selector: 'exai-view-previous-tickets',
  templateUrl: './view-previous-tickets.component.html',
  styleUrls: ['./view-previous-tickets.component.scss']
})
export class ViewPreviousTicketsComponent implements OnInit {

  form: FormGroup;
  description: string;

  viewUrl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  viewUrl$: Observable<string> = this.viewUrl.asObservable();

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private breakpointobserver: BreakpointObserver,
    public dialog: MatDialog,
    private dialogRef: MatDialogRef<ViewPreviousTicketsComponent>,
    private helpService: HelpService,
    private snackBar: SnackbarService,
    private dynamicFormService:DynamicFormService,
    @Inject(MAT_DIALOG_DATA) data) {
    this.description = data.description;
  }

  previousTickets$: Observable<TicketForm[]>
  previousTicketDetails$: Observable<TicketDetail[]>
  currentTicket: number | null = null
  fileUploadModel: DynamicFormModel;
  fileUploadFormGroup: FormGroup = null;
  supportDocs;


  ngOnInit(): void {
    this.form = this.fb.group({
      description: [this.description, []],
    });

    this.previousTickets$ = this.helpService.getPreviousTickets()
    //this.ticketdata[0].attachments.map((x:any)=>{ return x.fileName+ '|' + x.systemFileName })
   
    this.previousTickets$.subscribe(data => {
      if (data.length == 0) {
        this.snackBar.callSnackbaronSuccess("No Previous Tickets found");
      }
    })
  }

  selectCategory(id: number) {
    this.previousTicketDetails$ = this.helpService.getPreviousTicketDetail(id),
    this.previousTicketDetails$.subscribe(data => {
      if(data && data[0])
      this.supportDocs= data[0].attachments;

      this.fileUploadModel = [
        new DynamicFileUploadModel({
        required: false,
        id: 'attachments',
        name: 'attachments',
        label: 'Supporting Documents',
        disabled: true,
        value: this.supportDocs.map((x:any)=>{ 
          if(x.fileName && x.systemFileName)
           return x.fileName+ '|' + x.systemFileName
          return '' }),
        accept: ['.pdf', '.doc', '.docx'],
        multiple: true,
        updateOn: DynamicFormHook.Change,
        showFileList: true,
        additional: {
          appearance: 'outline'
        }
        })
      ]
      this.fileUploadFormGroup = this.dynamicFormService.createFormGroup(this.fileUploadModel);
    })
      this.currentTicket = id

   
  }


  // -------------dialog handlers?
  save() {
    this.dialogRef.close(this.form.value);
  }


  // downloadForView(attachment: number) {
  //   var url = URL.BASE_URL_SHORTER + `formmsvc/api/File/url?systemFileName=${attachment['systemFileName'].split(',')[0]}`;
  //   this.http.get(url).subscribe((response: any) => {
  //     this.viewUrl.next(response.url);
  //     const dialogConfig = new MatDialogConfig();
  //     dialogConfig.hasBackdrop = true;
  //     this.breakpointobserver
  //       .observe(['(min-width : 1024px)']).subscribe((result: { matches: any }) => {
  //         if (result.matches) {
  //           dialogConfig.minWidth = '60vw';
  //           dialogConfig.minHeight = '85vh';
  //           dialogConfig.maxWidth = '70vw';
  //           dialogConfig.maxHeight = "85vh";
  //         } else {
  //           dialogConfig.minWidth = '90vw';
  //           dialogConfig.minHeight = '90vh';
  //         }
  //       });
  //     dialogConfig.data = {
  //       viewUrl: this.viewUrl$
  //     }
  //     const dialogRef = this.dialog.open(FileViewPopupComponent, dialogConfig);
  //     dialogRef.afterClosed().subscribe((data) => {
  //       this.viewUrl.next(null);
  //     });
  //   })
  // }
}
