.my-12 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.fb-container {
    // padding-left: 0.5rem!important;
    // padding-right: 0.5rem!important;
}

.fb-ht-fixed {
    // height: 80vh;
    // overflow: auto;
    // @screen xl {
    //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.5rem);
    // }
    // @screen lg {
    //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.5rem);
    // }
    // @screen md {
    //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.5rem);
    // }
}

.fb-ht-fixed-sm {
    height: 15vh;
    overflow: auto;
}

#wMsg {
    display: none;
}

.add-icon {
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 1.7rem!important;
    cursor: pointer;
}

.editor {
    width: 92%!important;
    margin: 0.5rem;
}

.basic-card {
    margin: 1rem 1rem;
    margin-bottom: 4rem;
    max-width: 100%;
    min-height: fit-content;
    min-width: fit-content;
    border: 1px solid rgba(49, 48, 48, 0.829);
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 0.92rem;
}

.basic-card-children {
    margin: 0.5rem 0.5rem;
    width: stretch;
    height: stretch;
    min-height: fit-content;
    min-width: fit-content;
    border: none;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 0.9rem 0rem;
}

.genesis-form-feild {
    width: 27%;
    margin: 0.7rem;
}

.form-card {
    // margin-bottom: 4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    padding-top: 0.25rem;
}

.bg-red-900 {
    background: red;
}

::ng-deep .trainingValueHide{
    display: none !important;
}

.header {
    color: #7D7D7D;
    line-height: 1.1;
    font-weight: 400;
    size: 16px;
    // margin-left: -59rem;
    // margin-top: -32px;
}

.formName {
    color: var(--text-color2);
    line-height: 1.1;
    font: Roboto;
    font-weight: 700;
    size: 20px;
    // margin-right: 2px;
}

// .Section{
//     color: #A7A8AC;
//     font: Roboto;
//     font-weight: 500;
//     size: 18px;
//     background-color: #F9F9F9;
// }
.expansion {
    height: 42px !important;
    background-color: #F9F9F9;
    color: #3d3d3d;
    font: Roboto;
    font-weight: 500;
    size: 18px;
    // background-color: #F9F9F9;
    //     border: 0.23px solid lightgray; 
    //     box-shadow: none;
    // border-bottom: 0.5px darkgrey solid;
    // border-top: 0.5px darkgrey solid;
    // border-left: 0.5px darkgrey solid;
    // border-right: 0.5px darkgrey solid;  
}

.exapansionBody {
    border-top: 0.13px solid lightgray;
    border-bottom: 0.13px lightgray solid;
    // border-top: 0.23px darkgrey solid;
    border-left: 0.13px lightgray solid;
    border-right: 0.13px lightgray solid;
    box-shadow: none;
}

.formCard {
    // border: 0.23px solid lightgray;
    // border-top: 0.23px solid lightgray;
    box-shadow: none;
    padding-bottom: 0px;
}

.mat-accordion .mat-expansion-panel:not(.mat-expanded),
.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing) {
    margin-bottom: 8px;
}

.button {
    background-color: var(--text-color2);
    color: #FFFFFF;
}

.info-content {
    background: #F9F9F9;
    // border: 0.2px solid #A7A8AC;
    box-sizing: border-box;
    // box-shadow: 0px 4px 4px rgb(0 0 0 / 15%);
    border-radius: 4px;
    //margin-top: 16px;
    justify-content: normal;
    text-align: justify;
}

.text-color {
    color: #A7A8AC;
}

.t-xs {
    font-size: 0.65rem;
}

.f-medium {
    color: #11263C;
}

.t-gray {
    color: #7D7D7D;
}

.disabled {
    pointer-events: none!important;
    opacity: 0.68!important;
}

::ng-deep .Overflow-x{

    overflow-x: auto !important;
    
    text-overflow:initial !important;
    white-space:initial !important;
    
    }

    ::ng-deep .SCRECI{
        margin-bottom: 0.65rem !important;
        line-height: 1.2rem !important;
    }

    ::ng-deep .Guidance{
        color:var(--text-color2) !important
    }

    ::ng-deep .hideradiobutton{
        display: none !important;
    }
    ::ng-deep .showradiobutton{
          display:  block !important;
    }

    
