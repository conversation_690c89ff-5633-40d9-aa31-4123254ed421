import { Injectable } from "@angular/core";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { SnackbarComponent } from "./core/common-component/snackbar/snackbar.component";

@Injectable({
  providedIn: "root",
})
export class SnackbarService {
  constructor(private _snackbar: MatSnackBar) {}

  callSnackbaronSuccess(msg) {
    this.openSnackBar(msg, "success-snackbar");
  }
  callSnackbaronError(msg) {
    this.openSnackBar(msg, "error-snackbar");
  }
  callSnackbaronWarning(msg) {
    this.openSnackBar(msg, "warning-snackbar");
  }
  openSnackBar(message: string, panelClass: string) {
    this._snackbar.openFromComponent(SnackbarComponent, {
      data: message,
      panelClass: panelClass,
       duration: 60000,
    });
  }
}
