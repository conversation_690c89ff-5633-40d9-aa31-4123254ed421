import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, OnInit, Output, Renderer2 } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatSelectChange } from '@angular/material/select';
import { SystemCheckService } from '../system-check.service';

@Component({
  selector: 'exai-devices',
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.scss']
})
export class DevicesComponent implements OnInit, AfterViewInit {
  @Output() deviceSelection: EventEmitter<number> = new EventEmitter();
  videoElement: any;
  audioSelect: string = '';
  videoSelect: string = '';
  videoStream: MediaStream;
  stream: MediaStream;
  audioStream: MediaStream;
  micStream: any;
  micNodes: any;
  disableContinue: boolean = true;
  audioDevices: Array<MediaDeviceInfo> = [];
  videoDevices: Array<MediaDeviceInfo> = [];
  form: FormGroup

  constructor(private _service: SystemCheckService, private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      video: [''],
      audio: ['']
    });
  }

  ngAfterViewInit(): void {
    this.videoElement = document.querySelector("video");
    this.checkConfig();
  }

  async checkConfig() {
    this.checkCamera("new").then(async () => {
      await this.checkMicrophone("new");
    });
  }

  checkCamera(source) {
    return new Promise((resolve, reject) => {
      const videoSource = this.videoSelect;
      this._service.videoStream = this.videoSelect;
      let nav = <any>navigator;
      nav.getUserMedia =
        nav.getUserMedia ||
        nav.webkitGetUserMedia ||
        nav.mozGetUserMedia ||
        nav.msGetUserMedia;
      const constraints = {
        audio: false,
        video: { deviceId: videoSource ? { exact: videoSource } : undefined },
      };
      if (nav.getUserMedia) {
        nav.getUserMedia(
          constraints,
          (stream: MediaStream) => {
            this.videoStream = stream;
            this.gotStream(stream, "camera")
              .then((data: MediaDeviceInfo[]) => {
                const devices = data.filter((ele: MediaDeviceInfo) => ele.kind == 'videoinput');
                source == "new" ? (this.videoDevices = devices, this.form.get('video').setValue(devices[0].deviceId)) : null;
              })
              .catch((err) => {});
            this.cdr.detectChanges();
            resolve(true);
          },
          (err) => {
            this.stopStream();
            if (
              err.message == "Could not start video source" ||
              err.message == "Starting video failed"
            ) {
              this._service.errorDetails = { message: "camera-1", status: false };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            } else {
              this._service.errorDetails = { message: "camera", status: false };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            }
            reject(false);
          }
        );
        return;
      } else if (nav.mediaDevices) {
        nav.mediaDevices.getUserMedia(constraints)
          .then((stream: MediaStream) => {
            this.videoStream = stream;
            this.gotStream(stream, "camera")
              .then((data: MediaDeviceInfo[]) => {
                const devices = data.filter((ele: MediaDeviceInfo) => ele.kind == 'videoinput');
                source == "new" ? (this.videoDevices = devices, this.form.get('video').setValue(devices[0].deviceId)) : null;
              })
              .catch((err) => {});
            this.cdr.detectChanges();
            resolve(true);
          })
          .catch((err: any) => {
            this.stopStream();
            if (
              err.message == "Could not start video source" ||
              err.message == "Starting video failed"
            ) {
              this._service.errorDetails = { message: "camera-1", status: false };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            } else {
              this._service.errorDetails = { message: "camera", status: false };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            }
            reject(false);
          });
        return;
      } else {
        this._service.errorDetails = { message: "camera", status: false };
        // this.router.navigateByUrl("/error");
        reject(false);
      }
    });
  }

  checkMicrophone(source) {
    return new Promise((resolve, reject) => {
      const audioSource = this.audioSelect;
      this._service.audioStream = this.audioSelect;
      let nav = <any>navigator;
      nav.getUserMedia =
        nav.getUserMedia ||
        nav.webkitGetUserMedia ||
        nav.mozGetUserMedia ||
        nav.msGetUserMedia;
      const constraints = {
        audio: { deviceId: audioSource ? { exact: audioSource } : undefined },
        video: false,
      };
      if (nav.getUserMedia) {
        nav.getUserMedia(
          constraints,
          (stream: MediaStream) => {
            this.audioStream = stream;
            this.gotStream(stream, "microphone")
              .then((data: MediaDeviceInfo[]) => {
                this.micStream = stream;
                const devices = data.filter((ele: MediaDeviceInfo) => ele.kind == 'audioinput');
                source == "new" ? (this.audioDevices = devices, this.form.get('audio').setValue(devices[0].deviceId)) : null;
                var audioContext = new AudioContext();
                var analyser = audioContext.createAnalyser();
                var microphone = audioContext.createMediaStreamSource(stream);
                this.micNodes = audioContext.createScriptProcessor(2048, 1, 1);
                analyser.smoothingTimeConstant = 0.3;
                analyser.fftSize = 1024;
                microphone.connect(analyser);
                analyser.connect(this.micNodes);
                this.micNodes.connect(audioContext.destination);
                this.micNodes.onaudioprocess = () => {
                  var array = new Uint8Array(analyser.frequencyBinCount);
                  analyser.getByteFrequencyData(array);
                  var values = 0;
                  var length = array.length;
                  for (var i = 0; i < length; i++) {
                    values += array[i];
                  }
                  var average = values / length;
                  this.stream = stream;
                  this.colorPids(average);
                };
              })
              .catch((err) => {});
            this.cdr.detectChanges();
            resolve(true);
            this.disableContinue = false;
          },
          (err) => {
            this.stopStream();
            if (
              err.message == "Could not start audio source" ||
              err.message == "Starting audio failed"
            ) {
              this._service.errorDetails = { message: "microphone-1", status: false };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            } else {
              this._service.errorDetails = { message: "microphone", status: false, };
              this._service.errorStatus.next(3);
              // this.router.navigateByUrl("/error");
            }
            reject(false)
          }
        );
      } else if (nav.mediaDevices) {
        nav.mediaDevices.getUserMedia(constraints)
          .then((stream: MediaStream) => {
            this.audioStream = stream;
            this.gotStream(stream, "microphone")
              .then((data: any) => {
                this.micStream = stream;
                const devices = data.filter((ele: MediaDeviceInfo) => ele.kind == 'audioinput');
                source == "new" ? (this.audioDevices = devices, this.form.get('audio').setValue(devices[0].deviceId)) : null;
                var audioContext = new AudioContext();
                var analyser = audioContext.createAnalyser();
                var microphone = audioContext.createMediaStreamSource(stream);
                this.micNodes = audioContext.createScriptProcessor(2048, 1, 1);
                analyser.smoothingTimeConstant = 0.3;
                analyser.fftSize = 1024;
                microphone.connect(analyser);
                analyser.connect(this.micNodes);
                this.micNodes.connect(audioContext.destination);
                this.micNodes.onaudioprocess = () => {
                  var array = new Uint8Array(analyser.frequencyBinCount);
                  analyser.getByteFrequencyData(array);
                  var values = 0;
                  var length = array.length;
                  for (var i = 0; i < length; i++) {
                    values += array[i];
                  }
                  var average = values / length;
                  this.stream = stream;
                  this.colorPids(average);
                };
              })
              .catch((err) => {});
            this.cdr.detectChanges();
            resolve(true);
            this.disableContinue = false;
          })
          .catch((err: any) => {
            this.stopStream();
            if (
              err.message == "Could not start audio source" ||
              err.message == "Starting audio failed"
            ) {
              this._service.errorDetails = { message: "microphone-1", status: false };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            } else {
              this._service.errorDetails = { message: "microphone", status: false, };
              // this.router.navigateByUrl("/error");
              this._service.errorStatus.next(3);
            }
            reject(false)
          })
        return;
      } else {
        this._service.errorDetails = { message: "microphone", status: false, };
        // this.router.navigateByUrl("/error");
        reject(false);
      }
    });
  }

  gotStream(stream, device: string) {
    if (device == "camera") {
      this.videoElement.srcObject = stream;
    }
    return new Promise((resolve, reject) => {
      navigator.mediaDevices
        .enumerateDevices()
        .then((data) => resolve(data))
        .catch((err) => reject(err));
    });
  }

  colorPids(vol: number): any {
    let pids = $(".pid");
    let numOfPids = Math.round(vol / 10);
    let range = pids.slice(0, numOfPids);
    for (var i = 0; i < pids.length; i++) {
      pids[i].style.backgroundColor = "#e6e7e8";
    }
    for (var i = 0; i < range.length; i++) {
      range[i].style.backgroundColor = "#69ce2b";
    }
  }

  stopStream() {
    if (this.videoStream) {
      this.videoStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      this.videoElement.srcObject = null;
    }
    if (this.audioStream) {
      this.audioStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
    }
  }

  videoChange(event: MatSelectChange): void {
    this.videoSelect = event.value;
    this._service.videoStream = event.value;
    this.checkCamera("changed");
  }

  audioChange(event: MatSelectChange): void {
    this.audioSelect = event.value;
    this._service.audioStream = event.value;
    this.checkMicrophone("changed");
  }

  continue() {
    if (!this.disableContinue) {
      this.deviceSelection.emit(2);
      // this.router.navigateByUrl("/check");
    }
  }

  ngOnDestroy() {
    try {
      this.stopStream();
    } catch (e) {

    }
  }

}
