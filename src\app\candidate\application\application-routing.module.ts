import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ApplicationFilledComponent } from './application-filled/application-filled.component';
import { ApplicationComponent } from './application.component';

const routes: Routes = [
  {
    path: '',
    component: ApplicationFilledComponent,
    // canActivate: [AuthGuard],
    data: {
      title: 'Application',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Application',
          url: ''
        },
      ]
    },
  },
  {
    path: 'select-application',
    component: ApplicationComponent,
    // canActivate: [AuthGuard],
    data: {
      title: 'Application',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Application',
          url: '/application'
        },
        {
          label: 'Select Application',
          url: ''
        }
      ]
    },
  },
  {
    path: 'application-form',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    data: {
      title: 'application-form',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Application',
          url: '/application'
        },
        {
          label: 'Application Form',
          url: ''
        },
      ]
    },
  },
  
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ApplicationRoutingModule { }
