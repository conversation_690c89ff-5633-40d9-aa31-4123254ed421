<div [ngClass]="{hide1:untilPaymentProcess(0)}" class="px-gutter" exaiContainer>
    <div>
        <mat-spinner class="spinner"></mat-spinner>
        <div class="loading">
            <div class="flex justify-center test-base fontcustom">
                We're processing...
            </div>
            <div class="flex justify-center text-xs font-bold">
                This process may take a few seconds, so please be patient.
            </div>
        </div>
    </div>
</div>

<div [ngClass]="{hide:untilPaymentProcess(1) }" class="px-gutter pt-2" gdColumns="1fr " gdColumns.lt-md="1fr"
    gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="mb-2" fxLayout="column">
            <h5><strong>Payment</strong></h5>
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
                [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
    </div>

    <div class="py-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
        gdGap="12px" exaiContainer>
        <div class="justify-start touch-auto overflow-auto dashboard payment-card" gdColumn="1 / 6"
            gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
            <div class="exam-1 accord w-full" fxFlex="auto">

                <!-- <mat-expansion-panel class="mb-2 cardBorder" [expanded]="step === 6" (opened)="setStep(6)"
                      [disabled]="total == 0"> 
                      <mat-expansion-panel-header class="header">
                          <mat-panel-title class="text-xs">
                              Saved Cards
                          </mat-panel-title>
                          <mat-panel-description>
                          </mat-panel-description>
                      </mat-expansion-panel-header>
                      <div class="w-full touch-auto overflow-auto px-6" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                          gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                          gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr">
                          <div class="t-xs" gdColumn="5 / 8" gdColumn.lt-md="5 / 8" gdColumn.lt-sm="3 / 7">
                              Name on card
                          </div>
                          <div class="t-xs" gdColumn="8 / -1" gdColumn.lt-md="8 / -1" gdColumn.lt-sm="7 / -1">
                              Expires on
                          </div>
                      </div>
                      
                      <mat-accordion>
                          <mat-radio-group>
                              <mat-expansion-panel class="cardBorder" *ngFor="let card of paymentMethods let i = index"
                                  [expanded]="i==step2" (afterExpand)="setStep2(i)" (closed)="setStep2(-1)"
                                  hideToggle="true">
                                  <mat-expansion-panel-header class="header">
                                      <div class="w-full" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                          gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                          gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr">
                                          <div class="rowSpace" gdColumn="1 / 4" gdColumn.lt-md="1 / 4"
                                              gdColumn.lt-sm="1 / 6">
  
                                              <mat-radio-button [id]=i [value]="card.id" [checked]="i==step2">
                                                  <mat-panel-title class="text-xs">xxxx xxxx xxxx {{card.card_last_four}}
                                                      {{card.card_type}}
                                                  </mat-panel-title>
                                              </mat-radio-button>
                                          </div>
  
                                          <div class="t-xs" gdColumn="5 / 8" gdColumn.lt-md="5 / 8"
                                              gdColumn.lt-sm="3 / 7">
                                              <b> {{card.person_name}}</b>
                                          </div>
                                          <div class="t-xs" gdColumn="8 / -1" gdColumn.lt-md="8 / -1"
                                              gdColumn.lt-sm="7 / -1">
                                              <b>{{card.card_exp_datetime | date : "MM/yyyy"}}</b>
                                          </div>
                                      </div>
                                  </mat-expansion-panel-header>
                                  <span class="text-xs px-6">Enter CVV( ? ):</span>
                                  <form [formGroup]="cvvForm" class="pt-4 px-6">
                                      <mat-form-field class="pt-4 mt-4" appearance='outline' class="saveCardCVV">
                                          <mat-label class="text-xs">CVV</mat-label>
                                          <input matInput autocomplete="off" type="password" formControlName='cvv'
                                              pattern="[0-9]*" maxlength="3" placeholder='CVV'
                                              (keypress)="onlyNumberKey($event)"
                                              [ngClass]="{ 'is-invalid': cvvForm.get('cvv').touched && cvvForm.get('cvv').invalid }">
                                          <mat-error>
                                              <div *ngIf="cvvForm.get('cvv').touched && cvvForm.get('cvv').invalid"
                                                  class='invalid-feedback invalid-cvv'>
                                                  <div *ngIf="cvvForm.get('cvv').errors.required">CVV is Required</div>
                                              </div>
                                          </mat-error>
                                      </mat-form-field>
                                      <button mat-button class="btn-1 t-xs ml-4 mb-2"
                                          [disabled]="cvvForm.invalid || disablePaybutton" *ngIf='total > 0'
                                          (click)="payment(card,null)">Pay</button>
                                      <button mat-button class="btn-3 t-xs ml-4 mb-2"
                                          (click)="deleteCard(card)">Delete</button>
  
                                      
                                  </form>
                              </mat-expansion-panel>
                          </mat-radio-group>
                      </mat-accordion> 
              </divmat-expansion-panel> -->


                <mat-expansion-panel class="mb-2" [(expanded)]="closePanel" [disabled]="closeExapanel()">
                    <mat-expansion-panel-header class="header">
                        <mat-panel-title class="text-xs font-bold">
                            Payment
                        </mat-panel-title>
                        <mat-panel-description>

                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <hr class="-mr-6 -ml-6">
                    <div class="p-mt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px"
                        exaiContainer>
                        <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1" gdColumn.lt-sm="1">
                            <div class="text-active-color mt-2 ml-2"><strong class="text-black">Note : </strong>A
                                non-refundable 6.2% service charge will be applied to the transaction amount when using
                                PayPal. If you would prefer not to continue using PayPal, kindly select an alternative
                                payment method.
                            </div>
                            <div class="mt-2 flex">
                                <ng-container *ngFor="let paymentOption of paymentOptions; let i = index">
                                    <button type="button" class="px-4 mr-3 mb-3 pt-3 pb-3 state paymemntoptions buttom6"
                                        mat-stroked-button color="light"
                                        [ngClass]="{ active: steps == paymentOption.id }"
                                        (click)="selectrangeactive(paymentOption)">
                                        {{ paymentOption.name }}
                                        <br />
                                    </button>
                                </ng-container>
                                <div id="payPalButton"></div>
                            </div>
                            <form [formGroup]='form' *ngIf="steps === 1">
                                <div fxLayout="column" class="payment w-full container">
                                    <mat-form-field appearance='outline' class="py-1">
                                        <mat-label class="text-xs">Enter Card Number</mat-label>
                                        <input matInput formControlName='CardNumber' placeholder="cardnumber"
                                            id='cardnumber' (keypress)="onlyNumberKey($event)"
                                            [ngClass]="{ 'is-invalid': form.get('CardNumber').touched && form.get('CardNumber').invalid }"
                                            placeholder='CardNumber'>
                                        <mat-error>
                                            <div *ngIf="form.get('CardNumber').touched && form.get('CardNumber').invalid"
                                                class='invalid-feedback '>
                                                <div *ngIf="form.get('CardNumber').errors.required">Card Number is
                                                    Required
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <mat-form-field class="py-1" appearance='outline'>
                                        <mat-label class="text-xs">Enter Card Name</mat-label>
                                        <input matInput formControlName='CardName' placeholder='CardName'
                                            id='first_last_name'>
                                        <mat-error>
                                            <div *ngIf="form.get('CardName').touched && form.get('CardName').invalid"
                                                class='invalid-feedback'
                                                [ngClass]="{ 'is-invalid': form.get('CardName').touched && form.get('CardName').invalid }">
                                                <div *ngIf="form.get('CardName').errors.required">Card Name is Required
                                                </div>
                                                <div *ngIf="form.get('CardName').errors.pattern">ImProper Name
                                                </div>
                                                <div *ngIf="form.get('CardName').errors.ValidateLastName">Name is less
                                                    than 3 letters
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <div class="py-1" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px"
                                        fxLayoutGap.lt-sm="0">
                                        <mat-form-field appearance='outline' fxFlex="auto">
                                            <mat-label class="text-xs">YYYY/MM</mat-label>
                                            <input matInput formControlName='Year' name='expirationdate'
                                                autocomplete="off" id='expirationdate'
                                                (keypress)="onlyNumberKey($event)"
                                                [ngClass]="{ 'is-invalid': form.get('Year').touched && form.get('Year').invalid }"
                                                placeholder='YYYY/MM'>
                                            <mat-error>
                                                <div *ngIf="form.get('Year').touched && form.get('Year').invalid"
                                                    class='invalid-feedback'>
                                                    <div *ngIf="form.get('Year').errors.required">Year is Required</div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                        <mat-form-field appearance='outline' class="phone" fxFlex="auto">
                                            <mat-label class="text-xs">CVV</mat-label>
                                            <input matInput autocomplete="off" name='securitycode' id='securitycode'
                                                type="password" formControlName='CVV' placeholder='CVV'
                                                (keypress)="onlyNumberKey($event)"
                                                [ngClass]="{ 'is-invalid': form.get('CVV').touched && form.get('CVV').invalid }">
                                            <mat-error>
                                                <div *ngIf="form.get('CVV').touched && form.get('CVV').invalid"
                                                    class='invalid-feedback'>
                                                    <div *ngIf="form.get('CVV').errors.required">CVV is Required</div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </form>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="mb-2 container"
                                *ngIf="steps === 1">
                                <button mat-button class="buuton1 add text-xs" type="button"
                                    (click)='cancel()'>Cancel</button>
                                <button class="buuton2 text-xs" mat-raised-button type="button"
                                    (click)='payment(null,null)'
                                    [disabled]='!form.valid || disablePaybutton'>Pay</button>
                            </div>

                            <!-- <div  class="shadow-none iframese container  justify-start " *ngIf="steps === 2">
                                  <iframe #myIframe  [src]="urlSafe"  class="w-full" style="height:90%;"></iframe>
                              </div> -->

                        </div>
                    </div>
                </mat-expansion-panel>
                <!-- <mat-ex-panel *ngIf="isPracticeSkillFlow" class="mb-2" [(expanded)]="closePanel" [disabled]="closeExapanel()">
                    <mat-expansion-panel-header class="header">
                        <mat-panel-title class="text-xs font-bold">
                            Payment
                        </mat-panel-title>
                        <mat-panel-description>
                
                        </mat-panel-description>
                   </mat-expansion-panel-header>
                    <hr class="-mr-6 -ml-6">
                    <div fxLayout="row" fxLayoutGap="24px">
                        <div fxFlex="100%">
                            <mat-tab-group mat-stretch-tabs dynamicHeight [(selectedIndex)]="selectedTabIndex">
                                <mat-tab label="Debit/Credit Card">
                                    <div class="p-4">
                                        
                                        <mat-radio-group [(ngModel)]="selectedCard1" class="flex flex-col gap-4">
                                        <div *ngFor="let card of cards" class="card-option-custom rounded p-4">
                                            <label class="flex justify-between items-start gap-4 w-full cursor-pointer">
                                            <div class="flex gap-4 items-center">
                                                <mat-radio-button [value]="card" class="mt-1"></mat-radio-button>
                                                <img [src]="card.logoUrl" alt="Card logo" class="w-10 h-6 object-contain" />
                                                <div>
                                                <div class="flex items-center gap-2">
                                                    <span class="font-medium">Personal Card</span>
                                                    <span *ngIf="card.isPrimary" class="primary_card text-xs px-2 py-0.5 rounded">Primary</span>
                                                </div>
                                                <div class="text-xs">{{ card.bankName }} ••••{{ card.last4 }}</div>
                                                </div>
                                            </div>

                                            <div class="flex items-center gap-1">
                                                <mat-icon color="primary" class="mt-1">verified</mat-icon>
                                                <span class="text-xs">Verified</span>
                                            </div>
                                            </label>

                                            <div *ngIf="selectedCard1 === card" class="mt-4 cvv-container rounded p-4">
                                            <div class="flex gap-4 items-start">
                                                <mat-icon class="infoicon">info</mat-icon>
                                                <div>
                                                <div class="font-semibold text-sm mb-1">Security Verification Required</div>
                                                <p class="text-xs mb-4">
                                                    Please enter the CVV code for your {{ card.type }} card to continue
                                                </p>

                                                <div class="flex gap-6">
                                                    <div>
                                                    <label class="block text-sm font-medium">CVV Code</label>
                                                    <input type="password" maxlength="4"
                                                            class="mt-1 block w-20 px-2 py-1 primary_card rounded text-center"
                                                            placeholder="•••" />
                                                    </div>

                                                    <div class="mt-2 ml-2 p-2">
                                                    <label class="block text-sm ">Where to find CVV?</label>
                                                    <p class="text-xs">3–4 digit code on the back of your card</p>
                                                    </div>
                                                </div>
                                                </div>
                                            </div>
                                            </div>
                                        </div>
                                        </mat-radio-group>
                                        <div class="mt-4">
                                            <a href="#" class="add-new">+ Add New</a>
                                        </div>
                                    </div>
                                </mat-tab>
                                <mat-tab label="PayPal">
                                    <div class="m-6">
                                    <div #paypalButton id="payPalButton"></div>
                                    </div>
                                </mat-tab>
                                <mat-tab label="Online Banking">
                                    <div class="m-6">Online banking instructions...</div>
                                </mat-tab>
                                <mat-tab label="Affirm">
                                    <div class="m-6">Affirm info...</div>
                                </mat-tab>
                                <mat-tab label="ACH">
                                    <div class="m-6">ACH bank transfer fields...</div>
                                </mat-tab>
                            </mat-tab-group>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="10px" class="mb-2 p-2">
                            <button mat-stroked-button color="warn" (click)="cancel()">Cancel</button>
                            <button mat-raised-button color="primary" (click)="makePayment()">Make Payment</button>
                            </div>
                        </div>
                    </div>
                  </mat-expansion-panel>   -->

                <!-- <mat-expansion-panel class="mb-2" [expanded]="step === 3" (opened)="setStep(3)" [disabled]='total==0'>
                      <mat-expansion-panel-header class="header">
                          <mat-panel-title class="text-xs">
                              ACH
                          </mat-panel-title>
                          <mat-panel-description>
  
                          </mat-panel-description>
                      </mat-expansion-panel-header>
                      <hr class="-mr-6 -ml-6">
                      <div class="px-6 p-mt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px"
                          exaiContainer>
                          <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1" gdColumn.lt-sm="1">
                              <div class="h2 mb-2 pt-2 active text-xs">ACH</div>
                              <form [formGroup]='ACHform'>
                                  <div>
                                  </div>
                                  <div fxLayout="column" class="payment w-full">
                                      <mat-form-field appearance='outline' class="py-1">
                                          <mat-label class="text-xs">Enter Account Number</mat-label>
                                          <input matInput formControlName='AccountNumber' name="Accountnumber"
                                              id='Accountnumber' (keypress)="onlyNumberKey($event)"
                                              [ngClass]="{ 'is-invalid': ACHform.get('AccountNumber').touched && ACHform.get('AccountNumber').invalid }"
                                              placeholder='AccountNumber'>
                                          <mat-error>
                                              <div *ngIf="ACHform.get('AccountNumber').touched && ACHform.get('AccountNumber').invalid"
                                                  class='invalid-feedback '>
                                                  <div *ngIf="ACHform.get('AccountNumber').errors.required">Account Number
                                                      is
                                                      Required
                                                  </div>
                                              </div>
                                          </mat-error>
                                      </mat-form-field>
                                      <mat-form-field class="py-1" appearance='outline'>
                                          <mat-label class="text-xs">Enter AccountHolder Name</mat-label>
                                          <input matInput formControlName='AccountHolderName'
                                              placeholder='AccountHolderName'
                                              pattern="^[a-zA-Z]*\s[a-zA-Z]*\s?([a-zA-Z]*)?\s?([a-zA-Z]*)?"
                                              id='first_last_name'>
                                          <mat-error>
                                              <div *ngIf="ACHform.get('AccountHolderName').touched && ACHform.get('AccountHolderName').invalid"
                                                  class='invalid-feedback'
                                                  [ngClass]="{ 'is-invalid': ACHform.get('AccountHolderName').touched && ACHform.get('AccountHolderName').invalid }">
                                                  <div *ngIf="ACHform.get('AccountHolderName').errors.required">
                                                      AccountHolder
                                                      Name is Required
                                                  </div>
                                                  <div *ngIf="ACHform.get('AccountHolderName').errors.pattern">improper
                                                      name
                                                  </div>
                                                  <div *ngIf="ACHform.get('AccountHolderName').errors.ValidateLastName">
                                                      Name
                                                      is less
                                                      than 3 letters
                                                  </div>
                                              </div>
                                          </mat-error>
                                      </mat-form-field>
                                      <div class="block-1 py-1" fxLayout.lt-sm="column" fxLayoutGap.lt-sm="0"
                                          fxLayoutGap="16px">
                                          <mat-form-field appearance='outline' class="form-bank">
                                              <mat-label class="text-xs">Enter Routing Number</mat-label>
                                              <input matInput formControlName='RoutingNumber' name="Routingnumber"
                                                  id='Routingnumber' (keypress)="onlyNumberKey($event)"
                                                  [ngClass]="{ 'is-invalid': ACHform.get('RoutingNumber').touched && ACHform.get('RoutingNumber').invalid }"
                                                  placeholder='RoutingNumber'>
                                              <mat-error>
                                                  <div *ngIf="ACHform.get('RoutingNumber').touched && ACHform.get('RoutingNumber').invalid"
                                                      class='invalid-feedback '>
                                                      <div *ngIf="ACHform.get('RoutingNumber').errors.required">Routing
                                                          Number is
                                                          Required
                                                      </div>
                                                  </div>
                                              </mat-error>
                                          </mat-form-field>
                                          <mat-form-field appearance="outline" class="form-bank">
                                              <mat-label>Bank Type</mat-label>
                                              <mat-select formControlName="bankType">
                                                  <mat-option *ngFor="let state of bankType" [value]="state">{{state}}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>
                                          <mat-form-field appearance="outline" class="form-bank">
                                              <mat-label>Bank Holder Type</mat-label>
                                              <mat-select formControlName="bankHoldeType">
                                                  <mat-option *ngFor="let state of bank_holder_type" [value]="state">
                                                      {{state}}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>
                                      </div>
                                  </div>
                              </form>
                              <div class="mb-2" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                                  <button mat-button class="buuton1 add text-xs" type="button"
                                      (click)='cancel()'>Cancel</button>
                                  <button class="buuton2 text-xs" mat-raised-button type="button"
                                      (click)='payment(null,"bank")' [disabled]='!ACHform.valid'>Pay</button>
                              </div>
                          </div>
                      </div>
                  </mat-expansion-panel> -->
            </div>
        </div>
        <div class="justify-start dashboard registerCard" gdColumn="6 / -1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
            <div class="card cardBorder shadow-none  exam overflow-scroll" fxFlex="auto">
                <div class="flex p-3">
                    <div>Payment Summary</div>
                    <div>
                        <div class="ml-auto">
                            <button class="buuton2" mat-button type="button"
                                *ngIf="registeredExams?.length<2 && listExam?.length < 2" (click)="addExam()">
                                Add Another Exam
                            </button>
                        </div>
                    </div>
                </div>

                <div class="content2">

                    <div class="mb-1 content1 px-4" exaiContainer>
                        <div class="" fxLayoutGap="6px grid">
                            <div fxLayout="row wrap" fxLayoutGap="10px grid">
                                <div [fxFlex]="(100/1) + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                                    *ngFor="let item of listExam; let i = index">
                                    <div class="card shadow-none cardBorder mb-2" fxFlex="auto">
                                        <div class="bg-color -pt-2">
                                            <div class="flex justify-between px-4 pt-2" fxLayout="row">
                                                <div class="" *ngIf="item.examCode !='NA-PAR'">
                                                    <h5 class="-ml-1 t-xs font "><strong>{{item.examName}}</strong></h5>
                                                </div>
                                                <div class="" *ngIf="item.examCode =='NA-PAR'">
                                                    <h5 class="-ml-1 t-xs font "><strong>{{item.skillTitle}}</strong>
                                                    </h5>
                                                </div>
                                                <div class="flex text-left text-base pb-2">
                                                    <span class="text-xs">${{item.amount}} </span>
                                                    <mat-icon *ngIf="item.examCode !='NA-PAR'" class="-mr-3 ml-2 delete"
                                                        (click)="deleteItem(item.personEventCartId, i,'Exams')">delete
                                                    </mat-icon>
                                                    <mat-icon *ngIf="item.examCode =='NA-PAR' && Allowedit" class="-mr-3 ml-2 delete"
                                                        (click)="editSkill()">edit
                                                    </mat-icon>
                                                    <mat-icon *ngIf="item.examCode =='NA-PAR' && !Allowedit" class="-mr-3 ml-2 delete"
                                                    (click)="cancelSkill()">cancel
                                                </mat-icon>
                                                    <mat-icon *ngIf="item.examCode =='NA-PAR'" class="-mr-3 ml-2 delete"
                                                        (click)="deleteSkill(item)">delete
                                                    </mat-icon>
                                                </div>

                                           



                                            </div>
                                            <div fxLayout="row" class="px-3 -mt-2 "
                                                *ngIf="item.examName!='Renewal Fee' && item.examName!='Reciprocity Fee'&& item.examName!='Medication Assistant Reciprocity Fee' && item.examName!='Medication Assistant Renewal Fee'&& item.cartItemTypeId !='7' && item.cartItemTypeId !='9' && item.examCode !='NA-PAR' ">
                                                <h6 class="t-xs mb-1 status1 ">{{item.eligibilityRouteName}}</h6>
                                            </div>
                                            <div class="px-3 -mt-2 "
                                                *ngIf="item.examName!='Renewal Fee' && item.examName!='Reciprocity Fee'&& item.examName!='Medication Assistant Reciprocity Fee' && item.examName!='Medication Assistant Renewal Fee'&& item.cartItemTypeId !='7' && item.cartItemTypeId !='9' && item.examCode =='NA-PAR' ">
                                                <p class="text-xs mb-1  font">{{item.skillDescription}}</p>
                                                <div class="flex">
                                                    <div class="w-1/5">
                                                        <div class="meta-label">Duration</div>
                                                        <div class="meta-value ml-2">{{ item.skillDuration }}</div>
                                                    </div>
                                                    <div class="w-1/5">
                                                        <div class="meta-label">Steps</div>
                                                        <div class="meta-value ml-2">{{ item ?.skillSteps }}</div>
                                                    </div>
                                                    <div class="w-1/5">
                                                        <div class="meta-label">Attempts</div>
                                                        <div class="meta-value ml-2 ">{{ item?.skillTotalAttempt }}
                                                        </div>
                                                    </div>
                                                    <div class="w-1/5">
                                                        <div class="meta-label">Validity</div>
                                                        <div class="meta-value ml-2">{{ item?.validity }}</div>
                                                    </div>
                                                    <div class="w-1/5">
                                                        <div class="meta-label">Price</div>
                                                        <div class="meta-value ml-2">{{ item.amount }}</div>
                                                    </div>
                                                </div>
                                                <div *ngIf="!Allowedit" class="mt-2 flex items-center gap-3 text-xs mb-3">
                                                    <span class="mt-1 px-2">Additional Attempts</span>
                                                    <button  class="qty-btn" (click)="decreaseAttempts()">-</button>
                                                    <span class="w-6 text-center mt-1">{{ item.skillTotalAttempt
                                                        }}</span>
                                                    <button  class="qty-btn px-2" (click)="increaseAttempts()">+</button>
                                                    <span class="mt-1 px-2">
                                                        x ${{item.additionalAttemptsPrice}} <span
                                                            class="italic per_attempt">per
                                                            attempt</span>
                                                    </span>
                                                </div>


                                            </div>
                                        </div>
                                        <div fxLayout="column"
                                            *ngIf="item.examName!='Renewal Fee' && item.examName!='Reciprocity Fee'&& item.examName!='Medication Assistant Reciprocity Fee' && item.examName!='Medication Assistant Renewal Fee'&& item.cartItemTypeId !='7' && item.cartItemTypeId !='9' && item.examCode !='NA-PAR' ">
                                            <div class="pt-2 mb-2 px-3"
                                                gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                                gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                                exaiContainer>
                                                <div gdColumn="1/4" gdColumn.lt-md="1/4" gdColumn.lt-sm="1/4">
                                                    <div *ngIf="item.testCenterName==null" class="h4 status t-xs">
                                                        ExamMode</div>
                                                    <div *ngIf="item.testCenterName!=null" class="h4 status t-xs">Test
                                                        Center Name</div>
                                                </div>
                                                <div gdColumn="5/8" gdColumn.lt-md="5/8" gdColumn.lt-sm="5/8">
                                                    <div class="h4  status t-xs">Exam Date</div>
                                                </div>
                                                <div gdColumn="8/-1" gdColumn.lt-md="8/-1" gdColumn.lt-sm="8/-1">
                                                    <div class="h4 status t-xs">Exam Time</div>
                                                </div>
                                            </div>
                                            <div class="px-3 mb-2"
                                                gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                                gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                                exaiContainer>
                                                <div gdColumn="1/4" gdColumn.lt-md="1/4" gdColumn.lt-sm="1/4">
                                                    <div *ngIf="item.testCenterName==null" class="h4 status1 t-xs">
                                                        {{item.examMode}}</div>
                                                    <div *ngIf="item.testCenterName!=null" class="h4 status1 t-xs">
                                                        {{item.testCenterName}}</div>
                                                    <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->
                                                </div>
                                                <div gdColumn="5/8" gdColumn.lt-md="5/8" gdColumn.lt-sm="5/8">
                                                    <!-- <div class="h4 status1 t-xs">{{item.eventDate | date}}</div> -->
                                                    <div class="h4 status1 t-xs">{{ item.eventDate |date:
                                                        "MM/dd/yyyy":'+0000' }}</div>
                                                </div>
                                                <div gdColumn="8/-1" gdColumn.lt-md="8/-1" gdColumn.lt-sm="8/-1">
                                                    <!-- <div class="h4  status1 ml-2 t-xs">{{item.eventDate | date:"HH:mm"}} -->
                                                    <div class="h4  status1  t-xs">{{item.eventDate
                                                        |date:'shortTime':'+0000'}} {{item.timeZoneAbbreviation}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div fxLayout="column" *ngFor="let vocher of VocherList">
                                            <div class="px-3 mb-2" gdColumns="1fr 1fr 1fr "
                                                gdColumns.lt-md="1fr 1fr 1fr  " gdColumns.lt-sm="1fr 1fr 1fr "
                                                exaiContainer>
                                                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3"
                                                    *ngIf="vocher.examTypeId?.length <=1 && itemCode.includes(vocher.itemCode)">
                                                    <div *ngFor="let vocher1 of vocher.examTypeId">

                                                        <ng-container *ngFor="let voucherlist of ExamCombined ">
                                                            <div *ngIf="item.examTypeId  == vocher1 && item.voucherAmount == null && voucherlist.ItemCode === vocher.itemCode && voucherlist.ExamName === item.examName || item.examTypeId  == vocher1 && (item.voucherCode == null) && voucherlist.ItemCode === vocher.itemCode && voucherlist.ExamName === item.examName"
                                                                class="h4 status1 mt-2 t-xs">
                                                                {{vocher.voucherCode}}
                                                            </div>
                                                            <div *ngIf="item.examTypeId == vocher1 &&  voucherlist.itemCode == vocher.itemCode && item.voucherAmount != null && item.examTypeId  == vocher1 && (item.voucherCode == vocher.voucherCode)"
                                                                class=" Apply h4 status1 mt-2 t-xs">
                                                                {{examName}}<mat-icon>
                                                                    done
                                                                </mat-icon>
                                                            </div>
                                                        </ng-container>

                                                    </div>

                                                </div>

                                                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3"
                                                    *ngIf="vocher.examTypeId?.length <=1 && !itemCode.includes(vocher.itemCode)">
                                                    <div *ngFor="let vocher1 of vocher.examTypeId">

                                                        <div *ngIf="item.examTypeId  == vocher1 && item.voucherAmount == null || item.examTypeId  == vocher1 && (item.voucherCode == null)"
                                                            class="h4 status1 mt-2 t-xs">
                                                            {{vocher.voucherCode}}
                                                        </div>
                                                        <div *ngIf="item.examTypeId == vocher1  && item.voucherAmount != null && item.examTypeId  == vocher1 && (item.voucherCode == vocher.voucherCode)"
                                                            class=" Apply h4 status1 mt-2 t-xs">{{examName}}<mat-icon>
                                                                done
                                                            </mat-icon>
                                                        </div>

                                                    </div>

                                                </div>

                                                <div gdColumn="3/-1" gdColumn.lt-md="3/-1" gdColumn.lt-sm="3/-1"
                                                    *ngIf="vocher.examTypeId.length <=1 &&  !itemCode.includes(vocher.itemCode)">
                                                    <div *ngFor="let vocher1 of vocher.examTypeId">
                                                        <button
                                                            *ngIf="item.examTypeId  == vocher1 && item.voucherAmount == null  || item.examTypeId  == vocher1 && (item.voucherCode == null)"
                                                            mat-button color="var(--text-color2)"
                                                            class="btn-4 font-bold t-xs"
                                                            (click)="VocherApplyCode(vocher,i)">
                                                            Apply Code
                                                        </button>


                                                    </div>

                                                    <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->

                                                </div>
                                                <div gdColumn="3/-1" gdColumn.lt-md="3/-1" gdColumn.lt-sm="3/-1"
                                                    *ngIf="vocher.examTypeId.length <=1 &&  itemCode.includes(vocher.itemCode)">
                                                    <div *ngFor="let vocher1 of vocher.examTypeId">
                                                        <ng-container *ngFor="let voucherlist of ExamCombined ">
                                                            <button
                                                                *ngIf="item.examTypeId  == vocher1 && item.voucherAmount == null && voucherlist.ItemCode === vocher.itemCode && voucherlist.ExamName === item.examName || item.examTypeId  == vocher1 && (item.voucherCode == null) && voucherlist.ItemCode === vocher.itemCode && voucherlist.ExamName === item.examName"
                                                                mat-button color="var(--text-color2)"
                                                                class="btn-4 font-bold t-xs"
                                                                (click)="VocherApplyCode(vocher,i)">
                                                                Apply Code
                                                            </button>
                                                        </ng-container>

                                                    </div>

                                                    <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>


                    <div gdColumn="1/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="1" class=" inline">
                        <form [formGroup]='Validator'>
                            <mat-form-field fxFlex="auto" appearance='outline' class="px-4 promo-code">
                                <!-- <mat-label class="text-xs">Enter the promo code</mat-label> -->
                                <input matInput required formControlName='code' placeholder="Enter the promo code"
                                    class="text-xs " FormControlName='code'>
                                <button class="-mt-2 button text-xs float-right " mat-button type="button"
                                    (click)='SponsorVocherCode()' [disabled]='!Validator.valid'>Apply Code</button>
                                <mat-error>
                                </mat-error>
                            </mat-form-field>
                        </form>
                    </div>

                    <div fxLayout="column" *ngFor="let vocher1 of VocherList;">
                        <div class="px-4 mb-2" gdColumns="1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr  "
                            gdColumns.lt-sm="1fr 1fr 1fr " exaiContainer>
                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3"
                                *ngIf="vocher1.examTypeId.length >= 2 && total > 0">
                                <div class="h4 status1 mt-2 t-xs">
                                    {{vocher1.voucherCode}}
                                </div>

                            </div>


                            <div class="flex justify-end" gdColumn="3/-1" gdColumn.lt-md="3/-1" gdColumn.lt-sm="3/-1"
                                *ngIf="vocher1.examTypeId.length >= 2 && total > 0 ">
                                <div>
                                    <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                                        (click)="VocherCode(vocher1.voucherCode)">
                                        Apply Code
                                    </button>

                                </div>

                                <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->

                            </div>
                        </div>
                    </div>
                    <div class="p-3">
                        Disclaimer: Please note that your exam slot is not reserved until
                        payment is processed and you receive a confirmation email.
                    </div>
                </div>

                <div *ngIf="listExam.length > 0">
                    <hr class='ml-5 mr-5 status1'>
                    <div class="px-2 pt-3 mb-3" gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
                        exaiContainer>
                        <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                            <div class="h4 ml-6 mb-2 total font-bold  text-xs">
                                Total
                            </div>
                        </div>
                        <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                            <div class="h4 text-right mr-6 mb-2 total font-bold text-xs">
                                ${{ExamTotal}}
                            </div>

                        </div>
                    </div>

                    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                        <button *ngIf='ExamTotal == 0 && cart?.length > 0 && voucherId >0 && voucherId !== 0'
                            class="buuton2 mr-4 text-xs" mat-raised-button type="button" (click)='schedule()'>Schedule
                            Now</button>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                        <button *ngIf='ExamTotal == 0 && cart?.length > 0 && voucherId <= 0 '
                            class="buuton2 mr-4 text-xs" mat-raised-button type="button"
                            (click)='schedule()'>Submit</button>
                    </div>
                </div>

                <div *ngIf="practiceSkill.length > 0" class="p-4 border rounded-md shadow-md">
                    <!-- <div class="text-xs flex justify-between px-2 py-1">
                        <span>Skill Price</span>
                        <span>+${{ skillPrice }}</span>
                    </div>

                    <div class="text-xs flex justify-between px-2 py-1">
                        <span>Additional Attempts ({{ count - 1 }})</span>
                        <span>+${{ additionalAttemptCharge }}</span>
                    </div>

                    <div class="text-xs flex justify-between px-2 py-1">
                        <span>Tax</span>
                        <span>${{ tax1 }}</span>
                    </div>

                    <hr class="mx-2 py-1" /> -->

                    <div class="flex justify-between font-bold text-sm px-2">

                        <span>Grand Total</span>
                        <span>${{ grandPracticeSkillTotal }}</span>
                    </div>
                </div>
                <div *ngIf="isPracticeBundleFlow && !isPracticeSkillFlow" class="p-4 border rounded-md shadow-md">
                    <div class="text-xs flex justify-between px-2 py-1">
                        <span>Bundle Price</span>
                        <span>+{{ practiceBundle?.price }}</span>
                    </div>

                    <div class="text-xs flex justify-between px-2 py-1">
                        <span>Additional Attempts ({{ count - 1 }})</span>
                        <span>+{{ grandTotal}}</span>
                    </div>

                    <div class="text-xs flex justify-between px-2 py-1">
                        <span>Tax</span>
                        <span>${{ tax }}</span>
                    </div>

                    <hr class="mx-2 py-1" />

                    <div class="flex justify-between font-bold text-sm px-2">
                        <span>Grand Total</span>
                        <span>${{ grandTotal }}</span>
                    </div>
                </div>

           
            </div>
        </div>
    </div>
</div>