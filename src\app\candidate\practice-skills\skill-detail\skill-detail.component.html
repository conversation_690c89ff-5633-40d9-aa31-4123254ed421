<div *ngIf="skill$ | async as skillData" class="bg-white">
    <div class="for_border px-gutter pt-2 pb-2 bg-white" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
        exaiContainer>
        <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
            <div class="pt-2 titleFont" fxLayout="column">
                <h5><strong>{{ this.lngSrvc.curLangObj.value.practice_skills_view }}</strong></h5>
                <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
                    [fontSize]="'0.65rem'">
                </app-ng-dynamic-breadcrumb>
            </div>
        </div>

    </div>
    <div class="px-gutter pt-2 pb-2 bg-white" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
        <div>
            <h2 class="skill_title text-sm mt-2 font-semibold">{{ skillData?.skillTitle }}</h2>
            <p class="bg_color p-2 leading-relaxed tracking-wide text-justify">{{ skillData?.description }}

            </p>

            <div class="mt-4">
                <h2 class="text-sm font-semibold mt-4">General Information</h2>

                <div class="info-grid p-2">
                    <label>Skill ID</label>
                    <div>: {{ skillData?.practiceSkillId }}</div>

                    <label>Skill Name</label>
                    <div>: {{skillData?.skillTitle}} </div>

                    <label>Default Attempts</label>
                    <div>: {{ skillData?.defaultAttempt }}</div>

                    <label>Program</label>
                    <div>: {{skillData?.programName}} </div>

                    <!-- <label>Created Date</label>
                    <div>: Feb 20, 2024 | 12:25</div> -->

                    <label> Expiry date</label>
                    <div>: {{skillData?.validity}} days (after purchase)</div>
                </div>
            </div>

            <div class="mt-6">
                <h3 class="text-sm font-semibold mb-2">Video Tutorial</h3>
                <video width="20%" class="p-2" controls>
                    <source [src]="skillData?.tutorialURL" type="video/mp4" />
                    Your browser does not support video playback.
                </video>
            </div>
            <div class="simpliy-height">
            </div>
        </div>
    </div>
</div>