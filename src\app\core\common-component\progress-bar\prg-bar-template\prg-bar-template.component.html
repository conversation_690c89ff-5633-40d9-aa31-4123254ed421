<exai-progress-bar *ngIf="!isTwo" [performlogs]="performlogs" [header]="header"></exai-progress-bar>
<div *ngIf="isTwo" class="progressbar-template">
    <mat-accordion *ngFor="let performlogs of performlogs">
        <mat-expansion-panel hideToggle>
            <mat-expansion-panel-header>
                <mat-panel-title class="text-xs font-semibold">
                    {{performlogs.formType}}
                </mat-panel-title>
            </mat-expansion-panel-header>
            <exai-progress-bar [performlogs]="performlogs.logs"></exai-progress-bar>
        </mat-expansion-panel>
    </mat-accordion>
</div>