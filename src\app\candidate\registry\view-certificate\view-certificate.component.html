<div class="px-gutter pt-2" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
        <div class="" fxLayout="column">
            <h5><strong> View Registration</strong></h5>
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
    </div>

    <div class="flex justify-end" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
        <div fxLayout="auto" class="items-center">
            <button mat-button class=" add btn-4 font-bold t-xs mr-2" *ngIf="certificateLink !='' " (click)="download()">
                <mat-icon class="icon_size">cloud_download</mat-icon>
                Download
            </button>

            <!-- <button mat-button [routerLink]="['../renewal-form']"
                class=" add .add-new btn-4 font-bold t-xs">Registration renewal</button> -->

            <!-- <button class="font-bold t-xs btn-5"> -->
            <!-- <mat-icon class="icon_size">more_vert</mat-icon> -->
            <!-- </button> -->
            <!-- <mat-menu class="px-2 btn-5"  xPosition="before"> -->
            <button class="add" (click)="getDuplicateForm()" *ngIf="StatusBoolean">
                    <span class="flex menu_color">
                        Duplicate Registration
                    </span>
                </button>
            <!-- </mat-menu> -->
        </div>
    </div>

</div>
<div class="py-2 w-full h-full" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
    <div class="justify-start touch-auto overflow-auto dashboard px-4" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="card shadow-none cardBorder p-4 overflow-y-auto" fxFlex="auto" *ngIf="certificateLink!=''">

            <div id="fullDiv" *ngIf="certificateLink!='' ">
                <div id="fullDiv" [innerHtml]="certificateLink | safeHmtl"></div>
            </div>

            <!-- certificate -->

            <!-- <div>

                <div class="" gdColumns="1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr "
                    exaiContainer>
                    <div gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
                        <img src="assets/img/Icons/PA_State_Seal.jpg" class="logo1">
                    </div>
                    <div class="flex items-center justify-center certificate-header" gdColumn="2 / 3"
                        gdColumn.lt-md="2 / 3" gdColumn.lt-sm="2 / 3">

                        COMMONWEALTH OF PENNSYLVANIA<br />
                        DEPARTMENT OF HEALTH <br />
                        NOTICE OF ENROLLMENT<br />

                    </div>
                    <div gdColumn="3 / -1" gdColumn.lt-md="3 / -1" gdColumn.lt-sm="3 / -1">
                        <img src="assets/img/Icons/PA_DOH_logo.png" class="logo2">
                    </div>
                </div>

                <div class="flex" gdColumns="1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr "
                    exaiContainer>
                    <div class="flex items-center justify-center text-2xl font-medium" gdColumn="2 / 3"
                        gdColumn.lt-md="2 / 3" gdColumn.lt-sm="2 / 3">
                        CRYSTAL N TESTRUTT
                    </div>
                </div>

                <div class="" gdColumns="1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr "
                    exaiContainer>

                    <div class="flex items-center justify-center content pt-4" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
                        gdColumn.lt-sm="2 / 3">

                        Has successfully completed a<br />
                        NURSE AIDE TRAINING AND <br />
                        COMPETENCY EVALUATION PROGRAM<br />
                        OR A COMPETENCY EVALUATION PROGRAM<br />

                    </div>
                </div>

                <div class="" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr"
                    gdColumns.lt-sm="1fr 1fr 1fr 1fr" exaiContainer>
                    <div class="flex items-center justify-center content pt-6" gdColumn="2 / 4" gdColumn.lt-md="2 / 4"
                        gdColumn.lt-sm="2 / 4">

                        And has been enrolled in the Pennsylvania Department of Health<br />
                        Division of Nursing Care Facilities <br />
                        NURSE AIDE REGISTRY<br />

                    </div>
                </div>

                <div class="" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr"
                    gdColumns.lt-sm="1fr 1fr 1fr 1fr" exaiContainer>
                    <div class="flex items-center justify-center content pt-6" gdColumn="2 / 4" gdColumn.lt-md="2 / 4"
                        gdColumn.lt-sm="2 / 4" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr">

                        <div class="flex items-center justify-center" fxLayout="column" gdColumn="1 / 2"
                            gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
                            Registration <br /> Number
                            <span class="flex pt-2">
                                20046213
                            </span>
                        </div>
                        <div class="flex items-center justify-center" fxLayout="column" gdColumn="2 / 3"
                            gdColumn.lt-md="2 / 3" gdColumn.lt-sm="2 / 3">
                            Enrollment <br /> Date
                            <span class="flex pt-2">
                                9/10/2021
                            </span>
                        </div>
                        <div class="flex items-center justify-center" fxLayout="column" gdColumn="3 / 4"
                            gdColumn.lt-md="3 / 4" gdColumn.lt-sm="3 / 4">
                            Expiration <br /> Date
                            <span class="flex pt-2">
                                9/10/2023
                            </span>
                        </div>
                        <div class="flex items-center justify-center" fxLayout="column" gdColumn="4 / -1"
                            gdColumn.lt-md="4 / -1" gdColumn.lt-sm="4 / -1">
                            Date of <br /> Birth
                            <span class="flex pt-2">
                                10/20/1983
                            </span>
                        </div>
                    </div>
                </div>

                <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr"
                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr" exaiContainer>
                    <div class="flex pt-6" fxLayout="column" gdColumn="2 / 4" gdColumn.lt-md="2 / 4"
                        gdColumn.lt-sm="2 / 4">

                        <img src="assets/img/Icons/signature.PNG" alt="Signature" class="logo">
                        <hr class="horzl">
                        DIRECTOR, DIVISION OF NURSING CARE FACILITIES<br />

                    </div>
                </div>

            </div> -->

            <!-- certificate -->

        </div>
    </div>
</div>