import { ExamModel } from "src/app/core/Dto/exam.model";
import { RegisteredExamsModel } from "src/app/core/Dto/registered-exams.model";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { VoucherModel } from "src/app/core/Dto/VoucherModel";
import { ActiveForm } from "./models/activeForm";
import { Cart } from "./models/cart";
import { CartItem } from "./models/cartItem";
import { chargeResponse } from "./models/charge";
import { customerId } from "./models/customerId";
import { Exam, PersonForm } from "./models/Exam";
import { MakePaymentResponse, VocherResponse } from "./models/makePayment";
import { MonthlySlot } from "./models/monthlySlot";
import {
  CreatePaymentCustomerIdResponse,
  CreatePaymentMethodRespnse,
  paymentMethod,
} from "./models/paymentMethods";
import { slot, Slot } from "./models/slot";
import { Timezone } from "./models/timezone.model";
import { VocherUpdateResponse } from "./models/vocher";

export interface ScheduledState {
  timeSlotsTestCenterStatus: "idle" | "loading" | "success" | "error";
  timezones: Timezone[];
  timeslots: Slot[];
  timeslotsTestCenter: slot[];
  cart: Cart;
  PracticeCart: Cart;
  monthlySlots: MonthlySlot[];
  examId: ExamModel[];
  examIdPractice: ExamModel[];
  route: string;
  registeredExams: RegisteredExamsModel[];
  registeredPracticeExams: RegisteredExamsModel[];
  makepaymentresponse: MakePaymentResponse;
  cartItems: CartItem[];
  schedule: string;
  isCancelled: boolean;
  isPracticeCancelled: boolean;
  rescheduleResponse: number;
  PracticerescheduleResponse: number;
  PracticeretryscheduleResponse: number;
  isPayment: boolean;
  isDeleted: boolean;
  isPracticeDeleted: boolean;
  VocherResponse: VocherResponse;
  Vocher_validate_apply_Response: number;
  Vocher: VoucherModel[];
  VochersApply: number;
  scheduleResponse: any;
  personForms: PersonForm[];
  paymentMethods: paymentMethod[];
  chargeResponse: chargeResponse;
  chargePracticeResponse: chargeResponse;
  customerIdObj: customerId;
  createPaymentMethodResponse: CreatePaymentMethodRespnse;
  createPaymnetCustomerIdResponse: CreatePaymentCustomerIdResponse;
  VocherUpdateResponse: VocherUpdateResponse;
  showRegisterExamStatus: ShowRegisterExamModel;
  loading: boolean;
  paymenterror: string;

  clearCartResponse: any;

  formProgressBar: any;
}

export const initScheduledState: ScheduledState = {
  timeSlotsTestCenterStatus: "idle",

  timezones: [],
  clearCartResponse: null,
  timeslots: null,
  timeslotsTestCenter: null,
  cart: null,
  monthlySlots: [],
  examId: [],
  route: null,
  registeredExams: [],
  registeredPracticeExams: [],
  PracticeretryscheduleResponse: null,
  makepaymentresponse: null,
  cartItems: [],
  examIdPractice: [],
  isPracticeDeleted: null,
  schedule: null,
  PracticeCart: null,
  isCancelled: null,
  isPracticeCancelled: null,
  rescheduleResponse: null,
  isPayment: null,
  isDeleted: null,
  VocherResponse: null,
  Vocher: null,
  VochersApply: null,
  scheduleResponse: null,
  personForms: [],
  paymentMethods: [],
  PracticerescheduleResponse: null,
  chargeResponse: null,
  chargePracticeResponse: null,
  customerIdObj: null,
  createPaymentMethodResponse: null,
  createPaymnetCustomerIdResponse: null,
  VocherUpdateResponse: null,
  showRegisterExamStatus: null,
  loading: false,
  paymenterror: null,
  formProgressBar: null,
  Vocher_validate_apply_Response: null,
};
