export interface eligibilityRoute {
  id: number;
  eligibilityName?: string;
  tenantCode?: string;
  eligibilityRouteDetails?: eligibilityRouteDetails;
  isFormSubmissionAllowed:boolean
}

export interface eligibilityRouteDetails {
  eligibilityRouteDetail: string;
  mandatoryInfo: string;
  optionalInfo: string,
}

export interface ConfirmERD {
  title: string;
  description: string;
  acknowledgement: string;
  importantNote?: any;
}



export interface Form {
  formID?: number
  formTypeID: number,
  stateID?: number,
  eligibilityID: number,
  formJSON?: any;
  dataDetail?: string;
  formUrl?: string;
  name?: string;
  personTenantRoleId?: number;
  tenantId?: number;
}

export interface PersonFormLog {
  personFormId: number;
  comment: string;
  name: string;
  actionOn: Date;
  reviewer: string;
  formTypeId:number;
}

export interface PersonForm{
  personFormId: number;
  name: string;
  state: string;
  eligiblityRoute: string;
  eligibilityRouteId?: number;
  formCode?: string;
  submittedDate: Date;
  lastUpdatedDate: Date;
  iconUrl: string;
  status: string;
  waitingTime?: any;
  comment : string;
  fields : string;
  formId : number;
  formTypeId : number;
  statusId: number;
  allowApplyAgain: boolean;
  testCenterId:string,
  testingPreferenceId:number
  testingPreferenceName:string
  isTestCenterAcknowledge:any
}

export interface response {
  applicationFormResponse?: any,
  accomodationFormResponse?: any;
}

export class ShowRegisterExamModel{
  public constructor(init?:Partial<ShowRegisterExamModel>) {
      Object.assign(this, init);
  }

  personTenantRoleId: number = 0;
  isApplication: boolean = false;
  isApplicationApproved: boolean = false;
  isAccommodation: boolean = false;
  isAccommodationApproved: boolean = false;
  showRegister: boolean = false;
}