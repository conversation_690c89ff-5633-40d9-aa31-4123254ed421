import { createReducer, on } from "@ngrx/store";
import { Examcancelled, getDashboardData, getForm, gotCandidateLogin, gotDashboardData, gotForm, gotShowRegisterExam, gotupcomingExam } from "./dashboard.actions";
import { DashboardState, initDashboardState } from "./dashboard.state";

const _dashboardReducer = createReducer(initDashboardState,
    on(getDashboardData, (state, action) => {
        return {
            ...state,
            loading: true
        }
    }),
    on(gotDashboardData, (state, action) => {
        return {
            ...state,
            form: action.dashboardData.form,
            upcomingExam: action.dashboardData.upcomingExam,
            personForms: action.dashboardData.personForms,
            loading : false
        }
    }),
    on(gotForm, (state, action) => {
        return {
            ...state, form: action.form
        }
    }),
    on(gotupcomingExam, (state, action) => {
        return {
            ...state, upcomingExam: action.upcomingExam
        }
    }), on(Examcancelled, (state, action) => {
        return {
            ...state, isCancelled: action.isCancelled
        }
    }),
    on(gotCandidateLogin, (state, action) => {
        return {
            ...state, LoginResponse: action.Result
        }
    }),
    on(gotShowRegisterExam, (state, action) => {
        return {
            ...state,
            showRegisterExamStatus: action.data
        }
    })
)

export function dashboardReducer(state, action) {
    return _dashboardReducer(state, action);
}