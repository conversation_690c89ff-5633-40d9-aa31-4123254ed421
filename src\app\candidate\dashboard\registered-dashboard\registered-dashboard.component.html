<div *ngIf="(upcomingExam.examModeId === 1 || this.ExamCode ==='CBT-WR')" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr" gdColumns.lt-sm="1fr"
  gdGap="12px">
  <!-- Section 1 -->
  <div class="" gdColumn="1 / 8" gdColumn.lt-md="1 /3" gdColumn.lt-sm="1">
    <div class="h-full" fxLayout="row wrap" fxLayoutGap=" grid">
      <div class="card shadow-none cardBorder h-full" fxFlex="auto">
        <div class="bg-color px-4 py-3">
          <div class="flex justify-between" fxLayout="row">
            <div class="t-xs title-hed">
              <strong>{{ upcomingExam.examName }}</strong>
            </div>
          </div>
          <div class="t-xs state-elig pt-1" fxLayout="row">
            State/Eligibility Route:{{ upcomingExam.stateCode }} /
            {{ upcomingExam.eligibilityRouteCode }}
          </div>
        </div>
        <div class="" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr">
          <div gdColumn="1 / 2" gdColumns.lt-md="1 / 2" fxLayout="column">
            <div class="pl-4" fxFlexFill>
              <span class="status t-xs">{{
                this.lngSrvc.curLangObj.value.currentStatus
                }}</span><br/>
              <span><img src="{{ upcomingExam.iconUrl }}" class="inline iconSize" /></span>
              <span class="t-xs ml-2 -mt-3 active2" [style.color]="
              upcomingExam.examStatus == 'Exam Scheduled' ? '#00AB72'
              : '#F7685B' ">
                {{ upcomingExam.examStatus }}</span>
              <div class="italic t-xs state-elig">
                <!-- {{upcomingExam.registeredDateTime|date}}hello -->
                {{ upcomingExam.registeredDateTime }}
              </div>
              <div class="pt-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                  <!-- <div class="h4 status t-xs">
                    {{ this.lngSrvc.curLangObj.value.examMode }}

                  </div> -->

                  <div *ngIf="upcomingExam.examMode=='Online'" class="h4 status t-xs">Exam Mode</div>
                  <div  *ngIf="upcomingExam.examMode=='Test Center'"class="h4 status t-xs">Test Center Name</div>



                </div>
                <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                  <div class="status t-xs">
                    {{ this.lngSrvc.curLangObj.value.examDate }}
                  </div>
                </div>
                <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                  <div class="status t-xs">
                    {{ this.lngSrvc.curLangObj.value.examTime }}
                  </div>
                </div>
              </div>
              <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                  <!-- <div class="h4 status1 t-xs">
                    {{ upcomingExam.examMode }}
                  </div> -->
                  <div *ngIf= "upcomingExam.examMode=='Online'" class="h4 status1 t-xs">{{upcomingExam.examMode}}</div>
                  <div  *ngIf="upcomingExam.examMode == 'Test Center'" class="h4 status1 t-xs">{{upcomingExam.testCenterDetails.testCenterName}}</div>
                </div>
                <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                  <div class="status1 t-xs">
                    <!-- {{upcomingExam.examDateTime |date}}hiii -->
                    <div class="status1 t-xs"> {{ upcomingExam.examDateTime | date: "MM/dd/yyyy":'+0000' }}</div>
                  </div>
                </div>
                <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                  <div class="status1 t-xs">
                    <!-- {{upcomingExam.examDateTimeUtc | date:'shortTime'}} -->
                    {{upcomingExam.examDateTime |date:'shortTime':'+0000' }} {{upcomingExam.timeZoneAbbreviation}}
                  </div>
                </div>
              </div>
              <div class="update py-2" *ngIf="upcomingExam.examStatus == 'Event Assigned'">
                Note: This exam is already been scheduled and can't be
                rescheduled. Incase you want to reschedule or cancel the exam
                please contact your school/university or our customer support
                for queries.
              </div>
            </div>
          </div>

          <!-- Timer -->

          <div class="items-center pb-2 pr-6" fxLayout="column">
            <mat-card class="time-card shadow-none">
              <div fxLayout="column" fxFlex>
                <div class="text-center text-xs pt-4">
                  <mat-card-subtitle> Your Exam Start in.. </mat-card-subtitle>
                </div>
                <div fxLayout="row" fxFlex>
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.days }}
                    </mat-card-title>
                    <mat-card-subtitle>
                      Days
                    </mat-card-subtitle>
                  </mat-card-header>:
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.hours }}
                    </mat-card-title>
                    <mat-card-subtitle>
                      Hours
                    </mat-card-subtitle>
                  </mat-card-header>:
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.minutes }}
                    </mat-card-title>
                    <mat-card-subtitle>
                      Min
                    </mat-card-subtitle>
                  </mat-card-header>:
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.seconds }}
                    </mat-card-title>
                    <mat-card-subtitle> Sec </mat-card-subtitle>
                  </mat-card-header>
                </div>
                <div class="text-center text-xs pt-4">
                  <mat-card-subtitle> Your Exam Starts in... </mat-card-subtitle>
                </div>
              </div>
            </mat-card>
            <mat-card-actions class="" fxFlexAlign="end center" fxLayout="row" *ngIf="
            upcomingExam.examStatus == 'Exam Scheduled' ||
                upcomingExam.examStatus == 'Waiting for Onboarding' ||
                upcomingExam.examStatus == 'Onboarding'
              ">
              <div *ngIf="upcomingExam.allowReschedule" class="mx-2">
                <button *ngIf="upcomingExam.cancel"  (click)="getConfirmation(upcomingExam)" mat-button class="btn-5 t-xs">Cancel</button>
              </div>
              <div *ngIf="upcomingExam.allowReschedule" class="mx-2">
                <button *ngIf="upcomingExam.reschedule"  mat-button class="btn-4 t-xs" (click)="reschedule(upcomingExam)">
                  Reschedule
                </button>
              </div>
              <div class="mx-2">
                <button mat-button class="btn-4 t-xs" *ngIf="upcomingExam.allowLaunchExam" (click)='startExam()' >
                  Start Exam
                </button>
              </div>
            </mat-card-actions>
            <mat-card-subtitle class="text-center t-xs"> You can start the exam 60 minutes before the Exam Time, and up to 59 minutes after it. </mat-card-subtitle>
       
          </div>

          <!-- Timer -->
        </div>
      </div>
    </div>
  </div>

  <!-- System Check -->
  <div gdColumn="8/11" gdColumn.lt-md="3/-1" gdColumn.lt-sm="1" *ngIf="systemCheckStatus">
    <div class="card shadow-none cardBorder h-full">
      <div class="flex justify-center">
        <img class="imgSize pt-3 pb-4" src="assets/img/System-Check 2-Blue.svg" /><br/>
      </div>
      <div class="content1">
        <section class="mr-5 ml-5">
          <div class="bg-color">
            <span class="
                welc-note
                flex
                justify-center
                text-center text-xs
                px-8
                pt-4
              ">
              Please complete your system check before your
            </span>
            <span class="welc-note flex justify-center text-center text-xs pb-4">
              online exam date.
            </span>
          </div>
        </section>
        <div class="flex justify-center -mt-4 mb-4">
          <button mat-flat-button color="primary" (click)="_systemCheck()">System check</button>
        </div>
        <ng-template #systemCheck class="pb-0">
          <div mat-dialog-title class="flex mb-0">
            <h5 class="system-title ml-3 mt-3"><strong>System check</strong></h5>
            <button mat-icon-button mat-dialog-close class="ml-auto systemCheckDialog">
              <mat-icon>close</mat-icon>
            </button>
          </div>
          <!-- <div mat-dialog-content> -->
          <exai-system-check ></exai-system-check>
          <!-- </div> -->
        </ng-template>
      </div>
    </div>
  </div>
</div>

<div *ngIf="upcomingExam.examModeId == 2 && alloworal.includes(upcomingExam.examName) && upcomingExam.allowLaunchExam" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr" gdColumns.lt-sm="1fr"
  gdGap="12px">
  <!-- Section 1 -->
  <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 /3" gdColumn.lt-sm="1">
    <div class="h-full" fxLayout="row wrap" fxLayoutGap=" grid">
      <div class="card shadow-none cardBorder h-full" fxFlex="auto">
        <div class="bg-color px-4 py-3">
          <div class="flex justify-between" fxLayout="row">
            <div class="t-xs title-hed">
              <strong>{{ upcomingExam.examName }}</strong>
            </div>
          </div>
          <div class="t-xs state-elig pt-1" fxLayout="row">
            State/Eligibility Route:{{ upcomingExam.stateCode }} /
            {{ upcomingExam.eligibilityRouteCode }}
          </div>
        </div>
        <div class="" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr">
          <div gdColumn="1 / 2" gdColumns.lt-md="1 / 2" fxLayout="column">
            <div class="pl-4" fxFlexFill>
              <span class="status t-xs">{{
                this.lngSrvc.curLangObj.value.currentStatus
                }}</span><br/>
              <span><img src="{{ upcomingExam.iconUrl }}" class="inline iconSize" /></span>
              <span class="t-xs ml-2 -mt-3 active2" [style.color]="
              upcomingExam.examStatus == 'Exam Scheduled' ? '#00AB72'
              : '#F7685B' ">
                {{ upcomingExam.examStatus }}</span>
              <div class="italic t-xs state-elig">
                <!-- {{upcomingExam.registeredDateTime|date}}hello -->
                {{ upcomingExam.registeredDateTime }}
              </div>
              <div class="pt-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                  <!-- <div class="h4 status t-xs">
                    {{ this.lngSrvc.curLangObj.value.examMode }}

                  </div> -->

                  <div *ngIf="upcomingExam.examMode=='Online'" class="h4 status t-xs">Exam Mode</div>
                  <div  *ngIf="upcomingExam.examMode=='Test Center'"class="h4 status t-xs">Test Center Name</div>



                </div>
                <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                  <div class="status t-xs">
                    {{ this.lngSrvc.curLangObj.value.examDate }}
                  </div>
                </div>
                <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                  <div class="status t-xs">
                    {{ this.lngSrvc.curLangObj.value.examTime }}
                  </div>
                </div>
              </div>
              <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                  <!-- <div class="h4 status1 t-xs">
                    {{ upcomingExam.examMode }}
                  </div> -->
                  <div *ngIf= "upcomingExam.examMode=='Online'" class="h4 status1 t-xs">{{upcomingExam.examMode}}</div>
                  <div  *ngIf="upcomingExam.examMode == 'Test Center'" class="h4 status1 t-xs">{{upcomingExam.testCenterDetails.testCenterName}}</div>
                </div>
                <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                  <div class="status1 t-xs">
                    <!-- {{upcomingExam.examDateTime |date}}hiii -->
                    <div class="status1 t-xs"> {{ upcomingExam.examDateTime | date: "MM/dd/yyyy":'+0000' }}</div>
                  </div>
                </div>
                <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                  <div class="status1 t-xs">
                    <!-- {{upcomingExam.examDateTimeUtc | date:'shortTime'}} -->
                    {{upcomingExam.examDateTime |date:'shortTime':'+0000' }} {{upcomingExam.timeZoneAbbreviation}}
                  </div>
                </div>
              </div>
              <div class="update py-2" *ngIf="upcomingExam.examStatus == 'Event Assigned'">
                Note: This exam is already been scheduled and can't be
                rescheduled. Incase you want to reschedule or cancel the exam
                please contact your school/university or our customer support
                for queries.
              </div>
            </div>
          </div>

          <!-- Timer -->

          <div class="items-center pb-2 pr-6" fxLayout="column">
            <mat-card class="time-card shadow-none">
              <div fxLayout="column" fxFlex>
                <div class="text-center text-xs pt-4">
                  <mat-card-subtitle> Your Exam Start in.. </mat-card-subtitle>
                </div>
                <div fxLayout="row" fxFlex>
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.days }}
                    </mat-card-title>
                    <mat-card-subtitle>
                      Days
                    </mat-card-subtitle>
                  </mat-card-header>:
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.hours }}
                    </mat-card-title>
                    <mat-card-subtitle>
                      Hours
                    </mat-card-subtitle>
                  </mat-card-header>:
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.minutes }}
                    </mat-card-title>
                    <mat-card-subtitle>
                      Min
                    </mat-card-subtitle>
                  </mat-card-header>:
                  <mat-card-header>
                    <mat-card-title class="text-center">
                      {{ timerInfo.seconds }}
                    </mat-card-title>
                    <mat-card-subtitle> Sec </mat-card-subtitle>
                  </mat-card-header>
                </div>
                <div class="text-center text-xs pt-4">
                  <mat-card-subtitle> Your Exam Starts in... </mat-card-subtitle>
                </div>
              </div>
            </mat-card>
            <mat-card-actions class="" fxFlexAlign="end center" fxLayout="row" *ngIf="
            upcomingExam.examStatus == 'Exam Scheduled' ||
                upcomingExam.examStatus == 'Waiting for Onboarding' ||
                upcomingExam.examStatus == 'Onboarding'
              ">
              <div *ngIf="upcomingExam.allowReschedule" class="mx-2">
                <button *ngIf="upcomingExam.cancel"  (click)="getConfirmation(upcomingExam)" mat-button class="btn-5 t-xs">Cancel</button>
              </div>
              <div *ngIf="upcomingExam.allowReschedule" class="mx-2">
                <button *ngIf="upcomingExam.reschedule"  mat-button class="btn-4 t-xs" (click)="reschedule(upcomingExam)">
                  Reschedule
                </button>
              </div>
              <div class="mx-2">
                <button mat-button class="btn-4 t-xs" *ngIf="upcomingExam.allowLaunchExam" (click)='startExam()' >
                  Start Exam
                </button>
              </div>
            </mat-card-actions>
            <mat-card-subtitle class="text-center t-xs"> You can start the exam 60 minutes before the Exam Date, and up to 59 minutes after it. </mat-card-subtitle>
       
          </div>

          <!-- Timer -->
        </div>
      </div>
    </div>
  </div>
</div>