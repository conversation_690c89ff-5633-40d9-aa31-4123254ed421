import { NgModule, Type } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HelpRoutingModule } from './help-routing.module';

import { HelpComponent } from './help.component';
import { SelectCategoryComponent } from './select-category/select-category.component';
import { TicketDetailsComponent } from './ticket-details/ticket-details.component';
import { SupportTicketComponent } from './support-ticket/support-ticket.component';
import { TicketsComponent } from './tickets/tickets.component';
import { SelectCategorySideBarComponent } from './select-category-side-bar/select-category-side-bar.component';
import { SelectTicketSideBarComponent } from './dialogs/select-ticket-side-bar/select-ticket-side-bar/select-ticket-side-bar.component';
import { ViewPreviousTicketsComponent } from './dialogs/view-previous-tickets/view-previous-tickets.component';

// ----------Angular Material Imports
import { FlexLayoutModule } from '@angular/flex-layout';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { BreadcrumbsModule } from 'src/@exai/components/breadcrumbs/breadcrumbs.module';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';

import { DynamicFormControl, DynamicFormControlModel, DynamicFormsCoreModule, DYNAMIC_FORM_CONTROL_MAP_FN, } from '@ng-dynamic-forms/core';
import { DynamicFileUploadControlComponent } from 'src/app/core/examroom-formbuilder/dynamic-file-upload-control/dynamic-file-upload-control.component';
import { DynamicFormsMaterialUIModule } from "@ng-dynamic-forms/ui-material";
import { NgxMaskModule } from 'ngx-mask';
import { CardComponent } from './tickets/card/card.component';
import { MatButtonModule } from '@angular/material/button';



@NgModule({
  declarations: [
    HelpComponent,
    SelectCategoryComponent,
    TicketDetailsComponent,
    SupportTicketComponent,
    TicketsComponent,
    SelectCategorySideBarComponent,
    ViewPreviousTicketsComponent,
    SelectTicketSideBarComponent,
    CardComponent
  ],
  imports: [
    CommonModule,
    HelpRoutingModule,
    ReactiveFormsModule,
    NgDynamicBreadcrumbModule,
    BreadcrumbsModule,
    FlexLayoutModule,
    ContainerModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    DynamicFormsMaterialUIModule,
    NgxMaskModule.forRoot(),
    DynamicFormsCoreModule.forRoot(),
  ],
  providers: [
    {
    provide: DYNAMIC_FORM_CONTROL_MAP_FN,
    useValue: (model: DynamicFormControlModel): Type<DynamicFormControl> | null  => {
      switch (model.type) {
        case "FILE_UPLOAD":
          return DynamicFileUploadControlComponent;
        }
     }
  },
  ],
  entryComponents: [ ViewPreviousTicketsComponent, DynamicFileUploadControlComponent]
})
export class HelpModule { }
