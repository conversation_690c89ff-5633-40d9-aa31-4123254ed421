import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DynamicFormComponentComponent } from './dynamic-form-component/dynamic-form-component.component';
import { FormBuilderComponent } from './form-builder.component';
import { FormJsonOutputComponent } from './form-json-output/form-json-output.component';
import { FormJsonComponent } from './form-json/form-json.component';
import { TrueFormComponentComponent } from './true-form-component/true-form-component.component';

const routes: Routes = [{
  path: '', component: FormBuilderComponent,
  children: [
    { path: 'trueFormBuilder', component: TrueFormComponentComponent },
    { path: 'dynFormComponent', component: DynamicFormComponentComponent },
    { path: 'BuiltFormJson', component: FormJsonComponent },  
    { path: 'formJsonOutput', component: FormJsonOutputComponent },
    // { path: '**', pathMatch:'full',redirectTo:'trueFormBuilder' },
]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FormBuilderRoutingModule { }
