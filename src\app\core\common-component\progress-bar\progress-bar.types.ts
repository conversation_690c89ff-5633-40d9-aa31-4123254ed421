export interface PersonFormLog {
  personFormId: number;
  comment: string;
  name: string;
  actionOn: Date;
  reviewer: string;
  formTypeId: number;
  statusTypeId: number;
  statusType: string;
  iconUrl: string;
}

export interface Log {
  personFormId: number;
  comment: string;
  name: string;
  actionOn: Date;
  reviewer: string;
  formTypeId: number;
  nextAsigneeRoleId: number;
  nextAssigneRole: string;
  statusTypeId: number;
  iconUrl: string;
  statusType: string;
}

export class prgLogCandi {
  formName: string;
  formType: string;
  logs: Log[];
}