import { createAction, props } from "@ngrx/store";
import { Excusedlist, ExcusedAbsense, SaveGrievnaceForm, StatusLog, upcomingExam, ViewGrievanceForm } from "./excused.model";

export const loadAll = createAction('[EXCUSED] Load All');

export const loadAllSuccess = createAction(
  '[EXCUSED] Load All Success',
  props<{ data: Excusedlist[] }>()
);

export const loadAllFailure = createAction(
  '[EXCUSED] Load All Failure',
  props<{ error: any }>()
);

export const loadAbsenseform = createAction('[EXCUSED] Load Report');

export const loadAbsenseformSuccess = createAction(
  '[EXCUSED] Load Report Success',
  props<{ data: ExcusedAbsense }>()
)

export const loadReportGrievanceFailure = createAction(
  '[EXCUSED] Load Report Failure',
  props<{ error: any }>()
)

export const requestReportGrievance = createAction(
  '[EXCUSED] Request Save Report Grievance',
  props<{ params: SaveGrievnaceForm }>()
);

export const postReportGrievanceSuccess = createAction(
  '[EXCUSED] Post Save Report Grievance Success',
  props<{ data: any }>()
)

export const postReportGrievanceFailure = createAction(
  '[EXCUSED] Post Save Report Grievance Failure',
  props<{ error: any }>()
)

export const requestSaveDraftReportGrievance = createAction(
  '[EXCUSED] Request Save Draft Report Grievance',
  props<{ params: SaveGrievnaceForm }>()
);

export const postSaveDraftReportGrievanceSuccess = createAction(
  '[EXCUSED] Post Save Draft Report Grievance Success',
  props<{ data: any }>()
)

export const postSaveDraftReportGrievanceFailure = createAction(
  '[EXCUSED] Post Save Draft Report Grievance Failure',
  props<{ error: any }>()
)

export const requestViewGrievance = createAction(
  '[EXCUSED] Request View Grievance',
  props<{ params: ViewGrievanceForm }>()
);

export const postViewGrievanceSuccess = createAction(
  '[EXCUSED] Post View Grievance Success',
  props<{ data: any }>()
)

export const postViewGrievanceFailure = createAction(
  '[EXCUSED] Post View Grievance Failure',
  props<{ error: any }>()
)

export const requestPersonProgress = createAction(
  '[EXCUSED] Get Request Person Progress',
  props<{ personFormId: number }>()
)

export const getPersonProgressSuccess = createAction(
  '[EXCUSED] Get Request Person Progress Success',
  props<{ data: Array<StatusLog> }>()
)

export const getPersonProgressFailure = createAction(
  '[EXCUSED] Get Request Person Progress Failure',
  props<{ error: any }>()
)
export const cancelExam = createAction('[Cancel] cancel Exam', props<{personFormId:number, candidateId: number}>())


export const Examcancelled = createAction('[Cancel] FormCancelled',
props<{isCancelled:number}>())



export const getupcomingExam = createAction('[Froms] GET upcomingExam',
  props<{ candidateId: number }>())

export const gotupcomingExam = createAction('[Froms] GOT upcomingExam',
  props<{ upcomingExam: upcomingExam[] }>())


  

export const clearGrievanceState = createAction('[CLEAR] Clearing Grievance Store');