import { ViewCertificateComponent } from './view-certificate/view-certificate.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RegistryComponent } from './registry.component';
import { AuthGuard } from 'src/app/core/auth.guard';


const routes: Routes = [
  {
    path: '',
    component: RegistryComponent,
    canActivate: [AuthGuard],
    data: {
      title: 'Registry',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Registration',
          url: ''
        },
      ]
    }
  },
  {
    path: 'view-certificate',
    component: ViewCertificateComponent,
    canActivate: [AuthGuard],
    data: {
      title: 'Registry',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Registration',
          url: '/registry'
        },
        {
          label: 'View Registration',
          url: ''
        },
      ]
    }
  },
  {
    path: 'reciprocity-form',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    data: {
      title: 'Registry',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Registration',
          url: '/registry'
        },
        {
          label: 'Reciprocity Form',
          url: ""
        }
      ]
    }
  },
  {
    path: 'duplicate-form',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    data: {
      title: 'Registry',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Registration',
          url: '/registry'
        },
        {
          label: 'Duplicate Registration Form',
          url: ""
        }
      ]
    }
  },
  {
    path: 'renewal-form',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    data: {
      title: 'Registry',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Registration',
          url: '/registry'
        },
        {
          label: 'Renewal Registration Form',
          url: ""
        }
      ]
    }
  },
  {
    path: 'reinstate',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    data: {
      title: 'Registry',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Registration',
          url: '/registry'
        },
        {
          label: 'Reinstatement',
          url: ""
        }
      ]
    }
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RegistryRoutingModule { }
