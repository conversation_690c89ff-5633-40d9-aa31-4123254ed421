.active2 {
    color: #209e91;
}

.content {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-right: 16px;
}

.iconSize {
    width: 12px;
    height: 12px;
}

.icon_size {
    font-size: 16px;
}

.text_style1 {
    font-style: italic;
}

.text_size {
    font-size: 10px;
    color: var(--text-color1);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    // width: 250px;
}

.text_color2 {
    color: var(--text-color1);
}

.status {
    color: var(--text-color5);
}

.status1 {
    color: var(--text-color1);
}

.bg-color {
    background-color: var(--background-base2);
}

.cardBorder {
    border: var(--border);
    border-radius: var(--border-radius);
}

.borderBottom {
    border-bottom: var(--border);
    background-color: var(--text-edit);
}

.cardHeight {
    height: 300px;
}

.example-card {
    max-width: 400px;
}

mat-card {
    max-width: 400px;
}

// .fullscreen {
//     position: fixed;
//     top: 0;
//     left: 0;
//     bottom: 0;
//     right: 0;
//     overflow: auto;
// }

.div-pad {
    padding-top: 2rem !important;
}
.btn-4{
    line-height: 22px !important;
    font-size: 0.75rem;
    color: var(--text-color2);
    background-color: var(--background-base3);
    border-radius: 4px;
    padding: 0 8px;
}

.left-mar {
    margin-left: 15px;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.minimise {
    width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
