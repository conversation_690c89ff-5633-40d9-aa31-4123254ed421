export interface TicketCertificate {
    id: number,
    personId: number,
    personTenantRoleId: number,
    personEventId: number,
    registryStatusId: number,
    registryStatus: string,
    registryName: string,
    effectiveDate: string,
    expirationDate: string,
    statusSinceDate: string,
    certNumber: string,
    certpath: string,
    workedBy: string,
    stateName: string,
    stateId: number,
    examId: number,
    examName: string,
    certType: number,
    actionOn: string,
    actionBy: number,
    comment: string,
    eligibilityRouteId: number,
    eligibilityRoute: string,
    firstName: string,
    middleName: string,
    lastName: string,
    state: string,
    zipCode: string
}
