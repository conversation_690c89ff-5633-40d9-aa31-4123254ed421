import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { HelpService } from '../help.service';
import { Category } from '../interfaces/category';

@Component({
  selector: 'exai-select-category-side-bar',
  templateUrl: './select-category-side-bar.component.html',
  styleUrls: ['./select-category-side-bar.component.scss']
})
export class SelectCategorySideBarComponent implements OnInit {

  constructor(
    private helpService: HelpService,
    private router: Router,
  ) { }

  categories$: Observable<Category[]>
  currentCategory: number | null = null

  ngOnInit(): void { 
    this.categories$ = this.helpService.fetchCategories()
  }

  selectCategory(category: Category) {
    this.currentCategory = category.id
    this.helpService.selectedCategory.next(category)
    this.router.navigate(['help', category.name])
  }
  
}