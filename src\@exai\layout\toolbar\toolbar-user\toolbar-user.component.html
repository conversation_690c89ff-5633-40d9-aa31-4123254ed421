<div #originRef
     (click)="showPopover(originRef)"
     [class.bg-hover]="dropdownOpen"
     class="flex items-center rounded cursor-pointer relative trans-ease-out select-none py-1 pr-1 pl-1 hover:bg-hover"
     matRipple>
  <!-- <div class="body-1 font-medium leading-snug ltr:mr-3 rtl:ml-3" fxHide.xs><PERSON></div> -->
  <div class="rounded-full w-12 h-12 user-button flex items-center justify-center text-primary bg-primary-light">
    <mat-icon>account_circle</mat-icon>
  </div>
</div>
