.page-container {
    bottom: 0;
    display: flex;
    flex-direction: column;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
}

.sidenav {
    background: var(--sidenav-background);
    // @screen md {
    //   top: 60px!important;
    // }
    // @screen sm {
    //   top: 60px!important;
    // }
    ::ng-deep .mat-drawer-inner-container {
        overflow: hidden;
    }
}

.help-btn {
    position: fixed;
    bottom: 2rem !important;
    right: 0.65rem;
    z-index: 100000000 !important;
    border: 1px solid var(--text-color2);
    border-radius: 4px;
    background-color: var(--text-color2);
}

.chat {
    color: var(--background-base);
}
.chat1 {
    color: var(--background-base);
    vertical-align: text-top;
}

.chatContainer {
    height: fit-content;
    width: 30vw;
    position: absolute;
    bottom: 2rem !important;
    right: 0.65rem;
    border-radius: 0.25rem;
}

.content {
    // background: var(--background-base);
    background: var(--background-base1);
    min-height: calc(100vh - var(--toolbar-height) - var(--navigation-height));
    position: relative;
    width: 100%;
}

.has-footer .content {
    min-height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height));
}

.scroll-disabled {
    overflow: hidden;
    .content {
        height: calc(100% - var(--toolbar-height) - var(--navigation-height));
        min-height: unset;
        overflow: hidden;
    }
    &.has-fixed-footer .content,
    &.has-footer .content {
        height: calc(100% - var(--toolbar-height) - var(--navigation-height) - var(--footer-height));
        min-height: unset;
    }
}

.is-mobile {
    ::ng-deep .vex-toolbar {
        position: fixed;
        width: 100%;
    }
    .content {
        // margin-top: var(--toolbar-height);
    }
}

.sidenav-container {
    background: var(--background-base);
    height: 100%;
    transition: transform 0.5s cubic-bezier(0.2, 1, 0.3, 1);
}

.sidenav-content {
    overflow-x: hidden;
    overflow-y: auto;
}

.with-search {
    overflow: hidden;
    position: fixed;
    .sidenav-container {
        pointer-events: none;
        transform: perspective(1000px) translate3d(0, 50vh, 0) rotate3d(1, 0, 0, 30deg);
        transform-origin: 50vw 50vh;
        transition: transform 0.5s cubic-bezier(0.2, 1, 0.3, 1);
        @apply rounded shadow-2xl overflow-visible;
    }
}

.toolbar-fixed {
    ::ng-deep .vex-toolbar {
        position: fixed;
        width: var(--toolbar-width);
        z-index: 50;
    }
    .content {
        margin-top: calc(var(--toolbar-height) + var(--navigation-height));
    }
}

.has-fixed-footer {
    ::ng-deep .vex-footer {
        box-shadow: var(--footer-elevation);
        position: fixed;
    }
    .content {
        margin-bottom: var(--footer-height);
        min-height: calc(100% - var(--toolbar-height) - var(--navigation-height) - var(--footer-height));
    }
    &.scroll-disabled .content {
        height: calc(100% - var(--toolbar-height) - var(--navigation-height) - var(--footer-height));
    }
}

.mat-icon {
    height: 16px;
    width: 16px;
    font-size: 18px;
}
