import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CandidateRoutingModule } from './candidate-routing.module';
import { CommonComponentModule } from '../core/common-component/common-component.module';
import { AccessDeniedComponent } from './common/access-denied/access-denied.component';
import { NotFoundComponent } from './common/not-found/not-found.component';
import { TokenInterceptorService } from '../core/token-interceptor.service';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { StoreModule } from '@ngrx/store';
import { ScheduledEffects } from './scheduled/state/scheduled.effects';
import { EffectsModule } from '@ngrx/effects';
import { scheduledReducer } from './scheduled/state/scheduled.reducers';
import { SCHEDULED_STATE_NAME } from './scheduled/state/scheduled.selectors';
@NgModule({
  declarations: [
    AccessDeniedComponent,
    NotFoundComponent
  ],
  imports: [
    CommonModule,
    CandidateRoutingModule,
    CommonComponentModule,
    StoreModule.forFeature(SCHEDULED_STATE_NAME, scheduledReducer),
    EffectsModule.forFeature([ScheduledEffects]),
  ],
  providers: [
    // { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class CandidateModule { }
