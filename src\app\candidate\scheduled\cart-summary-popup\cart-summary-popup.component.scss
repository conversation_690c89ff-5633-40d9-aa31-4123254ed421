
.header {
  height: 36px;

  .title {
    color: #000000;
    font-size: 14px;
  }
}
.meta-label {
  color: var(--text-edit);
}
.meta-value {
  color: var(--text-color2);
}
.h2 {
  font-size: 18px;
}
.font {
  color: #11263c;
}
.active {
  color: var(--text-color2);
}
.add {
  border: 1px solid var(--text-color2);
  color: var(--text-color2);
  width: 84px;
}

.bg-color {
  background-color: #fafafa;
}
.content {
  padding: 16px;
}
.total {
  color: #11263c;
}

.content1 > mat-card {
  margin: 12px;
}
.status1 {
  color: #7d7d7d;
}

hr.status1 {
  border-top: 0.3px solid #dcdcdc;
}
.status {
  color: #c4c4c4;
}

.btn1 {
  border: none !important;
}
.buuton2 {
  line-height: 32px !important;
  min-width: unset !important;
  font-size: 12px;
  background-color: var(--button-background) !important;
  color: #ffff;
}
.buuton1 {
  line-height: 26px !important;
  min-width: unset !important;
  font-size: 12px;
  color: var(--button-background) !important;
  border: 1px solid var(--button-background) !important;
}
.Apply {
  color: var(--button-background) !important;
}

.button {
  color: var(--button-background) !important;
  margin-top: -0.9em;
}

::ng-deep .mat-input-element {
  width: 40%;
}

::ng-deep .mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,
.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay {
  opacity: 0 !important;
}
::ng-deep .mat-icon {
  font-size: 17px !important;
  cursor: pointer !important ;
}
::ng-deep button.mat-accent.mat-button-disabled,
.mat-flat-button.mat-warn.mat-button-disabled,
.mat-flat-button.mat-button-disabled.mat-button-disabled,
.mat-raised-button.mat-primary.mat-button-disabled,
.mat-raised-button.mat-accent.mat-button-disabled,
.mat-raised-button.mat-warn.mat-button-disabled,
.mat-raised-button.mat-button-disabled.mat-button-disabled,
.mat-fab.mat-primary.mat-button-disabled,
.mat-fab.mat-accent.mat-button-disabled,
.mat-fab.mat-warn.mat-button-disabled,
.mat-fab.mat-button-disabled.mat-button-disabled,
.mat-mini-fab.mat-primary.mat-button-disabled,
.mat-mini-fab.mat-accent.mat-button-disabled,
.mat-mini-fab.mat-warn.mat-button-disabled,
.mat-mini-fab.mat-button-disabled.mat-button-disabled {
  // background-color: rgba(0, 0, 0, 0.12);
  color: #fff !important;
}

.delete {
  cursor: pointer;
}

// .content1 {
//   overflow: auto;
//   // height: calc(100vh - 13.5rem);
//   @screen xl {
//     height: calc(
//       100vh - var(--toolbar-height) - var(--navigation-height) -
//         var(--footer-height) - 7.5rem
//     );
//   }
//   @screen lg {
//     height: calc(
//       100vh - var(--toolbar-height) - var(--navigation-height) -
//         var(--footer-height) - 8.5rem
//     );
//   }
//   @screen md {
//     height: calc(
//       100vh - var(--toolbar-height) - var(--navigation-height) -
//         var(--footer-height) - 8.5rem
//     );
//   }
//   @screen sm {
//     height: calc(
//       100vh - var(--toolbar-height) - var(--navigation-height) -
//         var(--footer-height) - 7.5rem
//     );
//   }
//   // @screen xs {
//   //   height: calc(
//   //     100vh - var(--toolbar-height) - var(--navigation-height) -
//   //       var(--footer-height) - 7.5rem
//   //   );
//   // }
//   // @screen xxs {
//   //   height: calc(
//   //     100vh - var(--toolbar-height) - var(--navigation-height) -
//   //       var(--footer-height) - 3.5rem
//   //   );
//   // }
// }
