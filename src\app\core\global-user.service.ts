import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Subject } from 'rxjs';
import { DecodedIdentityToken, UserDetails } from '../candidate/candiate.types';
import { get_userDetails } from '../candidate/state/shared/shared.selectors';
import { PersonDetails } from './Dto/persondetail';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: "root",
})
export class GlobalUserService {
  userId: any;
  emailIdToStartExam:any
  candidateId: any;
  personFormId: number;
  stateName:string
  personId:number
  formId: number;
  eligibilityRouteId: number;
  stateId: number;
  stateIds:number = 16
  clientId: number;
  FilesUpload=[]
  grievancePersonEventId: number = 0;
  General_Terms_Conditions: string = "General Terms and Conditions";
  Acceptable_UsePolicy: string = "Acceptable Use Policy";
  Cancellation_Policy: string = "Cancellation Policy";
  Privacy_Policy: string = "Privacy Policy";
  absensePersonEventId:number=0;
  SkillsItems=[]
  navigate:string
  tenantId: number;
  Candidate_RoleID: number = 1;
  Client_RoleID: number = 2;
  SuperAdmin_RoleID: number = 13;
  OperationStaff_RoleID: number = 17;
  StateClient_RoleID: number = 14;
  TrainingInstitute_RoleID: number = 15;
  Sponser_RoleID: number = 18;
  SupportStaff: number = 8;
  Employer: number = 19
  FinanaceRole:number = 21
  PayRole:number = 26
  Hrms:number = 25
  operationaccount:number = 27
  Evalator:number = 22
  testsite:number = 23
  Proctor:number =4
  QAC:number=28
  roleId: number
  roleName:string;
  userDetails: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  userstatus: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  No_slot_avaiable: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  ScheduledStatusId = 1;
    // register form validation msg's
  // First name
  First_name_is_required: string = "First name is required";
  Maximum_15_characters_are_allowed = "Maximum 15 characters are allowed";
  Maximum_50_characters_are_allowed = "Maximum 50 characters are allowed";
  Please_enter_valid_first_name = "Please enter valid first name";
  // Last name
  Last_name_is_required = "Last name is required";
  Please_enter_valid_last_name = "Please enter valid last name";
  // OrgID
  Please_enter_your_organization_ID = "Organization ID is required";
  // DOB
  Please_select_your_DOB = "DOB is required";
  // gender
  Please_select_your_gender = "Gender is required";
  // address
  Please_enter_your_address = "Address is required";
  Maximum_225_characters_are_allowed = "Maximum 225 characters are allowed";
  // city
  Please_enter_your_city = "City is required";
  Please_enter_valid_City_name = "Please enter valid city name";
  // zipcode
  Zip_code_is_required = "Zip code is required";
  Maximum_5_numbers_are_allowed = "Maximum 5 numbers are allowed";
  // state
  Please_enter_your_state = "State is required";
  Maximum_25_characters_are_allowed = "Maximum 25 characters are allowed";
  Please_enter_valid_state_name = "Please enter valid state name";
  // ssn
  Please_enter_your_Social_Security_number = "Social Security Number is required";
  Please_enter_valid_SSN = "Please enter valid SSN";

  // ssn -Colorado
  Please_enter_your_SSN_or_ITIN = "SSN or ITIN is required";
  Please_enter_valid_SSN_or_ITIN = "Please enter valid SSN or ITIN";
  // phNo
  Phone_number_is_required = "Phone number is required";
  Enter_a_valid_phone_number = "Please enter valid phone number";
  Email_is_required = "Email is required";
  Enter_a_valid_email = "Please enter valid email";
  Ssn_not_matched: string = "SSN didn't matched.";
  SsnItin_not_matched :string = "SSN or ITIN didn't matched.";
  Confirm_Ssn = "Confirm SSN";
  SSN: string = "SSN";
  PaymentPendingStatusId = 66;
  isPassed = true
  paymentCompleted = ExamStatus.payment_Completed;
  scheduling_error = ExamStatus.scheduling_error;
  GrievamceEvaluator = new BehaviorSubject<any>("");
  GrievamceEvaluatorresponse = new BehaviorSubject<any>("");
  Grievanceroute:string
  no_Show=ExamStatus.Exam_NoShow;
  Exam_Approved = ExamStatus.Approved
  Exam_Cancelled = ExamStatus.Exam_Cancelled
  Exam_Completed = ExamStatus.Exam_Completed
  INF_Validation_message = "Test center code must start with the letters INF followed by 5 digits"
  INF_Validation="Test Center code must start with the letters INF followed by 5 digits. If you do not know your INF code please contact your training program."

  days=9;
  clickedviewIconDetails
  personEventId
  // For StartExam
  family_name: string;
  given_name: string;
  clientIDforStartExam: number = 833;
  roleIDforStartExam: number = 10;
  remainigFiles:string;
  
  //for enable slots
 
  appointment_Leadtime_for_Online=1;
  Alsaka_Leadtime_for_Online =10
  appointment_Leadtime_for_Offline=2;

  stateCodestoClearSSNValidation: Array<String> = ["PA"]


  constructor(private store: Store,private http:HttpClient) {

  }

  public getUserIdToken(): string{
    return sessionStorage.getItem("Token");
  }

  public setUserIdToken(identityToken: string): void {
    sessionStorage.setItem('Token', identityToken);
  }

  public setUserData(data: DecodedIdentityToken): void{
    sessionStorage.setItem('UserData', JSON.stringify(data));
  }

  public getUserData(): DecodedIdentityToken{
    const information = sessionStorage.getItem('UserData');
    const userInfo: DecodedIdentityToken = JSON.parse(information);
    return userInfo;
  }

  public setUserInformation(data: UserDetails): void{
    sessionStorage.setItem('UserInformation', JSON.stringify(data));
  }

  public getUserInformation(): UserDetails{
    const information = sessionStorage.getItem('UserInformation');
    const userInfo: UserDetails = JSON.parse(information);
    return userInfo;
  }

  getPersonTraniningValue(personId:number){
    var url =`${environment.baseUrl}tenantmsvc/api/TrainingInstitute/getTpByPersonId?personid=${personId}`
    return this.http.get(url)
 }



  public clearSessionStorage(): void{
    sessionStorage.clear();
  }

  getStates() {
    var url = `${environment.baseUrl}login/states`;
    return this.http.get(url);
  }

  UpdateUser(body){
    var url =`${environment.baseUrl}candidate/api/Account/updateuser`
    return this.http.post(url,body);
  }


  SelectRole(role, idToken, stateId): void {
    switch (role.roleId) {
      case this.Candidate_RoleID: {
        window.location.href = environment.redirectUrls + environment.candidate + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.SuperAdmin_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.OperationStaff_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.StateClient_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + environment.state +`/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.TrainingInstitute_RoleID: {
        let roleId = stateId ==this.stateIds?environment.sponsor:environment.training
        window.location.href = environment.redirectUrls + environment.client + roleId + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Sponser_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + environment.sponsor + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.SupportStaff: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Employer: {
        window.location.href = environment.redirectUrls + environment.client + environment.Employees + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.FinanaceRole: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Evalator: {
        window.location.href = environment.Evalutor + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}&personId=${role.personId}`;
        break;
      }
      case this.testsite: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Proctor: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Hrms: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.operationaccount: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.PayRole: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.QAC: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      default: {
        // this.services.callSnackbaronError(this.global.Please_check_your_credentials);
        break;
      }
    }
  }
  
}
export enum ExamStatus {
  'scheduling_error'=68,
  "payment_Completed" = 70,
  "Approved" = 7,
  "Exam_Cancelled" = 9,
  "Exam_Completed" = 8,
  "Exam_NoShow" = 10

}

export enum PaymentOptions
{
  PayPal = 'PayPal',
  Credit = 'Credits/Debit Cards',
  ACH = 'ACH Bank'
}
