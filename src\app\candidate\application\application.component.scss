.eligibility {
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 15rem);
        max-width: calc(100vw - var(--sidebar-width) - 40vw);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 15rem);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 15rem);
    }
}
.dashboard {
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 85px);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 85px);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 85px);
    }
}

.content-line {
    display: inline-block;
      width: 220px;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis;
  }

.eligibility-desc {
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 9.1rem);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 9.1rem);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 9.1rem);
    }
}

.state {
    color: var(--text-input);
}

.elgibilityDet {
    color: var(--text-color1);
}

.elgibilityTitle {
    color: var(--text-color3);
}

.horzl {
    border: var(--border-hrz);
}

.add-new {
    background-color: var(--button-background) !important;
    color: var(--button-color) !important;
    padding: 0rem 2rem;
}
.active {
    color: var(--text-color2);
    border: var(--save-draft-border) !important;
}

.mat-stroked-button:not(.mat-button-disabled) {
    border-color: var(--theader);
}
.mat-button.mat-button-disabled.mat-button-disabled{
    opacity: 0.7;
}


.empty-eligible {
    color: var(--text-color1);
}
.right {
    padding-right: 30px !important;
}
:host::ng-deep {
    .mat-form-field-flex {
        height: 39px !important;
    }
}
.fontColor2 {
    color: black;
    font-size: 12px !important;
}

.line {
    line-height: 1.25rem;
}

.size {
    height: 70px;
    width: 70px;
}
.titleFont {
    font-size: 1.3em;
    // font-weight: bolder;
    font-family: "Roboto", sans-serif;
}

::ng-deep .mat-checkbox-layout {
    white-space: inherit!important;
    align-items: flex-start!important;
}

::ng-deep .mat-checkbox-inner-container {
    margin: inherit!important;
    margin-top: 2px!important;
    margin-right: 8px!important;
}

::ng-deep .mat-checkbox-layout .mat-checkbox-label {
    line-height: 16px!important;
}