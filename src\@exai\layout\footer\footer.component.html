<div class="footer w-full jusify-between" fxLayout="row" fxLayoutAlign="start center" exaiContainer>

  <!-- <div class="flex absolute flex copy-left pt-0" fxFlex="auto" fxLayout="row" fxLayoutAlign="start center"
    exaiContainer>

    <a href="https://www.linkedin.com/company/examroomai">
      <img src="assets/img/Icons/LinkedIn.svg" class="pl-8 w-12">
    </a>
    <a href="https://www.facebook.com/examroomai24x7/">
      <img src="assets/img/Icons/FaceBook.svg" class="pl-6 facebook">
    </a>
    <a href="https://twitter.com/examroomai?lang=en">
      <img src="assets/img/Icons/Twitter.svg" class="pl-6 w-10">
    </a>
 
  </div> -->

  <div class="flex copy-right justify-center text-xs" fxHide fxShow.gt-xs>
    Copyright 2014 - {{present_year}} &nbsp; <a class="color" href="https://examroom.ai/" target="_blank">ExamRoom.AI.</a> &nbsp; All rights
    reserved.
  </div>

 <!-- Temproray disable of chat icon on lijo cmd -->
  <!-- <button (mouseover)="helpButtonExpanded.next(true)" (mouseout)="helpButtonExpanded.next(false)" mat-button
    class="flex justify-items-center font-semibold px-1 help-btn justify-end"> -->

    
  <!-- <button mat-button
    class="flex justify-items-center font-semibold px-1 help-btn justify-end"> -->



    <!-- <ng-container *ngIf="!(helpButtonExpanded$ | async)">
      <mat-icon class="chat">chat</mat-icon>
      <span mat-button class="exai-link t-xs" (click)="this.chatPanelExpanded.next(!this.chatPanelExpanded.value)">
        Live Chat
      </span>
    </ng-container> -->


    
    <!-- <ng-container *ngIf="!(helpButtonExpanded$ | async)">
      <div  class="t-xs">
        <mat-icon class="chat1 mr-1">chat</mat-icon>
        <span mat-button class="exai-link t-xs" (click)="this.chatPanelExpanded.next(!this.chatPanelExpanded.value)">
          Live Chat
        </span>
      </div>
    </ng-container>
  </button> -->

  <ng-container *ngIf="(chatPanelExpanded$ | async)">
    <div class="chatContainer bg-white">
      <ng-container *ngTemplateOutlet="chatPanelRef"></ng-container>
    </div>
  </ng-container>

</div>