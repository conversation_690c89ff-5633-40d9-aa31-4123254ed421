import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import {  HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from "src/app/core/token-interceptor.service";
import { ExaminationResultRoutingModule } from "./examination-result-routing.module";
import { ExamDetailsComponent } from "./exam-details/exam-details.component";
import { AreaPerformanceComponent } from "./area-performance/area-performance.component";
import { QuestionPerformanceComponent } from "./question-performance/question-performance.component";
import { ExaminationResultComponent } from "./examination-result.component";
import { MatIconModule } from "@angular/material/icon";

@NgModule({
  declarations: [
    ExamDetailsComponent,
    AreaPerformanceComponent,
    QuestionPerformanceComponent,
    ExaminationResultComponent
  ],
  imports: [
    CommonModule,
    ExaminationResultRoutingModule,
    MatIconModule,
    
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptorService,
      multi: true,
    },
  ]
})
export class ExaminationResultModule {}
