import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpResponse,
} from '@angular/common/http';
import Tracker from '@openreplay/tracker';
import OpenReplay from '@openreplay/tracker'


type ReqRespType = {
  request: HttpRequest<any>,
  response: HttpResponse<any>
}

@Injectable({
  providedIn: 'root'
})
export class ReplaySessionService {
  tracker: OpenReplay|null = null

  constructor() {

  
   }


   openReplay(UserId:string){
    this.tracker = new Tracker({
        projectKey: "Fyy83O5CfBlF29Q7IciW",
        ingestPoint: "https://openreplay.examroom.ai/ingest",
        capturePerformance: true,
        __DISABLE_SECURE_MODE: true,
          captureExceptions:true,
          capturePageLoadTimings:true,
          network:{capturePayload:true}
         
      });
      this.tracker.start()
      this.tracker.setUserID(UserId);
      this.tracker.setUserAnonymousID(UserId)
   }

  sendEventToReplaySession(event: string, params: ReqRespType): void {
    const {request, response} = params

    this.tracker?.event(event + "[request]", {
      method: request.method,
      url: request.url,
      params: request.params
      
    })
    this.tracker?.event(event + "[response]", {
      body: response.body,
      status: response.status,
      headers: response.headers
    })
  }
}