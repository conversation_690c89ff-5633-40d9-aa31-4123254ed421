export interface Cart {
    id: number;
    personTenantRoleId: number;
    totalAmount: number;
    currencyId: number;
    cartStatusId: number;
    isActive: boolean;
    isDeleted: boolean;
    createdBy: number;
    createdOn: Date;
    modifiedBy: number;
    modifiedOn: Date;
}

export interface ExamDetail {
    candidateId: number;
    examId: number;
    slotId: number;
    timeZone: string;
    offSet: string;
    examModeId: number
    personTenantRoleId: number
    examDateTime:string
    totalAttempts?:number
  
  
}

export interface Details {
    personTenantRoleId: number;
    amount: number;
    currencyId: number;
    examDetail: ExamDetail;
    cartItemTypeId: number,
    personEventId?: number,
    voucherCode?: string
    attempts?:number
   
    
}

export const ExamName=[121]