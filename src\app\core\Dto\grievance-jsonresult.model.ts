export class formResponseModel{
    public constructor(init?:Partial<formResponseModel>) {
        Object.assign(this, init);
    }
    valid: boolean=false;
    errorMessages: any[]=[];
    formValue: formValue[]=[];
}

export class formValue{
    public constructor(init?:Partial<formValue>) {
        Object.assign(this, init);
    }
    grievance__grievance_form_grievance: grievance__grievance_form_grievance=new grievance__grievance_form_grievance();
    grievance__grievance_form_q2: grievance__grievance_form_q2=new grievance__grievance_form_q2();
}

export class grievance__grievance_form_grievance	{
    public constructor(init?:Partial<grievance__grievance_form_grievance>) {
        Object.assign(this, init);
    }
    "386e18a44dfd4496aa1db86fdc8c4667": string='';
    "fc528712cbb6431d90aaf8565d8207d8": string='';
}

export class grievance__grievance_form_q2 {
    public constructor(init?: Partial<grievance__grievance_form_q2>) {
        Object.assign(this, init);
    }
    "c8e558db99684e9ebb6d061b6ad28b10": string = '';
    "a2f9e45cff37423e93dd6d4ca6ee721f": string = '';
   " 6f4a9c6e612548ec87e4dd26c8f04ef3": number ;
    "289e9a27b0b14a55894c84a8cd44d780": string = '';
    "c94384c8b92341ac8a2f2156c6dff3d2": string = '';
    "0ac07e47e7ce45f8937f87eddd518ad2": string = '';
    "6c8485ff87e947d7bbbb2c2856396298": number ;
    "5cf74d48eb76452c8101336aaae8afb1": string = '';
    "bc6071278343491d8f2460034c8633e3": string = '';
    "e5ad83e8fd4f4486bba529a8d523b5e1": string = '';
    "3a1b9b7d581c4c388e8c991e566088a7": string = '';
    "a5b5872898e84921ac5b47b42985ea63": Date ;
    "0b8f8e0e823d45e6a0ed78f5883364e4": string = '';
    "47d149a01b0b4c2bbdbba0c7d7740c1d": string = '';
    "d252c8418f414fed96e236fe9779c529": string = '';
    "89bfbab2fdf6474fbdea55dc3b30c1ca": string = '';
    "7d8b16e179394cd9aee5e5e1a1f58f8f": string = '';
    "228fe6a01a68410685afac75749833ce": string = '';
    "250274aeca544ef4ac4f1ee407d3e3c7": string = '';
    "9aac4001738446dd8cb7d7152d041b2e": string = '';
    "f0c1c3c1e88c420fbe52ed6ca8c4555e": string = '';
    "b8aac6fc535a4164a09e2b634858c70c": string = '';
    "bc152dc8b49744a9845af138c9e4339c": string = '';
    "3a5707993ad94e0693ab276366e57fa8": string='' ;
    "fb99089e5ed74d95ad1748e0a4a14ace": string = '';
    "beae06a5688745cc8a49b241875b72f8": string = '';
    "86cf190f61e849b4bfa3bc20a1fd969b": string = '';
    "fdec163833be4b2e9d44b1feacd8dcbc": string = '';
    "696d8b0c796e43078d79467e7ac98cab": string = '';
    "9ef3e90de0e946579ca319bc88fc82d6": string = '';
    "20e39b0e77594ca5af92c9268ec5ab77": string = '';
    "5418390e278e4328ace4290d7bc2f00c": string = '';
    "1cfa98798bc247bead0fb548672533da": string = '';
    "3321d06a714843a4a3d3229510f1d498": string = '';
    "065f817736844a03b58a0e57aec71c94": string = '';
    "8f00b4c521fb462fa52ec4f6afa6fb25": string='';
    "e434da614b854636916c296540468a23": string = '';
    "ecd767f654d945f499011e9e9aa65f62": string = '';
    "f481a11ac6fd4cf692c0c60fa3861412": string = '';
    "bb93ad40b609460fa5aa95fd5baca955": string = '';
    "4419513d668b4a76ac8f13221e58c696": string = '';
    "4d6e0924062c45dbb7fc936fe127ec9a": string = '';
    "e784b182428844bb84fefdbca57df21c": string = '';
    "c88b005b9f6c420696db6cf7ee300f84": string = '';
}