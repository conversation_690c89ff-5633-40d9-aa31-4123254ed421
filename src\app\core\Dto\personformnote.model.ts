export class PersonFormNoteModel{
    public constructor(init?:Partial<PersonFormNoteModel>) {
        Object.assign(this, init);
    }

    id: number = 0;
    title: string = '';
    body: string = '';
    files: PersonFormNoteFileModel[] = [];
    personFormId: number = 0;
    noteTypeId: number = 0;
}

export class PersonFormNoteFileModel{
    public constructor(init?:Partial<PersonFormNoteFileModel>) {
        Object.assign(this, init);
    }

    id: number = 0;
    name: string = '';
    filePath: string = '';
    personFormNoteId: number = 0;
}

