import { createAction, props } from "@ngrx/store";
import { FormTypes } from "src/app/core/Dto/enum";
import { PersonFormLogModel } from "src/app/core/Dto/personform-log.model";
import { Form } from "../../application/application.types";
import { CertificateModel, RequestModel, StateModel } from "./registry.model";


export const loadAll = createAction('[REGISTRY] Load All');

/**
 * LOADING CERTIFICATES
 */

export const loadAllCertificates = createAction(
    '[REGISTRY] Load All Certificates',
    props<{ pageNumber: number, pageSize: number,personId:number }>());

export const loadedAllCertificates = createAction(
    '[REGISTRY] Loaded All Certificates',
    props<{ data: CertificateModel[] }>());

/**
 * LOADING REQUESTS
 */

export const loadAllRequests = createAction(
    '[REGISTRY] Load All Requests',
    props<{ candidateId: number }>());

export const loadedAllRequests = createAction(
    '[REGISTRY] Loaded All Requests',
    props<{ data: RequestModel[] }>());

/**
* LOADING STATES
*/

export const loadAllStates = createAction(
    '[REGISTRY] Load All States',
    props<{ clientId: number }>());

export const loadedAllStates = createAction(
    '[REGISTRY] Loaded All States',
    props<{ data: StateModel[] }>());


/**
* LOADING LOGS
*/

export const loadLogs = createAction(
    '[REGISTRY] Load All Logs',
    props<{ personFormId: number }>());

export const loadedLogs = createAction(
    '[REGISTRY] Loaded All Logs',
    props<{ logs: PersonFormLogModel[] }>());


/**
 * LOAD FORMS ACTIONS
 */
export const getForm = createAction(
    '[REGISTRY] GET form',
    props<{ formTypeId: FormTypes, clientId: number }>()
)

export const getFormReciprocity = createAction(
    '[REGISTRY] GET form',
    props<{ formTypeId: FormTypes, clientId: number, stateId: number }>()
)

export const gotForm = createAction(
    '[REGISTRY] GOT form',
    props<{ form: Form }>()
)


export const getDraftedForm = createAction(
    '[REGISTRY] GET drafted form',
    props<{ personFormId: number }>()
)

export const gotDraftedForm = createAction(
    '[REGISTRY] GOT drafted form',
    props<{ draftedForm: any }>()
)
/**
 * LOAD CERTIFICATE DETAILS
 */

export const loadCertificatePath = createAction(
    '[REGISTRY] GET certificates',
    props<{ Id?:number}>()
)

export const loadedCertificatePath = createAction(
    '[REGISTRY] GOT certificates',
    props<{ certificatePath: any }>()
)

/**
 * SAVE PERSON FORM ACTIONS
 */
export const requestSaveForm = createAction(
    '[REGISTRY] Request Save Person Form',
    props<{ params: any }>()
);

export const postSaveFormSuccess = createAction(
    '[REGISTRY] Request Save Person Form Success',
    props<{ data: any }>()
)

/**
 *  SET SELECTED CERTIFICATE
 */
export const setSelectedCertificate = createAction(
    '[REGISTRY] SETTING SELECTED CERTIFICATE',
    props<{ data: CertificateModel }>()
)
export const setSelectedRequest = createAction(
    '[REGISTRY] SETTING SELECTED REQUEST',
    props<{ data: RequestModel }>()
)

export const setSelectedState = createAction(
    '[REGISTRY] SETTING SELECTED STATE',
    props<{ data: StateModel }>()
)
/**
 *  CATCH ERROR
 */
export const catchErrorRegistry = createAction(
    '[REGISTRY] Got error',
    props<{ error: any }>()
)
export const clearDraftedState = createAction('[CLEAR DRAFTED DATA] Cleared Drafted Data');

export const deleteForm = createAction('[DELETE FORM] Delete Saved FORM', props<{
    candidateId: number,
    personFormId: number
}>());

/**
 * LOADING REQUESTS
 */

 export const DO_Check_Registry = createAction(
    '[REGISTRY] Load All Do_check',
    props<{ candidateId: number }>());

export const DO_Check_Registrys = createAction(
    '[REGISTRY] Loaded All Do_checks',
    props<{ data:boolean }>());

export const updateDeletedRenewedFormState = createAction('[UPDATE RENEWED FORM DELETE STATUS]');
  

export const clearForm = createAction('[CLEAR FORM DATA] Cleared Form Data');

export const makerequestnull = createAction('request call null');
  

export const madenull = createAction('made request call null',props<{pass:any}>());