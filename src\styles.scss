// @import "~simplebar/dist/simplebar.css";
// @import "~highlight.js/styles/androidstudio.css";
// Every external style (External libraries) should be imported by angular.json file
// as well as global styles

// exai Core
@import "./@exai/styles/core";
@import "./app/core/examroom-formbuilder/form-builder-styles.scss";
// @import '~@angular/material/prebuilt-themes/indigo-pink.css';
/*
  You can override any CSS Variable or style here
  Example:

  :root {
   --sidenav-background: #333333;
  }

  All possible variables can be found in @exai/styles/core or by simply inspecting the element you want to change
  in Chrome/Firefox DevTools
*/

/**
  Uncomment the below code and adjust the values to fit your colors, the application will automatically adjust
 */

::ng-deep .no-padding-dialog .mat-dialog-container {
  padding: 0 !important;
}

.blue-snackbar {
  // background: #209e91;
  color: var(--text-color2);
}

.red-snackbar {
  // background: #F44336;
  color: #f44336;
}

* {
  font-family: "Roboto", sans-serif;
}
:root {
  // --color-primary: blue;
  // --color-primary-contrast: white;
  // --color-accent: yellow;
  // --color-accent-contrast: black;
  // --color-warn: yellow;
  // --color-warn-contrast: black;
}
