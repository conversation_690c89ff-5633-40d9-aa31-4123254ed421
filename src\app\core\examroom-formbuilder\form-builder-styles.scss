// use this file to add custom scss to be applied to form-builder
.bg-gray-text {
    background: #f9f9f9 !important;
}

#dynamicForm .mat-expansion-panel-header {
    // color: #209291;
    color: #a7a8ac;
    background: #f9f9f9;
    box-shadow: none;
}

#dynamicForm .mat-radio-label {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    white-space: normal;
    vertical-align: middle;
    width: fit-content;
}

// #dynamicForm .mat-radio-label-content {
//     padding-top: 3px;
// }

#dynamicForm .mat-radio-inner-circle {
    height: 16px;
    width: 16px;
}

#dynamicForm .mat-radio-outer-circle {
    height: 16px;
    width: 16px;
}

.t-xs {
    font-size: 0.65rem;
}

.f-medium {
    color: #11263c;
}

.t-gray {
    color: #7d7d7d;
}
.t-blue{
    color: #c82c2f;
}

#dynamicForm .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.2em 0 0.8em 0;
    border-top: 0.75em solid transparent;
}

#dynamicForm .mat-form-field {
    margin-bottom: 4px;
    max-width: 24.5rem;
    width: -webkit-fill-available;
}

#dynamicForm .mat-form-field-label {
    font-size: smaller;
}

#dynamicForm .mat-form-field-required-marker {
    display: none;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle :hover,
:focus {
    background-color: none !important;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle,
.mat-radio-outer-circle :hover,
:focus {
    background-color: none !important;
}

.mat-radio-button-checked:not(.mat-radio-button-disabled).mat-accent .mat-ripple-element,
.mat-radio-button:active:not(.mat-radio-button-disabled).mat-accent .mat-ripple-element {
    background: none!important;
}

.mat-radio-button .mat-ripple-element {
    background: none!important;
}

//changes for input text
#dynamicForm input.mat-input-element {
    font-size: small;
    color: #7D7D7D;
    width:-webkit-fill-available;
}

#dynamicForm .mat-select-value-text {
    font-size: small;
    color: #7D7D7D;
}

#dynamicForm textarea.mat-input-element.cdk-textarea-autosize {
    font-size: small;
    color: #7D7D7D;
}

#dynamicForm .mat-select-panel .mat-optgroup-label,
.mat-select-panel .mat-option {
    margin: inherit;
}

#dynamicForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #7D7D7D;
}

#dynamicForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: var(--text-color2);
}

#dynamicForm p {
    font-size: 0.75rem !important;
}

#dynamicForm .mat-expansion-panel-body {
    padding: 10px 24px 16px;
    overflow: auto;
}

.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,
.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,
.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap {
    border-width: 1px !important;
}

.mat-error {
    display: block;
    font-size: 0.65rem;
}

// .mat-form-field-wrapper {
//     padding-bottom: 1.1em;
// }

.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
.mat-checkbox-checked.mat-accent .mat-checkbox-background {
    // background-color: #7D7D7D !important;
    background-color: var(--text-color2)!important;
}

#dynamicForm .mat-form-field-appearance-outline .mat-form-field-subscript-wrapper {
    padding: 0;
}

#dynamicForm .mat-button-ripple-round {
    --tw-shadow: none !important;
    --tw-ring-offset-shadow: none;
    --tw-ring-shadow: none;
}

#questionGroupPopup .mat-checkbox-label {
    line-height: 24px;
    white-space: break-spaces!important;
}