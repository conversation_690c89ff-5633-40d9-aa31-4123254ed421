import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  DynamicFormGroupModel,
  DynamicSelectModel,
  DynamicInputModel,
  DynamicFormHook,
  DynamicCheckboxModel,
  DynamicRadioGroupModel,
  DynamicDatePickerModel,
  DynamicFileUploadModel,
  DynamicSliderModel,
  DynamicSwitchModel,
  DynamicTextAreaModel,
  DynamicFormArrayModel,
  MATCH_VISIBLE,
  DynamicFormControlModel,
  MATCH_REQUIRED,
  DynamicRatingModel,
  DynamicColorPickerModel,
} from "@ng-dynamic-forms/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";

import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { FormBuilderPopupComponent } from "../form-builder-popup/form-builder-popup.component";
import { QuesGrpPopupComponent } from "../ques-grp-popup/ques-grp-popup.component";
import { feildType, form, section, AbRelatedFeilds, parsingMetaData, sectionsEdit, defaultFeildCss, replicable, QInstruc, fetchOptionsFromApi, DateAttr } from '../form-builder.types';

import { FormBuilderService } from "../form-builder.service";
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { FormBuilderComponent } from '../form-builder.component';
import { customCheckboxRequiredValidator } from '../formBuilder.validators';

@Component({
  selector: 'exai-true-form-component',
  templateUrl: './true-form-component.component.html',
  styleUrls: ['./true-form-component.component.scss']
})
export class TrueFormComponentComponent implements OnInit, OnDestroy {

  constructor(
    private dialog: MatDialog,
    public formBuilderService: FormBuilderService,
    private formBuilder: FormBuilder,
  ) {
    this.jsonTOLoad = this.formBuilder.group({
      formJSON: ["", [Validators.required]],
      editData: ["", [Validators.required]]
    })
  }

  showLoadTextArea: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  $showLoadTextArea: Observable<boolean> = this.showLoadTextArea.asObservable();

  jsonTOLoad: FormGroup;

  createNewQuestionGroupSub: Subscription;
  createNewFormOrSectionSub: Subscription;

  ngOnInit(): void { }

  // type 1 = create new form, 2 = create new section
  // type 3= edit form, type 4 = edit section
  public createNewFormOrSection(type: number, sectionIndex: number = null) {
    try {
      const dialogConfig = new MatDialogConfig();
      dialogConfig.hasBackdrop = true;
      dialogConfig.minWidth = "75vw";
      dialogConfig.minHeight = "84vh";
      dialogConfig.disableClose = true;
      dialogConfig.id = "form-builder-popup";
      dialogConfig.data = {
        type,
        id: type == 1 ? "" : [3, 4].includes(type) ?
          (type == 3 ? this.formBuilderService.genesisForm.id : this.formBuilderService.genesisForm.sections[sectionIndex].id) : this.formBuilderService.genesisForm.id + '_',
        name: [3, 4].includes(type) ?
          (type == 3 ? this.formBuilderService.genesisForm.name : this.formBuilderService.genesisForm.sections[sectionIndex].name) : null,
        description: [3, 4].includes(type) ?
          (type == 3 ? this.formBuilderService.genesisForm.description : this.formBuilderService.genesisForm.sections[sectionIndex].description) : null,
        instructions: [3, 4].includes(type) ?
          (type == 3 ? this.formBuilderService.genesisForm.instructions : this.formBuilderService.genesisForm.sections[sectionIndex].instructions) : null,
        showInstructionInIButton: [3, 4].includes(type) ?
          (type == 3 ? this.formBuilderService.genesisForm.showInstructionInIButton : this.formBuilderService.genesisForm.sections[sectionIndex].showInstructionInIButton) : null,
        fetchFromApi: type == 3 ? this.formBuilderService.genesisForm.fetchFromApi : null,
        requestDetails: type == 3 ? this.formBuilderService.genesisForm.requestDetails : null,
      };

      const dialogRef = this.dialog.open(
        FormBuilderPopupComponent,
        dialogConfig
      );
      this.createNewFormOrSectionSub = dialogRef.afterClosed().subscribe((data: any) => {
        if (data) {
          switch (type) {
            case 1:
              this.formBuilderService.genesisForm = new form(data);
              break;
            case 2:
              this.formBuilderService.genesisForm.sections.push(new section(data));
              this.formBuilderService.editData.sections.push(new sectionsEdit());
              break;
            case 3:
              this.formBuilderService.genesisForm.id = data.id;
              this.formBuilderService.genesisForm.name = data.name;
              this.formBuilderService.genesisForm.description = data.description;
              this.formBuilderService.genesisForm.instructions = data.instructions;
              this.formBuilderService.genesisForm.showInstructionInIButton = data.showInstructionInIButton;
              this.formBuilderService.genesisForm.fetchFromApi = data.fetchFromApi;
              this.formBuilderService.genesisForm.requestDetails = data.requestDetails;
              break;
            case 4:
              // this.formBuilderService.genesisForm.sections.splice(sectionIndex, 1, new section(data))
              this.formBuilderService.genesisForm.sections[sectionIndex].id = data.id;
              this.formBuilderService.genesisForm.sections[sectionIndex].name = data.name;
              this.formBuilderService.genesisForm.sections[sectionIndex].description = data.description;
              this.formBuilderService.genesisForm.sections[sectionIndex].instructions = data.instructions;
              this.formBuilderService.genesisForm.sections[sectionIndex].showInstructionInIButton = data.showInstructionInIButton;
              break;
            default: console.error('no case matched');
          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  }
  public createNewQuestionGroup(sectionIndex: number, quesGrpIndex: number = null) {
    try {
      const dialogConfig = new MatDialogConfig();
      dialogConfig.hasBackdrop = true;
      dialogConfig.minWidth = "90vw";
      dialogConfig.minHeight = "95vh";
      dialogConfig.disableClose = true;
      dialogConfig.id = "ques-builder-popup";

      dialogConfig.data = {
        sectionIndex,
        id: this.formBuilderService.genesisForm.sections[sectionIndex].id + '_',
        feildCssClasses: defaultFeildCss,
        quesGrpClasses: this.formBuilderService.defaultQGPcss,
      }
      if (quesGrpIndex != null) {
        dialogConfig.data['editData'] = this.formBuilderService.editData.sections[sectionIndex].QuestionGroups[quesGrpIndex];
      }
      const dialogRef = this.dialog.open(QuesGrpPopupComponent, dialogConfig);

      this.createNewQuestionGroupSub = dialogRef.afterClosed().subscribe((data: any) => {
        if (data) {
          if (data.quesGrpDetails.replicable && (quesGrpIndex == null)) {
            this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds.push(
              <replicable>{
                parentModelID: data.quesGrpDetails.parentModelID,
                modelID: data.quesGrpDetails.id,
                removeLabel: data.quesGrpDetails.removeLabel,
                addLabel: data.quesGrpDetails.addLabel,
                totalCount:data.quesGrpDetails.totalCount
              });
          }
          else if (data.quesGrpDetails.replicable && (quesGrpIndex != null)) {
            let index = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds.findIndex((x: replicable) => { return x.modelID == data.quesGrpDetails.id });
            if (index > -1) {
              this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds[index] = 
                <replicable>{
                  parentModelID:data.quesGrpDetails.parentModelID,
                  modelID: data.quesGrpDetails.id,
                  removeLabel: data.quesGrpDetails.removeLabel,
                  addLabel: data.quesGrpDetails.addLabel,
                  totalCount:data.quesGrpDetails.totalCount
                };
            }
            else {
              this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds.push(
                <replicable>{
                  parentModelID: data.quesGrpDetails.parentModelID,
                  modelID: data.quesGrpDetails.id,
                  removeLabel: data.quesGrpDetails.removeLabel,
                  addLabel: data.quesGrpDetails.addLabel,
                  totalCount:data.quesGrpDetails.totalCount
                });
            }
          }
          if (data.quesGrpDetails.showInstructions) {
            this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions.push(<QInstruc>{
              qgpID: data.quesGrpDetails.id,
              label: data.quesGrpDetails.label,
              instructions: data.quesGrpDetails.instructions,
            })
          }
          if (data.quesGrpDetails.AbRelated && (quesGrpIndex == null)) {
            this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.ABrelatedFeilds.push(
              new AbRelatedFeilds(data.quesGrpDetails.relatedFeildID, data.quesGrpDetails.id));
          }

          quesGrpIndex != null ?
            this.formBuilderService.editData.sections[sectionIndex].QuestionGroups.splice(quesGrpIndex, 1, data)
            :
            this.formBuilderService.editData.sections[sectionIndex].QuestionGroups.push(data);


          //this statement is to make sure the optionsFromApi array is of the same length as QuestionGroups
          // not needed i think
          if (quesGrpIndex == null)
            this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi.push([]);


          let temp: DynamicFormControlModel = data.quesGrpDetails.replicable ?
            new DynamicFormArrayModel({
              id: data.quesGrpDetails.id,
              name: data.quesGrpDetails.name,
              label: data.quesGrpDetails.showInstructions ? "" : data.quesGrpDetails.label,
              initialCount: data.quesGrpDetails.initialCount,
              groupFactory: () => {
                return this.getFeildValue(data.selectedFeildTypes, data.feildDetails, sectionIndex, data.quesGrpDetails.id, quesGrpIndex);
              }
            })
            :
            new DynamicFormGroupModel({
              id: data.quesGrpDetails.id,
              name: data.quesGrpDetails.name,
              label: data.quesGrpDetails.showInstructions ? '' : data.quesGrpDetails.label,
              group: this.getFeildValue(
                data.selectedFeildTypes,
                data.feildDetails,
                sectionIndex,
                data.quesGrpDetails.id,
                quesGrpIndex
              ),
            })

          quesGrpIndex != null ?
            this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroups.splice(quesGrpIndex, 1, temp)
            :
            this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroups.push(temp);

          this.formBuilderService.genesisForm.formLayout[data.quesGrpDetails.id] = data.quesGrpDetails.styling;
          for (let feildDetail of data.feildDetails) {
            this.formBuilderService.genesisForm.formLayout[feildDetail.id] = feildDetail.styling;
          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  }

  private getFeildValue(selectedFeildType: Array<feildType>, feildDetails: Array<any>, sectionIndex: number = -1, parentQuesGrpId: string = null, quesGrpIndex: number) {
    let retValue = [];
    for (let i = 0; i < feildDetails.length; i++) {
      let validationsAndErrors = this.getValidations(feildDetails[i]);
      let relations = [];

      if (feildDetails[i].hidden) {
        relations.push({
          match: MATCH_VISIBLE,
          when: [{ id: feildDetails[i].relatedFeildId, value: feildDetails[i].relatedFeildValueAtWhichThisFeildHasToBeShown }]
        })
        if (feildDetails[i].required) {
          relations.push({
            match: MATCH_REQUIRED,
            when: [{ id: feildDetails[i].relatedFeildId, value: feildDetails[i].relatedFeildValueAtWhichThisFeildHasToBeShown }]
          })
        }
      }
      this.filterAndAmmendMalformedJSON(feildDetails[i], sectionIndex, parentQuesGrpId, quesGrpIndex, selectedFeildType[i]);
      switch (selectedFeildType[i].id) {
        
        // different types of input feilds
        case 1:
        case 2:
        case 3:
        case 16:
        case 4:
          retValue.push(
            new DynamicInputModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              label: feildDetails[i].label,
              name: feildDetails[i].name,
              placeholder: feildDetails[i].placeholder,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              multiple: selectedFeildType[i].multiple,
              inputType: selectedFeildType[i].subType,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations,
              additional: {
                appearance: 'outline'
              }
            })
          );
          break;
        // different types of select feilds
        case 5:
        case 6:
          retValue.push(
            new DynamicSelectModel<any>({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              label: feildDetails[i].label,
              name: feildDetails[i].name,
              placeholder: feildDetails[i].placeholder,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              multiple: selectedFeildType[i].multiple,
              options: feildDetails[i].options,
              filterable: feildDetails[i].filterable,
              prefix: feildDetails[i].prefix,
              suffix: feildDetails[i].suffix,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations,
              additional: {
                appearance: 'outline'
              }
            })
          );
          break;
        case 7:
          retValue.push(
            new DynamicCheckboxModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              label: feildDetails[i].label,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations
            })
          );
          break;
        case 8:
          retValue.push(
            new DynamicRadioGroupModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              label: feildDetails[i].label,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              options: feildDetails[i].options,
              value: feildDetails[i].options.filter((x: any) => { return x.defaultValue })[0]?.value,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations
            })
          );
          break;
        case 9:
          retValue.push(
            new DynamicDatePickerModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              label: feildDetails[i].label,
              placeholder: feildDetails[i].placeholder,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations,
              //input : disabled,
              //disabled: true,
              readOnly: true,
              additional: {
                appearance: 'outline'
              }
            })
          );
          break;
        case 10:
          retValue.push(
            new DynamicFileUploadModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              label: feildDetails[i].label,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              url: feildDetails[i].url,
              removeUrl: feildDetails[i].removeUrl,
              minSize: Number(feildDetails[i].minSize),
              maxSize: Number(feildDetails[i].maxSize),
              accept: feildDetails[i].accept.split(","),
              showFileList: feildDetails[i].showFileList ? feildDetails[i].showFileList : false,
              autoUpload: feildDetails[i].autoUpload ? feildDetails[i].autoUpload : false,
              multiple: feildDetails[i].multiple ? feildDetails[i].multiple : false,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations,
              additional: {
                appearance: 'outline'
              }
            })
          );
          break;
        case 11:
          retValue.push(
            new DynamicSliderModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              controlTooltip: feildDetails[i].controlTooltip,
              updateOn: DynamicFormHook[feildDetails[i].updateOn],
              min: feildDetails[i].min,
              max: feildDetails[i].max,
              
              step: feildDetails[i].step,
              vertical: feildDetails[i].vertical,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations
            })
          );
          break;
        case 12:
          retValue.push(
            new DynamicSwitchModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              label: feildDetails[i].label,
              offLabel: feildDetails[i].offLabel,
              onLabel: feildDetails[i].onLabel,
              labelPosition: feildDetails[i].labelPosition,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations
            })
          );
          break;
        case 14:
          retValue.push(
            new DynamicTextAreaModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              placeholder: feildDetails[i].placeholder,
              label: feildDetails[i].label,
              cols: Number(feildDetails[i].cols),
              rows: Number(feildDetails[i].rows),
              wrap: feildDetails[i].wrap,
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations,
              additional: {
                appearance: 'outline'
              }
            })
          );
          break;
        case 15:
          retValue.push(
            new DynamicInputModel({
              required: feildDetails[i].required,
              id: feildDetails[i].id,
              name: feildDetails[i].name,
              label: feildDetails[i].label,
              placeholder: feildDetails[i].placeholder,
              inputType: "time",
              validators: validationsAndErrors[0],
              errorMessages: validationsAndErrors[1],
              relations: relations,
              additional: {
                appearance: 'outline'
              }
            })
          );
            break;
            case 17:
              retValue.push(
                 new DynamicRatingModel({
                    required: feildDetails[i].required,
                    id: feildDetails[i].id,
                    name: feildDetails[i].name,
                    label: feildDetails[i].label,
                    updateOn: DynamicFormHook[feildDetails[i].updateOn],
                    validators: validationsAndErrors[0],
                    errorMessages: validationsAndErrors[1],
                    disabled:feildDetails[i].disable,
                    hidden:feildDetails[i].hidden,
                    relations: relations,
                    additional: {
                      appearance: 'outline',
                     
                    }
                  })
              );
              break;
              case 18:
              retValue.push(
                 new DynamicColorPickerModel({
                    required: feildDetails[i].required,
                    id: feildDetails[i].id,
                    name: feildDetails[i].name,
                    label: feildDetails[i].label,
                    updateOn: DynamicFormHook[feildDetails[i].updateOn],
                    validators: validationsAndErrors[0],
                    errorMessages: validationsAndErrors[1],
                    relations: relations,
                    additional: {
                      appearance: 'outline',
                     
                    }
                  })
              );
              break;
        }
      }
    
  
    return retValue;
  }

  private filterAndAmmendMalformedJSON(feildDetailsAtI: any, sectionIndex: number, parentQuesGrpId: string, quesGrpIndex: number, selectedFeildTypeAtI:feildType) {
    if (selectedFeildTypeAtI.id == 9) {
      var dateAttrObj: DateAttr = {
        feildID: feildDetailsAtI.id,
        pastDisabled: feildDetailsAtI.pastDisabled,
        displayCurrentDate:feildDetailsAtI.displayCurrentDate,
        displayClosedDate:feildDetailsAtI.displayClosedDate,
        futureDisabled: feildDetailsAtI.futureDisabled,
        todateDisabled:feildDetailsAtI.todateDisabled,
        futureOffset: feildDetailsAtI.futureOffset,
        pastOffset: feildDetailsAtI.pastOffset,
        disableBasedOnApiRes: feildDetailsAtI.disableBasedOnApiRes,
        requestDetails: null,
        parentQuesGrpId,
      
      } 
      if (feildDetailsAtI.disableBasedOnApiRes)
        dateAttrObj.requestDetails = {
          fetchUrl: feildDetailsAtI.fetchUrl,
          requestBody: null,
          requestParams: feildDetailsAtI.requestParams,
          method: feildDetailsAtI.method,
        }
      var index = -1;
      if(this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes)
        index = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes.findIndex((x: DateAttr) => { return x.feildID == feildDetailsAtI.id });
      else
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes = [];
      if (index == -1)
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes.push(dateAttrObj)
      else
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes[index] = dateAttrObj;
    }
    for (let i = 0; i < this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi.length; i++) {
      if (this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[i].length > 0) {
        var arrayToTrackIds = [];
        var arrayOfIndexesToDelete = [];
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[i].forEach(
          (element: fetchOptionsFromApi, index: number) => {
            if (arrayToTrackIds.includes(element.feildID))
              arrayOfIndexesToDelete.push(index);
            else arrayToTrackIds.push(element.feildID);
          })
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[i] =
          this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[i].filter((x: fetchOptionsFromApi, index: number) => {
            return !arrayOfIndexesToDelete.includes(index);
          })

      }
    }
    if (feildDetailsAtI.fetchOptionsFromApi) {
      if (quesGrpIndex == null)
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi
        [this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi.length - 1].push({
          feildID: feildDetailsAtI.id,
          apiUrl: feildDetailsAtI.apiUrl,
          method: feildDetailsAtI.method,
          fetchOptionsBasedOnResponseAnotherField: feildDetailsAtI.fetchOptionsBasedOnResponseAnotherField,
          relatedFieldId: feildDetailsAtI.fetchOptionsRelatedFeildId,
          requestDetails: {
            fetchUrl: feildDetailsAtI.fetchUrl.split('/'),
            requestBody: null,
            requestParams: feildDetailsAtI.requestParams,
            method: feildDetailsAtI.method,
          }
        })
      else {
        let index = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[quesGrpIndex].findIndex((x: fetchOptionsFromApi) => { return x.feildID == feildDetailsAtI.id });
        if (index > -1)
          this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi
          [quesGrpIndex][index] = {
            feildID: feildDetailsAtI.id,
            apiUrl: feildDetailsAtI.apiUrl,
            method: feildDetailsAtI.method,
            fetchOptionsBasedOnResponseAnotherField: feildDetailsAtI.fetchOptionsBasedOnResponseAnotherField,
            relatedFieldId: feildDetailsAtI.fetchOptionsRelatedFeildId,
            requestDetails: {
              fetchUrl: feildDetailsAtI.fetchUrl.split('/'),
              requestBody: null,
              requestParams: feildDetailsAtI.requestParams,
              method: feildDetailsAtI.method,
            }
          }
        else
          this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi
          [quesGrpIndex].push({
            feildID: feildDetailsAtI.id,
            apiUrl: feildDetailsAtI.apiUrl,
            method: feildDetailsAtI.method,
            fetchOptionsBasedOnResponseAnotherField: feildDetailsAtI.fetchOptionsBasedOnResponseAnotherField,
            relatedFieldId: feildDetailsAtI.fetchOptionsRelatedFeildId,
            requestDetails: {
              fetchUrl: feildDetailsAtI.fetchUrl.split('/'),
              requestBody: null,
              requestParams: feildDetailsAtI.requestParams,
              method: feildDetailsAtI.method,
            }
          })
      }
    }
    // the following logic is added to make sure that the same feild is not pushed again and again into the parsingMetaData
    // this is done as a temporary fix, can removed/replaced later
    var arrayToTrackIds = [];
    var arrayOfIndexesToDelete = [];
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.forEach(
      (element: parsingMetaData, index: number) => {
        if (arrayToTrackIds.includes(element.feildID))
          arrayOfIndexesToDelete.push(index);
        else arrayToTrackIds.push(element.feildID);
      })
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData =
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.filter((x: parsingMetaData, index: number) => {
        return !arrayOfIndexesToDelete.includes(index);
      })
    if (this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.find((x: parsingMetaData) => { return x.feildID == feildDetailsAtI.id })) {
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData[
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.findIndex((x: parsingMetaData) => { return x.feildID == feildDetailsAtI.id })
      ] = {
        feildID: feildDetailsAtI.id,
        orientation: feildDetailsAtI.orientation,
        mergeToNextRow: feildDetailsAtI.mergeToNextRow,
        colspan: feildDetailsAtI.colspan,
        parentQuesGrpId,
        identifier: feildDetailsAtI.identifier,
        prefillValue: feildDetailsAtI.prefillValue,
        elementPropertyToBeExtracted: feildDetailsAtI.elementPropertyToBeExtractedForPrefill
      };
    }
    else
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.push({
        feildID: feildDetailsAtI.id,
        orientation: feildDetailsAtI.orientation,
        mergeToNextRow: feildDetailsAtI.mergeToNextRow,
        colspan: feildDetailsAtI.colspan,
        parentQuesGrpId,
        identifier: feildDetailsAtI.identifier,
        prefillValue: feildDetailsAtI.prefillValue,
        elementPropertyToBeExtracted: feildDetailsAtI.elementPropertyToBeExtractedForPrefill
      })
    //fixing ques grp instructions
    var arrayToTrackIds = [];
    var arrayOfIndexesToDelete = [];
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions?.forEach(
      (element: QInstruc, index: number) => {
        if (arrayToTrackIds.includes(element.qgpID))
          arrayOfIndexesToDelete.push(index);
        else arrayToTrackIds.push(element.qgpID);
      })
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions =
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions?.filter((x: QInstruc, index: number) => {
        return !arrayOfIndexesToDelete.includes(index);
      })
    
  }

  private getValidations(feildDetails: any) {
    //  retValue[0] is for validations object, retValue[1] is for errorMessages object
    let retValue = [{}, {}];
    if (feildDetails.required) {
      retValue[1]["required"] = "This field is required";
    }
    for (let validation of feildDetails.validations) {
      if (validation.validationSelected == true) {
        retValue[0][validation.validationName] =
          validation.inputVal === "" ? null : validation.inputVal;
        retValue[1][validation.validationName] = validation.errorMessage;
      }
    }
    return retValue;
  }

  public removeQuesGroup(sectionIndex: number, quesGrpIndex: number): void {
    var quesGrpId = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroups[quesGrpIndex].id;
    if (this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroups[quesGrpIndex].type == "ARRAY") {
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds.splice(
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds
          .indexOf(this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds.filter(
            (x: replicable) => {
              return x.modelID == quesGrpId
            })[0]), 1);
    }
    if (this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions.find((x: QInstruc) => {
      return x.qgpID == quesGrpId
    })) {
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions.splice(
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions
          .indexOf(this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.qGpWithInstructions.filter(
            (x: QInstruc) => {
              return x.qgpID == quesGrpId
            })[0]), 1);
    }
    let ABrelatedFeildsOfThis = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.ABrelatedFeilds.filter((x: AbRelatedFeilds) => {
      return x.feild2ID == quesGrpId || x.feild1ID == quesGrpId;
    })
    if (ABrelatedFeildsOfThis.length > 0) {
      for (let feild of ABrelatedFeildsOfThis) {
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.ABrelatedFeilds.splice(
          this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.ABrelatedFeilds.indexOf(feild), 1);
      }
    }
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData =
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.filter((x: parsingMetaData) => { return x.parentQuesGrpId != quesGrpId; });

    if (this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes)
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes =
      this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes.filter((x: DateAttr) => { return x.parentQuesGrpId != quesGrpId; });
    
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroups.splice(quesGrpIndex, 1);
    this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi.splice(quesGrpIndex, 1);
    this.formBuilderService.editData.sections[sectionIndex].QuestionGroups.splice(quesGrpIndex, 1);
  }

  drop(event: CdkDragDrop<section[]>, index: number) {
    moveItemInArray(this.formBuilderService.genesisForm.sections[index].QuestionGroups, event.previousIndex, event.currentIndex);
    moveItemInArray(this.formBuilderService.editData.sections[index].QuestionGroups, event.previousIndex, event.currentIndex);
  }

  relocateSection(curSectionIndex: number, newSectionIndex: number) {
    if (newSectionIndex >= 0 && newSectionIndex < this.formBuilderService.genesisForm.sections.length) {
      moveItemInArray(this.formBuilderService.genesisForm.sections, curSectionIndex, newSectionIndex);
      moveItemInArray(this.formBuilderService.editData.sections, curSectionIndex, newSectionIndex);
    }
  }

  loadJSON() {
    FormBuilderComponent.existingForm = JSON.parse(this.jsonTOLoad.value.formJSON);
    FormBuilderComponent.existingEditData = JSON.parse(this.jsonTOLoad.value.editData);
    this.formBuilderService.genesisForm = FormBuilderComponent.parseform(FormBuilderComponent.existingForm);
    this.formBuilderService.editData = FormBuilderComponent.existingEditData;
  }

  ngOnDestroy() {
    this.createNewQuestionGroupSub?.unsubscribe();
    this.createNewFormOrSectionSub?.unsubscribe();
  }

}
