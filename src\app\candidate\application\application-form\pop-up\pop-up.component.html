<div class="p-2 touch-auto overflow-auto" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div *ngIf="data.message !='Would you like to schedule an Online Examination instead?'" class="flex justify-end cursor-pointer" fxLayout="row">
                <mat-icon class="text-sm flex justify-end"  mat-dialog-close>close</mat-icon>
            </div>
        </div>
    </div>
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div class="flex justify-center submit" fxLayout="row">
                <h3> <b>{{data.title}}</b></h3>
            </div>
        </div>
    </div>
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div class="pb-4 flex justify-center t-xs confirm items-center" fxLayout="row">
            <h6>{{data.message}}</h6>
        </div>  
    </div>
    <div class="pb-4" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="16px">
        <div class="" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
            <div class="" fxLayout="column">
                <button mat-dialog-close class="btn-3" mat-button>{{data.cancelButton}}</button>

            </div>
        </div>
        <div class="" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="2 / -1">
            <div class="" fxLayout="column">
                <button (click)="submitApp()" class="btn-1" mat-button color=primary>{{data.OkButton}}</button>
            </div>
        </div>
    </div>
</div>