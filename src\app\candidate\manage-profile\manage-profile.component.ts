import { HttpErrorResponse } from '@angular/common/http';
import { Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Action, select, Store } from '@ngrx/store';
import { PersonDetails } from 'src/app/core/Dto/persondetail';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http-services/http.service';
import * as actions from './state/profile-form.action';
import { ProfileState } from './state/profile-form.model';
import { MANAGE_PROFILE_STATE } from './state/profile-form.state';
import * as moment from 'moment';
import { ManageProfileService } from './manage-profile.service';
import { AppService } from 'src/app/app.service';
import { Observable, Subscription } from 'rxjs';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { DataService } from '../shared/data-service';
import { PersonForm } from '../application/application.types';
import { FormTypes } from '../forms-wrapper/forms-wrapper.types';
import { DatePipe } from '@angular/common';
import { FormStatuses } from 'src/app/core/Dto/enum';
import { getUpcomingExams } from './state/profile-form.selectors';

@Component({
  selector: 'exai-manage-profile',
  templateUrl: './manage-profile.component.html',
  styleUrls: ['./manage-profile.component.scss']
})
export class ManageProfileComponent implements OnInit, OnDestroy {
  @ViewChild('changePhoto') changePhoto: ElementRef;
  personDetails: PersonDetails;
  profileUrl: string = 'assets/img/profile.svg';
  items: Array<PersonForm> = [];
  personForm: Array<any> = [];
  disableEditProfileBtn: boolean = false;
  manageProfile$: Observable<ProfileState>;
  subscription: Subscription;
  formattedDOB;
  formstatusEnum : typeof FormStatuses;

  constructor(private router: Router, private http: HttpService, private global: GlobalUserService,
    private store: Store<{ MANAGE_PROFILE_STATE: ProfileState}>,
    private _service: ManageProfileService,
    private _appService: AppService,
    
   
   
    private snackbar: SnackbarService,
    private dataService: DataService) {
    this.store.dispatch<Action>(actions.getPersonDetails());
    this.store.dispatch<Action>(actions.getPersonForm());
    this.store.dispatch<Action>(actions.clearDemographicData());
    this.store.dispatch<Action>(actions.clearPersonFormlogsState());
    this.formstatusEnum = FormStatuses;
  }

  ngOnInit(): void {
    this.manageProfile$ = this.store.pipe(select(MANAGE_PROFILE_STATE));
    this.subscription = this.manageProfile$.subscribe((data: ProfileState) => {
      const items: Array<PersonForm> = [];
      data.personDetails == null ? this.store.dispatch<Action>(actions.getPersonDetails()) : this.personDetails = data.personDetails;
      if(data.personDetails != null){
        //(date cant assigned to string) formattedDOB is a variable, so date is assigned to formattedDOB
        // this.formattedDOB = moment(data.personDetails.dateofBirth).format('MM/DD/YYYY');
        var datePipe = new DatePipe("en-US");
        this.formattedDOB = datePipe.transform(data.personDetails.dateofBirth, 'MM/dd/yyyy', '+0000');
     }
      data.personForm == null ? this.store.dispatch<Action>(actions.getPersonForm()) : (this.personForm = data.personForm, this.personForm.forEach(ele => items.push(ele)), this.items = items);
      this.store.select(getUpcomingExams).subscribe((data)=>{
        if(data.length == 0){
          this.disableEditProfileBtn = true
        }
    })
      if (data.upcomingExams == null) {
        this.store.dispatch<Action>(actions.getupcomingExam({ candidateId: this.global.candidateId }))
      } else if (data.upcomingExams.length > 0) {
        const info = data.upcomingExams.find(ele => ele.isPrimary == true);
        if (info) {
        // days in below line is getting from service.ts , 9 is stored in service.ts
          const startDate = moment(info.examDateTimeUtc).subtract(this.global.days, 'days').format('DD-MMM-YYYY');
          const endDate = moment(info.examDateTimeUtc).format('DD-MMM-YYYY');
          const today = moment(new Date()).format('DD-MMM-YYYY');
          if (moment(today).isBefore(startDate) || moment(today).isAfter(endDate)) {
            //condition satisfied if today's date is not between 9 of exam
            this.disableEditProfileBtn = true;
            this._service.editStatus = false;
          } else {
            this.disableEditProfileBtn = false;
            this._service.editStatus = true;
          }
        }
      }
    });
  }

  public profilePicUpload(event: Event | any): void {
    
    const files = event.target.files as Array<File>;
    let userInfo = this.global.getUserInformation();
    if (!files && !userInfo.personId) return;
    const form = new FormData();
   let supportedFiles =files[0].name.split(".");
   let lastElement = supportedFiles[supportedFiles.length - 1]
   if(lastElement == "png" || lastElement == "jpg" || lastElement == "jpeg" || lastElement == "PNG" || lastElement == "JPEG" || lastElement == "JPG"){
    form.append('File', files[0], files[0].name);
    form.append("UserId", userInfo.personId.toString());
    form.append('ProfileUrl', files[0].name);
    this.http.uploadProfilePic(form, userInfo.personId).subscribe((data: boolean) => {
      this.snackbar.callSnackbaronSuccess('Successfully uploaded.');
      data ? this.store.dispatch<Action>(actions.getPersonDetails()) : null;
      this.changePhoto.nativeElement.value = '';
    }, (error: HttpErrorResponse) => {});
    }
  else{
    this.snackbar.callSnackbaronError("'File with this extensions not allowed")
  }
}
  public route(): void {
    let url: string = '/manage-profile/correction-form';
    this.dataService.setPersonForm("0");
    this.router.navigateByUrl(url);
  }

  public editProfile(): void {
    let url: string = '/manage-profile/profile-edit';
    this.dataService.setPersonForm("0");
    this.router.navigateByUrl(url);
  }

  public editCorrectionForm(request: any): void {
    // let url: string = '/manage-profile/correction-form';
    // this.dataService.setPersonForm(personFormId.toString());
    // this.router.navigateByUrl(url);
    this.router.navigate(['manage-profile','correction-form', FormTypes.Demographic, this.global.candidateId, 0, this.global.stateId, 0, request.personFormId.toString(),0,request.statusId]);
  }

  ngOnDestroy(): void {
    this.subscription ? this.subscription.unsubscribe() : null;
  }
  
}
