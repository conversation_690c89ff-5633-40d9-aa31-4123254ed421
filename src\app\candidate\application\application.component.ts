import { Component, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import { Observable } from "rxjs";
import { IconService } from "src/app/core/icon.service";
import { LanguageService } from "src/app/core/language.service";
import {
  getEligibilityRoutes,
  getEligibilityRouteDetails,
} from "./state/application.actions";
import {
  selectEligibilityRouteIndex,
  selectEligibilityRoutes,
} from "./state/application.selectors";
import { ApplicationState } from "./state/application.state";
import { ConfirmERD, eligibilityRoute } from "./application.types";
import { FormTypes } from "src/app/core/Dto/enum";
import { GlobalUserService } from "src/app/core/global-user.service";
import { HttpClient } from "@angular/common/http";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { URL } from "src/app/core/url";
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpService } from "src/app/core/http-services/http.service";

@Component({
  selector: "exai-application",
  templateUrl: "./application.component.html",
  styleUrls: ["./application.component.scss"],
})
export class ApplicationComponent implements OnInit {
  form: FormGroup;
  M1_eligibilty='A candidate that has completed a Kentucky Board of Nursing approved Certified Medication Aide I program'
  M2_eligibilty='A candidate that failed the MACE twice or did not test within the 60-day timeframe from completion of the course and has retaken the course. '
  M3_eligibilty='TRAINED Endorsement candidate from another state that did not take the MACE exam'
  registeredExams
  icon: boolean = false;
  activeEligibilityRouteIndex$: Observable<number>;
  activeEligibiltyRouteIndex: number;
  eligibilityRoutes$: Observable<Array<eligibilityRoute>>;

  eligibilityRoutes: Array<eligibilityRoute> = null;

  routeDetails: any = null;
  state;
  confirmERDEtails: ConfirmERD;
  checked: boolean;
  eligibilityRoutesId: number;
  constructor(
    public store: Store<ApplicationState>,
    public fb: FormBuilder,
    private router: Router,
    private iconService: IconService,
    public lngSrvc: LanguageService,
    private global: GlobalUserService,
    private http: HttpClient,
    private snackbar: SnackbarService,
    private https:HttpService
  ) {}

  ngOnInit(): void {
    this.global.userDetails.subscribe((data) => {
      this.state = data;
      if (!data) return;
      this.store.dispatch<Action>(
        getEligibilityRoutes({ stateId: data.stateId,candidateId:this.global.personId})
      );
    });
    this.eligibilityRoutes$ = this.store.select(selectEligibilityRoutes);
    this.activeEligibilityRouteIndex$ = this.store.select(
      selectEligibilityRouteIndex
    );
    this.eligibilityRoutes$.subscribe((x: eligibilityRoute[]) => {
      this.eligibilityRoutes = x;
    });
    this.activeEligibilityRouteIndex$.subscribe((index: number) => {
      if (index != null) {
        this.eligibilityRoutesId = this.eligibilityRoutes[index].id;
        this.routeDetails =
          this.eligibilityRoutes[index].eligibilityRouteDetails;
        this.activeEligibiltyRouteIndex = index;
      }
    });
    this.https.scheduledExam(this.global.candidateId).subscribe(data=>{
        this.registeredExams =data
    })
    this.form = this.fb.group({
      stateName: [{ value: "", disabled: true }, [Validators.required]],
    });
  }

  showOptions(event: MatCheckboxChange): void {
    this.checked = event.checked;
  }
 
  disable(data): any {
if(this.eligibilityRoutes){
  return this.eligibilityRoutes.findIndex(x=>(x.isFormSubmissionAllowed ==false && x.id)== data.id)>-1
}else{
  return false
}
  }


  onactive(event: eligibilityRoute, index: number) {
    this.checked = false;
    this.http
      .get(URL.BASE_URL + `EligibilityRoute/acknowledgement?id=${event.id}`)
      .subscribe((response: ConfirmERD) => {
        if (response) {
          this.confirmERDEtails = response;
        }
      });
    this.store.dispatch<Action>(
      getEligibilityRouteDetails({ eligibilityRoute: event, routeIndex: index })
    );
  }

  startApplication() {
    this.router.navigate(['application', 'application-form', FormTypes.Application, this.global.candidateId, this.eligibilityRoutes[this.activeEligibiltyRouteIndex].id, this.global.stateId, '0','']);
  }
}
