<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28 0H0V1.33333H28V0ZM28 2.66667H0V4H28V2.66667ZM0 5.33333H28V6.66667H0V5.33333ZM28 8H0V9.33333H28V8ZM0 10.6667H28V12H0V10.6667ZM28 13.3333H0V14.6667H28V13.3333ZM0 16H28V17.3333H0V16ZM28 18.6667H0V20H28V18.6667Z" fill="#D02F44"/>
<rect width="12" height="9.33333" fill="#46467F"/>
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.66671 1.9987C2.66671 2.36689 2.36823 2.66536 2.00004 2.66536C1.63185 2.66536 1.33337 2.36689 1.33337 1.9987C1.33337 1.63051 1.63185 1.33203 2.00004 1.33203C2.36823 1.33203 2.66671 1.63051 2.66671 1.9987ZM5.33337 1.9987C5.33337 2.36689 5.0349 2.66536 4.66671 2.66536C4.29852 2.66536 4.00004 2.36689 4.00004 1.9987C4.00004 1.63051 4.29852 1.33203 4.66671 1.33203C5.0349 1.33203 5.33337 1.63051 5.33337 1.9987ZM7.33337 2.66536C7.70156 2.66536 8.00004 2.36689 8.00004 1.9987C8.00004 1.63051 7.70156 1.33203 7.33337 1.33203C6.96518 1.33203 6.66671 1.63051 6.66671 1.9987C6.66671 2.36689 6.96518 2.66536 7.33337 2.66536ZM10.6667 1.9987C10.6667 2.36689 10.3682 2.66536 10 2.66536C9.63185 2.66536 9.33337 2.36689 9.33337 1.9987C9.33337 1.63051 9.63185 1.33203 10 1.33203C10.3682 1.33203 10.6667 1.63051 10.6667 1.9987ZM3.33337 3.9987C3.70156 3.9987 4.00004 3.70022 4.00004 3.33203C4.00004 2.96384 3.70156 2.66536 3.33337 2.66536C2.96518 2.66536 2.66671 2.96384 2.66671 3.33203C2.66671 3.70022 2.96518 3.9987 3.33337 3.9987ZM6.66671 3.33203C6.66671 3.70022 6.36823 3.9987 6.00004 3.9987C5.63185 3.9987 5.33337 3.70022 5.33337 3.33203C5.33337 2.96384 5.63185 2.66536 6.00004 2.66536C6.36823 2.66536 6.66671 2.96384 6.66671 3.33203ZM8.66671 3.9987C9.0349 3.9987 9.33337 3.70022 9.33337 3.33203C9.33337 2.96384 9.0349 2.66536 8.66671 2.66536C8.29852 2.66536 8.00004 2.96384 8.00004 3.33203C8.00004 3.70022 8.29852 3.9987 8.66671 3.9987ZM10.6667 4.66536C10.6667 5.03355 10.3682 5.33203 10 5.33203C9.63185 5.33203 9.33337 5.03355 9.33337 4.66536C9.33337 4.29717 9.63185 3.9987 10 3.9987C10.3682 3.9987 10.6667 4.29717 10.6667 4.66536ZM7.33337 5.33203C7.70156 5.33203 8.00004 5.03355 8.00004 4.66536C8.00004 4.29717 7.70156 3.9987 7.33337 3.9987C6.96518 3.9987 6.66671 4.29717 6.66671 4.66536C6.66671 5.03355 6.96518 5.33203 7.33337 5.33203ZM5.33337 4.66536C5.33337 5.03355 5.0349 5.33203 4.66671 5.33203C4.29852 5.33203 4.00004 5.03355 4.00004 4.66536C4.00004 4.29717 4.29852 3.9987 4.66671 3.9987C5.0349 3.9987 5.33337 4.29717 5.33337 4.66536ZM2.00004 5.33203C2.36823 5.33203 2.66671 5.03355 2.66671 4.66536C2.66671 4.29717 2.36823 3.9987 2.00004 3.9987C1.63185 3.9987 1.33337 4.29717 1.33337 4.66536C1.33337 5.03355 1.63185 5.33203 2.00004 5.33203ZM4.00004 5.9987C4.00004 6.36689 3.70156 6.66536 3.33337 6.66536C2.96518 6.66536 2.66671 6.36689 2.66671 5.9987C2.66671 5.63051 2.96518 5.33203 3.33337 5.33203C3.70156 5.33203 4.00004 5.63051 4.00004 5.9987ZM6.00004 6.66536C6.36823 6.66536 6.66671 6.36689 6.66671 5.9987C6.66671 5.63051 6.36823 5.33203 6.00004 5.33203C5.63185 5.33203 5.33337 5.63051 5.33337 5.9987C5.33337 6.36689 5.63185 6.66536 6.00004 6.66536ZM9.33337 5.9987C9.33337 6.36689 9.0349 6.66536 8.66671 6.66536C8.29852 6.66536 8.00004 6.36689 8.00004 5.9987C8.00004 5.63051 8.29852 5.33203 8.66671 5.33203C9.0349 5.33203 9.33337 5.63051 9.33337 5.9987ZM10 7.9987C10.3682 7.9987 10.6667 7.70022 10.6667 7.33203C10.6667 6.96384 10.3682 6.66536 10 6.66536C9.63185 6.66536 9.33337 6.96384 9.33337 7.33203C9.33337 7.70022 9.63185 7.9987 10 7.9987ZM8.00004 7.33203C8.00004 7.70022 7.70156 7.9987 7.33337 7.9987C6.96518 7.9987 6.66671 7.70022 6.66671 7.33203C6.66671 6.96384 6.96518 6.66536 7.33337 6.66536C7.70156 6.66536 8.00004 6.96384 8.00004 7.33203ZM4.66671 7.9987C5.0349 7.9987 5.33337 7.70022 5.33337 7.33203C5.33337 6.96384 5.0349 6.66536 4.66671 6.66536C4.29852 6.66536 4.00004 6.96384 4.00004 7.33203C4.00004 7.70022 4.29852 7.9987 4.66671 7.9987ZM2.66671 7.33203C2.66671 7.70022 2.36823 7.9987 2.00004 7.9987C1.63185 7.9987 1.33337 7.70022 1.33337 7.33203C1.33337 6.96384 1.63185 6.66536 2.00004 6.66536C2.36823 6.66536 2.66671 6.96384 2.66671 7.33203Z" fill="url(#paint0_linear)"/>
</g>
</g>
<defs>
<filter id="filter0_d" x="1.33337" y="1.33203" width="9.33333" height="7.66667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="1.33337" y1="1.33203" x2="1.33337" y2="7.9987" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F0F0"/>
</linearGradient>
</defs>
</svg>
