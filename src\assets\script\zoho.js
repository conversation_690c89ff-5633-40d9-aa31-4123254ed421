// var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq || {widgetcode: "siq82ea5af9567ced271524dd594b2cda6d38d1d16c39814bda130b05a3445c6a29", values:{},ready:function(){}};var d=document;s=d.createElement("script");s.type="text/javascript";s.id="zsiqscript";s.defer=true;s.src="https://salesiq.zohopublic.com/widget";t=d.getElementsByTagName("script")[0];t.parentNode.insertBefore(s,t);
//  $zoho.salesiq.ready = function()
//  {
//  $zoho.salesiq.customfield.add( 
//  { 
//     "name" :"Select State", 
//     "hint" :"Select State", 
//     "required" :"true", 
//     "type" :"selectbox", 
//     "visibility" :"both", 
//     "options" : [{ 
//       "text" :"Alabama", 
//       "value":"Alabama" 
//     },
//     { 
//       "text" :"Alaska", 
//       "value":"Alaska" 
//     },
//     { 
//       "text" :"California", 
//       "value":"California" 
//     },
//     { 
//       "text" :"Colorado", 
//       "value":"Colorado" 
//     },
//     { 
//       "text" :"District of Columbia", 
//       "value":"District of Columbia" 
//     },
//     { 
//       "text" :"Georgia", 
//       "value":"Georgia" 
//     },
//     { 
//       "text" :"Maryland", 
//       "value":"Maryland" 
//     },
//     { 
//       "text" :"Mississippi", 
//       "value":"Mississippi" 
//     },
//     { 
//       "text" :"North Carolina", 
//       "value":"North Carolina" 
//     },
//     { 
//       "text" :"Nevada", 
//       "value":"Nevada" 
//     },
//     { 
//       "text" :"Pennsylvania", 
//       "value":"Pennsylvania" 
//     },
//     { 
//       "text" :"Rhode Island", 
//       "value":"Rhode Island" 
//     },
//     { 
//       "text" :"South carolina", 
//       "value":"South carolina" 
//     },
//     { 
//       "text" :"Virginia", 
//       "value":"Virginia" 
//     },
//     { 
//       "text" :"Washington", 
//       "value":"Washington" 
//     }
//     ] 
//  });
  
//  $zoho.salesiq.customfield.add( 
//  { 
//     "name" :"Select Exam", 
//     "hint" :"Select Exam", 
//     "required" :"true", 
//     "type" :"selectbox", 
//     "visibility" :"both", 
//     "options" : [{ 
//       "text" :"MACE", 
//       "value":"MACE" 
//     },
//     { 
//       "text" :"NNAAP", 
//       "value":"NNAAP" 
//     },
//     { 
//       "text" :"HHA", 
//       "value":"HHA" 
//     },
//     { 
//       "text" :"Other", 
//       "value":"Other" 
//     }
//     ] 
//  });
//  let personDetails =JSON.parse(sessionStorage.UserData)
//  $zoho.salesiq.visitor.email(personDetails.email);
//  $zoho.salesiq.visitor.contactnumber(personDetails.phone_number);
//  $zoho.salesiq.visitor.name(personDetails.given_name);
//  }


let personDetails =JSON.parse(sessionStorage.UserInformation);
let userDetails =JSON.parse(sessionStorage.UserData)

var widgetCode = "";
  if (personDetails.stateCode !== "PA") {
widgetCode = "siq82ea5af9567ced271524dd594b2cda6d38d1d16c39814bda130b05a3445c6a29";
  } else {
  widgetCode = "siq1181c4091109b962a6dac31fcff4e40d";
  }

  function step2() {
    document.getElementById("step1").classList.toggle("hidden");
    document.getElementById("step2").classList.toggle("hidden");
  }
  function step1() {
    document.getElementById("step1").classList.toggle("hidden");
    document.getElementById("step2").classList.toggle("hidden");
  }

  var $zoho = $zoho || {};
  $zoho.salesiq = $zoho.salesiq || {widgetcode: widgetCode, values: {}, ready: function() {}};
  var d = document;
  var s = d.createElement("script");
  s.type = "text/javascript";
  s.id = "zsiqscript";
  s.defer = true;
  s.src = "https://salesiq.zohopublic.com/widget";
  var t = d.getElementsByTagName("script")[0];
  t.parentNode.insertBefore(s, t);

  $zoho.salesiq.ready = function() {
    if (personDetails.stateCode !== "PA") {
      $zoho.salesiq.customfield.add({
        "name": "Select State",
        "hint": "Select State",
        "required": "true",
        "type": "selectbox",
        "visibility": "both",
        "options": [
          {"text": "Alabama", "value": "Alabama"},
          {"text": "Alaska", "value": "Alaska"},
          {"text": "California", "value": "California"},
          {"text": "Colorado", "value": "Colorado"},
          {"text": "District of Columbia", "value": "District of Columbia"},
          {"text": "Georgia", "value": "Georgia"},
          {"text": "IOWA", "value": "IOWA"},
          { text: "Kentucky", value: "Kentucky" },
          {"text": "Maryland", "value": "Maryland"},
          {"text": "North Carolina", "value": "North Carolina"},
          {"text": "Nevada", "value": "Nevada"},
          {"text": "Rhode Island", "value": "Rhode Island"},
          {"text": "South Carolina", "value": "South Carolina"},
          { text: "Utah", value: "Utah" },
          {"text": "Virginia", "value": "Virginia"},
          {"text": "Washington", "value": "Washington"}
        ]
      });
    }

    $zoho.salesiq.customfield.add({
      "name": "Select Exam",
      "hint": "Select Exam",
      "required": "true",
      "type": "selectbox",
      "visibility": "both",
      "options": [
        {"text": "MACE", "value": "MACE"},
        {"text": "NNAAP", "value": "NNAAP"},
        {"text": "HHA", "value": "HHA"},
        {"text": "Other", "value": "Other"}
      ]
    });

  

   
    $zoho.salesiq.visitor.email(userDetails.email);
    $zoho.salesiq.visitor.contactnumber(userDetails.phone_number);
    $zoho.salesiq.visitor.name(userDetails.given_name);

    $zoho.salesiq.floatbutton.visible("hide");

  };
 

  function redr() {
    const selectedValue = personDetails.stateCode;
    if (selectedValue === '') {
      hideElement('popup');
    } else {
      fetchCurrentTime().then(() => {
        const popupSpan = document.querySelector('.popup span');
        if (popupSpan) popupSpan.innerHTML = selectedValue;
      });
    }
  }


  async function fetchCurrentTime() {
    try {
      const response = await fetch('https://credentia.com/time.php');
      const data = await response.json();
      checkTimeSlot(data);
    } catch (error) {
    }
  }



 

  function hideElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) element.style.display = 'none';
  }
  

   
  
  function showElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) element.style.display = 'block';
  }



  

  
  function checkTimeSlot(currentTime) {
    const currentHour = currentTime.hour;
    const currentDay = new Date(currentTime.dateTime).getDay(); // Convert to day number (0-6)
 
    const isWorkingHours = checkWorkingHours(currentDay, currentHour);
    const selectedState = personDetails.stateCode;
      if(selectedState == 'PA'){
         if(isWorkingHours){
          $zoho.salesiq.floatbutton.visible("show");
          $zoho.salesiq.chatwindow.visible('show');
          document.getElementById('aslct').style.display ='none'
          document.querySelector('.zsiq_floatmain.zsiq_theme1.siq_bR').click()
         }
         else {
          removeAllChips();
          addChip('Pennsylvania');
          showElement('emailModal');
         }
      } else {
        $zoho.salesiq.floatbutton.visible("show");
          $zoho.salesiq.chatwindow.visible('show');
          document.getElementById('aslct').style.display ='none'
          document.querySelector('.zsiq_floatmain.zsiq_theme1.siq_bR').click()
      }
    
  }
  
  function checkWorkingHours(day, hour) {
    if (day >= 1 && day <= 5) {
      return hour >= 8 && hour < 23;
    } else if (day === 6) {
      return hour >= 8 && hour < 17;
    } else if (day === 0) {
      return hour >= 10 && hour < 16;
    }
    return false;
  }

  // Chip-related functions
  function addChip(value) {
    const chipsInput = document.getElementById('chipsInput');
    const input = document.getElementById('myInput');
    if (!chipsInput || !input) return;
  
    const chip = createChipElement(value);
    chipsInput.insertBefore(chip, input);
    input.value = '';
    input.focus();
    
    hideDropdownItem(value);
    setSelectedOption(value);
  }
  
  function createChipElement(value) {
    const chip = document.createElement('div');
    chip.className = 'chip bg-blue-100 text-blue-800 text-sm font-semibold p-2 rounded-full flex items-center';
    chip.textContent = value;
    const closeButton = createCloseButton(value, chip);
    chip.appendChild(closeButton);
    return chip;
  }
  
  function createCloseButton(value, chip) {
    const closeButton = document.createElement('span');
    closeButton.innerHTML = '&times;';
    closeButton.className = 'ml-2 cursor-pointer';
    closeButton.onclick = function() {
      chip.remove();
      showDropdownItem(value);
      unselectOption(value);
    };
    return closeButton;
  }
  
  function removeAllChips() {
    const chips = document.querySelectorAll('.chip');
    chips.forEach((chip) => {
    chip.remove();
    });
  }


  function cancel() {
    const selectElement = document.getElementById('slct');
    removeAllChips();
    if (selectElement) selectElement.value = '';
    hideElement('emailModal');
  }
  
  function hidePopupEmail() {
    const selectElement = document.getElementById('slct');
    removeAllChips();
    if (selectElement) selectElement.value = '';
    hideElement('emailModal');
  }
  
  
  function hideDropdownItem(value) {
    const item = document.querySelector(`#myDropdown div[onclick="addChip('${value}')"]`);
    if (item) item.style.display = 'none';
  }
  
  function showDropdownItem(value) {
    const item = document.querySelector(`#myDropdown div[onclick="addChip('${value}')"]`);
    if (item) item.style.display = 'block';
  }
  
  function setSelectedOption(value) {
    const option = document.querySelector(`#CASECF41 option[value="${value}"]`);
    if (option) option.setAttribute('selected', 'selected');
  }
  
  function unselectOption(value) {
    const option = document.querySelector(`#CASECF41 option[value="${value}"]`);
    if (option) option.removeAttribute('selected');
  }
  
  // Dropdown filter function
  function filterFunction() {
    const input = document.getElementById('myInput');
    const filter = input.value.toUpperCase();
    const dropdown = document.getElementById('myDropdown');
    if (!input || !dropdown) return;
  
    const divs = dropdown.getElementsByTagName('div');
  
    for (const div of divs) {
      const txtValue = div.textContent || div.innerText;
      div.style.display = txtValue.toUpperCase().indexOf(filter) > -1 ? '' : 'none';
    }
  }
  var zsWebFormMandatoryFields = new Array("First Name" ,"Contact Name", "Email", "Subject", "State", "Description", "Category","Priority");
  var zsFieldsDisplayLabelArray = new Array("First Name" ,"Last Name", "Email", "Subject", "State", "Description", "Category");

  function zsValidateMandatoryFields() {
    var name = '';
    var email = '';
    var isError = 0;
    for (var index = 0; index < zsWebFormMandatoryFields.length; index++) {
        isError = 0;
        var fieldObject = document.forms['zsWebToCase_547474000041204025'][zsWebFormMandatoryFields[index]];
        if (fieldObject) {
            if (((fieldObject.value).replace(/^\s+|\s+$/g, '')).length == 0) {
                alert(zsFieldsDisplayLabelArray[index] + ' cannot be empty ');
                fieldObject.focus();
                isError = 1;
                return false;
            } else {
                if (fieldObject.name == 'Email') {
                    if (!fieldObject.value.match(/^([\w_][\w\-_.+\'&]*)@(?=.{4,256}$)(([\w]+)([\-_]*[\w])*[\.])+[a-zA-Z]{2,22}$/)) {
                        isError = 1;
                        alert('Enter a valid email-Id');
                        fieldObject.focus();
                        return false;
                    }
                }
            }
            if (fieldObject.nodeName == 'SELECT') {
                if (fieldObject.options[fieldObject.selectedIndex].value == '-None-') {
                    alert(zsFieldsDisplayLabelArray[index] + ' cannot be none');
                    fieldObject.focus();
                    isError = 1;
                    return false;
                }
            }
            if (fieldObject.type == 'checkbox') {
                if (fieldObject.checked == false) {
                    alert('Please accept ' + zsFieldsDisplayLabelArray[index]);
 
                    function goBack() {
                        window.history.back();
                    }
                    fieldObject.focus();
                    isError = 1;
                    return false;
                }
            }
        }
    }
    if (isError == 0) {
        document.getElementById('zsSubmitButton_547474000041204025').setAttribute('disabled', 'disabled');
    }
}


  
  // Event listeners
  document.addEventListener('DOMContentLoaded', function() {
    setupEmailModal();
    setupDropdown();
    setupFileUpload();


  });
  setupDropdown();
  setupFileUpload()
  
  function setupEmailModal() {
    const emailLink = document.querySelector('a[href^="mailto:"]');
    const emailModal = document.getElementById('emailModal');
    const proceedButton = document.getElementById('proceedEmail');
    const cancelButton = document.getElementById('cancelModal');
  
    if (emailLink) {
      emailLink.addEventListener('click', function(event) {
        event.preventDefault();
        if (emailModal) emailModal.classList.remove('hidden');
      });
    }
  
    if (proceedButton) {
      proceedButton.addEventListener('click', handleProceedEmail);
    }
  
    if (cancelButton && emailModal) {
      cancelButton.addEventListener('click', function() {
        emailModal.classList.add('hidden');
      });
    }
  }
  
  function handleProceedEmail() {
    const fullName = document.getElementById('modal-fullname')?.value || '';
    const phone = document.getElementById('modal-phone')?.value || '';
    const category = document.getElementById('modal-category')?.value || '';
    const state = document.getElementById('modal-state')?.value || '';
  
    if (!fullName || !phone || !category || !state) {
      alert('Please fill in all fields correctly.');
      return;
    }
  
    const mailtoLink = `mailto:<EMAIL>?subject=Support Request&body=Full Name: ${fullName}%0D%0AState: ${state}%0D%0ACategory: ${category}%0D%0APhone Number: ${phone}%0D%0ADetail Description of Inquiry: `;
    window.location.href = mailtoLink;
    
    const emailModal = document.getElementById('emailModal');
    if (emailModal) emailModal.classList.add('hidden');
  }
  
  function setupDropdown() {
    const input = document.getElementById('myInput');
    const dropdown = document.getElementById('myDropdown');
    if (!input || !dropdown) return;
  
    input.addEventListener('focus', function() {
      dropdown.classList.remove('hidden');
    });
  
    document.addEventListener('click', function(event) {
      if (!input.contains(event.target) && !dropdown.contains(event.target)) {
        dropdown.classList.add('hidden');
      }
    });
  
    dropdown.addEventListener('click', function(event) {
      event.stopPropagation();
    });
  }
  
  function setupFileUpload() {
    const fileInput = document.getElementById('attachment_1');
    const dropZone = document.getElementById('drop_zone');
    const removeFileBtn = document.getElementById('removeFileBtn');
  
    if (fileInput) fileInput.addEventListener('change', handleFileSelect);
    if (dropZone) {
      dropZone.addEventListener('click', () => fileInput?.click());
      setupDragAndDrop(dropZone);
    }
    if (removeFileBtn) removeFileBtn.addEventListener('click', removeFile);
  }
  
  function handleFileSelect(event) {
    const file = event.target.files[0] || event.dataTransfer.files[0];
    if (!file) return;
    
    if (!isValidFileType(file.name)) {
      alert('Invalid file type. Only PNG, JPG, and GIF files are allowed.');
      return;
    }
    
    if (!isValidFileSize(file.size)) {
      alert('File size exceeds the limit of 2MB.');
      return;
    }
  
    displayFilePreview(file);
  }
  
  function isValidFileType(fileName) {
    return /\.(jpg|jpeg|png|gif)$/i.test(fileName);
  }
  
  function isValidFileSize(fileSize) {
    return fileSize <= 2 * 1024 * 1024; // 2MB
  }
  
  function displayFilePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
      const filePreview = document.getElementById('filePreview');
      const previewContainer = document.getElementById('previewContainer');
      if (filePreview) filePreview.src = e.target.result;
      if (previewContainer) previewContainer.classList.remove('hidden');
    };
    reader.readAsDataURL(file);
  }
  
  
  function setupDragAndDrop(dropZone) {
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('bg-gray-100');
    });
  
    dropZone.addEventListener('dragleave', (e) => {
      e.preventDefault();
      dropZone.classList.remove('bg-gray-100');
    });
  
    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('bg-gray-100');
      handleFileSelect(e);
    });
  }
  
  function removeFile() {
    document.getElementById('previewContainer').classList.add('hidden');
    document.getElementById('filePreview').src = '';
    document.getElementById('attachment_1').value = '';
  }



