export interface ResponseMeta {
  cardDisplay: string;
  eligibleForCardUpdater: string;
  storageState: string;
  fingerprint: string;
  retainedInVault: boolean;
  addPaymentMethodTransactionToken: string;
}


export   const Renewal=["Renewal-Reciprocity Payment Successfull"]


export interface Customer {
  id: string;
  firstname: string;
  lastname: string;
  company: string;
  email: string;
  cc_emails?: any;
  cc_sms?: any;
  phone: string;
  address_1: string;
  address_2: string;
  address_city: string;
  address_state: string;
  address_zip: string;
  address_country: string;
  notes?: any;
  reference: string;
  options?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: any;
  allow_invoice_credit_card_payments: boolean;
  gravatar: string;
}

export interface paymentMethod {
  id: string;
  customer_id: string;
  merchant_id: string;
  user_id: string;
  nickname: string;
  has_cvv: number;
  is_default: number;
  method: string;
  person_name: string;
  card_type: string;
  card_last_four: string;
  card_exp: string;
  bank_name?: any;
  bank_type?: any;
  bank_holder_type?: any;
  address_1?: any;
  address_2?: any;
  address_city?: any;
  address_state?: any;
  address_zip?: any;
  address_country?: any;
  purged_at?: any;
  deleted_at?: any;
  created_at: string;
  updated_at: string;
  meta: ResponseMeta;
  bin_type?: any;
  au_enrolled: number;
  au_last_event?: any;
  au_last_event_at?: any;
  card_exp_datetime: string;
  is_usable_in_vt: boolean;
  is_tokenized: boolean;
  customer: Customer;
}

export interface User {
    id: string;
    system_admin: boolean;
    name: string;
    email?: any;
    email_verification_sent_at?: any;
    email_verified_at?: any;
    is_api_key: boolean;
    created_at: string;
    updated_at: string;
    deleted_at?: any;
    gravatar: string;
    team_admin?: any;
    team_enabled?: any;
    team_role?: any;
}

export interface CreatePaymentMethod {
  method: string;
  person_name: string;
  lastname: string;
  card_number: string;
  card_cvv: string;
  card_exp: string;
  customer_id: string;
  bank_account: string;
  bank_routing: string;
  bank_name: string;
  bank_type: string;
  bank_holder_type: string;
}


export interface CreatePaymentMethodRespnse {
  customer_id: string;
  merchant_id: string;
  user_id: string;
  method: string;
  card_type: string;
  card_exp: string;
  has_cvv: boolean;
  address_1?: any;
  address_2?: any;
  address_city?: any;
  address_state?: any;
  address_zip?: any;
  address_country?: any;
  bank_name?: any;
  bank_type?: any;
  bank_holder_type?: any;
  is_default: string;
  person_name: string;
  meta: ResponseMeta;
  card_last_four: string;
  nickname: string;
  id: string;
  updated_at: string;
  created_at: string;
  card_exp_datetime: string;
  is_usable_in_vt: boolean;
  is_tokenized: boolean;
  customerdetails?: any;
}


export class CreatePaymentCustomerIdBody {
  firstname: string= "";
  lastname: string="";
  company: string="";
  email: string="";
  cc_emails: string[]=[];
  phone: string="";
  address_1: string="";
  address_2: string="";
  address_city: string="";
  address_state: string="";
  address_zip: string="";
  address_country: string="";
  reference: string="";
  personid: string="";
}

export interface CreatePaymentCustomerIdResponse {
  id: string;
  firstname: string;
  lastname: string;
  company: string;
  email: string;
  cc_emails: string[];
  cc_sms?: any;
  phone: string;
  address_1: string;
  address_2: string;
  address_city: string;
  address_state: string;
  address_zip: string;
  address_country: string;
  notes?: any;
  reference: string;
  options?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: any;
  allow_invoice_credit_card_payments: boolean;
  gravatar: string;
}



