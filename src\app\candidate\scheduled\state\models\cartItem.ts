export interface CartItem {
  cartItemTypeId: string;
  personEventCartId: string;
  cartId: number;
  examName: string;
  examCode: string;
  examTypeId: any;
  examNameDesc: string;
  examMode: string;
  timeZoneCode: string;
  examDateAndTime: Date;
  eventDate: Date;
  eventDateUtc: Date;
  durationInMinutes: number;
  amount: number;
  personEventId: number;
  cartStatusId: number;
  cartStatus: string;
  timeZoneOffset: string;
  eligibilityRouteName: string;
  examId: number;
  voucherAmount:number;
  voucherCode:string
  timeZoneAbbreviation:string
    
  }