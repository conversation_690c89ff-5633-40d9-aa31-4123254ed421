<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="969.176" height="818.073" viewBox="0 0 969.176 818.073">
  <defs>
    <filter id="Rectangle_3" x="573.5" y="267" width="369" height="215" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_56" data-name="Group 56" transform="translate(-17.5 -131)">
    <g id="Group_2" data-name="Group 2" transform="translate(3155.176 370.985)">
      <rect id="Rectangle_1" data-name="Rectangle 1" width="968.176" height="152.253" transform="translate(-3137.176 -239.985)" fill="#f1f3f4"/>
      <path id="Path_1" data-name="Path 1" d="M-2169-103.263h-443.79a60.565,60.565,0,0,1-60.565-60.566v-.059a60.565,60.565,0,0,1,60.565-60.566H-2169Z" fill="#fff"/>
      <path id="Path_2" data-name="Path 2" d="M-3014.417-167.436h-43.537l19.98-19.98-5.062-5.062-28.619,28.619,28.619,28.62,5.062-5.062-19.98-19.98h43.537Z" fill="#babcbe"/>
      <path id="Path_3" data-name="Path 3" d="M-2931.761-167.436h43.537l-19.98-19.98,5.062-5.062,28.619,28.619-28.619,28.62-5.062-5.062,19.98-19.98h-43.537Z" fill="#babcbe"/>
      <path id="Path_4" data-name="Path 4" d="M-2745.216-184.071a28.436,28.436,0,0,0-20.212-8.407,28.581,28.581,0,0,0-28.584,28.619,28.581,28.581,0,0,0,28.584,28.62,28.557,28.557,0,0,0,27.653-21.465h-7.441a21.429,21.429,0,0,1-20.212,14.31,21.459,21.459,0,0,1-21.465-21.465,21.459,21.459,0,0,1,21.465-21.464,21.32,21.32,0,0,1,15.114,6.35l-11.537,11.537h25.042v-25.042Z" fill="#5f6368"/>
      <path id="Path_5" data-name="Path 5" d="M-2582.4-171.284h-2.122v-4.242a10.611,10.611,0,0,0-10.607-10.607,10.611,10.611,0,0,0-10.607,10.607v4.242h-2.122a4.241,4.241,0,0,0-4.242,4.243v21.215a4.24,4.24,0,0,0,4.242,4.242h25.458a4.24,4.24,0,0,0,4.242-4.242v-21.215A4.241,4.241,0,0,0-2582.4-171.284Zm-6.153,0H-2601.7v-4.242a6.582,6.582,0,0,1,6.576-6.577,6.582,6.582,0,0,1,6.576,6.577Z" fill="#626365"/>
      <g id="Group_1" data-name="Group 1">
        <path id="Path_6" data-name="Path 6" d="M-2501.745-139.025q-6.706,0-10.292-4-3.586-4.016-3.653-11.736v-4.348q0-8.048,3.507-12.6a11.609,11.609,0,0,1,9.772-4.564q6.312,0,9.427,4.015t3.191,12.519v3.846h-20.052v.829c0,3.858.724,6.652,2.174,8.4a7.684,7.684,0,0,0,6.259,2.618,10.143,10.143,0,0,0,4.565-.982,11.372,11.372,0,0,0,3.7-3.1l3.057,3.717Q-2493.913-139.036-2501.745-139.025Zm-.666-32.2a6.175,6.175,0,0,0-5.412,2.5q-1.753,2.524-1.987,7.785h14.137v-.795q-.226-5.119-1.841-7.3A5.681,5.681,0,0,0-2502.411-171.228Z" fill="#0f0f0f"/>
        <path id="Path_7" data-name="Path 7" d="M-2472.365-162.5l6.137-13.115h6.809l-9.76,17.767,9.994,18.154h-6.739l-6.341-13.443-6.377,13.443h-6.773l9.994-18.154-9.726-17.767h6.739Z" fill="#0f0f0f"/>
        <path id="Path_8" data-name="Path 8" d="M-2434.422-139.691a12.717,12.717,0,0,1-.766-3.916q-3.12,4.576-7.966,4.582a10.273,10.273,0,0,1-7.621-2.747q-2.734-2.736-2.735-7.715a10.93,10.93,0,0,1,3.717-8.7c2.478-2.15,5.874-3.237,10.187-3.284h4.319v-3.822c0-2.151-.479-3.671-1.426-4.577a6.108,6.108,0,0,0-4.348-1.361,6.014,6.014,0,0,0-4.32,1.578,5.286,5.286,0,0,0-1.659,4h-5.874a9.536,9.536,0,0,1,1.625-5.26,11.323,11.323,0,0,1,4.383-3.933,13.12,13.12,0,0,1,6.143-1.426q5.5,0,8.381,2.735t2.939,7.989v18.118a21.859,21.859,0,0,0,1.128,7.213v.526Zm-7.838-4.676a7.8,7.8,0,0,0,4.115-1.2,7.332,7.332,0,0,0,2.858-2.981v-8.568h-3.32a11.141,11.141,0,0,0-6.6,1.87,5.973,5.973,0,0,0-2.426,5.073,6.582,6.582,0,0,0,1.228,4.431C-2445.591-144.835-2444.206-144.367-2442.26-144.367Z" fill="#0f0f0f"/>
        <path id="Path_9" data-name="Path 9" d="M-2414.8-175.612l.134,3.32a10.685,10.685,0,0,1,8.668-3.98q6,0,8.4,5.207a10.651,10.651,0,0,1,9.428-5.207q9.924,0,10.157,12.384v24.2h-5.844v-23.636q0-3.927-1.391-5.827c-.935-1.274-2.5-1.911-4.717-1.911a5.29,5.29,0,0,0-4.313,2.057,9.376,9.376,0,0,0-1.929,5.249v24.068h-5.908v-23.9q-.1-7.469-6.073-7.469-4.453,0-6.277,4.454v26.92h-5.844v-35.921Z" fill="#0f0f0f"/>
        <path id="Path_10" data-name="Path 10" d="M-2354.352-170.1a14.573,14.573,0,0,0-2.66-.234q-4.514,0-6.306,4.95v25.693h-5.873v-35.921h5.71l.1,3.653q2.358-4.314,6.669-4.313a5.2,5.2,0,0,1,2.326.461Z" fill="#0f0f0f"/>
        <path id="Path_11" data-name="Path 11" d="M-2350.4-159.306q0-7.854,3.8-12.4a12.414,12.414,0,0,1,10.04-4.564,12.538,12.538,0,0,1,10.041,4.447q3.806,4.437,3.9,12.11v3.787q0,7.8-3.787,12.356a12.467,12.467,0,0,1-10.087,4.547,12.5,12.5,0,0,1-10.012-4.419q-3.77-4.418-3.9-11.946Zm5.873,3.378q0,5.541,2.175,8.72a6.756,6.756,0,0,0,5.862,3.168q7.767,0,7.995-11.221v-4.045c0-3.682-.731-6.581-2.186-8.72a6.8,6.8,0,0,0-5.88-3.2,6.691,6.691,0,0,0-5.791,3.2c-1.45,2.139-2.175,5.027-2.175,8.685Z" fill="#0f0f0f"/>
        <path id="Path_12" data-name="Path 12" d="M-2316.275-159.306q0-7.854,3.8-12.4a12.415,12.415,0,0,1,10.041-4.564,12.537,12.537,0,0,1,10.04,4.447q3.806,4.437,3.905,12.11v3.787q0,7.8-3.788,12.356a12.467,12.467,0,0,1-10.087,4.547,12.5,12.5,0,0,1-10.012-4.419q-3.769-4.418-3.9-11.946Zm5.873,3.378q0,5.541,2.175,8.72a6.756,6.756,0,0,0,5.862,3.168q7.767,0,7.995-11.221v-4.045c0-3.682-.731-6.581-2.186-8.72a6.8,6.8,0,0,0-5.879-3.2,6.691,6.691,0,0,0-5.792,3.2c-1.45,2.139-2.175,5.027-2.175,8.685Z" fill="#0f0f0f"/>
        <path id="Path_13" data-name="Path 13" d="M-2275.381-175.612l.134,3.32a10.686,10.686,0,0,1,8.668-3.98q6,0,8.4,5.207a10.65,10.65,0,0,1,9.427-5.207q9.924,0,10.158,12.384v24.2h-5.844v-23.636q0-3.927-1.391-5.827c-.936-1.274-2.5-1.911-4.717-1.911a5.289,5.289,0,0,0-4.313,2.057,9.364,9.364,0,0,0-1.929,5.249v24.068h-5.909v-23.9q-.1-7.469-6.072-7.469-4.453,0-6.277,4.454v26.92h-5.845v-35.921Z" fill="#0f0f0f"/>
        <path id="Path_14" data-name="Path 14" d="M-2229.367-142.906a3.873,3.873,0,0,1,.946-2.665,3.631,3.631,0,0,1,2.835-1.064,3.749,3.749,0,0,1,2.875,1.064,3.813,3.813,0,0,1,.977,2.665,3.548,3.548,0,0,1-.977,2.548,3.774,3.774,0,0,1-2.875,1.029,3.655,3.655,0,0,1-2.835-1.029A3.6,3.6,0,0,1-2229.367-142.906Z" fill="#0f0f0f"/>
        <path id="Path_15" data-name="Path 15" d="M-2193.377-139.691a12.856,12.856,0,0,1-.771-3.916q-3.122,4.576-7.961,4.582c-3.249,0-5.8-.912-7.621-2.747s-2.735-4.4-2.735-7.715a10.93,10.93,0,0,1,3.717-8.7c2.478-2.15,5.88-3.237,10.193-3.284h4.313v-3.822c0-2.151-.479-3.671-1.426-4.577a6.126,6.126,0,0,0-4.348-1.361,5.98,5.98,0,0,0-4.314,1.578,5.265,5.265,0,0,0-1.659,4h-5.88a9.495,9.495,0,0,1,1.625-5.26,11.364,11.364,0,0,1,4.383-3.933,13.114,13.114,0,0,1,6.137-1.426q5.523,0,8.381,2.735,2.875,2.744,2.946,7.989v18.118a22.124,22.124,0,0,0,1.122,7.213v.526Zm-7.832-4.676a7.815,7.815,0,0,0,4.115-1.2,7.4,7.4,0,0,0,2.852-2.981v-8.568h-3.32a11.131,11.131,0,0,0-6.6,1.87,5.993,5.993,0,0,0-2.431,5.073,6.516,6.516,0,0,0,1.239,4.431C-2204.54-144.835-2203.161-144.367-2201.209-144.367Z" fill="#0f0f0f"/>
        <path id="Path_16" data-name="Path 16" d="M-2172.5-185.138a3.787,3.787,0,0,1-.83,2.489,3.079,3.079,0,0,1-2.525.994,3.066,3.066,0,0,1-2.49-.994,3.787,3.787,0,0,1-.83-2.489,3.929,3.929,0,0,1,.83-2.525,3.034,3.034,0,0,1,2.49-1.029,3.061,3.061,0,0,1,2.513,1.046A3.883,3.883,0,0,1-2172.5-185.138Zm-.468,45.447h-5.868v-35.921h5.868Z" fill="#0f0f0f"/>
      </g>
    </g>
    <g id="Ellipse_1" data-name="Ellipse 1" transform="translate(481.82 131.09)" fill="none" stroke="#fc1e00" stroke-miterlimit="10" stroke-width="8">
      <circle cx="76.067" cy="76.067" r="76.067" stroke="none"/>
      <circle cx="76.067" cy="76.067" r="72.067" fill="none"/>
    </g>
    <rect id="Rectangle_2" data-name="Rectangle 2" width="968.176" height="665.321" transform="translate(18 283.253)" fill="#fff" stroke="#babcbe" stroke-width="1"/>
    <g id="Group_11" data-name="Group 11" transform="translate(-458 -1)">
      <path id="Path_72" data-name="Path 72" d="M22.131-8.332a9.241,9.241,0,0,1-3.023,6.39A10.307,10.307,0,0,1,12.094.352,9.98,9.98,0,0,1,6.706-1.1,9.519,9.519,0,0,1,3.111-5.221a14.875,14.875,0,0,1-1.318-6.205v-2.391A14.983,14.983,0,0,1,3.076-20.2a9.671,9.671,0,0,1,3.683-4.254A10.272,10.272,0,0,1,12.3-25.945a9.893,9.893,0,0,1,6.82,2.294,9.586,9.586,0,0,1,3.006,6.5H17.7a6.322,6.322,0,0,0-1.608-3.981A5.316,5.316,0,0,0,12.3-22.359a5.193,5.193,0,0,0-4.456,2.12,10.6,10.6,0,0,0-1.591,6.22v2.27A11.241,11.241,0,0,0,7.743-5.416a4.929,4.929,0,0,0,4.351,2.182,5.75,5.75,0,0,0,3.938-1.178,6.141,6.141,0,0,0,1.67-3.92ZM37.055,0a6.592,6.592,0,0,1-.492-1.775A6.649,6.649,0,0,1,31.57.352a6.71,6.71,0,0,1-4.676-1.635,5.23,5.23,0,0,1-1.811-4.043,5.4,5.4,0,0,1,2.259-4.667,10.946,10.946,0,0,1,6.46-1.626h2.619v-1.248a3.331,3.331,0,0,0-.826-2.364,3.275,3.275,0,0,0-2.514-.888,3.762,3.762,0,0,0-2.391.729,2.258,2.258,0,0,0-.932,1.854H25.488a4.741,4.741,0,0,1,1.037-2.927A6.909,6.909,0,0,1,29.347-18.6a9.91,9.91,0,0,1,3.981-.773,7.981,7.981,0,0,1,5.326,1.679,5.986,5.986,0,0,1,2.039,4.72v8.578a9.722,9.722,0,0,0,.721,4.1V0ZM32.361-3.076a4.859,4.859,0,0,0,2.382-.615,4.11,4.11,0,0,0,1.679-1.652V-8.93h-2.3a6.3,6.3,0,0,0-3.568.826,2.676,2.676,0,0,0-1.2,2.338,2.5,2.5,0,0,0,.817,1.96A3.165,3.165,0,0,0,32.361-3.076ZM49.184-19.02l.123,1.986a6.844,6.844,0,0,1,5.484-2.338q3.814,0,5.221,2.918a6.746,6.746,0,0,1,5.836-2.918,5.93,5.93,0,0,1,4.685,1.74A7.662,7.662,0,0,1,72.105-12.5V0H67.834V-12.375a3.783,3.783,0,0,0-.791-2.654,3.463,3.463,0,0,0-2.619-.844,3.556,3.556,0,0,0-2.382.782,4.125,4.125,0,0,0-1.292,2.048L60.768,0H56.5V-12.516q-.088-3.357-3.428-3.357a3.76,3.76,0,0,0-3.639,2.092V0H45.158V-19.02ZM84.867.352a8.837,8.837,0,0,1-6.583-2.558,9.278,9.278,0,0,1-2.522-6.812v-.527a11.413,11.413,0,0,1,1.1-5.089,8.362,8.362,0,0,1,3.085-3.489,8.156,8.156,0,0,1,4.43-1.248,7.465,7.465,0,0,1,6,2.479Q92.5-14.414,92.5-9.879v1.723H80.068A5.712,5.712,0,0,0,81.642-4.43a4.723,4.723,0,0,0,3.472,1.371,5.784,5.784,0,0,0,4.781-2.373l2.3,2.2a7.69,7.69,0,0,1-3.05,2.646A9.548,9.548,0,0,1,84.867.352Zm-.51-16.295a3.6,3.6,0,0,0-2.839,1.23,6.378,6.378,0,0,0-1.38,3.428h8.139V-11.6a5.192,5.192,0,0,0-1.143-3.243A3.57,3.57,0,0,0,84.357-15.943Zm21.41.826a10.57,10.57,0,0,0-1.74-.141,3.918,3.918,0,0,0-3.955,2.25V0H95.8V-19.02h4.078l.105,2.127a4.813,4.813,0,0,1,4.289-2.479,4,4,0,0,1,1.512.246ZM119.3,0a6.592,6.592,0,0,1-.492-1.775A6.649,6.649,0,0,1,113.818.352a6.71,6.71,0,0,1-4.676-1.635,5.23,5.23,0,0,1-1.811-4.043,5.4,5.4,0,0,1,2.259-4.667,10.946,10.946,0,0,1,6.46-1.626h2.619v-1.248a3.331,3.331,0,0,0-.826-2.364,3.275,3.275,0,0,0-2.514-.888,3.762,3.762,0,0,0-2.391.729,2.258,2.258,0,0,0-.932,1.854h-4.271a4.741,4.741,0,0,1,1.037-2.927,6.909,6.909,0,0,1,2.821-2.136,9.91,9.91,0,0,1,3.981-.773,7.981,7.981,0,0,1,5.326,1.679,5.986,5.986,0,0,1,2.039,4.72v8.578a9.722,9.722,0,0,0,.721,4.1V0Zm-4.693-3.076a4.859,4.859,0,0,0,2.382-.615,4.11,4.11,0,0,0,1.679-1.652V-8.93h-2.3A6.3,6.3,0,0,0,112.8-8.1a2.676,2.676,0,0,0-1.2,2.338,2.5,2.5,0,0,0,.817,1.96A3.165,3.165,0,0,0,114.609-3.076Z" transform="translate(667 384)" fill="#202020"/>
      <g id="Rectangle_3-2" data-name="Rectangle 3" transform="translate(1059 338)" fill="#fff" stroke="#a4a4a4" stroke-width="1">
        <rect width="319" height="65" rx="5" stroke="none"/>
        <rect x="0.5" y="0.5" width="318" height="64" rx="4.5" fill="none"/>
      </g>
      <path id="Path_73" data-name="Path 73" d="M2.6,0V-25.594h8.771q4.342,0,6.609,1.74a6.129,6.129,0,0,1,2.268,5.186A5.527,5.527,0,0,1,19.3-15.5a6.11,6.11,0,0,1-2.777,2.18,5.649,5.649,0,0,1,3.208,2.118,6.169,6.169,0,0,1,1.134,3.735,6.814,6.814,0,0,1-2.312,5.537Q16.242,0,11.936,0ZM7.049-11.549v8h4.939a4.78,4.78,0,0,0,3.27-1.037,3.64,3.64,0,0,0,1.178-2.883q0-3.99-4.078-4.078Zm0-3.27h4.359a5.041,5.041,0,0,0,3.243-.94,3.222,3.222,0,0,0,1.169-2.663,3.266,3.266,0,0,0-1.081-2.742,5.459,5.459,0,0,0-3.366-.844H7.049ZM29.443,0H25.172V-27h4.271Zm3.85-9.686A11.215,11.215,0,0,1,34.4-14.722a8.088,8.088,0,0,1,3.111-3.445,8.766,8.766,0,0,1,4.605-1.2,8.308,8.308,0,0,1,6.249,2.479,9.748,9.748,0,0,1,2.593,6.574l.018,1A11.315,11.315,0,0,1,49.9-4.289,7.991,7.991,0,0,1,46.8-.861,8.831,8.831,0,0,1,42.152.352,8.25,8.25,0,0,1,35.71-2.329a10.267,10.267,0,0,1-2.417-7.146Zm4.271.369a7.664,7.664,0,0,0,1.213,4.6,3.943,3.943,0,0,0,3.375,1.661,3.9,3.9,0,0,0,3.366-1.687,8.465,8.465,0,0,0,1.2-4.939,7.565,7.565,0,0,0-1.239-4.57,3.957,3.957,0,0,0-3.366-1.687,3.921,3.921,0,0,0-3.322,1.661A8.3,8.3,0,0,0,37.564-9.316ZM62.4-3.059a3.862,3.862,0,0,0,2.654-.932,3.183,3.183,0,0,0,1.125-2.3h4.025A6.274,6.274,0,0,1,69.1-2.979,7.264,7.264,0,0,1,66.287-.545a8.381,8.381,0,0,1-3.832.9,8.109,8.109,0,0,1-6.328-2.584,10.249,10.249,0,0,1-2.338-7.137v-.439a10.078,10.078,0,0,1,2.32-6.952,8.035,8.035,0,0,1,6.328-2.61,7.814,7.814,0,0,1,5.528,1.978A7.084,7.084,0,0,1,70.207-12.2H66.182a3.911,3.911,0,0,0-1.116-2.689A3.591,3.591,0,0,0,62.4-15.943a3.759,3.759,0,0,0-3.2,1.5,7.669,7.669,0,0,0-1.143,4.562v.686a7.8,7.8,0,0,0,1.116,4.614A3.756,3.756,0,0,0,62.4-3.059Zm17.209-5.1-1.9,1.951V0H73.441V-27h4.271v15.574l1.336-1.67L84.3-19.02h5.133l-7.066,7.928L90.193,0H85.254Z" transform="translate(1075 384)" fill="#202020"/>
      <g id="Group_4" data-name="Group 4" transform="translate(334.067 -2768.664)">
        <path id="Path_19" data-name="Path 19" d="M1007.763,3131.834l9.17,9.17,9.17-9.17,2.83,2.83-12,12-12-12Z" fill="#202020"/>
        <path id="Path_20" data-name="Path 20" d="M992.933,3114.664h48v48h-48Z" fill="none"/>
      </g>
      <g id="Group_7" data-name="Group 7">
        <g id="Group_3" data-name="Group 3" transform="translate(542 335)">
          <path id="Path_17" data-name="Path 17" d="M-242.177,3855.954h70.943V3926.9h-70.943Z" transform="translate(242.177 -3855.954)" fill="none"/>
          <path id="Path_18" data-name="Path 18" d="M-190.753,3879.385v-10.447a2.974,2.974,0,0,0-2.964-2.985h-41.5a2.974,2.974,0,0,0-2.964,2.985v35.816a2.974,2.974,0,0,0,2.964,2.984h41.5a2.974,2.974,0,0,0,2.964-2.984v-10.447l11.856,11.939v-38.8Z" transform="translate(244.009 -3851.375)" fill="#202020"/>
        </g>
        <g id="Group_14" data-name="Group 14" transform="translate(762.443 -1399.5)">
          <path id="Path_30" data-name="Path 30" d="M-152.695,1772.5h-19.9a2.843,2.843,0,0,0-2.844,2.844v19.9a2.843,2.843,0,0,0,2.844,2.843h19.9a2.843,2.843,0,0,0,2.843-2.843v-19.9A2.843,2.843,0,0,0-152.695,1772.5Z" fill="#fff"/>
          <path id="Path_31" data-name="Path 31" d="M-152.87,1773.344h-19.013a2.715,2.715,0,0,0-2.716,2.716v19.013a2.715,2.715,0,0,0,2.716,2.716h19.013a2.715,2.715,0,0,0,2.716-2.716V1776.06A2.715,2.715,0,0,0-152.87,1773.344Zm-5.665,17.985-3.841-3.841-3.841,3.841-1.92-1.921,3.841-3.841-3.841-3.841,1.92-1.92,3.841,3.841,3.841-3.841,1.92,1.92-3.841,3.841,3.841,3.841Z" transform="translate(-0.271 -0.271)" fill="#ff1e00"/>
        </g>
      </g>
    </g>
    <g id="Group_19" data-name="Group 19" transform="translate(378 -810)">
      <path id="Path_75" data-name="Path 75" d="M8.367-25.594,15.75-5.977l7.365-19.617h5.748V0h-4.43V-8.437l.439-11.285L17.314,0H14.133L6.592-19.705,7.031-8.437V0H2.6V-25.594ZM38.25,0H33.979V-19.02H38.25ZM33.715-23.959a2.267,2.267,0,0,1,.624-1.635,2.357,2.357,0,0,1,1.784-.65,2.387,2.387,0,0,1,1.793.65,2.25,2.25,0,0,1,.633,1.635,2.2,2.2,0,0,1-.633,1.608,2.406,2.406,0,0,1-1.793.642,2.375,2.375,0,0,1-1.784-.642A2.214,2.214,0,0,1,33.715-23.959Zm17,20.9a3.862,3.862,0,0,0,2.654-.932,3.183,3.183,0,0,0,1.125-2.3h4.025A6.274,6.274,0,0,1,57.41-2.979,7.264,7.264,0,0,1,54.6-.545a8.381,8.381,0,0,1-3.832.9,8.109,8.109,0,0,1-6.328-2.584A10.249,10.249,0,0,1,42.1-9.369v-.439a10.078,10.078,0,0,1,2.32-6.952,8.035,8.035,0,0,1,6.328-2.61,7.814,7.814,0,0,1,5.528,1.978A7.084,7.084,0,0,1,58.518-12.2H54.492a3.911,3.911,0,0,0-1.116-2.689,3.591,3.591,0,0,0-2.663-1.055,3.759,3.759,0,0,0-3.2,1.5,7.669,7.669,0,0,0-1.143,4.562v.686a7.8,7.8,0,0,0,1.116,4.614A3.756,3.756,0,0,0,50.713-3.059ZM71.7-15.117a10.57,10.57,0,0,0-1.74-.141,3.918,3.918,0,0,0-3.955,2.25V0H61.734V-19.02h4.078l.105,2.127a4.813,4.813,0,0,1,4.289-2.479,4,4,0,0,1,1.512.246Zm1.266,5.432a11.215,11.215,0,0,1,1.107-5.036,8.088,8.088,0,0,1,3.111-3.445,8.766,8.766,0,0,1,4.605-1.2,8.308,8.308,0,0,1,6.249,2.479,9.748,9.748,0,0,1,2.593,6.574l.018,1a11.315,11.315,0,0,1-1.081,5.027A7.991,7.991,0,0,1,86.476-.861,8.831,8.831,0,0,1,81.826.352a8.25,8.25,0,0,1-6.442-2.681,10.267,10.267,0,0,1-2.417-7.146Zm4.271.369a7.664,7.664,0,0,0,1.213,4.6,3.943,3.943,0,0,0,3.375,1.661,3.9,3.9,0,0,0,3.366-1.687,8.465,8.465,0,0,0,1.2-4.939,7.565,7.565,0,0,0-1.239-4.57,3.957,3.957,0,0,0-3.366-1.687,3.921,3.921,0,0,0-3.322,1.661A8.3,8.3,0,0,0,77.238-9.316Zm33.68,0a11.324,11.324,0,0,1-2,7.04A6.418,6.418,0,0,1,103.535.352a6.461,6.461,0,0,1-5.01-2.057V7.313H94.254V-19.02h3.938l.176,1.934a6.274,6.274,0,0,1,5.115-2.285,6.483,6.483,0,0,1,5.458,2.593,11.626,11.626,0,0,1,1.978,7.2Zm-4.254-.369A7.933,7.933,0,0,0,105.53-14.2a3.686,3.686,0,0,0-3.243-1.67,3.931,3.931,0,0,0-3.762,2.162v8.438a3.967,3.967,0,0,0,3.8,2.215,3.682,3.682,0,0,0,3.19-1.644A8.732,8.732,0,0,0,106.664-9.686Zm12.076-7.26a6.686,6.686,0,0,1,5.291-2.426q6.082,0,6.17,6.943V0H125.93V-12.27a3.756,3.756,0,0,0-.853-2.786,3.485,3.485,0,0,0-2.5-.817,4.115,4.115,0,0,0-3.832,2.285V0h-4.271V-27h4.271Zm14.977,7.26a11.215,11.215,0,0,1,1.107-5.036,8.088,8.088,0,0,1,3.111-3.445,8.766,8.766,0,0,1,4.605-1.2,8.308,8.308,0,0,1,6.249,2.479,9.748,9.748,0,0,1,2.593,6.574l.018,1a11.315,11.315,0,0,1-1.081,5.027,7.991,7.991,0,0,1-3.094,3.428A8.831,8.831,0,0,1,142.576.352a8.25,8.25,0,0,1-6.442-2.681,10.267,10.267,0,0,1-2.417-7.146Zm4.271.369a7.664,7.664,0,0,0,1.213,4.6,3.943,3.943,0,0,0,3.375,1.661,3.9,3.9,0,0,0,3.366-1.687,8.465,8.465,0,0,0,1.2-4.939,7.565,7.565,0,0,0-1.239-4.57,3.957,3.957,0,0,0-3.366-1.687,3.921,3.921,0,0,0-3.322,1.661A8.3,8.3,0,0,0,137.988-9.316Zm20.988-9.7.123,2.2a6.839,6.839,0,0,1,5.537-2.549q5.941,0,6.047,6.8V0h-4.271V-12.322A3.917,3.917,0,0,0,165.63-15a3.29,3.29,0,0,0-2.558-.87,4.116,4.116,0,0,0-3.85,2.338V0h-4.271V-19.02ZM183.41.352a8.837,8.837,0,0,1-6.583-2.558A9.278,9.278,0,0,1,174.3-9.018v-.527a11.413,11.413,0,0,1,1.1-5.089,8.362,8.362,0,0,1,3.085-3.489,8.156,8.156,0,0,1,4.43-1.248,7.465,7.465,0,0,1,6,2.479q2.118,2.479,2.118,7.014v1.723H178.611a5.712,5.712,0,0,0,1.573,3.727,4.723,4.723,0,0,0,3.472,1.371,5.784,5.784,0,0,0,4.781-2.373l2.3,2.2a7.69,7.69,0,0,1-3.05,2.646A9.548,9.548,0,0,1,183.41.352Zm-.51-16.295a3.6,3.6,0,0,0-2.839,1.23,6.378,6.378,0,0,0-1.38,3.428h8.139V-11.6a5.192,5.192,0,0,0-1.143-3.243A3.57,3.57,0,0,0,182.9-15.943Z" transform="translate(-169 1315)" fill="#202020"/>
      <g id="Rectangle_3-3" data-name="Rectangle 3" transform="translate(223 1269)" fill="#fff" stroke="#a4a4a4" stroke-width="1">
        <rect width="319" height="65" rx="5" stroke="none"/>
        <rect x="0.5" y="0.5" width="318" height="64" rx="4.5" fill="none"/>
      </g>
      <text id="Block" transform="translate(239 1315)" fill="#202020" font-size="36" font-family="Roboto-Medium, Roboto" font-weight="500"><tspan x="0" y="0">Block</tspan></text>
      <g id="Group_4-2" data-name="Group 4" transform="translate(-501.933 -1837.664)">
        <path id="Path_19-2" data-name="Path 19" d="M1007.763,3131.834l9.17,9.17,9.17-9.17,2.83,2.83-12,12-12-12Z" fill="#202020"/>
        <path id="Path_20-2" data-name="Path 20" d="M992.933,3114.664h48v48h-48Z" fill="none"/>
      </g>
      <g id="Group_7-2" data-name="Group 7" transform="translate(-836 931)">
        <g id="Group_18" data-name="Group 18" transform="translate(755.443 -1375.5)">
          <path id="Path_32" data-name="Path 32" d="M-182.751,1749.972a8.85,8.85,0,0,0,8.838-8.868l.03-17.736a8.856,8.856,0,0,0-8.868-8.868,8.865,8.865,0,0,0-8.868,8.868V1741.1A8.875,8.875,0,0,0-182.751,1749.972Zm15.667-8.868c0,8.868-7.493,15.075-15.667,15.075s-15.667-6.207-15.667-15.075h-5.025c0,10.094,8.04,18.43,17.736,19.864v9.7h5.912v-9.7c9.7-1.434,17.736-9.77,17.736-19.864Z" transform="translate(4.78 1.912)" fill="#202020"/>
          <path id="Path_33" data-name="Path 33" d="M-213.443,1710.5H-142.5v70.943h-70.943Z" transform="translate(0)" fill="none"/>
        </g>
      </g>
    </g>
    <g id="Group_22" data-name="Group 22" transform="translate(377 -1132)">
      <g id="Group_21" data-name="Group 21">
        <g transform="matrix(1, 0, 0, 1, -359.5, 1263)" filter="url(#Rectangle_3)">
          <g id="Rectangle_3-4" data-name="Rectangle 3" transform="translate(582.5 273)" fill="#fff" stroke="#a4a4a4" stroke-width="1">
            <rect width="351" height="197" rx="3" stroke="none"/>
            <rect x="0.5" y="0.5" width="350" height="196" rx="2.5" fill="none"/>
          </g>
        </g>
        <path id="Path_74" data-name="Path 74" d="M16.928-5.959H7.014L4.939,0H.316L9.984-25.594h3.99L23.66,0H19.02ZM8.262-9.545H15.68L11.971-20.162ZM36.809-5.168a1.936,1.936,0,0,0-.94-1.74,10,10,0,0,0-3.12-1.055,16.039,16.039,0,0,1-3.639-1.16q-3.2-1.547-3.2-4.482a5.063,5.063,0,0,1,2.074-4.113,8.2,8.2,0,0,1,5.273-1.652,8.532,8.532,0,0,1,5.511,1.688,5.355,5.355,0,0,1,2.1,4.377H36.6a2.632,2.632,0,0,0-.914-2.048,3.5,3.5,0,0,0-2.426-.817,3.785,3.785,0,0,0-2.294.65,2.05,2.05,0,0,0-.888,1.74,1.731,1.731,0,0,0,.826,1.529,11.361,11.361,0,0,0,3.34,1.1,15.81,15.81,0,0,1,3.946,1.318A5.588,5.588,0,0,1,40.315-8a4.684,4.684,0,0,1,.694,2.6,4.956,4.956,0,0,1-2.127,4.157A9.069,9.069,0,0,1,33.311.352a9.835,9.835,0,0,1-4.166-.844A6.856,6.856,0,0,1,26.3-2.812a5.492,5.492,0,0,1-1.02-3.182h4.148a3.01,3.01,0,0,0,1.143,2.329,4.44,4.44,0,0,0,2.795.817,4.319,4.319,0,0,0,2.566-.642A1.986,1.986,0,0,0,36.809-5.168Zm14.1-2.988-1.9,1.951V0H44.736V-27h4.271v15.574l1.336-1.67L55.6-19.02h5.133l-7.066,7.928L61.488,0H56.549Zm21.639-2.25a26.786,26.786,0,0,1,1.09-7.708,21.157,21.157,0,0,1,3.2-6.521A12.7,12.7,0,0,1,81.3-28.67l.844,2.5a13.048,13.048,0,0,0-4.017,5.739A25.7,25.7,0,0,0,76.447-11.6L76.43-10.09a27.944,27.944,0,0,0,1.5,9.5,14,14,0,0,0,4.21,6.354L81.3,8.139a12.725,12.725,0,0,1-4.474-4.052,21.154,21.154,0,0,1-3.2-6.521A27.842,27.842,0,0,1,72.545-10.406Zm11.689.756a11.308,11.308,0,0,1,2.039-7.058,6.533,6.533,0,0,1,5.467-2.663,6.208,6.208,0,0,1,4.887,2.109V-27H100.9V0H97.031L96.82-1.969A6.3,6.3,0,0,1,91.705.352,6.5,6.5,0,0,1,86.3-2.338,11.722,11.722,0,0,1,84.234-9.65Zm4.271.369a7.917,7.917,0,0,0,1.116,4.526,3.622,3.622,0,0,0,3.173,1.626,4.034,4.034,0,0,0,3.832-2.338v-8.121a3.973,3.973,0,0,0-3.8-2.285,3.649,3.649,0,0,0-3.2,1.644A8.817,8.817,0,0,0,88.506-9.281ZM113.73.352a8.837,8.837,0,0,1-6.583-2.558,9.278,9.278,0,0,1-2.522-6.812v-.527a11.413,11.413,0,0,1,1.1-5.089,8.362,8.362,0,0,1,3.085-3.489,8.156,8.156,0,0,1,4.43-1.248,7.465,7.465,0,0,1,6,2.479q2.118,2.479,2.118,7.014v1.723H108.932A5.712,5.712,0,0,0,110.5-4.43a4.723,4.723,0,0,0,3.472,1.371,5.784,5.784,0,0,0,4.781-2.373l2.3,2.2a7.69,7.69,0,0,1-3.05,2.646A9.548,9.548,0,0,1,113.73.352Zm-.51-16.295a3.6,3.6,0,0,0-2.839,1.23A6.378,6.378,0,0,0,109-11.285h8.139V-11.6A5.192,5.192,0,0,0,116-14.845,3.57,3.57,0,0,0,113.221-15.943ZM126.176,0V-15.855h-2.9V-19.02h2.9v-1.74a6.532,6.532,0,0,1,1.758-4.887,6.733,6.733,0,0,1,4.922-1.723,9.872,9.872,0,0,1,2.391.316l-.105,3.34a8.413,8.413,0,0,0-1.635-.141q-3.059,0-3.059,3.146v1.688h3.867v3.164h-3.867V0ZM148.8,0a6.592,6.592,0,0,1-.492-1.775A6.649,6.649,0,0,1,143.314.352a6.71,6.71,0,0,1-4.676-1.635,5.23,5.23,0,0,1-1.811-4.043,5.4,5.4,0,0,1,2.259-4.667,10.946,10.946,0,0,1,6.46-1.626h2.619v-1.248a3.331,3.331,0,0,0-.826-2.364,3.275,3.275,0,0,0-2.514-.888,3.762,3.762,0,0,0-2.391.729,2.258,2.258,0,0,0-.932,1.854h-4.271a4.741,4.741,0,0,1,1.037-2.927,6.909,6.909,0,0,1,2.821-2.136,9.91,9.91,0,0,1,3.981-.773,7.981,7.981,0,0,1,5.326,1.679,5.986,5.986,0,0,1,2.039,4.72v8.578a9.722,9.722,0,0,0,.721,4.1V0Zm-4.693-3.076a4.859,4.859,0,0,0,2.382-.615,4.11,4.11,0,0,0,1.679-1.652V-8.93h-2.3a6.3,6.3,0,0,0-3.568.826,2.676,2.676,0,0,0-1.2,2.338,2.5,2.5,0,0,0,.817,1.96A3.165,3.165,0,0,0,144.105-3.076Zm24.328,1.213A6.625,6.625,0,0,1,163.09.352a5.9,5.9,0,0,1-4.685-1.811A7.7,7.7,0,0,1,156.814-6.7V-19.02h4.271V-6.75q0,3.621,3.006,3.621,3.111,0,4.2-2.232V-19.02h4.271V0h-4.025ZM181.459,0h-4.271V-27h4.271Zm10-23.643v4.623h3.357v3.164h-3.357V-5.238a2.338,2.338,0,0,0,.431,1.573,2,2,0,0,0,1.538.483,6.58,6.58,0,0,0,1.494-.176v3.3a10.52,10.52,0,0,1-2.812.4q-4.922,0-4.922-5.432V-15.855h-3.129V-19.02h3.129v-4.623Zm14.854,13.518a26.448,26.448,0,0,1-1.072,7.532,21.341,21.341,0,0,1-3.252,6.609,12.735,12.735,0,0,1-4.553,4.122l-.844-2.373a13.509,13.509,0,0,0,4.175-6.231,27.68,27.68,0,0,0,1.538-9.571v-.4a28.089,28.089,0,0,0-1.336-8.9,15.7,15.7,0,0,0-3.832-6.442l-.545-.492.844-2.391A12.6,12.6,0,0,1,201.8-24.8a20.983,20.983,0,0,1,3.234,6.214A25.883,25.883,0,0,1,206.3-11.5ZM16.928,59.041H7.014L4.939,65H.316L9.984,39.406h3.99L23.66,65H19.02ZM8.262,55.455H15.68L11.971,44.838ZM30.691,65H26.42V38h4.271Zm9.193,0H35.613V38h4.271Zm3.85-9.686a11.215,11.215,0,0,1,1.107-5.036,8.088,8.088,0,0,1,3.111-3.445,8.766,8.766,0,0,1,4.605-1.2,8.308,8.308,0,0,1,6.249,2.479A9.748,9.748,0,0,1,61.4,54.682l.018,1a11.315,11.315,0,0,1-1.081,5.027,7.991,7.991,0,0,1-3.094,3.428,8.831,8.831,0,0,1-4.649,1.213,8.25,8.25,0,0,1-6.442-2.681,10.267,10.267,0,0,1-2.417-7.146Zm4.271.369a7.664,7.664,0,0,0,1.213,4.6,3.943,3.943,0,0,0,3.375,1.661,3.9,3.9,0,0,0,3.366-1.687,8.465,8.465,0,0,0,1.2-4.939,7.565,7.565,0,0,0-1.239-4.57,4.177,4.177,0,0,0-6.688-.026A8.3,8.3,0,0,0,48.006,55.684Zm33.732,3.428L84.762,45.98h4.166L83.742,65H80.227L76.148,51.939,72.141,65H68.625l-5.2-19.02h4.166l3.076,12.99,3.9-12.99h3.217ZM2.6,130V104.406h8.771q4.342,0,6.609,1.74a6.129,6.129,0,0,1,2.268,5.186A5.527,5.527,0,0,1,19.3,114.5a6.11,6.11,0,0,1-2.777,2.18,5.649,5.649,0,0,1,3.208,2.118,6.169,6.169,0,0,1,1.134,3.735,6.814,6.814,0,0,1-2.312,5.537Q16.242,130,11.936,130Zm4.447-11.549v8h4.939a4.78,4.78,0,0,0,3.27-1.037,3.64,3.64,0,0,0,1.178-2.883q0-3.99-4.078-4.078Zm0-3.27h4.359a5.041,5.041,0,0,0,3.243-.94,3.222,3.222,0,0,0,1.169-2.663,3.266,3.266,0,0,0-1.081-2.742,5.459,5.459,0,0,0-3.366-.844H7.049ZM29.443,130H25.172V103h4.271Zm3.85-9.686a11.215,11.215,0,0,1,1.107-5.036,8.088,8.088,0,0,1,3.111-3.445,8.766,8.766,0,0,1,4.605-1.2,8.308,8.308,0,0,1,6.249,2.479,9.748,9.748,0,0,1,2.593,6.574l.018,1a11.315,11.315,0,0,1-1.081,5.027,7.991,7.991,0,0,1-3.094,3.428,8.831,8.831,0,0,1-4.649,1.213,8.25,8.25,0,0,1-6.442-2.681,10.267,10.267,0,0,1-2.417-7.146Zm4.271.369a7.664,7.664,0,0,0,1.213,4.6,3.943,3.943,0,0,0,3.375,1.661,3.9,3.9,0,0,0,3.366-1.687,8.465,8.465,0,0,0,1.2-4.939,7.565,7.565,0,0,0-1.239-4.57,4.177,4.177,0,0,0-6.688-.026A8.3,8.3,0,0,0,37.564,120.684ZM62.4,126.941a3.862,3.862,0,0,0,2.654-.932,3.183,3.183,0,0,0,1.125-2.3h4.025a6.274,6.274,0,0,1-1.107,3.313,7.264,7.264,0,0,1-2.812,2.435,8.381,8.381,0,0,1-3.832.9,8.109,8.109,0,0,1-6.328-2.584,10.249,10.249,0,0,1-2.338-7.137v-.439a10.078,10.078,0,0,1,2.32-6.952,8.035,8.035,0,0,1,6.328-2.61,7.814,7.814,0,0,1,5.528,1.978,7.084,7.084,0,0,1,2.241,5.194H66.182a3.911,3.911,0,0,0-1.116-2.689,3.591,3.591,0,0,0-2.663-1.055,3.759,3.759,0,0,0-3.2,1.5,7.669,7.669,0,0,0-1.143,4.562v.686a7.8,7.8,0,0,0,1.116,4.614A3.756,3.756,0,0,0,62.4,126.941Zm17.209-5.1-1.9,1.951V130H73.441V103h4.271v15.574l1.336-1.67L84.3,110.98h5.133l-7.066,7.928L90.193,130H85.254Z" transform="translate(239 1584)" fill="#202020"/>
      </g>
      <g id="Rectangle_3-5" data-name="Rectangle 3" transform="translate(223 1602)" fill="none" stroke="red" stroke-width="3">
        <rect width="351" height="65" stroke="none"/>
        <rect x="1.5" y="1.5" width="348" height="62" fill="none"/>
      </g>
    </g>
  </g>
</svg>
