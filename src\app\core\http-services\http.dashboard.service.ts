import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { GlobalUserService } from "../global-user.service";
import { Observable } from "rxjs";
import { URL } from 'src/app/core/url';
import { FormModel } from "src/app/candidate/dashboard/state/dashboard.models";
import { PersonForm } from "src/app/candidate/application/application.types";
import { upcomingExam } from "src/app/candidate/dashboard/state/dashboard.models/Upcomingexam";
import { ShowRegisterExamModel } from "../Dto/show-register-exam.model";

@Injectable({
  providedIn: "root",
})
export class HttpDashboardService {  

  constructor(private http: HttpClient,
    private global: GlobalUserService) {       
    }

  public getactiveForms(candidateId: number, formTypeIds: number[]): Observable<FormModel[]> {
    let params = {'candidateId': candidateId, 'formTypeIds': formTypeIds};
    return this.http.get<FormModel[]>(`${URL.BASE_URL}dashboard/activeform`, {params});          
  }

  public getUpcomingExam(candidateId: number) : Observable<upcomingExam[]> {
    return this.http.get<upcomingExam[]>(URL.BASE_URL + `exam/upcomingexam?candidateId=${candidateId}`);
  }
  
  public getPersonForms(candidateId: number, formTypeIds: number[]): Observable<PersonForm[]> {
    let params = {'candidateId': candidateId, 'formTypeId': formTypeIds};
    return this.http.get<PersonForm[]>(`${URL.BASE_URL}form/personform`, {params});          
  }

  public getshowregisterExam(personTenantRoleId: number): Observable<ShowRegisterExamModel> {
    return this.http.get<ShowRegisterExamModel>(URL.BASE_URL + `Exam/showregisterexam?personTenantRoleId=${personTenantRoleId}`);
  }
}
