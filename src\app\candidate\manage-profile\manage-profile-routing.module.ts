import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ManageProfileComponent } from './manage-profile.component';
import { ProfileEditComponent } from './profile-edit/profile-edit.component';
// import { AuthGuard } from 'src/app/core/auth.guard';

const routes: Routes =
[
  {
    path: '',
    component: ManageProfileComponent,
    // canActivate: [AuthGuard],
    data: {
      title: 'profile',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Manage Profile',
          url: ''
        }
      ]
    },
  },

  {
    path: 'profile-edit', component: ProfileEditComponent,
    // canActivate: [AuthGuard],
    data: {
      title: 'profileEdit',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Manage Profile',
          url: '/manage-profile'
        },
        {
          label: 'Edit Profile',
          url: ''
        },

      ]
    },
  },
  {
    path: 'correction-form', 
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    // canActivate: [AuthGuard],
    data: {
      title: 'correction-form',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Manage Profile',
          url: '/manage-profile'
        },
        {
          label: 'Edit Profile',
          url: '/manage-profile/profile-edit'
        },
        {
          label: 'Correction Form',
          url: ''
        },

      ]
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ManageProfileRoutingModule { }
