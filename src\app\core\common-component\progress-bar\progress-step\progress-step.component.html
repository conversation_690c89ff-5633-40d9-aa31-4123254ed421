<!-- <div fxLayout="column" fxFlex class="name-container">
    <p class="mat-body-4">{{ reviewer }}</p>
</div> -->
<div fxLayout="column" fxFlex>
    <div fxLayout="row" fxFlex>
        <div class="status-icon1">

            <div class="status-icon"
                [ngClass]="{'dot': performlog.statusTypeId == 1, 'selected-dot': performlog.statusTypeId ==2, 'incomplete-dot': performlog.statusTypeId == 3}">
                <img class="" src="{{performlog.iconUrl}}">
            </div>
        </div>
        <div fxLayout="row" fxFlex class="name-container font-semibold item-center pl-4 text-xs">
            {{ performlog.reviewer }}
        </div>

    </div>
    <div fxLayout="row" fxFlex>
        <div class="line-box" fxLayout="row">
            <div *ngIf="!isLast" class="line">
            </div>
        </div>
        <div fxLayout="column">
            <p class="mat-body-2 pl-4 text-xs font-normal">
                {{ performlog.name }}</p>
            <div class="pl-4 text-xs">
                <p class="mat-body-1 stepper-date">{{ performlog.actionOn |date:'d MMM, y | h:mm a' }}</p>
            </div>
            <div>
                <div class="mat-body-3 pl-4 text-xs" [ngClass]="{'limitTextHeight': isReadMore ,onhover:onHover}"
                    (click)="showText()">
                    {{ performlog.comment }}
                    <p *ngIf="isReadMore">...</p>
                </div>
            </div>
        </div>
    </div>
</div>