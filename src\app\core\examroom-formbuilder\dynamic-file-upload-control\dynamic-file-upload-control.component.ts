import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewChild } from "@angular/core";
import { FormGroup } from "@angular/forms";
import {
  DynamicFileUploadModel,
    DynamicFormControlComponent,
    DynamicFormControlCustomEvent,
    DynamicFormControlLayout,
    DynamicFormLayoutService,
    DynamicFormValidationService,
} from "@ng-dynamic-forms/core";
import { FileUploadControlComponent } from "../file-upload-control/file-upload-control.component";

@Component({
  selector: 'exai-dynamic-file-upload-control',
  templateUrl: './dynamic-file-upload-control.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class DynamicFileUploadControlComponent extends DynamicFormControlComponent {
    @Input() group!: FormGroup;
    @Input() layout?: DynamicFormControlLayout;
    @Input() model!: DynamicFileUploadModel;

    @Output() blur: EventEmitter<any> = new EventEmitter();
    @Output() change: EventEmitter<any> = new EventEmitter();
    @Output() customEvent: EventEmitter<DynamicFormControlCustomEvent> = new EventEmitter();
    @Output() focus: EventEmitter<any> = new EventEmitter();

    @ViewChild(FileUploadControlComponent) FileUploadControlComponent!: FileUploadControlComponent;

    constructor(protected layoutService: DynamicFormLayoutService,
                protected validationService: DynamicFormValidationService) {
        super(layoutService, validationService);
    }
}