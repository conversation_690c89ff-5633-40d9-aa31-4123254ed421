import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExaminationResultComponent } from './examination-result.component';

const routes: Routes = [
  {
    path: '',
    component: ExaminationResultComponent,
    // canActivate: [AuthGuard],
    data: {
      title: 'Grievance',
      breadcrumb: [
        {
          label: 'Home',
          url: '/Examination Result'
        },
        {
          label: 'Examination Result',
          url: ''
        },
      ]
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ExaminationResultRoutingModule { }
