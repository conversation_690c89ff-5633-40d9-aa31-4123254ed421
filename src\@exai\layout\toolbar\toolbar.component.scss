:host {
  background: var(--toolbar-background);
  box-sizing: border-box;
  color: var(--toolbar-color);
  display: block;
  white-space: nowrap;
  width: 100%;
  z-index: var(--toolbar-z-index);
}

.toolbar {
  height: var(--toolbar-height);
  place-content: space-between!important;
}

.dropdown2 {
  background: var(--background-card);
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  box-shadow: var(--elevation-z4);
  max-width: 100vw;
  width: 180px;
  overflow: hidden;
  @apply rounded;
}

.mat-icon {
  color: var(--text-color2);
}

a {
  color: var(--toolbar-color);
  text-decoration: none;
}

.vl {
  border-left: 1px solid var(--toolbar-vl-color);
  height: 50px;
}

.logo {
  width: 8rem;
}

.toolbar-active{
  color: var(--toolbar-icon-color-active)
}

// .credLogo
@media screen and (min-width:768px) {
  #credLogo {max-width: 8rem;}
}
@media screen and (max-width:767px) {
  #credLogo {max-width: 2rem;}
}
// .credLogo
.clr{
  color:#0076C1;
}

.button_life{
  width: 20px !important;
    height: 20px !important;
    margin: 10px 0px 10px 12px !important;
    background-color: #0076C1;
}

.console-color{
  color: rgba(0, 188, 212, 1) !important;
  background: rgba(0, 150, 136, .1) !important;
}