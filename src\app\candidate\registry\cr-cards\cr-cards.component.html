<div class=" card shadow-none cardBorder h-full" fxFlex="auto " gdGap="">

    <div class="bg-color ">
        <div class="flex justify-between pt-2 " fxLayout="row ">
            <div class="mx-4">
                <h6 class="t-xs font -mt-1 mb-3 space">
                    <strong *ngIf="isCertificate">{{certificate.RegistryName}}</strong>
                    <strong *ngIf="!isCertificate">
                        <span *ngIf="request.certificateName">{{request.certificateName}} -</span>
                        {{request.name}}</strong>
                </h6>
            </div>

        </div>
        <div fxLayout="row " class="pb-1">
            <div class="mx-4">
                <h6 *ngIf="isCertificate " class="text_size text_color2 -mt-4">Registration No: {{certificate.CertNumber}}
                </h6>
                <span *ngIf="request?.formTypeId!=6">
                <h6 *ngIf="!isCertificate && request.certificateNumber !=null" class="text_size text_color2 -mt-4">Registration No:
                    {{request.certificateNumber}}</h6>
                </span>
            </div>
        </div>
    </div>
    <div fxLayout="column ">
        <div class="mx-4">
            <h4 *ngIf="isCertificate" class="status t-xs ">Status</h4>
            <h4 *ngIf="!isCertificate" class="status t-xs ">Current Status</h4>
        </div>
        <div class="small-container ml-4 " fxFlexFill>

            <img *ngIf="isCertificate" [src]="getImagePath(certificate.RegistryStatus)" class='inline iconSize'>
            <img *ngIf="!isCertificate" [src]="request.iconUrl" class='inline iconSize'>

            <span *ngIf="isCertificate" class="t-xs ml-2 active2 -mt-3 ">{{certificate.RegistryStatus}}</span>
            <span *ngIf="!isCertificate" class="t-xs ml-2 active2 -mt-3 ">{{request.status}}</span>


            <div *ngIf="isCertificate" class="text_style1 "><span
                    class="t-xs status1 -mt-3 ">{{getDateFormat(certificate.StatusSinceDate +'Z')}}</span>
            </div>
            <div *ngIf="!isCertificate" class="text_style1 "><span
                    class="t-xs status1 -mt-3 ">{{getDateFormat(request.lastUpdatedDate)}}</span>
            </div>

            <div *ngIf="!isCertificate && request.statusId === 3" class="flex-column">
                <span *ngIf="!isCertificate && request.statusId === 3" class="text_size text_color2">
                    Reason for Rejection:
                </span>
                <span *ngIf="!isCertificate && request.statusId === 3" class="text_size text_color2 minimise" matTooltip="{{request.comment}}">
                    {{request.comment}}
                </span>
            </div>
            <div *ngIf="!isCertificate && request.statusId === 4" class="flex-column" matTooltip="{{request.comment}}">
                <span *ngIf="!isCertificate && request.statusId === 4" class="text_size text_color2 minimise">
                    {{request.comment}}
                </span>
            </div>

            <div class="pt-2 " gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr " exaiContainer>
                <div *ngIf="isCertificate" gdColumn="1/3 " gdColumn.lt-md="1/3 " gdColumn.lt-sm="1/3 ">
                    <div class="h4 status t-xs ">Issued Date</div>
                </div>
                <div *ngIf="isCertificate" gdColumn="4/-1 " gdColumn.lt-md="4/-1 " gdColumn.lt-sm="4/-1 ">
                    <div class="h4 status t-xs ">Expiry Date</div>
                </div>
            </div>
            <div class="pt-1 pb-2 " gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr " exaiContainer>
                <div gdColumn="1/3 " gdColumn.lt-md="1/3 " gdColumn.lt-sm="1/3 ">
                    <div *ngIf="isCertificate" class="h4 status1 t-xs "> {{certificate.EffectiveDate | date:
                        "MM/dd/yyyy":"+0000" }}</div>
                </div>
                <div gdColumn="4/-1 " gdColumn.lt-md="4/-1 " gdColumn.lt-sm="4/-1">
                    <div *ngIf="isCertificate" class="h4 status1 t-xs ">{{certificate.ExpirationDate | date:
                        "MM/dd/yyyy":"+0000" }}</div>
                </div>
            </div>
            <div class="flex text-left text-base cursor-pointer text_color2 ">
                <button class="btn-4 mt-3" *ngIf="isCertificate" [routerLink]="['/registry/view-certificate']"
                    (click)="saveLSdata()">
                    View Registration
                </button>

                <button class="btn-4 mt-3 ml-4" *ngIf="(certificate?.AllowRenewal|| DCRenewalActive) && !showrenewal" (click)="viewRenewal(certificate)">
                    Renewal
                </button>

                <button class="btn-4 mt-3 ml-4" *ngIf="(certificate?.AllowReinstatement)" (click)="viewRenewal(certificate)">
                    Reinstate
                </button>
                <button *ngIf="!isCertificate && (request.status === 'Drafted'  || request.status === 'Change Request')" mat-button color="#209E91"
                    class="btn-4 font-bold t-xs" (click)="goToFinishForm()">
                    Edit Form
                </button>
                <button *ngIf="!isCertificate && request.status !== 'Drafted' && request.status !== 'Change Request' && request.status !== 'Cancelled'" mat-button color="#209E91"
                    class="btn-4 font-bold t-xs" (click)="goToFinishForm()">
                    View Form
                </button>
                <!-- <button *ngIf="request?.allowApplyAgain" mat-button color="#209E91"
                    class="btn-4 font-bold t-xs left-mar" (click)="applyAgain()">
                    Apply Again
                </button> -->
            </div>
        </div>
    </div>
    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="pt-2 px-4 pb-1 font-bold t-xs">

    </div>

</div>