import { Component, OnInit } from '@angular/core';
import { SystemCheckService } from '../system-check.service';

@Component({
  selector: 'exai-report',
  templateUrl: './report.component.html',
  styleUrls: ['./report.component.scss']
})
export class ReportComponent implements OnInit {
  errorDetails: any;
  browserDetails: any;
  imageSource: string = "";
  errorMessage: any = "";

  constructor(private _service: SystemCheckService) {
    this._service.reportDetails = 1;
    if (this._service.errorDetails != undefined && typeof this._service.errorDetails == 'object') {
      this.errorDetails = this._service.errorDetails;
    } else {
      this._service.errorStatus.next(1);
    }
  }

  ngOnInit(): void {
    if (this.errorDetails.message == 'camera-1') {
      this.imageSource = 'assets/img/webcam.svg';
      this.errorMessage = this._service.cameraUsed;
    } else if (this.errorDetails.message == 'microphone-1') {
      this.imageSource = 'assets/img/microphone.svg';
      this.errorMessage = this._service.microphoneUsed;
    } else {
      this.detectBrowser();
    }
  }

  recheck():void{
    this._service.errorStatus.next(1);
  }

  detectBrowser() {
    var navigator: any = window && window.navigator;
    // Returned result object.
    var result: any = {};
    result.browser = null;
    result.version = null;
    // Fail early if it's not a browser
    if (typeof window === "undefined" || !window.navigator) {
      result.browser = "Not a browser.";
    }

    if (navigator.mozGetUserMedia) {
      // Firefox.
      result.browser = "firefox";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /Firefox\/(\d+)\./,
        1
      );
      if (this.errorDetails.message == "camera") {
        this.imageSource = "assets/img/firefox-camera.svg";
        this.errorMessage = this._service.firefoxCamera;
      } else if ((this.errorDetails.message == "microphone")) {
        this.imageSource = "assets/img/firefox-mic.svg";
        this.errorMessage = this._service.firefoxMicrophone;
      } else if (this.errorDetails.message == "cookie") {
        this.imageSource = "assets/img/firefox-cookies.svg";
        this.errorMessage = this._service.firefoxCookies;
      }
    } else if (navigator.webkitGetUserMedia) {
      // Chrome, Chromium, Webview, Opera.
      // Version matches Chrome/WebRTC version.
      result.browser = "chrome";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /Chrom(e|ium)\/(\d+)\./,
        2
      );
      if (this.errorDetails.message == "camera") {
        this.imageSource = "assets/img/chrome-camera.svg";
        this.errorMessage = this._service.chromeCamera;
      } else if ((this.errorDetails.message == "microphone")) {
        this.imageSource = "assets/img/chrome-mic.svg";
        this.errorMessage = this._service.chromeMicrophone;
      } else if (this.errorDetails.message == "cookie") {
        this.imageSource = "assets/img/chrome-cookies.svg";
        this.errorMessage = this._service.chromeCookies;
      }
    } else if (
      navigator.mediaDevices &&
      navigator.userAgent.match(/Edge\/(\d+).(\d+)$/)
    ) {
      // Edge.
      result.browser = "edge";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /Edge\/(\d+).(\d+)$/,
        2
      );
      if (this.errorDetails.message == "camera") {
        this.imageSource = "assets/img/chrome-camera.svg";
        this.errorMessage = this._service.chromeCamera;
      } else if ((this.errorDetails.message == "microphone")) {
        this.imageSource = "assets/img/chrome-mic.svg";
        this.errorMessage = this._service.chromeMicrophone;
      } else if (this.errorDetails.message == "cookie") {
        this.imageSource = "assets/img/chrome-cookies.svg";
        this.errorMessage = this._service.chromeCookies;
      }
    } else if (
      window["RTCPeerConnection"] &&
      navigator.userAgent.match(/AppleWebKit\/(\d+)\./)
    ) {
      // Safari.
      result.browser = "safari";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /AppleWebKit\/(\d+)\./,
        1
      );
      if (this.errorDetails.message == "camera") {
        this.imageSource = "assets/img/safari-camera.svg";
        this.errorMessage = this._service.chromeCamera;
      } else if ((this.errorDetails.message == "microphone")) {
        this.imageSource = "assets/img/safari-mic.svg";
        this.errorMessage = this._service.chromeMicrophone;
      } else if (this.errorDetails.message == "cookie") {
        this.imageSource = "assets/img/chrome-cookies.svg";
        this.errorMessage = this._service.chromeCookies;
      }
    } else {
      result.browser = "Not a supported browser.";
    }
  }

}
