import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { exaiModule } from '../@exai/exai.module';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { CustomLayoutModule } from './candidate/custom-layout/custom-layout.module';
import { LocationStrategy, HashLocationStrategy, PathLocationStrategy } from '@angular/common';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { TokenInterceptorService } from './core/token-interceptor.service';
import { StoreRouterConnectingModule } from '@ngrx/router-store';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { appReducer } from './candidate/state/candidate.state';
import { environment } from 'src/environments/environment';
import { CustomSerializer } from './candidate/state/router/custom-serializer';
import { SharedEffects } from './candidate/state/shared/shared.effects';
import { BufferInterceptor } from './core/buffer-service.service';
import { AppService } from './app.service';
import { AuthGuard } from './core/auth.guard';
import { DataService } from './candidate/shared/data-service';
import { HttpAccountService } from './core/http-services/http.account.service';
import { LoggerInterceptor } from './logs/logger.interceptor';
import { loggerReducer } from './logs/logger.reducers';
import { ReplaySessionService } from './core/openreplay.services';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    MatSnackBarModule,
    exaiModule,
    CustomLayoutModule,
    EffectsModule.forRoot([SharedEffects]),
    StoreModule.forRoot(appReducer),
    StoreDevtoolsModule.instrument({
      logOnly: environment.production,
    }),
    StoreRouterConnectingModule.forRoot({
      serializer: CustomSerializer,
    }),
    StoreModule.forFeature("httpLogger", loggerReducer),
  ],
  providers: [
    { provide: LocationStrategy, useClass: PathLocationStrategy },
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: LoggerInterceptor, multi: true },
    AuthGuard,
    DataService,
    AppService,
    HttpAccountService,
    ReplaySessionService
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
