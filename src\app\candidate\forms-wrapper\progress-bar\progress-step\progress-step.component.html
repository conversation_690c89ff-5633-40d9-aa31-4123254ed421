<div fxLayout="column" fxFlex>
    <div fxLayout="row" fxFlex>
        <mat-icon class="text-base items-center flex justify-around" [ngClass]="{'dot': status == 'VALID', 'selected-dot': status =='UNTOUCHED' , 'invalid':status == 'INVALID'}">
            radio_button_checked</mat-icon>
        <div fxLayout="column" fxFlex>
            <p class="mat-body-1 t-xs" [ngClass]="{'dot': status == 'VALID', 'selected-dot': status =='UNTOUCHED' , 'invalid':status == 'INVALID'}">
                {{ sectionName }}</p>
        </div>
    </div>
    <div fxLayout="row" fxFlex>
        <div class="line-box" fxLayout="row">
            <div *ngIf="index != size-1" class="line">
            </div>
        </div>
    </div>
</div>