import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatIconModule } from '@angular/material/icon';
import { RegistryProgressBarComponent } from './progress-bar.component';
import { RegistryProgressLabelComponent } from './progress-label/progress-label.component';
import { RegistryProgressStepperComponent } from './progress-stepper/progress-stepper.component';
import { RegistryProgressStepComponent } from './progress-step/progress-step.component';
@NgModule({
  declarations: [
    RegistryProgressBarComponent,
    RegistryProgressLabelComponent,
    RegistryProgressStepComponent,
    RegistryProgressStepperComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    FlexLayoutModule,
    MatButtonModule,
    MatIconModule
  ],
  exports: [RegistryProgressBarComponent]
})
export class RegistryProgressBarModule { }
