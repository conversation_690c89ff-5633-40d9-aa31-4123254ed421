import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AfterViewInit, Component, OnDestroy, OnInit } from '@angular/core';
import { SystemCheckService } from '../system-check.service';
import * as UAParser from "../../../../assets/script/ua-parser";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from 'src/environments/environment';

@Component({
  selector: 'exai-check',
  templateUrl: './check.component.html',
  styleUrls: ['./check.component.scss']
})
export class CheckComponent implements OnInit, AfterViewInit, OnDestroy {
  statusTitle: string;
  camVerification: any = { status: true, message: "Verifiying.." };
  microphoneVerification: any = { status: true, message: "Verifiying.." };
  screenShareVerification: any = { status: true, message: "Verifiying.." };
  browserVerification: any = { status: true, message: "Verifiying.." };
  osVerification: any = { status: true, message: "Verifiying.." };
  ramVerification: any = { status: true, message: "Verifiying.." };
  processorVerification: any = { status: true, message: "Verifiying.." };
  cpuVerification: any = { status: true, message: "Verifiying.." };
  cookieVerification: any = { status: true, message: "Verifiying.." };
  webSocketVerification: any = { status: true, message: "Verifiying.." };
  internetSpeedResults: any = {
    upload: { status: true, message: "Verifying.." },
    download: { status: true, message: "Verifying.." },
  };
  browserCompatibility: any = { message: "Verifying..", status: true };
  showResults = false;
  multiDownloadPackets: any[] = [];
  activeStatus: string = '1';
  completeStatus: boolean = false;
  audioSelect: string = '';
  videoSelect: string = '';
  videoStream: MediaStream;
  audioStream: MediaStream;
  browserResults: any;
  allSet: boolean;
  parser: any = UAParser();
  browserName: string;
  osName: any;
  internetSpeed: any;
  results: any;
  animeIcons = {
  
    hardware: {
      path: "assets/lottie/hardware.json",
      autoplay: true,
      loop: true,
    },
    software: {
      path: "assets/lottie/software.json",
      autoplay: true,
      loop: true,
    },
    browser: { path: "assets/lottie/browser-config.json", autoplay: true, loop: true },
    network: { path: "assets/lottie/network.json", autoplay: true, loop: true },
    bot: { path: "assets/lottie/bot.json", autoplay: true, loop: true },
  };
  successIcon = "fa fa-check-circle-o success";
  failIcon = "fa fa-times-circle-o";

  constructor(private _service: SystemCheckService, private http: HttpClient, private snackbar: SnackbarService) {
    this.statusTitle = 'Checking hardware configurations';
    this._service.videoStream == undefined && this._service.audioStream == undefined ?
      this._service.errorStatus.next(1) : "";

    this.multiDownloadPackets = [
      { url:environment.redirectUrl + "candidate/assets/img/logo-1.jpg", size: 1105920, passed: false },
      { url:environment.redirectUrl +  "candidate/assets/img/logo-2.jpg", size: 2740382, passed: false },
      { url:environment.redirectUrl +  "candidate/assets/img/logo-3.jpg", size: 4866701, passed: false }
    ]

    this.detectBrowser();
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    this.audioSelect = this._service.audioStream;
    this.videoSelect = this._service.videoStream;
    this.hardwareConfig();
  }

  async hardwareConfig() {
    this.activeStatus = "1";
    await this.checkCamera("new");
    await this.checkMicrophone("new");
    await this.processorCheck();
    await this.ramCheck();
    await this.cpuCheck();
    setTimeout(() => {
      this.softwareConfig();
    }, 2000);
  }

  async softwareConfig() {
    this.activeStatus = "2";
    this.statusTitle = 'Checking software configurations';
    await this.osCheck();
    await this.browserCheck();
    setTimeout(() => {
      this.browserConfig();
    }, 2000);
  }

  async browserConfig() {
    try {
      this.activeStatus = "3";
      this.statusTitle = 'Checking browser configurations';
      await this.cookieCheck();
      await this.screenShareTest();
      setTimeout(() => {
        this.networkConfig();
      }, 2000);
    } catch (e) { }
  }

  async networkConfig() {
    this.activeStatus = "4";
    this.statusTitle = 'Checking network configurations';
    await this.checkInternet();
    await this.downloadSpeed();
    await this.uploadSpeed();
    await this.webSocketCheck();
    this.activeStatus = "5";
    this.statusTitle = '';
    this.completeStatus = true;
    this.shareResults();
  }

  checkInternet() {
    return new Promise((resolve, reject) => {
      this.http.get('https://jsonplaceholder.typicode.com/todos/1')
        .subscribe(data => {
          resolve(true);
        }, (err) => {
          this.internetSpeedResults.download = { status: false, message: 'No internet.' };
          this.internetSpeedResults.upload = { status: false, message: 'No internet.' };
          const networkError = {
            status: true,
            message: 'Make sure you are connected with internet.',
          }
          this.activeStatus = "5";
          resolve(true);
        })
    });
  }

  checkCamera(source) {
    return new Promise((resolve, reject) => {
      const videoSource = this.videoSelect;
      let nav = <any>navigator;
      nav.getUserMedia =
        nav.getUserMedia ||
        nav.webkitGetUserMedia ||
        nav.mozGetUserMedia ||
        nav.msGetUserMedia;
      const constraints = {
        audio: false,
        video: { deviceId: videoSource ? { exact: videoSource } : undefined },
      };
      if (nav.getUserMedia) {
        nav.getUserMedia(
          constraints,
          (stream) => {
            resolve(true);
            this.videoStream = stream;
            this.camVerification = { status: true, message: "Success" };
          },
          (err) => {
            resolve(true);
            if (
              err.message == "Could not start video source" ||
              err.message == "Starting video failed"
            ) {
              this.camVerification = {
                status: false,
                message: "Active on other app.",
              };
              this._service.errorDetails = { message: "camera-1", status: false };
              this.allSet = false;
              this._service.errorStatus.next(3);
            } else {
              this.camVerification = {
                status: false,
                message: "Permission denied"
              };
              this._service.errorDetails = { message: "camera", status: false };
              this.allSet = false;
            }
          }
        );
        return;
      } else if (nav.mediaDevices) {
        nav.mediaDevices.getUserMedia(constraints)
          .then((stream: any) => {
            resolve(true);
            this.videoStream = stream;
            this.camVerification = { status: true, message: "Success" };
          })
          .catch((err: any) => {
            resolve(true);
            if (
              err.message == "Could not start video source" ||
              err.message == "Starting video failed"
            ) {
              this.camVerification = {
                status: false,
                message: "Active on other app."
              };
              this._service.errorDetails = { message: "camera-1", status: false };
              this.allSet = false;
              this._service.errorStatus.next(3);
            } else {
              this.camVerification = {
                status: false,
                message: "Permission denied"
              };
              this._service.errorDetails = { message: "camera", status: false };
              this.allSet = false;
              this._service.errorStatus.next(3);
            }
          });
        return;
      }
      else {
        this.camVerification = { message: "Not supported", status: false };
        resolve(true);
      }
    });
  }

  checkMicrophone(source) {
    return new Promise((resolve, reject) => {
      const audioSource = this.audioSelect;
      let nav = <any>navigator;
      nav.getUserMedia =
        nav.getUserMedia ||
        nav.webkitGetUserMedia ||
        nav.mozGetUserMedia ||
        nav.msGetUserMedia;
      const constraints = {
        audio: { deviceId: audioSource ? { exact: audioSource } : undefined },
        video: false,
      };
      if (nav.getUserMedia) {
        nav.getUserMedia(
          constraints,
          (stream) => {
            resolve(true);
            this.audioStream = stream;
            this.microphoneVerification = { status: true, message: "Success" };
          },
          (err) => {
            resolve(true);
            if (
              err.message == "Could not start audio source" ||
              err.message == "Starting audio failed"
            ) {
              this.microphoneVerification = {
                status: false,
                message: "Active on other apps."
              };
              this._service.errorDetails = { message: "microphone-1", status: false };
              this.allSet = false;
              this._service.errorStatus.next(3);
            } else {
              this.microphoneVerification = {
                status: false,
                message: "Permission denied"
              };
              this._service.errorDetails = {
                message: "microphone",
                status: false,
              };
              this.allSet = false;
              this._service.errorStatus.next(3);
            }
          }
        );
        return;
      } else if (nav.mediaDevices) {
        nav.mediaDevices.getUserMedia(constraints)
          .then((stream: any) => {
            resolve(true);
            this.audioStream = stream;
            this.microphoneVerification = { status: true, message: "Success" };
          })
          .catch((err: any) => {
            resolve(true);
            if (
              err.message == "Could not start audio source" ||
              err.message == "Starting audio failed"
            ) {
              this.microphoneVerification = {
                status: false,
                message: "Active on other apps."
              };
              this._service.errorDetails = { message: "microphone-1", status: false };
              this.allSet = false;
              this._service.errorStatus.next(3);
            } else {
              this.microphoneVerification = {
                status: false,
                message: "Permission denied"
              };
              this._service.errorDetails = {
                message: "microphone",
                status: false,
              };
              this.allSet = false;
              this._service.errorStatus.next(3);
            }
          })
        return;
      }
      else {
        this.microphoneVerification = { message: "Not supported", status: false };
        resolve(true);
      }
    });
  }

  ramCheck() {
    if (navigator["deviceMemory"]) {
      let nav = <any>navigator;
      var n = nav.deviceMemory;
      if (n >= 4) {
        this.ramVerification = { status: true, message: n + " GB", detail: n + " GB" };
      } else {
        this.ramVerification = { status: false, message: "4 GB is minimum requirement" };
      }
    } else if (navigator["deviceMemory"] == undefined) {
      this.ramVerification = { status: true, message: "--", detail: "--" };
    } else {
      this.ramVerification = { status: false, message: "--", detail: "--" };
    }
  }

  processorCheck() {
    if (navigator["hardwareConcurrency"]) {
      let nav = <any>navigator;
      var n = nav.hardwareConcurrency;
      this.processorVerification = { status: true, message: n + " Cores", detail: n + " Cores" };
    } else {
      this.processorVerification = { status: false, message: "--", detail: "--" };
    }
  }

  cpuCheck() {
    this.cpuVerification = {
      status: true,
      message: this.parser.cpu.architecture,
      detail: this.parser.cpu.architecture
    };
  }

  osCheck() {
    this.osName = this.parser.os.name;
    var osName = this.parser.os.name;
    var version = parseInt(this.parser.os.version);
    if (osName.toLowerCase() === "windows" && version >= 7) {
      this.osVerification = {
        status: true,
        message: this.osName + " " + this.parser.os.version,
        detail: this.osName + " " + this.parser.os.version
      }
    } else if (osName.toLowerCase() === "mac os" && version >= 10) {
      this.osVerification = {
        status: true,
        message: this.osName + " " + this.parser.os.version,
        detail: this.osName + " " + this.parser.os.version
      }
    } else if (osName.toLowerCase() === "ubuntu" && version >= 16) {
      this.osVerification = {
        status: true,
        message: this.osName + " " + this.parser.os.version,
        detail: this.osName + " " + this.parser.os.version
      }
    } else if (osName.toLowerCase() === 'chromium os') {
      this.osVerification = {
        status: true,
        message: this.osName + " " + this.parser.os.version,
        detail: this.osName + " " + this.parser.os.version
      }
    } else {
      this.osVerification = {
        status: false,
        message: this.osName + " " + this.parser.os.version + " is not supported."
      };
    }
  }

  browserCheck() {
    var browserName = this.parser.browser.name; //browserResults
    var browserVersion = parseInt(this.parser.browser.major);
    var check = browserName.toLowerCase();
    this.browserName = browserName;
    if (check == "chrome" && browserVersion > 71) {
      return this.browserVerification = {
        status: true,
        message: browserName + " " + browserVersion,
        detail: browserName + " " + browserVersion,
      };
    } else if (check == "chrome" && browserVersion <= 71) {
      return this.browserVerification = {
        status: false,
        message: browserName + " needs to update."
      };
    } else if (check == "firefox" && browserVersion > 52) {
      return this.browserVerification = {
        status: true,
        message: browserName + " " + browserVersion,
        detail: browserName + " " + browserVersion,
      };
    } else if (check == "firefox" && browserVersion <= 52) {
      return this.browserVerification = {
        status: false,
        message: browserName + " needs to update."
      };
    } else if (check == 'edge' && browserVersion > 79) {
      return this.browserVerification = {
        status: true,
        message: browserName + " " + browserVersion,
        detail: browserName + " " + browserVersion,
      };
    } else if (check == 'edge' && browserVersion <= 79) {
      return this.browserVerification = {
        status: false,
        message: browserName + " needs to update."
      };
    } else if (check == 'safari' && browserVersion >= 10) {
      return this.browserVerification = {
        status: true,
        message: browserName + " " + browserVersion,
        detail: browserName + " " + browserVersion,
      };
    } else if (check == 'safari' && browserVersion < 10) {
      return this.browserVerification = {
        status: false,
        message: browserName + " needs to update."
      };
    } else {
      this.browserVerification = {
        status: false,
        message: browserName + " is not supported"
      };
    }
  }

  cookieCheck(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      let nav = <any>navigator;
      if (nav) {
        var n = nav.cookieEnabled;
        if (n == true) {
          this.cookieVerification = { status: true, message: "Enabled" };
          resolve(true)
        } else {
          this.cookieVerification = { status: false, message: "Disabled" };
          this._service.errorDetails = { message: "cookie", status: false };
          this.allSet = false;
          this.stopStream();
          reject(true);
          this._service.errorStatus.next(3);
        }
      }
    })
  }

  webSocketCheck() {
    let n = "WebSocket" in window || "MozWebSocket" in window;
    n == true
      ? (this.webSocketVerification = { status: true, message: "Enabled" })
      : (this.webSocketVerification = { status: false, message: "Browser is not compatible to find." });
  }

  screenShareTest() {
    return new Promise((resolve, reject) => {
      OT.checkScreenSharingCapability((response: any) => {
        if (!response.supported || response.extensionRegistered === false) {
          this.screenShareVerification = {
            status: false,
            message: "Not Supported"
          };
          resolve({ status: false, message: "Not Supported" });
        } else if (response.extensionInstalled === false) {
          this.screenShareVerification = { status: false, message: "Install" };
          resolve({ status: false, message: "Install" });
        } else {
          this.screenShareVerification = { status: true, message: "Success" };
          resolve({ status: true, message: "Success" });
        }
      });
    });
  }

  downloadSpeed() {
    return new Promise(async (resolve, reject) => {
      var sendDate = (new Date()).getTime();
      var passedBits = 0;
      await this.multiDownload();
      this.multiDownloadPackets.forEach(ele => {
        passedBits += ele.passed ? ele.size : 0;
      });
      var receiveDate = (new Date()).getTime();
      var duration = (receiveDate - sendDate) / 1000;
      var bitsLoaded = passedBits * 8;
      var speedBps: any = (bitsLoaded / duration).toFixed(2);
      var speedKbps: any = (speedBps / 1024).toFixed(2);
      var speedMbps: any = (speedKbps / 1024).toFixed(2);
      var speedGbps: any = (speedMbps / 1024).toFixed(2);
      var response = speedMbps;
      response = response + ' Mbps';
      if (parseFloat(speedMbps) >= 3) {
        this.internetSpeedResults.download = { status: true, message: response, detail: response };
      } else {
        this.internetSpeedResults.download = { status: false, message: 'Download speed is less than 3 Mbps' };
      }
      resolve(true);
    });
  }

  multiDownload() {
    return new Promise((resolve, reject) => {
      this.multiDownloadPackets.forEach(async (ele, i) => {
        await this.apiCall(ele.url)
          .then(data => {
            this.multiDownloadPackets[i].passed = true;
            if (i == 2) {
              resolve(true);
            }
          })
          .catch(err => {
            this.multiDownloadPackets[i].passed = false;
            if (i == 2) {
              resolve(true);
            }
          })
      });
    });
  }

  apiCall(url: string) {
    return new Promise((resolve, reject) => {
      $.ajax({
        type: "GET",
        url: url,
        timeout: 60000,
        cache: false,
        success: (data) => {
          resolve(true);
        },
        error: (err) => {
          reject(false);
        }
      });
    })
  }

  uploadSpeed() {
    return new Promise((resolve, reject) => {
      const uploadSize = 1500000;
      const params = { payloadJson: this.randomString(uploadSize) };
      var sendDate = (new Date()).getTime();
      this.http.post('https://api.examroom.ai/api/SystemChecklogs', params)
        .subscribe((data: any) => {
          var receiveDate = (new Date()).getTime();
          var duration = (receiveDate - sendDate) / 1000;
          var bitsLoaded = uploadSize * 8;
          var speedBps: any = (bitsLoaded / duration).toFixed(2);
          var speedKbps: any = (speedBps / 1024).toFixed(2);
          var speedMbps: any = (speedKbps / 1024).toFixed(2);
          var speedGbps: any = (speedMbps / 1024).toFixed(2);
          var response = speedMbps;
          response = response + ' Mbps';
          if (parseFloat(speedMbps) >= 1) {
            this.internetSpeedResults.upload = { status: true, message: response, detail: response };
          } else {
            this.internetSpeedResults.upload = { status: false, message: 'Upload speed is less than 1 Mbps' };
          }
          resolve(true);
        }, err => {
          this.internetSpeedResults.upload = { status: false, message: 'Connection timeout' };
          resolve(true);
        });
    });
  }

  randomString(length) {
    var chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    var result = '';
    for (var i = length; i > 0; --i) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
  }


  stopStream() {
    if (this.videoStream) {
      this.videoStream.getTracks().forEach((track) => track.stop());
    }
    if (this.audioStream) {
      this.audioStream.getTracks().forEach((track) => track.stop());
    }
  }

  shareResults() {
    this.results = {
      cameraStatus: this.camVerification.status,
      cameraMessage: this.camVerification.message,
      cameraDetail: this.camVerification.detail,
      microphoneStatus: this.microphoneVerification.status,
      microphoneMessage: this.microphoneVerification.message,
      microphoneDetails: this.microphoneVerification.detail,
      screenShareStatus: this.screenShareVerification.status,
      screenShareMessage: this.screenShareVerification.message,
      screenShareDetail: this.screenShareVerification.detail,
      browserStatus: this.browserVerification.status,
      browserMessage: this.browserVerification.message,
      browserDetail: this.browserVerification.detail,
      browserCompatibilityStatus: this.browserCompatibility.status,
      browserCompatibilityMessage: this.browserCompatibility.message,
      browserCompatibilityDetail: this.browserCompatibility.detail,
      internetSpeedStatus: this.internetSpeedResults.download.status,
      internetSpeedMessage: this.internetSpeedResults.download.message,
      internetSpeedDetail: this.internetSpeedResults.download.detail,
      internetUploadSpeedStatus: this.internetSpeedResults.upload.status,
      internetUploadSpeedMessage: this.internetSpeedResults.upload.message,
      internetUploadSpeedDetail: this.internetSpeedResults.upload.detail,
      cookies: this.cookieVerification.status,
      cookiesMessage: this.cookieVerification.message,
      cookiesDetail: this.cookieVerification.detail,
      webSockets: this.webSocketVerification.status,
      webSocketsMessage: this.webSocketVerification.message,
      webSocketsDetail: this.webSocketVerification.detail,
      operatingSystemStatus: this.osVerification.status,
      operatingSystemMessage: this.osVerification.message,
      operatingSystemDetail: this.osVerification.detail,
      ramStatus: this.ramVerification.status,
      ramMessage: this.ramVerification.message,
      ramDetail: this.ramVerification.detail,
      processorStatus: this.processorVerification.status,
      processorMessage: this.processorVerification.message,
      processorDetail: this.processorVerification.detail,
    };
    // this.saveReports(this.results);
    this._service.reportDetails = this.results;
    this.snackbar.callSnackbaronSuccess('System check Completed');
    if (this.allSet) {
      // this.router.navigateByUrl("/reports");
    }
  }

  saveReports(data) {
    const httpOptions = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    }
    this.http.post('https://api.examroom.ai/api/SystemChecklogs', JSON.stringify(data), httpOptions)
      .subscribe(data => {
      }, (err) => {
      });
  }

  detectBrowser() {
    var navigator: any = window && window.navigator;
    // Returned result object.
    var result: any = {};
    result.browser = null;
    result.version = null;
    // Fail early if it's not a browser
    if (typeof window === "undefined" || !window.navigator) {
      result.browser = "Not a browser.";
      this.browserResults = result;
      this.browserCompatibility = { message: "Not compatible", status: false };
    }

    if (navigator.mozGetUserMedia) {
      // Firefox.
      result.browser = "firefox";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /Firefox\/(\d+)\./,
        1
      );
      this.browserCompatibility = { message: "Yes", status: true };
    } else if (navigator.webkitGetUserMedia) {
      // Chrome, Chromium, Webview, Opera.
      // Version matches Chrome/WebRTC version.
      result.browser = "chrome";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /Chrom(e|ium)\/(\d+)\./,
        2
      );
      this.browserCompatibility = { message: "Yes", status: true };
    } else if (
      navigator.mediaDevices &&
      navigator.userAgent.match(/Edge\/(\d+).(\d+)$/)
    ) {
      // Edge.
      result.browser = "edge";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /Edge\/(\d+).(\d+)$/,
        2
      );
      this.browserCompatibility = { message: "Yes", status: true };
    } else if (
      window["RTCPeerConnection"] &&
      navigator.userAgent.match(/AppleWebKit\/(\d+)\./)
    ) {
      // Safari.
      result.browser = "safari";
      result.version = this._service.extractVersion(
        navigator.userAgent,
        /AppleWebKit\/(\d+)\./,
        1
      );
      this.browserCompatibility = { message: "Yes", status: true };
    } else {
      result.browser = "Not a supported browser.";
      this.browserCompatibility = { message: "Not compatible", status: false };
      this.browserResults = result;
    }
    this.browserResults = result;
  }

  ngOnDestroy() {
    try {
      this.stopStream();
    } catch (e) {

    }
  }
}
