import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import { chatConnectionAtSuppCen, closeChatConn, configMsgTexts, message, sender } from './chat.types';
import Peer from 'peerjs';
import { Editor, Toolbar } from 'ngx-editor';
import { CHAT_SNIPPETS } from './tempresponses';


@Component({
  selector: 'exai-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit {

  constructor() {
    this.searchControl = new FormControl('');
    this.editor = new Editor();
  }

  CHAT_SNIPPETS = CHAT_SNIPPETS;
  @Input() senderID?: string;
  @Input() peer?: Peer
  @Input() connection: chatConnectionAtSuppCen;
  @Input() index?: number = 0;
  @Output() closeChatConnection: EventEmitter<closeChatConn> = new EventEmitter();

  editor: Editor;
  messageControl: FormControl = new FormControl('', [Validators.required]);
  collapsed: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  $collapsed: Observable<boolean> = this.collapsed.asObservable();

  newMsgBadge: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  $newMsgBadge: Observable<boolean> = this.newMsgBadge.asObservable();

  fullscreenStream: BehaviorSubject<MediaStream> = new BehaviorSubject<MediaStream>(null);

  searchText = '';
  selectedResponse;
  searchControl: FormControl;
  formGroup: FormGroup;
  responsesList;
  // responses: response[] = [
  //   {viewValue: 'Greeting', value: 'Hello,, thank you for contacting Credentia. My name is'},
  //   {viewValue: 'Help',value: 'How may I assist you today?'},
  //   {viewValue: 'Assurance',value: 'I can certainly help you with that.'},
  //   {viewValue: 'Application Status',value: 'Your application is currently in ****** status'},
  //   {viewValue: 'How to check application status',value: "<ol><li><p>&nbsp;To check an application status :</p></li><li><p>Click on Application on the left side of your screen.</p></li><li><p>Click on the Summary Button at the bottom right.</p></li></ol>"}, 
  // ];

  ngOnInit(): void {
    this.playAudio(AudioAlerts.NewChat);
    this.messageControl.valueChanges.subscribe((value: string) => {
    });
    this.connection.chatConnection.on('data', (data: message) => {
      if (data.type == 'message') {
        this.connection.conversation.push(data);
        document.getElementById('chatMessage' + this.connection.conversation.length.toString())?.scrollIntoView({ behavior: 'smooth' });
        this.playAudio(AudioAlerts.NewMessage);
        if (!this.collapsed.value) {
          this.newMsgBadge.next(true);
          // remove the new message badge after 5 seconds if 
          // the chat window is not collapsed
          setTimeout(() => {
            this.newMsgBadge.next(false);
          }, 5000);
          // else it will get removed when the guy expands the chat
        }
        else this.newMsgBadge.next(true);
      }
      else {
        switch (data.text) {
          case configMsgTexts.removeCandidateScreenShare: this.removeCandidateScreenShare();
            break;
          case configMsgTexts.closeChatConnectionCompletely:
            this.closeChatConnection.emit({closerId:data.sender.senderPeerjsID});
            break;
          default:
        }
      }
    });
    this.connection.chatConnection.on('close', function () {
      // self.status = "Connection reset<br>Awaiting connection...";
      this.connection.chatConnection = null;
    });
    this.connection.clientORcandidateRemoteStream.subscribe((stream: MediaStream) => {
      if (stream && this.senderID != this.connection.clientORcandidatePeerjsID)
        this.streamRemoteVideo(stream);
    })
    this.fullscreenStream.subscribe((value: MediaStream) => {
      if (value)
        this.setFullScreenStream(value);
      else
        this.removeFullScreenStream();
    })
     this.responsesList = this.CHAT_SNIPPETS;
  }

  private setFullScreenStream(stream: MediaStream) {
    const video = document.createElement('video');
    const title = document.createElement('span');
    const div = document.createElement('span');
    title.classList.add('text-red', 'font-bold', 'text-l', 'absolute', 'left-3', 'top-3');
    div.classList.add('w-full', 'h-full', 'relative');
    video.classList.add('video');
    video.id = this.connection.clientORcandidatePeerjsID + 'fullscreenStream'
    title.id = this.connection.clientORcandidatePeerjsID + 'fullscreenStream'
    div.id = this.connection.clientORcandidatePeerjsID + 'fullscreenStream'
    video.srcObject = stream;
    video.title = this.connection.clientORcandidateName ? this.connection.clientORcandidateName : this.connection.clientORcandidatePeerjsID.substr(0, 7);
    title.textContent = this.connection.clientORcandidateName ? this.connection.clientORcandidateName : this.connection.clientORcandidatePeerjsID.substr(0, 7);
    video.play();
    document.getElementById('full-screen-stream').append(div);
    document.getElementById(this.connection.clientORcandidatePeerjsID + 'fullscreenStream').append(video);
    document.getElementById(this.connection.clientORcandidatePeerjsID + 'fullscreenStream').append(title);
    // document.getElementById('full-screen-stream').append(title);
  }
  removeFullScreenStream() {
    let fullScreeStream = document.getElementById(this.connection.clientORcandidatePeerjsID + 'fullscreenStream');
    if (fullScreeStream) {
      document.getElementById('full-screen-stream')?.removeChild(document.getElementById(this.connection.clientORcandidatePeerjsID + 'fullscreenStream'));
      this.fullscreenStream.next(null);
    }
  }

  private streamRemoteVideo(stream: any): void {
    const video = document.createElement('video');
    video.classList.add('video');
    video.id = this.connection.clientORcandidatePeerjsID
    video.srcObject = stream;
    video.play();
    document.getElementById(this.connection.clientORcandidatePeerjsID + 'remote-video').append(video);
  }

  private removeCandidateScreenShare() {
    document.getElementById(this.connection.clientORcandidatePeerjsID + 'remote-video').removeChild(document.getElementById(this.connection.clientORcandidatePeerjsID));
    this.connection.clientORcandidateRemoteStream.next(null);
    this.removeFullScreenStream();
  }

  shareCandidateScreen(): void {
    // @ts-ignore
    navigator.mediaDevices.getDisplayMedia(<MediaTrackConstraints>{
      video: true,
      audio: false
    }).then((stream: MediaStream) => {
      this.connection.clientORcandidateRemoteStream.next(stream);
      this.connection.screenShareConnection = this.peer.call(this.connection.agentPeerjsID, stream);
      this.connection.screenShareConnection.on('stream', () => {

      });
      this.connection.screenShareConnection.on('close', () => {
      })
    }).catch(err => {
    });
  }

  stopScreenShare() {
    const screenShareTrack = this.connection.clientORcandidateRemoteStream.value.getTracks()[0];
    screenShareTrack.stop();
    this.connection.clientORcandidateRemoteStream.value.removeTrack(screenShareTrack);
    this.connection.screenShareConnection.close();
    this.connection.chatConnection.send(<message>{
      type: 'config',
      sender: <sender>{
        senderPeerjsId: this.senderID
      },
      senderType: 'clientORcandidate',
      timestamp: new Date(Date.now()),
      text: configMsgTexts.removeCandidateScreenShare
    })
    this.connection.clientORcandidateRemoteStream.next(null);
  }
  sendMessageHandler() {
    if (this.connection.chatConnection && this.messageControl.value) {
      let msg = <message>{
        type: 'message',
        sender: <sender>{
          senderPeerjsID: this.senderID
        },
        senderType: 'agent',
        timestamp: new Date(Date.now()),
        text: this.messageControl.value
      }
      this.connection.chatConnection.send(msg);
      this.connection.conversation.push(msg);
      document.getElementById('chatMessage' + this.connection.conversation.length.toString())?.scrollIntoView({ behavior: 'smooth' });
      this.messageControl.setValue('', { emitEvent: false });
    }
  }
  public closeChat() {
    
    this.connection.chatConnection.send(<message>{
      type: 'config',
      sender: <sender>{
        senderPeerjsId: this.senderID
      },
      senderType: null,
      timestamp: new Date(Date.now()),
      text: configMsgTexts.closeChatConnectionCompletely
    })
    // need to come back to this
    this.closeChatConnection.emit({closerId:this.senderID});
    this.removeFullScreenStream();
  }

  
  filterUsers() {
    this.responsesList=this.CHAT_SNIPPETS.filter(
      item => item.label.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }
  selectResponse(event:Event){
    this.searchControl.setValue("");
    this.messageControl.setValue(event["option"]["value"]);
  }

  toolbar: Toolbar = [
    ["bold", "italic"],
    ["underline", "strike"],
    ["code", "blockquote"],
    ["ordered_list", "bullet_list"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["link", "image"],
    ["text_color", "background_color"],
    ["align_left", "align_center", "align_right", "align_justify"]
  ];

  playAudio(alertType: AudioAlerts) {
    var audio = new Audio();
    audio.src = `assets/audios/${alertType}.mp3`;
    audio.load();
    audio.play();
  }
}

export enum AudioAlerts {
  NewMessage = "newMessage",
  NewChat = "newChat",
}




