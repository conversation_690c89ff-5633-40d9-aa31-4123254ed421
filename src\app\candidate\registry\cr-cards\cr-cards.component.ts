import { Component, OnInit, Input } from '@angular/core';
import { Store, Action } from '@ngrx/store';
import * as moment from 'moment/moment';
import {
  CertificateModel,
  RequestModel,
  RegistryModuleState,
  RegistryStatus
} from './../state/registry.model';
import {
  clearDraftedState,
  getDraftedForm,
  setSelectedCertificate,
  setSelectedRequest
} from '../state/registry.actions';
import { Router } from '@angular/router';
import { selectorLoadAllCertificates, selectorLoadDraftedForm } from '../state/registry.selectors';
import { FormTypes } from 'src/app/core/Dto/enum';
import { GlobalUserService } from 'src/app/core/global-user.service';

@Component({
  selector: 'exai-cr-cards',
  templateUrl: './cr-cards.component.html',
  styleUrls: ['./cr-cards.component.scss']
})
export class CrCardsComponent implements OnInit {
    certificates=[]
  certificate: CertificateModel = null;
  request: RequestModel = null;

  @Input() data = null;
  @Input() requestData;
  registryId
  todaysDate: Date = new Date();
  isCertificate: boolean = false;
  showrenewal:boolean
  renewCert:boolean=true;
  appplyAgaiinterm:boolean = true;
  isRenewalActive:boolean = false; // property for displaying renewal btn
   DCRenewalActive:boolean = false
  constructor(private store: Store<RegistryModuleState>,
    private router: Router,
    public global: GlobalUserService) { }

  public ngOnInit(): void {
    this.certificate = null;
    this.request = null;
    if (("personFormId" in this.data)) {
      
      this.request = this.data;
      this.isCertificate = false;
      this.requestData.forEach((element)=>{
        if((element.certificateNumber==this.data.certificateNumber) && (element.status=="Approved" || element.status=="Pending")){
          this.appplyAgaiinterm = !this.appplyAgaiinterm;
          }
          else{
            this.appplyAgaiinterm = this.appplyAgaiinterm;
          }
      })
    }
    else {
      this.certificate = this.data;
      this.certificates.push(this.certificate)
      this.isCertificate = true;
      this.showrenewal = RegistryStatus.includes(this.certificate.RegistryStatusId)
      this.requestData?.forEach((element)=>{
        if((element.certificateNumber==this.data.CertNumber)&&element.status=="Pending"){
          this.renewCert = false;
        }
      });

      

      //Need to revisit this logic
      this.CalculateRenewal();
      this.calculate()
    }
  }
  
  private CalculateRenewal() {
    let today = new Date();
    let certExpiryDay = new Date(this.certificate.ExpirationDate);
    let renewFinalDay = new Date(this.certificate.ExpirationDate);
    let days = (this.global.stateId == 5 || this.global.stateId == 6) ? 60 : 90;
    certExpiryDay.setDate(certExpiryDay.getDate() - days);
    renewFinalDay.setDate(renewFinalDay.getDate() + 30);
    if(this.global.stateId == 7){
        let today = new Date();
        let certExpiryDay1 = new Date(this.certificate.ExpirationDate);
        let renewFinalDay1 = new Date(this.certificate.ExpirationDate);
        certExpiryDay1.setDate(certExpiryDay1.getDate() - days);
        renewFinalDay1.setDate(renewFinalDay1.getDate() + 60);
        this.isRenewalActive = (today > certExpiryDay1 && today <= renewFinalDay1) ? true : false;
      }
    else{
      this.isRenewalActive = (today > certExpiryDay && today <= renewFinalDay) ? true : false;
    }
     let Today = moment(today).format("YYYY/MM/DD")
    let a =this.certificates.filter(x=> x.RegistryStatus ==="Active" && moment(x.ExpirationDate).format("YYYY/MM/DD")<=Today)
       this.isRenewalActive = a.length > 0?true:false

     

}

  public getImagePath(RegistryStatus: string): string {
    if (RegistryStatus == "Active")
      return "./assets/img/Icons/approved.svg";
    else {
      return "./assets/img/Icons/pending.svg";
    }
  }

  public getDateFormat(date: any): string {
    let n = Intl.DateTimeFormat().resolvedOptions()
    return moment(date).format("Do MMMM, YYYY / h:mm A")
  }

  public saveLSdata(): void {
    this.store.dispatch<Action>(clearDraftedState());
    this.store.dispatch<Action>(setSelectedCertificate({ data: this.certificate }));
    this.store.dispatch<Action>(setSelectedRequest({ data: null }));
  }


  public saveLSdataReq(): void {
    this.store.dispatch<Action>(clearDraftedState());
    this.store.dispatch<Action>(setSelectedRequest({ data: this.request }));
    this.store.dispatch<Action>(setSelectedCertificate({ data: null }));
  }

  public goToFinishForm(): void {
    let DraftedFormDetails=0;
    this.store.dispatch<Action>(clearDraftedState());
    this.store.dispatch<Action>(getDraftedForm({ personFormId: this.request.personFormId }));
    this.store.select(selectorLoadAllCertificates).subscribe((data:any) => {
      this.registryId = data.result
      let cert = data.result.find(element => element.Id == this.request.certificateId);
    
      if (cert)
        this.store.dispatch<Action>(setSelectedCertificate({ data: cert }))
      else
        this.store.dispatch<Action>(setSelectedCertificate({ data: null }))
    })
    switch (this.request.formTypeId) {
      case FormTypes.RenewalSCMA:
      case FormTypes.CertificateRenewal:
       (this.request.formTypeId ==FormTypes.CertificateRenewal || this.request.formTypeId == FormTypes.RenewalSCMA)? this.store.select(selectorLoadDraftedForm).subscribe(draftedFormsDetails=>{
          if(draftedFormsDetails){
          DraftedFormDetails=this.registryId[0].Id
          this.request.formTypeId == FormTypes.CertificateRenewal ? this.router.navigate(['registry', 'renewal-form', FormTypes.CertificateRenewal, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, DraftedFormDetails, this.request.personFormId,0,this.request.statusId]):this.router.navigate(['registry', 'renewal-form', FormTypes.RenewalSCMA, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, DraftedFormDetails, this.request.personFormId,0,this.request.statusId]);
          this.request =null
          this.global.userstatus.next(draftedFormsDetails[0])
          }
        }):null
        break;
      case FormTypes.CertificateReciprocity:
        this.router.navigate(['registry', 'reciprocity-form', FormTypes.CertificateReciprocity, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, 0, this.request.personFormId,0,this.request.statusId]);
        this.global.userstatus.next(this.request)

        break;
      case FormTypes.CertificateDuplicate:
        this.router.navigate(['registry', 'duplicate-form', FormTypes.CertificateDuplicate, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, 0, this.request.personFormId, 0]);
        break;
        case FormTypes.ReciporatingSCMA:
          this.router.navigate(['registry', 'duplicate-form', FormTypes.ReciporatingSCMA, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, 0, this.request.personFormId, 0,this.request.statusId]);
          break;
          case FormTypes.SC_reinstate:
            this.router.navigate(['registry', 'duplicate-form', FormTypes.SC_reinstate, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, 0, this.request.personFormId, 0,this.request.statusId]);
            break;
      default:
        null
    }
  }

  applyAgain(): void {
    switch (this.request.formTypeId) {
      case FormTypes.CertificateRenewal:
        this.router.navigate(['registry', 'renewal-form', FormTypes.CertificateRenewal, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, this.request.certificateId]);
        break;
      case FormTypes.CertificateReciprocity:
        this.router.navigate(['registry', 'reciprocity-form', FormTypes.CertificateReciprocity, this.global.candidateId, 0, this.global.stateId, this.global.stateId]);
        break;
      case FormTypes.CertificateDuplicate:
        this.router.navigate(['registry', 'duplicate-form', FormTypes.CertificateDuplicate, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, this.request.certificateId]);
        break;
        case FormTypes.RenewalSCMA:
          this.router.navigate(['registry', 'renewal-form', FormTypes.RenewalSCMA, this.global.candidateId, this.request.eligibilityRouteId, this.global.stateId, this.request.certificateId]);
          break;
          case FormTypes.ReciporatingSCMA:
            this.router.navigate(['registry', 'reciprocity-form', FormTypes.ReciporatingSCMA, this.global.candidateId, 0, this.global.stateId, this.global.stateId]);
      default:
        null
    }
  }

  public viewRenewal(cert: CertificateModel) {
    //Need to revisit this logic
    this.CalculateRenewal();
    ((cert.CertType == 1 && ExamRenewal.includes(cert.RegistryName)) || cert.CertType == 14)?this.router.navigate(['registry', 'renewal-form', FormTypes.RenewalSCMA, this.global.candidateId, cert.EligibilityRouteId, this.global.stateId, cert.Id,cert.CertNumber]): cert.AllowReinstatement ==true? this.router.navigate(['registry', 'renewal-form', FormTypes.SC_reinstate, this.global.candidateId, cert.EligibilityRouteId, this.global.stateId, cert.Id,cert.CertNumber]):   this.router.navigate(['registry', 'renewal-form', FormTypes.CertificateRenewal, this.global.candidateId, cert.EligibilityRouteId, this.global.stateId, cert.Id,cert.CertNumber])

  }

  calculate(){
    if(this.global.userDetails.getValue().stateId === 5){
      const currentDate:any = new Date();
      // Create a new Date object for the expiration date
      const expirationDate:any = new Date(this.certificate.ExpirationDate);
      // Calculate the date that is 60 days from today
      const timeDifference = currentDate.getTime() - expirationDate.getTime();
      const timeDifference1 = expirationDate.getTime() - currentDate.getTime();
      // Convert the difference to days
      const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
      this.DCRenewalActive = daysDifference > 60 ||timeDifference1 > 60 ?false:true;
    }
 


  }


}

export const ExamRenewal =[
  'M1 - South Carolina State Approved Medication Assistant Trained Candidate',
 "M2- Lapsed or Expired less than 24 months South Carolina Medication Assistant Registry Applicants"
]
