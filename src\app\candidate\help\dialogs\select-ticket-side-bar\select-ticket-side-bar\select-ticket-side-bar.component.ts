import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { FormTypes } from 'src/app/core/Dto/enum';
import { HelpService } from '../../../help.service';

@Component({
  selector: 'exai-select-ticket-side-bar',
  templateUrl: './select-ticket-side-bar.component.html',
  styleUrls: ['./select-ticket-side-bar.component.scss']
})
export class SelectTicketSideBarComponent implements OnInit {

  constructor(
    private helpService: HelpService,
    private router: Router,
  ) { }
  formtypes$: Observable<FormTypes[]>
  ngOnInit(): void {
    // formtypes$ = this.helpService.fetchCategories() 
  }
  selectFormtype(category: FormTypes) {
    // this.helpService.selectedFormType.next(formtypes)
    // this.router.navigate(['help', formtype.name])
  }
}
