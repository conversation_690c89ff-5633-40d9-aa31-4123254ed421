import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { RegisteredExamsModel } from "src/app/core/Dto/registered-exams.model";
import { Excusedlist } from "../excused-absense/state/excused.model";
import { GrievanceFormsList } from "./state/grievance.model";

@Injectable({
    providedIn: 'root'
})
export class GrievanceFormService implements OnDestroy {
    viewGrievance: GrievanceFormsList;
    viewabsense:Excusedlist;
    
    
    constructor() { }

    ngOnDestroy() {
        this.viewGrievance = null;
    }
}
