import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { switchMap, map, tap, take } from "rxjs/operators";
import { PersonFormDetailsModel } from "src/app/core/Dto/personform-details.model";
import { PersonFormModel } from "src/app/core/Dto/personform.model";
import { HttpService } from "src/app/core/http-services/http.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { PersonForm } from "../../application/application.types";
import { upcomingExam } from "../../grievance-form/state/grievance.model";
import {
  clearDemographicData,
  deletedCorrectionForm, deletedDemographicForm, fetchPersonData, getCorrectionFormData,
  getFormJSON, getImageUrl, getPersonDetails, getPersonForm, getPersonFormLogs, getupcomingExam, gotFormJSON,
  gotPersonDetails, gotupcomingExam, isPersonFormLogs, passCorrectionFormData, passPersonDetail, submitDemographicData,
  submitedDemographicData, successMessage,
} from "./profile-form.action";

import { MngProfileReducer } from "./profile-form.reducer";

@Injectable({
  providedIn: 'root',
})

export class MngProfileEffects {
  public url: any;
  constructor(
    public snackbar: SnackbarService,
    public http: HttpService,
    public action$: Actions,
    private router: Router
  ) { }

  public form$ = createEffect(() => {
    return this.action$.pipe(
      ofType(getFormJSON),
      switchMap((action) => {
        return this.http.demographicForm().pipe(map((data: any) => {
          return gotFormJSON(data);
        }),
          take(1));
      }))
  });

  public Personform$ = createEffect(() => {
    return this.action$.pipe(
      ofType(getPersonDetails),
      switchMap((action) => {
        return this.http.getAccountPersonDetails().pipe(map((data: any) => {
          return gotPersonDetails({ value: data });
        }), take(1));
      }))
  });


  public submitData$ = createEffect(
    () => {
      return this.action$.pipe(
        ofType(passPersonDetail),
        switchMap(
          (state: any) => {
            return this.http.editProfile(state.personDetails).pipe(map((data: any) => {
              if (data) {
                if (data.status) {
                  return successMessage({ value: true });
                }
              }
            }),

            tap(() => {
              this.snackbar.callSnackbaronSuccess('Form is Edited Successfully');
              this.router.navigateByUrl('/manage-profile');
            }),
              take(1)
            );
          }))
    });

  submitDemographic$ = createEffect(() => this.action$.pipe(
    ofType(submitDemographicData),
    switchMap(
      (action: any) => {
        return this.http.saveDemographic(action.SubmitDemographic).pipe(
          map((data: number) => {
            return submitedDemographicData({ savedResId: data });
          }
          ),
          tap(() => {
            this.snackbar.callSnackbaronSuccess('Form is Saved');
            this.router.navigateByUrl('/manage-profile');
          }),
          take(1)
        );
      }))
  );

  public getPersonForm$ = createEffect(
    () => {
      return this.action$.pipe(
        ofType(getPersonForm),
        switchMap(
          (action: any) => {
            return this.http.getPersonForms().pipe(
              map((data: Array<PersonForm>) => {
                return fetchPersonData({ value: data })
              }), take(1));
          }))
    });

  public getCorrection$ = createEffect(
    () => {
      return this.action$.pipe(
        ofType(getCorrectionFormData),
        switchMap(
          (action: any) => {
            if (typeof (action.value) == 'number') {
              return this.http.acessCorrectionFormValue(action.value).pipe(
                map((data: PersonFormModel[]) => {
                  return passCorrectionFormData({ value: data })
                }
                ), take(1));
            }
            else {
              return this.http.acessCorrectionFormValue(action.value[0].id).pipe(
                map((data: PersonFormModel[]) => {
                  return passCorrectionFormData({ value: data })
                }
                ), take(1));
            }

          }))
    });

  deleteCorrectionForm$ = createEffect(
    () => {
      return this.action$.pipe(
        ofType(deletedCorrectionForm),
        switchMap(
          (action: any) => {
            try {
              if (typeof (action.correctionForm) == 'number') {
                return this.http.deleteDemographicForm(action.correctionForm).pipe(
                  map((data: any) => {
                    return deletedDemographicForm({ correctionForm: data })
                  }
                  ),
                  tap(() => {
                    this.snackbar.callSnackbaronWarning('Form is Deleted');
                    this.router.navigateByUrl('/manage-profile');
                  }), take(1));
              }
              else {
                return this.http.deleteDemographicForm(action.correctionForm[0].id).pipe(
                  map((data: any) => {
                    return deletedDemographicForm({ correctionForm: data })
                  }
                  ), take(1));
              }
            }
            catch (e) {
            }

          }))
    });

  public getPersonCorrectionLogs$ = createEffect(
    () => {
      return this.action$.pipe(
        ofType(getPersonFormLogs),
        switchMap(
          (action: any) => {
            return this.http.getCorrectionlogs(action.personFormLogs).pipe(
              map((data: Array<any>) => {
                return isPersonFormLogs({ personFormLogs: data })
              }
              ), take(1));
          }))
    });

  getUpcomingExams$ = createEffect(() => {
    return this.action$.pipe(
      ofType(getupcomingExam),
      switchMap((action: any) => {
        return this.http.getUpcomingExams(action.candidateId).pipe(
          map((data: upcomingExam[]) => gotupcomingExam({ upcomingExam: data })),
          take(1)
        )
      })
    )
  })
}