.db {
  justify-content: center !important;
}

.welc {
  color: var(--text-color2);
}

.welc-note {
  color: var(--text-color1);
}

.title-hed {
  color: var(--text-dropdown);
}

.state-elig {
  color: var(--text-color1);
}

.bg-color {
  background-color: var(--background-base2);
}

.minimise{
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow: hidden;
}

.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: 0.1rem 0.5rem;
}

.arrow {
  justify-content: center !important;
}

.iconSize {
  width: 12px;
  height: 12px;
}
.button-disabled {
  opacity: 0.4;
  pointer-events: none;
}

.status {
  color: var(--text-profile);
}
.status1 {
  color: var(--text-color1);
}

.active {
  color: var(--text-color2);
}

.content {
  padding-top: 16px;
  padding-bottom: 16px;
  padding-right: 16px;
}

.content > mat-card {
  margin-bottom: 10px;
}

mat-card {
  max-width: 410px;
}

.update {
  color: var(--text-color1);
}

.content1 {
  color: var(--text-color4);
}

// .active2 {
//     color: var(--text-color2);
// }

::ng-deep .mat-button.mat-primary.mat-button-disabled, .mat-button.mat-accent.mat-button-disabled, .mat-button.mat-warn.mat-button-disabled, .mat-button.mat-button-disabled.mat-button-disabled, .mat-icon-button.mat-primary.mat-button-disabled, .mat-icon-button.mat-accent.mat-button-disabled, .mat-icon-button.mat-warn.mat-button-disabled, .mat-icon-button.mat-button-disabled.mat-button-disabled, .mat-stroked-button.mat-primary.mat-button-disabled, .mat-stroked-button.mat-accent.mat-button-disabled, .mat-stroked-button.mat-warn.mat-button-disabled, .mat-stroked-button.mat-button-disabled.mat-button-disabled {
  /* color: rgba(0, 0, 0, 0.26); */
  background: #4444 !important;
}

.bottom-space{
  margin-bottom: 1rem;
}

.textColor {
  color: #6d6d6d;
  font-family: Roboto;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  align-items: center;
}
