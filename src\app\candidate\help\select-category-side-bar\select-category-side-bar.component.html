<div class="card shadow-none cardBorder relative w-full h-full" fxFlex="auto">

    <div class="px-6 pt-4 " fxLayout="row" fxLayoutAlign="start">
        <h6 class="mb-2 flex text-xs font-bold fontColor1">
            Select Help Topic
        </h6>
    </div>
    <div class="eligibility touch-auto overflow-auto">
        <div class="px-4 eligibility-list-btn my-2" fxLayout="column" *ngFor="let data of categories$ | async">
            <!-- inside button below [disabled]="examTypeDisable && steps1 != data.id" [ngClass]="{ active: steps1 == data.id }"  -->
            <button (click)="selectCategory(data)" [ngClass]="{ active: data.id == currentCategory }"
                class="btn my-1 flex t-xs justify-between" mat-stroked-button fxFlex="auto">
                Help with {{ data.name }}
                <mat-icon class="text-end flex icons" *ngIf="data.id === currentCategory ">
                    arrow_forward_ios
                </mat-icon>
            </button>
        </div>
    </div>
</div>