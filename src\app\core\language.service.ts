import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";

export type language = {
  dashboard: string;
  application: string;
  practice_skills:string;
  practice_skills_view:string;
  view_bundle:string;
  bundles:string;
  skills:string;
  No_slot_avaiable:string
  examscheduled: string;
  grievanceform: string;
  excuseabsence:string;
  manageprofile: string;
  help: string;
  registry: string;
  practice:string
  // Application component
  applicationProcess: string;
  accommodationProcess: string;
  currentStatus: string;
  submitDate: string;
  waitTime: string;
  reason: string;
  reason2:string;
  summary: string;
  apply: string;
  appForm: string;
  cancel: string;
  saveDraft: string;
  submit: string;
  save: string;
  leavePage: string;
  leavePageMsg: string;
  selectApp: string;
  state: string;
  stateError: string;
  selectEligibility: string;
  eligibilityDesc: string;
  start: string;
  welcome: string;
  welMsg: string;
  appId: string;
  examMode: string;
  examDate: string;
  examTime: string;
  registerExam: string;
  addNewApp: string;
  fillApp: string;
  startExam: string;
  checkExamScore: string;
  getCertified: string;
  systemCheck: string;
  deletePage: string;
  deleteRegistry: string;
  deleteRegistryMsg: string;
  deleteMsg: string;
  delete: string;
  correctionProcess: string;
  correctionProcessMsg:string;
  renewelPayment: string;
  addToCart:string;
  voucherConfirmation:string;
  voucherMessage:string;
  yes:string,
  no:string,
  registryDetail:string;
  registryDetails:string;
  GreivanceMsg:string
};

@Injectable({
  providedIn: "root",
})
export class LanguageService {
  english: language = {
    dashboard: "Dashboard",
    application: "Application",
    practice_skills: "Practice Skills",
    practice_skills_view: "Practice Skill View",
    view_bundle:"View Bundle",
    skills: "Skills",
    bundles:"Bundles",
    practice:'Practice Exam',
    examscheduled: "Exam Schedule",
    grievanceform: "Grievance Form",
    manageprofile: "Manage Profile",
    excuseabsence:"Excused Absence",
    help: "Help",
    registry: "Registry",
    applicationProcess: "Application Process",
    accommodationProcess: "Accommodation Process",
    correctionProcess: "Submit Correction Form",
    currentStatus: "Current Status",
    submitDate: "Submitted Date",
    waitTime: "Waiting Time",
    reason: "Reason for rejection",
    reason2:"Reason for change request",
    registryDetail:"No data found for registry payment",
    registryDetails:"Please delete the cart items",
    
    summary: "Summary",
    apply: "Apply Again",
    appForm: "Application Form",
    cancel: "Cancel",
    saveDraft: "Save as Draft",
    submit: "Submit",
    save: "Yes",
    leavePage: "Submit Application",
    leavePageMsg: "Are you sure you want to submit this application?",
    GreivanceMsg:"By clicking YES, I acknowledge that I have submitted all relevant information <br>regarding  the grievance and understand I will not be able to provide any <br> additional details thereafter.",
    selectApp: "Select Application",
    state: "Pennsylvania",
    stateError: "State is Required",
    selectEligibility: "Select Your Eligibility Route",
    eligibilityDesc: "Select an eligibility route to start your application",
    start: "Start",
    welcome: "Welcome,",
    No_slot_avaiable:"There are no slots available at a test center for that date, would you like to schedule an online exam instead?",
    welMsg: "Phasellus viverra nulla ut metus varius laoreet.",
    appId: "Application ID",
    addNewApp: "Start New Application",
    fillApp: "Fill Application Form",
    registerExam: "Register For Exam",
    startExam: "Start Exam",
    checkExamScore: "Check Exam Scores",
    getCertified: "Get Certified",
    systemCheck: "System Check",
    examMode: "Exam Mode",
    examDate: "Exam Date",
    examTime: "Exam Time",
    deletePage: "Delete Application",
    deleteRegistry: "Delete Request",
    deleteRegistryMsg: "Are you sure you want to delete this request",
    deleteMsg: "Are you sure you want to delete this application?",
    delete: "Delete",
    correctionProcessMsg:"Are you sure you want to submit this correction form?",
    renewelPayment:"Proceed to Pay",
    addToCart:"Add to Cart",
    voucherConfirmation:"Voucher Confirmation",
    voucherMessage:"You have active voucher(s) in the cart. Do you want to apply?",
    yes:'Yes',
    no:'No',
  };

  globalLanguage: BehaviorSubject<string> = new BehaviorSubject<string>(
    "english"
  );

  curLangObj: BehaviorSubject<language> = new BehaviorSubject<language>(
    this.english
  );
  $curLangObj: Observable<language> = this.curLangObj.asObservable();

  constructor() {
    this.globalLanguage.subscribe((value: string) => {
      switch (value) {
        case "english":
          this.curLangObj.next(this.english);
          break;
        default:
          this.curLangObj.next(this.english);
      }
    });
  }
}
