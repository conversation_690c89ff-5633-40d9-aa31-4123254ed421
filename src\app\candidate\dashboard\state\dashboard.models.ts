export class FormModel {
    public constructor(init?:Partial<FormModel>) {
        Object.assign(this, init);
    }
    personFormId: number;
    formTypeId: number;
    formId: number;
    name: string;
    examName?: any;
    comment?: any;
    stateName: string;
    eligiblityRouteName: string;
    eligibilityRouteId: number;
    stateCode: string;
    eligiblityRouteCode: string;
    submittedDate: Date;
    examDate?: any;
    lastUpdatedDate: Date;
    iconUrl: string;
    status: string;
    statusId: number;
    waitingTime: string;
}

export declare interface Timer {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    default: number;
}