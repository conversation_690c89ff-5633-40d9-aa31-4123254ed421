import { createFeatureSelector, createSelector } from "@ngrx/store";
import { ExamModel } from "src/app/core/Dto/exam.model";
import { Cart } from "./models/cart";
import { MonthlySlot } from "./models/monthlySlot";
import { slot, Slot } from "./models/slot";
import { Timezone } from "./models/timezone.model";
import { ScheduledState } from "./scheduled.state";

export const SCHEDULED_STATE_NAME = "ScheduleModuleState";

const getScheduledState =
  createFeatureSelector<ScheduledState>(SCHEDULED_STATE_NAME);

export const selectorGetTimeSlotsTestCenterStatus = createSelector(
  getScheduledState,
  (state): "idle" | "loading" | "success" | "error" =>
    state.timeSlotsTestCenterStatus
);

export const selectorGetTimezones = createSelector(
  getScheduledState,
  (state): Timezone[] => {
    return state.timezones;
  }
);
export const selectorGetTimeslots = createSelector(
  getScheduledState,
  (state): Slot[] => {
    return state.timeslots;
  }
);
export const selectorGetTimeSlotsTestCenter = createSelector(
  getScheduledState,
  (state): slot[] => {
    return state.timeslotsTestCenter;
  }
);
export const selectorGetCart = createSelector(
  getScheduledState,
  (state): Cart => {
    return state.cart;
  }
);
export const selectorGetPracticeCart = createSelector(
  getScheduledState,
  (state): Cart => {
    return state.PracticeCart;
  }
);
export const selectorGetMonthlyslots = createSelector(
  getScheduledState,
  (state): MonthlySlot[] => {
    return state.monthlySlots;
  }
);
export const selectorGetExamId = createSelector(
  getScheduledState,
  (state): ExamModel[] => {
    return state.examId;
  }
);
export const selectorPracticeGetExamId = createSelector(
  getScheduledState,
  (state): ExamModel[] => {
    return state.examIdPractice;
  }
);
export const selectorGetEligibilityroute = createSelector(
  getScheduledState,
  (state) => {
    return state.route;
  }
);
export const selectorGetRegisteredexam = createSelector(
  getScheduledState,
  (state) => {
    return state.registeredExams;
  }
);
export const selectorPracticeGetRegisteredexam = createSelector(
  getScheduledState,
  (state) => {
    return state.registeredPracticeExams;
  }
);
export const selectorGetMakepayment = createSelector(
  getScheduledState,
  (state) => {
    return state.makepaymentresponse;
  }
);
export const selectorGetRescheduledResponse = createSelector(
  getScheduledState,
  (state) => {
    return state.rescheduleResponse;
  }
);

export const selectorGetPracticeRescheduledResponse = createSelector(
  getScheduledState,
  (state) => {
    return state.PracticerescheduleResponse;
  }
);

export const selectorGetPracticeRetryscheduledResponse = createSelector(
  getScheduledState,
  (state) => {
    return state.PracticeretryscheduleResponse;
  }
);
export const selectorGetCartDeleteStatus = createSelector(
  getScheduledState,
  (state) => {
    return state.isDeleted;
  }
);
export const selectorGetCartPracticeDeleteStatus = createSelector(
  getScheduledState,
  (state) => {
    return state.isPracticeDeleted;
  }
);
export const selectorGetCartItems = createSelector(
  getScheduledState,
  (state) => {
    return state.cartItems;
  }
);
export const selectorGetSchedule = createSelector(
  getScheduledState,
  (state) => {
    return state.schedule;
  }
);
export const selectorGetPersonForm = createSelector(
  getScheduledState,
  (state) => {
    return state.personForms;
  }
);
export const selectorGetVoucherAssigns = createSelector(
  getScheduledState,
  (state) => {
    return state.Vocher;
  }
);
export const selectorGetVoucher = createSelector(getScheduledState, (state) => {
  return state.VocherResponse;
});

export const selectorGetVoucher_validate_apply = createSelector(
  getScheduledState,
  (state) => {
    debugger;
    return state.Vocher_validate_apply_Response;
  }
);
export const selectorGetIsCancelled = createSelector(
  getScheduledState,
  (state) => {
    return state.isCancelled;
  }
);

export const selectorGetPracticeIsCancelled = createSelector(
  getScheduledState,
  (state) => {
    return state.isPracticeCancelled;
  }
);

export const selectorGetIsPayment = createSelector(
  getScheduledState,
  (state) => {
    return state.isPayment;
  }
);
export const selectorGetVouchersApply = createSelector(
  getScheduledState,
  (state) => {
    return state.VochersApply;
  }
);
export const selectorGetScheduledExam = createSelector(
  getScheduledState,
  (state) => {
    return state.scheduleResponse;
  }
);
export const selectorclearCartData = createSelector(
  getScheduledState,
  (state) => {
    return state.clearCartResponse;
  }
);
export const selectorGetPaymentMethods = createSelector(
  getScheduledState,
  (state) => {
    return state.paymentMethods;
  }
);
export const selectorGetChargeResources = createSelector(
  getScheduledState,
  (state) => {
    return state.chargeResponse;
  }
);

export const selectorGetPracticeChargeResources = createSelector(
  getScheduledState,
  (state) => {
    return state.chargePracticeResponse;
  }
);
export const selectorGetCustomerId = createSelector(
  getScheduledState,
  (state) => {
    return state.customerIdObj;
  }
);

export const createPaymentMethodResponse$ = createSelector(
  getScheduledState,
  (state) => {
    return state.createPaymentMethodResponse;
  }
);

export const createPaymentCustomerId$ = createSelector(
  getScheduledState,
  (state) => {
    return state.createPaymnetCustomerIdResponse;
  }
);
export const selectorGetUpdateVocher = createSelector(
  getScheduledState,
  (state) => {
    return state.VocherUpdateResponse;
  }
);

export const selectorShowRegisterExam$ = createSelector(
  getScheduledState,
  (state) => {
    return state.showRegisterExamStatus;
  }
);

export const selectorPaymentError = createSelector(
  getScheduledState,
  (state) => {
    return state.paymenterror;
  }
);

export const selectorFormProgressBar = createSelector(
  getScheduledState,
  (state) => {
    return state.formProgressBar;
  }
);
