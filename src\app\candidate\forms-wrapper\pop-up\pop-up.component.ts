import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { LanguageService } from 'src/app/core/language.service';
import { ApplicationState } from '../state/application.state';

@Component({
  selector: 'exai-pop-up',
  templateUrl: './pop-up.component.html',
  styleUrls: ['./pop-up.component.scss'],
})
export class PopUpComponent implements OnInit {

  constructor(private router: Router,
    private dialogRef: MatDialogRef<PopUpComponent>,
    public lngSrvc: LanguageService,
    private store: Store<ApplicationState>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }


  ngOnInit(): void {


  }

  close() {
    this.dialogRef.close();
  }

  submitApp() {
    this.dialogRef.close({
      confirmed: true
    });
  }

  closeApp(){
    this.dialogRef.close({
      confirmed: false
    });
  }

}
