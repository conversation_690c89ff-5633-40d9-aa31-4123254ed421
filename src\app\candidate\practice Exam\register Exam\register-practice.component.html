<div class="px-gutter pt-2" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>

    <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="pt-3 -mt-2" fxLayout="column">
            <h5><strong>Register For Practice Exam</strong></h5>
        </div>
        <div class="-mt-1">
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
    </div>

    <!-- BODY SECTION -->
    <div class="py-4 -mt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
        <div class="shadow-none justify-start dashboard" gdColumn="1 / 3" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1" >
            <div class="card shadow-none cardBorder " fxFlex="auto">
                <div class="px-6 " fxLayout="row" fxLayoutAlign="start">
                    <h6 class="mb-2 flex text-xs font-bold fontColor1">
                        Select Exam Type
                    </h6>
                </div>
                <div class="eligibility touch-auto overflow-auto">
                    <div class="px-6  flex t-xs justify-between" fxLayout="column" *ngFor="let data of ExamID">
                        <button class="mb-3 flex t-xs" mat-stroked-button fxFlex="auto"  [ngClass]="steps1 == data.id? 'active': 'examDisablepractice'" (click)="setSelectedExam(data)" [disabled]="disable(data) || (examTypeDisable && steps1 != data.id)">
              {{ data.title }}
              <mat-icon class="ml-auto -mx-3 icons" *ngIf="steps1 == data.id">
                arrow_forward_ios
              </mat-icon>
            </button>
                    </div>
                </div>
                <!-- END SELECT EXAM TYPE SECTION -->

            </div>
            <!-- END LEFT CARD -->
        </div>

        <!-- RIGHT CARD -->
        <div class="shadow-none card cardBorder justify-start touch-auto overflow-auto dashboard px-2 py-2 registerCard" gdColumn="3/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1" *ngIf="examType">

            <div class="eligibility-desc">
                <div class="eligibility1 touch-auto overflow-auto">

                    <div class="exam">
                        <h2 class="px-4 text-xs font-bold fontColor1">
                           1. Select Your Exam Location
                        </h2>
                    </div>

                    <!-- RADIO BUTTONS [ Online / Test Center] -->
                    <mat-radio-group [formControl]="radioselect">
                        <ng-container *ngFor="let examType of examTypeModels; let i = index">
                            <mat-radio-button *ngIf="examType.name !='Test Center'" class="-ml-1 px-5 mb-3 pt-1 t-xs" [checked]="examType.checked"  [value]="examType.name" >{{ examType.name }}
                            </mat-radio-button>
                        </ng-container>

                    </mat-radio-group>
                    <!-- END RADIO BUTTONS [ Online / Test Center] -->

                    <!-- ONLINE -->
                    <div class="exam" >
                        <h2 class="px-5 text-xs font-bold fontColor1">
                           2. Select Your Exam Timezone and Date
                        </h2>
                    </div>
                    <div class="t-xs" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
                        <form [formGroup]="Validators">
                            <div class="justify-start w-full" gdColumn="1 / 3" gdColumn.lt-md="1" gdColumn.lt-sm="1">
                                <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" class="px-4 exam -mt-2" fxLayoutGap.lt-sm="0">

                                    <!-- TIME ZONE PICKER -->
                                    <mat-form-field class="pt-3" appearance="outline" fxFlex="50%" >
                                        <mat-label class="text-xs  fontColor2">Select Time Zone</mat-label>
                                        <mat-select class="text-xs" formControlName="timezone">
                                            <mat-option class="text-xs" *ngFor="let time of timezones" [value]="time.Id" (click)="examEvent(time)">
                                                {{ time.TimeZoneStandardName }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <!-- END TIME ZONE PICKER -->

                                    <!-- DATE PICKER -->
                                    <mat-form-field class="pt-3" appearance="outline" fxFlex="50%">
                                        <mat-label class="text-xs fontColor2">Select Date</mat-label>
                                        <input matInput disabled class="text-xs" formControlName="date" [matDatepicker]="picker" [min]="minDate" (dateChange)="selectDate($event)" />
                                        <mat-datepicker-toggle matSuffix [for]="picker" (click)="changeCalendar($event)">
                                        </mat-datepicker-toggle>
                                        <mat-datepicker #picker [disabled]="!calendarDisable"></mat-datepicker>
                                    </mat-form-field>
                                    <!-- END DATE PICKER -->
                                </div>
                            </div>
                        </form>
                    </div>
                    <hr class="mr-5 ml-5 line" *ngIf="radioselect.value == 'Online'" />

                    <div class="px-2 -mt-2" fxLayout="row" fxLayoutAlign="start center" *ngIf="radioselect.value == 'Online' && isSelect">
                        <h2 class="-mt-2 px-3 pt-4 text-xs font-bold fontColor1" >
                           3. Select Your Exam Range
                        </h2>
                    </div>
                    <div class="ml-2" *ngIf="radioselect.value == 'Online' && isSelect">
                        <ng-container *ngFor="let time of timeSlot; let i = index">
                            <button *ngIf="time.data.length > 0" class="px-4 ml-3 mb-3 pt-3 pb-3 state slots2 buttom6" mat-stroked-button color="light" [ngClass]="step == time.id? (time.timeperiod =='MIDNIGHT' || time.timeperiod =='EVENING' || time.timeperiod =='NIGHT'  ?'midnight':'SelectedRange') : time.color =='blue'?'highlight':'highlight1'" (click)="selectrangeactive(time)" >
                                <span class="t-xss block mb-">{{time.timeperiod}}</span>
                                {{ time.title }}<br />
                <!-- <span [ngClass]="{'active' : step == time.id}"
                             class="time ">{{time.heddle}}</span> -->
              </button>
                        </ng-container>
                    </div>
                    <!-- END RANGE SELECTOR -->

                    <!-- AVAILABLE SLOTS -->
                    <div class="px-2 -mt-3" fxLayout="row" fxLayoutAlign="start center" *ngIf="radioselect.value == 'Online' && isSelect">
                        <h2 class="m-0 px-3 pt-2 mb-1 text-xs font-bold fontColor1">
                            4. Select Your Exam Available Slots
                        </h2>
                    </div>
                    <div class="mat-testCenter touch-auto overflow-auto" *ngIf="radioselect.value == 'Online' && isSelect">
                        <div class="ml-2">
                            <ng-container *ngFor="let slot of slots">
                                <button [ngClass]="
                  bookedslot == slot.slotId
                    ? 'select-slot-btn'
                    : slot.availableSlots<=20
                    ? 'Limited_Slots'
                    : 'Available'
                " class="ml-3 mb-3 pt-1 pb-1 state slots2" mat-stroked-button color="light" matTooltip="Available Slots - {{
                  slot.totalSlots - slot.bookedSlots
                }}"  [attr.id]="slot.slotId" *ngIf="slot.totalSlots - slot.bookedSlots > 0"  (click)="bookslot(slot)">
                  {{ slot.strSlotTime }}
                </button>
                            </ng-container>
                        </div>
                    </div>

                </div>
             
            </div>

            <!-- PAYMENT BOTTOM PART -->
            <div fxLayout="row" fxLayoutAlign="end center" class="flex justify-end" fxLayoutGap="8px" *ngIf="radioselect.value == 'Online'">
                <button mat-raised-button type="button" *ngIf="payment" class="buuton1 mr-4" (click)="getCart(ExamID)" [disabled]="!buttonDisable">
          Add Cart
        </button>
                <!-- <button mat-raised-button class="mr-5 buuton2 height" *ngIf="payment" type="button" (click)="getPayment()" [disabled]="!buttonDisable">
          Pay Now
        </button> -->
        <div >
            
                <button mat-raised-button  class="mr-5 buuton2" *ngIf="(rescheduleloading == false && scheduleagainEnable==this.global.ScheduledStatusId ) && reschedule" type="button" (click)="Practicereschedule()">
                    {{ScheduleMessage}}
                  </button>
           
            
        </div>
        <div *ngIf="rescheduleloading  || scheduleloading  ">

            <div class="spinner__loading">
              <div>
                <mat-progress-spinner
                  diameter="60"
                  [value]="value"
                  mode="determinate">
                  </mat-progress-spinner>
              </div>
            </div>
          </div>
             
        <button mat-raised-button class="mr-5 buuton2" *ngIf="(scheduleagainEnable===this.global.paymentCompleted || scheduleagainEnable==this.global.scheduling_error || scheduleagainEnable==this.global.Exam_Cancelled || scheduleagainEnable == this.global.no_Show) && scheduleloading == false " type="button" (click)="PracticescheduleAagin()">
            Schedule
          </button>
            </div>
            <!-- END PAYMENT BOTTOM PART -->

            <!-- <----Online && TestCenter--->
        </div>

        
        <!-- END RIGHT CARD -->
        <!-- BEFORE SELECTING EXAM TYPE -->
        <div class="shadow-none card justify-start touch-auto overflow-auto dashboard registerCard" gdColumn="3/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1" *ngIf="noExamSelected">
            <div class="card shadow-none cardBorder" fxFlex="auto">
                <div class="flex justify-center pt-4 h-full touch-auto overflow-auto" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr" gdRows="1fr 1fr 1fr 1fr" gdRows.lt-md="1fr 1fr 1fr" gdRows.lt-sm="1fr 1fr 1fr">
                    <div class="flex item-center" fxLayoutAlign="end center" gdColumn="4 / 6" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="2 / 3" gdRow.lt-md=" 2 / 3" gdRow.lt-sm="2 / 3" fxLayout="column">
                        <div class="" fxLayout="column">
                            <!-- <img src="assets/img/no_exam_icon.svg" alt="" /> -->
                            <img src="assets/img/NoExam-Blue.svg" alt="" />
                        </div>
                    </div>
                    <div class="flex item-center" gdColumn="3 / 7" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="3 / 4" gdRow.lt-md=" 3 / 4" gdRow.lt-sm="3 / 6" fxLayout="column">
                        <div class="p-4" fxLayout="column">
                            <div class="text-center text-xs empty-eligible slots2">
                                Select exam type to schedule your exam
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END BEFORE SELECTING EXAM TYPE -->
    </div>

    
</div>