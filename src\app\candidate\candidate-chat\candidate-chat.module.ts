import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CandidateChatComponent } from './candidate-chat.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ChatModule } from 'src/app/core/common-component/chat/chat.module';
import { MatButtonModule } from '@angular/material/button';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { CandidateChatReducer } from './store/candidate-chat.reducers';
import { CandidateChatEffects } from './store/candidate-chat.effects';
import { CANDIDATE_CHAT_STATE_NAME } from './store/candidate-chat.selectors';
import { MatIconModule } from '@angular/material/icon';
@NgModule({
  declarations: [
    CandidateChatComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    ChatModule,
    MatButtonModule,
    MatIconModule,
    StoreModule.forFeature(CANDIDATE_CHAT_STATE_NAME, CandidateChatReducer),
    EffectsModule.forFeature([CandidateChatEffects]),
  ],
  exports: [CandidateChatComponent]
})
export class CandidateChatModule { }
