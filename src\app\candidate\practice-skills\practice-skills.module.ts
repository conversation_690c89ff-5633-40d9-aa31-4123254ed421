import { NgModule } from "@angular/core";
import { CommonModule, DatePipe } from "@angular/common";

import { PracticeSkillsRoutingModule } from "./practice-skills-routing.module";
import { PracticeSkillsComponent } from "./practice-skills.component";
import { BreadcrumbsModule } from "src/@exai/components/breadcrumbs/breadcrumbs.module";
import { NgDynamicBreadcrumbModule } from "ng-dynamic-breadcrumb";
import { MatTabsModule } from "@angular/material/tabs";
import { MatCardModule } from "@angular/material/card";
import { MatButtonModule } from "@angular/material/button";
import { MatDialogModule } from "@angular/material/dialog";
import { SkillDetailComponent } from "./skill-detail/skill-detail.component";
import { MatIconModule } from "@angular/material/icon";
import { PaymentComponent } from "../scheduled/payment/payment.component";
import { SkillsTabComponent } from "./skills-tab/skills-tab.component";
import { BundlesTabComponent } from "./bundles-tab/bundles-tab.component";
import { BrowserModule } from "@angular/platform-browser";
import { BundleAttemptsDialogComponent } from "./bundle-attempts-dialog/bundle-attempts-dialog.component";
import { FormsModule } from "@angular/forms";
import { ViewBundleComponent } from "./view-bundle/view-bundle.component";
import { StoreModule } from "@ngrx/store";
import { PRACTICE_FEATURE_KEY } from "./state/practice-skills.selectors";
import {
  practiceSkillBundlesReducer,
  practiceSkillByGuidReducer,
  practiceSkillModeReducer,
  practiceSkillsReducer,
} from "./state/practice-skills.reducer";
import { EffectsModule } from "@ngrx/effects";
import { PracticeSkillsEffects } from "./state/practice-skills.effects";
import { MatTooltipModule } from "@angular/material/tooltip";
import { SkillViewMoreDialogComponent } from "./skills-tab/skill-view-more-dialog/skill-view-more-dialog.component";
import { AddAttemptDialogComponent } from "./skills-tab/add-attempt-dialog/add-attempt-dialog.component";
import { SelectModeDialogComponent } from "./skills-tab/select-mode-dialog/select-mode-dialog.component";
import { CommonComponentModule } from "src/app/core/common-component/common-component.module";
import { SkillModeTypeComponent } from "./components/skill-mode-type/skill-mode-type.component";
import { SkillCardTypeComponent } from "./components/skill-card-type/skill-card-type.component";

@NgModule({
  declarations: [
    PracticeSkillsComponent,
    AddAttemptDialogComponent,
    SkillDetailComponent,
    SkillsTabComponent,
    BundlesTabComponent,
    BundleAttemptsDialogComponent,
    ViewBundleComponent,
    SkillViewMoreDialogComponent,
    SelectModeDialogComponent,
    SkillModeTypeComponent,
    SkillCardTypeComponent,

    // PaymentComponent
  ],
  imports: [
    CommonModule,
    CommonComponentModule,
    FormsModule,
    PracticeSkillsRoutingModule,
    BreadcrumbsModule,
    NgDynamicBreadcrumbModule,
    MatTabsModule,
    MatCardModule,
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    MatTooltipModule,
    StoreModule.forFeature(PRACTICE_FEATURE_KEY, {
      skillsState: practiceSkillsReducer,
      bundlesState: practiceSkillBundlesReducer,
      skillByGuidState: practiceSkillByGuidReducer,
      skillModeState: practiceSkillModeReducer,
    }),
    EffectsModule.forFeature([PracticeSkillsEffects]),
  ],
  providers: [DatePipe],
})
export class PracticeSkillsModule {}
