import { Form, GrevienceModuleState, GrievanceFormsList, ReportGrievance, upcomingExam } from "./grievance.model";

export const store: GrevienceModuleState = {
  grevienceFormList: [],
  reportGrievance: new ReportGrievance(),
  saveGrievance: null,
  saveDraftGrievance: null,
  viewFormStatus: null,
  viewFormProgress: [],
  deletedGrievanceForm:null,
  upcomingExam:[],
  
}


export interface DashboardState {
  form: Form[];
  upcomingExam: upcomingExam[];
  // personForms: PersonForm[],
  isCancelled: boolean,

}


export const initDashboardState: DashboardState = {
  form: [],
  upcomingExam: [],
  // personForms: [],
  isCancelled: null,
};
