import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { FormTypeID } from 'src/app/core/examroom-formbuilder/form-builder.types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { selectorGetChargeResources } from '../scheduled/state/scheduled.selectors';
import { getCartItems } from '../state/shared/shared.actions';
import { get_cartItems } from '../state/shared/shared.selectors';
import { FormsWrapperComponent } from './forms-wrapper.component';
import { FormCartItemId, FormFee, FormTypes, renewelFee, StateList } from './forms-wrapper.types';
import { addRenewlTocart } from './state/application.actions';
import { RenewelResponseCart } from './state/application.selectors';
import { ApplicationState } from './state/application.state';

@Injectable({
  providedIn: 'root'
})
export class FormsWrapperService {
  public userResponse=null
  existingcartId: any;
   renewal = new Subject<any>();
  renewalPayment = this.renewal.asObservable();
  alreadySubscribed=false;
  genrateScCertificate=false;
  savedResponse:SavedResponse;
  savedResponseId:number;
  constructor(
    private navigate:Router,
    public store: Store<ApplicationState>,
    public global: GlobalUserService,
  ) { }

  addTocartRenewlFee(formResponse,queryParams):void {
    this.getCartDetails()
    let renewelDetails:renewelFee
    let a=this.store.select(get_cartItems).subscribe(data => {
      if (data) {
        this.existingcartId = data;
        let CartDetails = [{
          cartItemTypeId: formResponse.formTypeID.includes(14)?FormCartItemId['V2-renewal-scmae']:formResponse.formTypeID.includes(17)?FormCartItemId['SC_reinstate_cartypeId']:FormCartItemId.Renewal,
          amount: formResponse.fees,
          examCode: formResponse.formCode,
          personEventCartId: this.existingcartId.length==0?0:Number(this.existingcartId[0].personEventCartId),
          Registryid:(this.global.stateId in StateList)?Number(queryParams.personEventId):null,
          quantity: 1
        }]
        renewelDetails = {
          cartItems: CartDetails,
          currencyId: 1,
          personTenantRoleId: this.global.candidateId,
        }
        if(this.existingcartId.some(item=>(item.examTypeId == FormTypes.Certificate_Renewal || item.examTypeId == FormTypes['V2-renewal-SCMAE'] || item.examTypeId == FormTypes.SC_Reinstate_Renewal))||this.existingcartId.length>=0){
        this.store.dispatch(addRenewlTocart({ renewelCartDetails: renewelDetails }))
        this.store.select(RenewelResponseCart).subscribe(data => {
          if (data!=null) {
            //this.navigate.navigateByUrl('/exam-scheduled/payment');
            this.getCartDetails();
          }
        }) 
      }
      else{
        //this.navigate.navigateByUrl('/exam-scheduled/payment');
      }
      }
      a.unsubscribe();
    })
    
  }

  addTocartReciprocityFee(formResponse,status?:number):void {
    this.getCartDetails()
    let renewelDetails:renewelFee
    let a=this.store.select(get_cartItems).subscribe(data => {
      if (data) {
        this.existingcartId = data;
        let CartDetails = [{
          cartItemTypeId: formResponse.formTypeID.includes(15)?FormCartItemId['V2-reciporating-scmae']:formResponse.formTypeID.includes(2) &&  status != 3?FormCartItemId['V2_greivance_code']:formResponse.formTypeID.includes(2) && status == 3?FormCartItemId['V2_greivance_code_appeal']:FormCartItemId.Reciprocity,
          amount: status == 3 && formResponse.formTypeID.includes(2)?26:formResponse.fees,
          examCode: formResponse.formCode,
          personEventCartId: this.existingcartId.length==0?0:Number(this.existingcartId[0].personEventCartId),
          quantity: 1
        }]
        renewelDetails = {
          cartItems: CartDetails,
          currencyId: 1,
          personTenantRoleId: this.global.candidateId
        }
        if(this.existingcartId.some(item=>(item.examTypeId == FormTypes.Certificate_Reciprocity) || item.examTypeId == FormTypes['V2-reciporating-SCMAE'] || item.examTypeId == FormTypes['GrievanceEvaluator'] || item.examTypeId ==FormTypes["examtype_appeal"] )||this.existingcartId.length>=0){
        this.store.dispatch(addRenewlTocart({ renewelCartDetails: renewelDetails }))
        this.store.select(RenewelResponseCart).subscribe(data => {
          if (data!=null) {
            //this.navigate.navigateByUrl('/exam-scheduled/payment');
            this.getCartDetails();
            this.global.Grievanceroute ='Evaluator'
          }
        }) 
      }
      else{
        //this.navigate.navigateByUrl('/exam-scheduled/payment');
      }
      }
      a.unsubscribe();
    })
    
  }

  getCartDetails() {
    this.store.dispatch(getCartItems({ personTenantRoleId: this.global.candidateId }))
   
  }

  applyrenewelForm(result,cartItem){
    this.alreadySubscribed=true;
    this.genrateScCertificate=true;
    this.renewal.next([result,cartItem]);
  }
}


export interface SavedResponse{
  formTypeID:number;
  userResponse:userResponse
}

export interface userResponse{
    personTenantRoleId:number;
    formResponse: string;
    formId:number;
    code: string;
    personEventId: any;
    actionBy: any;
}
