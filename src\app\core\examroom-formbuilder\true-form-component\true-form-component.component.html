<div class="card p-6 fb-ht-fixed" style="width: 95vw;" fxFlex fxFlex.xs="auto">
    <h4 class="mt-0 mb-6">Form Builder</h4>
    <div class="basic-card mb-6" *ngIf="formBuilderService.genesisForm;else createFormButton">
        <div>
            <mat-icon class="add-icon text-2l" style="top: 5px !important;" mat-button (click)="createNewFormOrSection(3)">edit
            </mat-icon>
        </div>
        <p><strong>Form Id:</strong> {{this.formBuilderService.genesisForm.id}} | <strong>Form Name</strong>: {{this.formBuilderService.genesisForm.name}}
        </p>
        <p><strong>Description</strong>: {{this.formBuilderService.genesisForm.description}}</p>
        <p><strong>Instructions:</strong><br>
            <div class="row" [innerHTML]="formBuilderService.genesisForm.instructions"></div>
            <!-- <div class="row">
                {{this.formBuilderService.genesisForm.instructions}}
            </div> -->
            <strong>Sections:</strong>
            <div class="basic-card-children" *ngFor="let section of formBuilderService.genesisForm.sections; let i = index;">
                <p><strong>Section Id:</strong> {{section.id}} | <strong>Section Name</strong>: {{section.name}}</p>
                <p><strong>Description</strong>: {{section.description}}</p>
                <p><strong>Instructions:</strong><br>
                    <div class="row" [innerHTML]="section.instructions"></div>
                    <!-- <div class="row">
                        {{section.instructions}}
                    </div> -->
                    <div>
                        <mat-icon *ngIf="i > 0" class="add-icon text-2l" style="right:120px!important;top: 5px !important;" mat-button (click)="relocateSection(i,i-1)">keyboard_arrow_up</mat-icon>
                        <mat-icon *ngIf="i < formBuilderService.genesisForm.sections.length-1" class="add-icon text-2l" style="right:80px!important;top: 5px !important;" mat-button (click)="relocateSection(i,i+1)">keyboard_arrow_down</mat-icon>
                        <mat-icon class="add-icon text-2l" style="right:40px!important;top: 5px !important;" mat-button (click)="createNewFormOrSection(4,i)">edit</mat-icon>
                        <mat-icon class="add-icon text-2l font-bold" style="top: 5px !important;" mat-button (click)="this.formBuilderService.genesisForm.sections.splice(i,1);this.formBuilderService.editData.sections.splice(i,1)">
                            close</mat-icon>
                    </div>
                    <div class="basic-card-children">
                        <strong>Question Groups:</strong>
                        <mat-accordion class="example-headers-align" multi cdkDropList (cdkDropListDropped)="drop($event,i)">
                            <mat-expansion-panel style="width:94%" class="m-4" *ngFor="let quesGroup of section.QuestionGroups; let qgpIndex=index">
                                <mat-expansion-panel-header class="relative" cdkDrag>
                                    <strong> Name: </strong> &nbsp;&nbsp;{{quesGroup.name}} | &nbsp;&nbsp; <strong> Label:
                            </strong> &nbsp;&nbsp;{{quesGroup.label}}
                                    <mat-icon class="mt-0 mb-1 text-2l cursor-pointer absolute" style="top:0.5rem; right:0.125rem" (click)="removeQuesGroup(i,qgpIndex)">
                                        close</mat-icon>
                                    <mat-icon *ngIf="formBuilderService.editData?.sections[i]?.QuestionGroups[qgpIndex]" class="mt-0 mb-1 text-2l cursor-pointer absolute" style="top:0.5rem; right:1.725rem" (click)="createNewQuestionGroup(i,qgpIndex)">
                                        edit</mat-icon>
                                </mat-expansion-panel-header>
                                <pre>{{quesGroup | json}}</pre>
                            </mat-expansion-panel>
                        </mat-accordion>
                    </div>
                    <div class="mat-elevation-z12">
                        <mat-icon class="add-icon mat-elevation-z12" mat-button [matTooltip]="'Add Question Group'" (click)="createNewQuestionGroup(i)">add_box</mat-icon>
                    </div>
            </div>
            <div class="mat-elevation-z12">
                <mat-icon class="add-icon mat-elevation-z12" [matTooltip]="'Add Section'" mat-button (click)="createNewFormOrSection(2)">add_box</mat-icon>
            </div>
    </div>
    <ng-template #createFormButton>
        <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="createNewFormOrSection(1)">
            Create Form
        </button>
        <button mat-button class="ml-4 bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="showLoadTextArea.next(!showLoadTextArea.value)">
            {{($showLoadTextArea | async) ? "Don't Load" : "Load"}} from JSON
        </button>
        <form [formGroup]="jsonTOLoad" *ngIf="($showLoadTextArea | async)" class="w-full flex flex-col p-1">
            <mat-form-field appearance="outline">
                <textarea matInput rows="10" placeholder="Paste Form JSON here and press enter" class="border-gray-900 border-2 w-full m-2" formControlName="formJSON" required></textarea>
            </mat-form-field>
            <mat-form-field appearance="outline">
                <textarea matInput rows="10" placeholder="Paste Edit Data here and press Enter" class="border-gray-900 border-2 w-full m-2" formControlName="editData" required></textarea>
            </mat-form-field>
            <button [disabled]="jsonTOLoad.invalid" (click)="loadJSON()" class="ml-4 bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow">
                Load
            </button>
        </form>
    </ng-template>
</div>