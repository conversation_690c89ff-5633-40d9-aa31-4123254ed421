import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { PracticeSkillResponse } from "../models/practice-skill-response.model";
import { URL } from "src/app/core/url";
import { PracticeSkillBundleResponse } from "../models/practice-skill-bundle.model";
import { PracticeSkillMode } from "../models/practice-skill-mode.model";
import {
  ApiSuccessResponse,
  PracticeSkillPayload,
} from "../models/practice-skill-payload";
@Injectable({
  providedIn: "root",
})
export class PracticeSkillService {
  constructor(private _http: HttpClient) {}

  /**
   * Fetching practice skills from API based on pagination and skillId
   * @param pageNo
   * @param pageSize
   * @param skillId
   * @returns
   */
  // getPracticeSkills( pageNo:number, pageSize:number):Observable<PracticeSkillResponse>{
  //   const apiUrl = `${URL.PRACTICE_SKILL_API_BASE}practice-skills?pageNo=${pageNo}&pageSize=${pageSize}`;
  //   return this._http.get<PracticeSkillResponse>(apiUrl);
  // }
  getPracticeSkills(
    pageNo: number,
    pageSize: number
  ): Observable<PracticeSkillResponse> {
    const programGuid = "20403475-448A-4F5D-AE72-BFD2127F9C0B";
    const tenantGuid = "7D8ED605-EC2C-4449-A41F-6F2ED50F792E";

    const apiUrl = `${URL.PRACTICE_SKILL_API_BASE}practice-skills?pageNo=${pageNo}&pageSize=${pageSize}`;

    const headers = new HttpHeaders({
      ProgramGuid: programGuid,
      TenantGuid: tenantGuid,
    });

    return this._http.get<PracticeSkillResponse>(apiUrl, { headers });
  }
  /**
   * Fetching Bundled practice skills from API based on pagination
   * @param pageNo
   * @param pageSize
   * @returns
   */
  getPracticeSkillBundles(
    pageNo: number,
    pageSize: number
  ): Observable<PracticeSkillBundleResponse> {
    const apiUrl = `${URL.PRACTICE_SKILL_API_BASE}bundle-skills?pageNo=${pageNo}&pageSize=${pageSize}`;
    return this._http.get<PracticeSkillBundleResponse>(apiUrl);
  }

  /**
   *
   * @param practiceSkillGuid
   * @returns
   */
  getPracticeSkillByGuid(
    practiceSkillGuid: string
  ): Observable<PracticeSkillResponse> {
    const programGuid = "20403475-448A-4F5D-AE72-BFD2127F9C0B";
    const tenantGuid = "7D8ED605-EC2C-4449-A41F-6F2ED50F792E";

    const apiUrl = `${URL.PRACTICE_SKILL_API_BASE}practice-skills`;

    const headers = new HttpHeaders({
      ProgramGuid: programGuid,
      TenantGuid: tenantGuid,
      PracticeSkillGuid: practiceSkillGuid,
    });

    return this._http.get<PracticeSkillResponse>(apiUrl, { headers });
  }

  getPracticeSkillModes(): Observable<PracticeSkillMode> {
    const programGuid = "20403475-448A-4F5D-AE72-BFD2127F9C0B";
    const tenantGuid = "7D8ED605-EC2C-4449-A41F-6F2ED50F792E";

    const apiUrl = `${URL.PRACTICE_SKILL_API_BASE}practice-skills/mode`;

    const headers = new HttpHeaders({
      ProgramGuid: programGuid,
      TenantGuid: tenantGuid,
    });

    return this._http.get<PracticeSkillMode>(apiUrl, { headers });
  }

  AddPracticeSkillToCart(
    payload: PracticeSkillPayload
  ): Observable<ApiSuccessResponse> {
    const apiUrl = `${URL.BASE_URL}PracticeSkill/addcart`;
    return this._http.post<ApiSuccessResponse>(apiUrl, payload);
  }
}
