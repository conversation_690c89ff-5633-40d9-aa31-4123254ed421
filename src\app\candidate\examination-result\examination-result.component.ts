import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ShowPracticeExamResult } from './service';
import { ExamResult } from './model';
import { SnackbarService } from 'src/app/snackbar.service';

@Component({
  selector: 'exai-examination-result',
  templateUrl: './examination-result.component.html',
  styleUrls: ['./examination-result.component.scss']
})
export class ExaminationResultComponent  {

  examResult:ExamResult[];
  constructor(  
    private dialogRef: MatDialogRef<ExaminationResultComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: any,
    private showPracticeExamResult :ShowPracticeExamResult,
    private snackbarService:SnackbarService)
  {
    
  }

  ngOnInit(){
    this.showPracticeExamResult.getPracticeExamResults(this.data.id).subscribe((examResult:ExamResult[]) => {
      this.examResult = examResult;
    },
    (error) =>{
      this.snackbarService.callSnackbaronError("Result Not Found");
      this.dialogRef.close()
    }
  );
  }
}

