import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { GrievanceFormRoutingModule } from './grievance-form-routing.module';
import { GrievanceFormComponent } from './grievance-form.component';
import { MatCardModule } from '@angular/material/card';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { FormBuilderModule } from 'src/app/core/examroom-formbuilder/form-builder.module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { GRIEVANCE_MODULE_STATE } from './state/grievance.selectors';
import { reducer } from './state/grievance.reducers';
import { GrievanceEffects } from './state/grievance.effects';
import { MatSnackBarModule} from '@angular/material/snack-bar';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
@NgModule({
  declarations: [
    GrievanceFormComponent
  ],
  imports: [
    CommonModule,
    GrievanceFormRoutingModule,
    MatCardModule,
    FlexLayoutModule,
    ContainerModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    NgDynamicBreadcrumbModule,
    FormBuilderModule,
    MatTooltipModule,
    MatMenuModule,
    MatSnackBarModule,
    StoreModule.forFeature(GRIEVANCE_MODULE_STATE, reducer),
    EffectsModule.forFeature([GrievanceEffects]),
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatDialogModule,
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class GrievanceFormModule { }
