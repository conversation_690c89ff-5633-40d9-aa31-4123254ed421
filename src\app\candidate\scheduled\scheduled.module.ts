import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ScheduledRoutingModule } from './scheduled-routing.module';
import {  ScheduledComponent } from './scheduled.component';
import { RegisterForExamComponent } from './register-for-exam/register-for-exam.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatExpansionModule} from '@angular/material/expansion';
import { PaymentComponent } from './payment/payment.component';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { PaymentTranscationDetailsComponent } from './payment/payment-transcation-details/payment-transcation-details.component';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ScheduledEffects } from './state/scheduled.effects';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { scheduledReducer } from './state/scheduled.reducers';
import { SCHEDULED_STATE_NAME } from './state/scheduled.selectors';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ConfirmationPopupComponent } from './confirmation-popup/confirmation-popup.component';
import { TestCenterComponent } from './test-center/test-center.component';
import { CartSummaryPopupComponent } from './cart-summary-popup/cart-summary-popup.component';
import { NgxMaskModule, IConfig } from 'ngx-mask';
import {IMaskModule} from 'angular-imask';
import {MatTabsModule} from '@angular/material/tabs';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { ImgPopUpComponent } from 'src/app/core/img-pop-up/img-pop-up.component';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { ReschudelPaymentComponent } from './reschudel-payment/reschudel-payment.component';
import { TestDirectionsDialogComponent } from './test-directions-dialog/test-directions-dialog.component';
import { CommonComponentModule } from 'src/app/core/common-component/common-component.module';
import { AgmCoreModule } from '@agm/core'; 
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatBadgeModule } from '@angular/material/badge';
// import { NgxSpinnerModule } from "ngx-spinner";
export const options: Partial<IConfig> | (() => Partial<IConfig>) = null;

@NgModule({
  declarations: [
    ScheduledComponent,
    RegisterForExamComponent,
    PaymentComponent,
    PaymentTranscationDetailsComponent,
    RegisterForExamComponent,
    ConfirmationPopupComponent,
    TestCenterComponent,
    CartSummaryPopupComponent,
    ImgPopUpComponent,
    ReschudelPaymentComponent,
    TestDirectionsDialogComponent,
  ],
  imports: [
    CommonModule,
    NgDynamicBreadcrumbModule,
    ScheduledRoutingModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatRadioModule,
    MatExpansionModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTabsModule,
    MatSelectModule,
    MatOptionModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    FlexLayoutModule,
    ContainerModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatIconModule,
    MatMenuModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatBadgeModule,
    NgxMaskModule.forRoot(),
    IMaskModule,
    // NgxSpinnerModule,
   FormsModule,
   MatProgressBarModule,
   CommonComponentModule,
    HttpClientModule,
    AgmCoreModule.forRoot({
      apiKey: 'AIzaSyBSjClx_SH3L-Li9-ilB8j_bfPHZ7QVeu4',
      libraries: ['places']
    })

  ],
  providers:[{provide: MatDialogRef,
    useValue: {}},
    DatePipe, 
    
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class ScheduledModule { }
