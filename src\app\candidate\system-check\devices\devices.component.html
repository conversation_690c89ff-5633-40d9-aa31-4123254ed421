<section class="text-center">
    <h5 class="header font-bold mb-4" >Device preview</h5>
    <div class="embed-responsive embed-responsive-16by9 video-frame">
        <video class="video" autoplay></video>
        <div class="more-bottom">
            <div class="pids-wrapper">
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
                <div class="pid"></div>
            </div>
        </div>
    </div>
    <form [formGroup]="form" class="device-selection">
        <mat-form-field appearance="outline">
            <mat-label>Video source</mat-label>
            <mat-select (change)="videoChange($event)" formControlName="video">
                <mat-option *ngFor="let n of videoDevices" [value]="n.deviceId">{{n.label}}</mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline">
            <mat-label>Audio source</mat-label>
            <mat-select (selectionChange)="audioChange($event)" formControlName="audio">
                <mat-option selec *ngFor="let n of audioDevices" [value]="n.deviceId">{{n.label}}</mat-option>
            </mat-select>
        </mat-form-field>
    </form>
    <button mat-flat-button color="primary" [disabled]="disableContinue" (click)="continue()">Continue</button>
</section>