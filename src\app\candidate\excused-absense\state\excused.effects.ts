import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { switchMap, map, take } from "rxjs/operators";
import * as ExcusedActions from './excused.action';
import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { cancelExam, Examcancelled, getupcomingExam, loadAll, loadAllSuccess } from "./excused.action";
import { FormTypes } from "src/app/core/Dto/enum";
import { URL } from 'src/app/core/url';
import { HttpService } from "src/app/core/http-services/http.service";
import { ExcusedAbsense,  } from "./excused.model";
@Injectable({
  providedIn: 'root'
})
export class ExcusedEffects {
  constructor(private actions$: Actions, private http: HttpClient,
    private global: GlobalUserService,
    private snackbar: SnackbarService,
    private httpService: HttpService) { }

  loadAll$ = createEffect(() => this.actions$.pipe(
    ofType(loadAll),
    switchMap(
      (action) => {
        return this.http
          .get<any>(`${URL.BASE_URL}form/personform?candidateId=${this.global.candidateId}&formTypeId=${FormTypes.ExcusedAbsense}`)
          .pipe(
            map(data => loadAllSuccess({ data :data })),
            take(1)
          );
      })
  ));

  loadAbsenseform$ = createEffect(() => this.actions$.pipe(
    ofType(ExcusedActions.loadAbsenseform),
    switchMap(
      (action) => {
        return this.httpService.loadAbsenseform().pipe(
          map((data: ExcusedAbsense) => {
            return ExcusedActions.loadAbsenseform();
          }),
          take(1)
        );
      })
  ));

  saveReportGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(ExcusedActions.requestReportGrievance),
    switchMap(
      (action) => {
        return this.http.post(`${URL.BASE_URL}form/savepersonform`, action.params)
          .pipe(
            map(data => {
              if (data) {
                this.snackbar.callSnackbaronSuccess("Submitted successfully")
              }
              return ExcusedActions.postReportGrievanceSuccess({ data })
            }),
            take(1)
          )
          ;
      })
  ));

  saveDraftReportGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(ExcusedActions.requestSaveDraftReportGrievance),
    switchMap(
      (action) => {
        return this.http.post(`${URL.BASE_URL}form/savepersonform`, action.params)
          .pipe(
            map(data => {
              if (data) {
                this.snackbar.callSnackbaronSuccess("Saved successfully")
              }
              return ExcusedActions.postSaveDraftReportGrievanceSuccess({ data })
            }),
            take(1)
          );
      })
  ));

  viewGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(ExcusedActions.requestViewGrievance),
    switchMap(
      (action) => {
        return this.http
          .get<any>(`${URL.BASE_URL}form/personform/list?personFormId=${action.params.personFormId}`)
          .pipe(
            map(data => ExcusedActions.postViewGrievanceSuccess({ data })),
            take(1)
          );
      })
  ));

  viewGrievanceProgress$ = createEffect(() => this.actions$.pipe(
    ofType(ExcusedActions.requestPersonProgress),
    switchMap(
      (action) => {
        return this.http
          .get<any>(`${URL.BASE_URL}form/personformlogs?personFormId=${action.personFormId}`)
          .pipe(
            map(data => ExcusedActions.getPersonProgressSuccess({ data })),
            take(1)
          );
      })
  ));


  effectivelyCancelExam$ = createEffect(() => this.actions$.pipe(
    ofType(cancelExam),
    switchMap(
      (action) => {
        return this.http
          .delete<any>(`${URL.BASE_URL}form/personform?candidateId=${action.candidateId}&personFormId=${action.personFormId}`)
          .pipe(
            map((data) => {
              if (data) {
                this.snackbar.callSnackbaronSuccess("Cancelled successfully")
              }
              return Examcancelled({ isCancelled: data })
            }),
            take(1)
          );
      })
  ));


  effectivelyGetupcomingExam$ = createEffect(() => this.actions$.pipe(
    ofType(getupcomingExam),
    switchMap(
      (action) => {
        return this.http.get<any>(`${URL.BASE_URL}exam/upcomingexam?candidateId=${this.global.candidateId}`)
          .pipe(
            map(upcomingExam => ExcusedActions.gotupcomingExam({ upcomingExam: upcomingExam })),
            take(1)
          );
      }
    )
  ))
}