import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { createEffect, Actions, ofType } from '@ngrx/effects';
import { switchMap, map, take } from 'rxjs/operators';
import { getEligibilityRouteDetails, getEligibilityRoutes , getPersonForms, getShowRegisterExam, gotEligibilityRouteDetails, gotEligibilityRoutes, gotPersonForms, gotShowRegisterExam} from './application.actions';
import { eligibilityRoute, eligibilityRouteDetails } from '../application.types';
import { URL } from 'src/app/core/url';
import { HttpExamService } from 'src/app/core/http-services/http.exams.service';
@Injectable({
  providedIn: 'root',
})
export class ApplicationEffects {

  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private _examHttpService:HttpExamService
  ) { }

  effectivelyGetEligibilityRoutes$ = createEffect(() => this.actions$.pipe(
    ofType(getEligibilityRoutes),
    switchMap((action) => {
      return this.httpClient
        .get<Array<eligibilityRoute>>(URL.BASE_URL + `eligibilityroute/listByState?stateId=${action.stateId}&candidateId=${action.candidateId}`)
        .pipe(
          map(eligibilityRoutes =>
            gotEligibilityRoutes({ eligibilityRoutes })),
            take(1)
        );
    }),
  ));

  effectivelyGetEligibilityRouteDetails$ = createEffect(() => this.actions$.pipe(
    ofType(getEligibilityRouteDetails),
    switchMap((action) => {
      // return this.httpClient.get<eligibilityRouteDetails>(URL.BASE_URL + '')
      return this.httpClient
        .get<eligibilityRouteDetails>(URL.BASE_URL + `eligibilityroute/detail/${action.eligibilityRoute.id}`)
        .pipe(
          map(eligibilityRouteDetails =>
            gotEligibilityRouteDetails({
              routeIndex: action.routeIndex,
              eligibilityRoute: action.eligibilityRoute,
              eligibilityRouteDetails: eligibilityRouteDetails,
              
            })),
            take(1)
        );
    }),
  ));

  effectivelyGetPersonForm$ = createEffect(() => this.actions$.pipe(
    ofType(getPersonForms),
    switchMap((action) => {
      return this.httpClient
        .get<any>(URL.BASE_URL + `form/personform?candidateId=${action.candidateId}&formTypeId=${action.formTypeId1}&formTypeId=${action.formTypeId2}`)
        .pipe(
          map(personForms =>
            gotPersonForms({
              personForms: personForms
            })),
          take(1)
        );
    }),
  ));

  effectivelyShowRegisterExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getShowRegisterExam),
      switchMap((action) => {
        return this._examHttpService.getshowregisterExam(action.personTenantRoleId).pipe(
          map(data =>
            gotShowRegisterExam({
              data: data
            })),
            take(1)
        )
      })
    )
  );

}
