import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { PracticeSkillModeDetail } from "../../state/models/practice-skill-mode.model";

@Component({
  selector: "exai-skill-mode-type",
  templateUrl: "./skill-mode-type.component.html",
  styleUrls: ["./skill-mode-type.component.scss"],
})
export class SkillModeTypeComponent implements OnInit {
  @Input() mode!: PracticeSkillModeDetail;
  @Input() selectedModeId!: number;
  @Input() tooltip?: string;
  @Input() extraInfo?: string; // Optional bottom line
  @Input() showPrice: boolean = true;

  @Output() modeSelected = new EventEmitter<PracticeSkillModeDetail>();

  selectMode() {
    this.modeSelected.emit(this.mode);
  }
  constructor() {}

  ngOnInit(): void {}
}
