<div class="w-full flex flex-col h-full max-h-full touch-auto overflow-auto relative" *ngIf="!conversation">
    <div style="width: 0px; height: 0px; border: 0px solid" id="remote-video"></div>
    <button type="button" class="inline-flex items-center justify-center rounded-full h-10 w-10 transition duration-500 ease-in-out text-gray-500 hover:bg-gray-300 focus:outline-none absolute" style="top:1rem;right:1rem">
        <mat-icon class="h-6 w-6"
                  (click)="closeChat({closerId:candidateORclientID})"> cancel </mat-icon>
    </button>
    <br>
    <p class="m-4">{{initialStatusMessage}}</p>
</div>
<div *ngIf="conversation" class="w-full flex flex-col h-full max-h-full touch-auto overflow-auto">
    <exai-chat [peer]="peer" [senderID]="this.candidateORclientID" [connection]="conversation" (closeChatConnection)="closeChat($event)"></exai-chat>
</div>