import { state } from "@angular/animations";
import { createReducer, on } from "@ngrx/store";
import { FormTypes } from "src/app/core/Dto/enum";
import { eligibilityRoute } from '../application.types';
import { gotEligibilityRouteDetails, gotEligibilityRoutes, gotPersonForms, clearApplicationState, getPersonForms, gotShowRegisterExam } from './application.actions';
import { ApplicationState, intialApplicationState } from "./application.state";

const _applicationReducer = createReducer<ApplicationState>(
  intialApplicationState,
  on(gotEligibilityRoutes, (state, action) => {
    return {
      ...state,
      eligibilityRoutes: action.eligibilityRoutes
    }
  }),
  on(getPersonForms, (state, action) => {
    return {
      ...state,
      loading: true
    }
  }),
  on(gotEligibilityRouteDetails, (state, action) => {
    return {
      ...state,
      eligibilityRoutes: [...state.eligibilityRoutes.map((x: eligibilityRoute) => {
        if (x.id == action.eligibilityRoute.id) {
          let new_x: eligibilityRoute = {
            id: x.id,
            eligibilityName: x.eligibilityName,
            tenantCode: x.tenantCode,
            eligibilityRouteDetails: action.eligibilityRouteDetails,
            isFormSubmissionAllowed:action.isFormSubmissionAllowed
            
          };
          return new_x;
        }
        return x;
      })],
      activeEligibilityRouteIndex: action.routeIndex
    }
  }),
  on(gotPersonForms, (state, action) => {
    return {
      ...state,
      personForms: action.personForms,
      loading : false
    }
  }),

  
  on(clearApplicationState, (state, action) => {
    return {
      ...state,
      eligibilityRoutes: [],
      activeEligibilityRouteIndex: null,
      eligiblityRouteDetails: null,
      personForms: [],
      loading: false
    }
  }),

  on(gotShowRegisterExam, (state, action) => {
    return {
        ...state,
        showRegisterExamStatus: action.data
    }
})
)


export function applicationReducer(state, action) {
  return _applicationReducer(state, action);
}

