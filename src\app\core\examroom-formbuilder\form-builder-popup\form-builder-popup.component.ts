import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { Editor, Toolbar } from 'ngx-editor';
import { v4 as uuidv4 } from 'uuid';
import { requestParam, requestDetails, paramTypes } from '../form-builder.types';
@Component({
  selector: 'exai-form-builder-popup',
  templateUrl: './form-builder-popup.component.html',
  styleUrls: ['./form-builder-popup.component.scss']
})
export class FormBuilderPopupComponent implements OnInit,OnDestroy {

  paramTypesKeys = Object.keys(paramTypes).filter(x => !(parseInt(x) >= 0))
  paramTypes = paramTypes;
  basicFormOrSectionDetailsSub: Subscription;
  requestParams: FormArray;
  basicFormOrSectionDetails: FormGroup;
  typeName: string;
  editor: Editor;
  toolbar: Toolbar = [
    ["bold", "italic"],
    ["underline", "strike"],
    ["code", "blockquote"],
    ["ordered_list", "bullet_list"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["link", "image"],
    ["text_color", "background_color"],
    ["align_left", "align_center", "align_right", "align_justify"]
  ];
  constructor(private formBuilder: FormBuilder,
    public dialogRef: MatDialogRef<FormBuilderPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public res: any,) {
    switch (res.type) {
      case 3:
      case 1: this.typeName = "Form";
        break;
      case 4:
      case 2: this.typeName = "Section";
        break;
    }
    this.basicFormOrSectionDetails = this.formBuilder.group({
      id: [res.id ? res.id : uuidv4()],
      name: [res.name ? res.name : ''],
      description: [res.description ? res.description : '', ],
      instructions:[res.instructions ? res.instructions : '',],
      showInstructionInIButton: [res.showInstructionInIButton ? res.showInstructionInIButton : ''],
      fetchFromApi: [res.fetchFromApi ? res.fetchFromApi : ''],
      fetchUrl: [res.requestDetails && res.requestDetails.fetchUrl ? res.requestDetails.fetchUrl.join('/') : ''],
      method: [res.requestDetails && res.requestDetails.method ? res.requestDetails.method : '']
    });
    this.setRequestParamForm(res.requestDetails && res.requestDetails.requestParams ? res.requestDetails.requestParams : null);
    this.basicFormOrSectionDetailsSub = this.basicFormOrSectionDetails.controls.name.valueChanges.subscribe((value: string) => {
      this.basicFormOrSectionDetails.controls.id.setValue(
        (this.res.id ? this.res.id : '') + value.split(' ').join('_').split('/').join('').split('&').join('').toLowerCase());
    })
  }

  ngOnInit(): void {
    this.editor = new Editor();
  }

  setRequestParamForm(requestParam: Array<requestParam> = null) {
    if (requestParam) {
      this.requestParams = new FormArray([]);
      for (let param of requestParam)
        this.requestParams.push(this.formBuilder.group({
          paramType: [param.paramType],
          paramValue: [param.paramValue],
          paramName: [param.paramName],
          elementPropertyToBeExtracted: [param.elementPropertyToBeExtracted],
          extractedFromGlobal: [param.extractedFromGlobal],
          extractedFromElement: [param.extractedFromElement],
          position: [param.position]
        })
        )
    }
    else {
      this.requestParams = new FormArray([
        this.formBuilder.group({
          paramType: [""],
          paramValue: [""],
          paramName: [""],
          elementPropertyToBeExtracted: [""],
          extractedFromGlobal: [""],
          extractedFromElement: [""],
          position: [""]
        }),
      ]);
    }
  }

  addParam() {
    this.requestParams.push(
      this.formBuilder.group({
        paramType: [""],
        paramValue: [""],
        paramName: [""],
        elementPropertyToBeExtracted: [""],
        extractedFromGlobal: [""],
        extractedFromElement: [""],
        position: [""]
      }
      ))
  }

  submit(){
    if (this.basicFormOrSectionDetails.valid) {
      this.dialogRef.close({
        id: this.basicFormOrSectionDetails.value.id,
        name: this.basicFormOrSectionDetails.value.name,
        description: this.basicFormOrSectionDetails.value.description,
        instructions:this.basicFormOrSectionDetails.value.instructions,
        showInstructionInIButton: this.basicFormOrSectionDetails.value.showInstructionInIButton,
        fetchFromApi: this.basicFormOrSectionDetails.value.fetchFromApi,
        requestDetails: <requestDetails>{
          fetchUrl: this.basicFormOrSectionDetails.value.fetchUrl?.split('/'),
          requestParams: this.requestParams.value,
          requestBody: null,
          method:this.basicFormOrSectionDetails.value.method
        }
      });
    }
  }

  ngOnDestroy() {
    this.basicFormOrSectionDetailsSub?.unsubscribe();
  }

}
