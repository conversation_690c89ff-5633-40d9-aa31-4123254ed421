import { Component, Inject, Input, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { SnackbarService } from 'src/app/snackbar.service';
import { CartItem } from '../../scheduled/state/models/cartItem';
import { bundleList, skillsList } from '../practice-skills.data';

@Component({
  selector: 'exai-bundle-attempts-dialog',
  templateUrl: './bundle-attempts-dialog.component.html',
  styleUrls: ['./bundle-attempts-dialog.component.scss']
})
export class BundleAttemptsDialogComponent implements OnInit {

  bundle: any;
  bundleData = [...bundleList];
  additionalAttempts: { [bundleId: string]: number } = {};
  pricePerAttempt = 5;
  quantity = 1;

  availableSkills = skillsList;
  selectedSkill2: any = null;
  selectedSkill1: any = null;
  constructor(
    private router: Router,
    private _dialogRef: MatDialogRef<BundleAttemptsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.bundle = data.bundle;
  }

  ngOnInit(): void { }

  closeDialog() {
    this._dialogRef.close();
  }


  /**
 * increaseAttempts by clicking ADD
 */
  increaseAttempts(bundleId?: number) {
    if (!bundleId) return;
    this.additionalAttempts[bundleId] = (this.additionalAttempts[bundleId] || 0) + 1;
  }
  /**
   * decreaseAttempts by clicking SUB
   */
  decreaseAttempts(bundleId?: number) {
    if ((this.additionalAttempts[bundleId] || 0) > 0) {
      this.additionalAttempts[bundleId] -= 1;
    }
  }
  /***
 * 
 */
  getTotal(bundle: any): number {

    const basicPrice = parseFloat(bundle.price.replace('$', '')) || 0;

    const attempts1 = this.selectedSkill1 ? (this.additionalAttempts[this.selectedSkill1.id] || 0) : 0;
    const attempts2 = this.selectedSkill2 ? (this.additionalAttempts[this.selectedSkill2.id] || 0) : 0;

    const totalExtraCost = (attempts1 + attempts2) * this.pricePerAttempt;

    return basicPrice + totalExtraCost;
  }

  proceedNow() {
    this.closeDialog();
    this.router.navigate(['/practice-skills/payment', this.bundle.id], {
      state: {
        type: 'bundle',
        bundleItem: this.bundle
      }
    });
  }
}
