import { Component, OnInit, ViewChild } from '@angular/core';
import { LayoutService } from '../../../@exai/services/layout.service';
import { debounceTime, filter, map, startWith } from 'rxjs/operators';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { checkRouterChildsData } from '../../../@exai/utils/check-router-childs-data';
import { BreakpointObserver } from '@angular/cdk/layout';
import { ConfigService } from '../../../@exai/services/config.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SidebarComponent } from '../../../@exai/components/sidebar/sidebar.component';
import { Action, Store } from '@ngrx/store';
import { getCartItems, getUserDetails, setUserDetails } from '../state/shared/shared.actions';
import { getErrorMessage, getLoading, get_cartItems, get_decodeInfo, get_userDetails } from '../state/shared/shared.selectors';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { DecodedIdentityToken, UserDetails } from '../candiate.types';
import { GlobalUserService } from 'src/app/core/global-user.service';


@UntilDestroy()
@Component({
  selector: 'exai-custom-layout',
  templateUrl: './custom-layout.component.html',
  styleUrls: ['./custom-layout.component.scss']
})
export class CustomLayoutComponent implements OnInit {

  sidenavCollapsed$ = this.layoutService.sidenavCollapsed$;
  isFooterVisible$ = this.configService.config$.pipe(map(config => config.footer.visible));
  isDesktop$ = this.layoutService.isDesktop$;
  userdataFromSession: any;
  toolbarShadowEnabled$ = this.router.events.pipe(
    filter(event => event instanceof NavigationEnd),
    startWith(null),
    map(() => checkRouterChildsData(this.router.routerState.root.snapshot, data => data.toolbarShadowEnabled))
  );
  chatPanelExpanded: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  chatPanelExpanded$: Observable<boolean> = this.chatPanelExpanded.asObservable();

  @ViewChild('configpanel', { static: true }) configpanel: SidebarComponent;
  querParams: any;
  loadingObs: Observable<boolean>;
  queryParamSub: Subscription;

  constructor(private layoutService: LayoutService,
    private configService: ConfigService,
    private breakpointObserver: BreakpointObserver,
    private router: Router,
    private store: Store,
    private snackbar: SnackbarService,
    private activatedRoute: ActivatedRoute, private global: GlobalUserService) { }


  ngOnInit() {
    this.queryParamSub = this.activatedRoute.queryParams.subscribe(params => {
      if (params) {
        this.store.dispatch<Action>(setUserDetails({ token: params.token }));
        this.queryParamSub?.unsubscribe();
      }
    }
    )

    this.store.select((get_decodeInfo)).subscribe((data: DecodedIdentityToken) => {
      if (data) {
        this.global.emailIdToStartExam= data.email;
        this.global.family_name= data.family_name;
        this.global.given_name= data.given_name;

        this.store.dispatch<Action>(getUserDetails({ emailId: data.email }));
      }
    })

    this.store.select(get_userDetails).subscribe(data => {
      if (data) {
        this.global.userId = data.personId;//personID from sessionStorage used in manage profile
        for (let key in data.roles) {
          this.global.candidateId = data.roles[key].personTenantRoleId; // personTenanatRoleID from sessionStorage
          this.global.tenantId = data.roles[key].tenantId;
          this.global.roleName=data.roles[key].roleName;
          this.global.roleId = data.roles[key].roleId;
        }
        this.global.userDetails.next(data);
      }
    })

    this.loadingObs = this.store.select(getLoading).pipe(
      debounceTime(50)
    );
    this.store.select(getErrorMessage).subscribe((error: any) => {
      if (error) {
        GreivanceMessage.includes(error.error)? this.snackbar.callSnackbaronWarning(typeof error.error == "object" ? "Something went wrong..." : error.error):AddingcartMessage.includes(error.error)?this.snackbar.callSnackbarsonSuccess(typeof error.error == "object" ? "Something went wrong..." : error.error):this.snackbar.callSnackbaronError(typeof error.error == "object" ? "Something went wrong..." : error.error)
      }
    })

    this.layoutService.configpanelOpen$.pipe(
      untilDestroyed(this)
    ).subscribe(open => open ? this.configpanel.open() : this.configpanel.close());
  }
}
export const GreivanceMessage =[
  "Please note, there will not be any skill details available, as the exam is a No-Show."
]

export const AddingcartMessage=["Due to low registration, the exam may be canceled or rescheduled. Please choose a different date or call for assistance."]