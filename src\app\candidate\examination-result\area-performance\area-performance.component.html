<div class="mt-5 p-color text-sm text-center">
    Content Area Performance
</div>

<div class="border-primary p-6 mt-5 mb-2 baground-result">
    <div class="flex p-2">
        <div class="w-1/2 text-gray text-sm ">Content Domain</div>
        <div class="w-1/2 flex">
            <div class="w-1/2 text-gray text-sm">
                Score
            </div>
            <div class="text-gray text-sm">
                Percentage
            </div>
        </div>
    </div>
      <div *ngFor="let performance of performances" class="flex p-2">
        <div class="w-1/2 text-sm p-color">
            {{ performance.contentDomain.slice(15).replaceAll("_"," ") }}
        </div>
        <div class="w-1/2 flex ">
            <div class="w-1/2 text-sm p-color">
                {{ performance.obtainedScore }}/{{ performance.totalScore }}
            </div>
            <div class="text-sm p-color">
                {{ performance.percentage }}%
            </div>
        </div>
    </div>
</div>