import { Component, Input, OnInit } from '@angular/core';
import moment from 'moment';

@Component({
  selector: 'progress-step-registry',
  templateUrl: './progress-step.component.html',
  styleUrls: ['./progress-step.component.scss']
})
export class RegistryProgressStepComponent implements OnInit {

  isReadMore = false;
  onHover: boolean = false;
  actionOnN;


  showText() {
    if (this.onHover) {
      this.isReadMore = !this.isReadMore
    }
  }

  @Input() comment: string;
  @Input() name: any;
  @Input() actionOn: Date;
  @Input() reviewer: string;
  @Input() isLast: boolean = false;
  constructor() { }

  ngOnInit(): void {
  //   if (this.comment.length > 10) {
  //     this.onHover = true;
  //     this.isReadMore = true
  //   }
  this.actionOnDateFormat()
  }

  actionOnDateFormat(){
    this.actionOnN= moment(this.actionOn).format("Do MMMM, YYYY / h:mm A");
  }

}