import { Component, Input, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatCalendar } from '@angular/material/datepicker';
import { MatAccordion } from '@angular/material/expansion';
import { Moment } from 'moment';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import * as moment from 'moment';
import { interval, Observable, Subject, Subscription } from 'rxjs';
import { HttpService } from 'src/app/core/http-services/http.service';
import { Cart } from '../state/models/cart';
import { ClearTimeslots, getCart, getEligibilityroute, getExamByERIdId, getMonthlySlots, getPersonForm, getRegisteredExam, getTimeSlots, getTimezones, reschedule, } from '../state/scheduled.actions';
import { selectorGetCart, selectorGetEligibilityroute, selectorGetExamId, selectorGetMonthlyslots, selectorGetPersonForm, selectorGetRegisteredexam, selectorGetRescheduledResponse, selectorGetTimeslots, selectorGetTimezones, } from '../state/scheduled.selectors';
import { ScheduledState } from '../state/scheduled.state';
import { ScheduledService } from '../scheduled.service';
import { getCartItems } from '../../state/shared/shared.actions';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpClient } from "@angular/common/http";
import { DatePipe } from "@angular/common";
import { Exam, PersonForm, schedule } from "../state/models/Exam";
import { get_cartItems, get_decodeInfo, getLoading } from "../../state/shared/shared.selectors";
import { Slot } from '../state/models/slot';
import { Timezone } from '../state/models/timezone.model';
import { ExamTypeModel } from 'src/app/core/Dto/exam-type.model';
import { ExamModel } from 'src/app/core/Dto/exam.model';
import { SlotModel } from 'src/app/core/Dto/slot.model';
import { LockChanges } from '@ngrx/store-devtools/src/actions';
import { DecodedIdentityToken } from '../../candiate.types';
import { FormTypes, StateList } from '../../forms-wrapper/forms-wrapper.types';
import { StateAllowTestCenterforOnline } from 'src/app/core/examroom-formbuilder/form-builder.types';
import { FormStatuses } from 'src/app/core/Dto/enum';
import { parseISO } from 'date-fns';
import { CartSummaryPopupComponent } from '../cart-summary-popup/cart-summary-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { PopUpComponent } from '../../application/application-form/pop-up/pop-up.component';
import { LanguageService } from 'src/app/core/language.service';
// import { StateAllowTestCenterforOnline } from 'src/app/core/examroom-formbuilder/form-builder.types';

@Component({
  selector: "exai-register-for-exam",
  templateUrl: "./register-for-exam.component.html",
  styleUrls: ["./register-for-exam.component.scss"],
})
export class RegisterForExamComponent implements OnInit, OnDestroy {
  @ViewChild(MatAccordion) accordion: MatAccordion;
  @ViewChild("calendar") calendar: MatCalendar<Moment>;
  scheduleEvent: Subject<any> = new Subject<any>();
  @Input() max: any;
  carts: any;
  optionExamType: number
  scheduleagainEnable: number
  rescheduleloading: boolean = false;
  scheduleloading: boolean = false;
  event_s: any = null;
  value = 0;
  loading = false;
  rescheduleload = false
  scheduleload = false
  Validators: FormGroup
  ShowTestCenter: boolean = false
  date: boolean = false;
  examType: boolean = false;
  noExamSelected: boolean = true;
  allowRetryschedule=allowSchedule
  calendarDisable: boolean = false;
  elapsedDateTime:string
  isSelect: boolean = false;
  payment: boolean = true;
  rescheduled: boolean = false;
  Eligibility: boolean = true
  isAvailable: boolean = false
  selectedDate: Date = null;
  slotsAvaiable: SlotModel[] = [];
  timeSlot: Array<any>;
  timeZoneId: Timezone;
  selectedExam: ExamModel;
  bookedslot: any;
  myHolidayDates = [];
  userdata
  time
  Date
  slots = [];
  errors: any;
  step: any = 0;
  steps1: number = 0;
  expanded = 0;
  eligibilityRoute: string;
  eligibilityRoute$: Observable<string>;
  cart: Cart;
  examModels: ExamModel[] = [];
  timezones: Timezone[];
  examName = [];
  Selectdate: Date;
  dateControl: FormControl = new FormControl([]);
  getSlotDates = [];
  form: FormGroup;
  paymentModeStatus = new FormControl("MI");
  radioselect = new FormControl("Online");
  getMonthSlots;
  minDate = new Date();
  examTypeDisable: boolean = false;
  buttonDisable: boolean = false;
  registeredExam: Exam[]
  disableDates: any;
  _getSlots: { month: number, year: number } = { month: null, year: null };
  _disableFilter: Array<any> = [];
  cartId: any;
  register: any;
  registeredExams: any;
  public usTimeZones: string[] = ["-05:00", "-06:00", "-07:00", "-08:00", "-09:00", "-10:00"];
  public examTypeModels: ExamTypeModel[] = new ExamTypeModel().getDefaultValues();
  examDateTime: any;
  countMonth: number = 0;
  maxDate = new Date();
  allowReschedule = AllowReschedulestatus;

  constructor(private http: HttpService, private fb: FormBuilder, private router: Router, public global: GlobalUserService,
    private store: Store<ScheduledState>, private _services: ScheduledService, private services: SnackbarService,
    private httpClient: HttpClient,
    public datepipe: DatePipe,
    private snackbar: SnackbarService,
    private dialog: MatDialog,
    private Language: LanguageService
  ) {
    this.store.dispatch<Action>(getRegisteredExam({ candidateId: this.global.candidateId }));
    this.store.dispatch<Action>(getTimezones());
    this.store.dispatch(
      getPersonForm({
        candidateId: this.global.candidateId,
        formTypeId1: FormTypes.Accomodation,
        formTypeId2: FormTypes.Application,
      })
    );
    //  this.global.userDetails.getValue().stateId == StateList.AK?this.minDate.setDate(this.minDate.getDate() + this.global.Alsaka_Leadtime_for_Online): this.minDate.setDate(this.minDate.getDate() + this.global.appointment_Leadtime_for_Online);
    this.minDate.setDate(this.minDate.getDate() + this.global.appointment_Leadtime_for_Online);


  }

  ngOnInit(): void {

    this.form = this.fb.group({
      CardNumber: new FormControl("", [Validators.required]),
    });
    this.Validators = this.fb.group({
      date: new FormControl("", [Validators.required]),
      timezone: new FormControl("", [Validators.required]),

    });
    this.form.controls["CardNumber"].disable();
    this.setSlot();
    this.store.dispatch(getRegisteredExam({ candidateId: this.global.candidateId }))
    this.store.select(selectorGetRegisteredexam).subscribe(data => {
      this.registeredExams = data;
      if (this.examModels.length > 0) {
        this.store.dispatch<Action>(
          getExamByERIdId({ eligibilityRouteId: this.examName[0].id, personTenantRoleId: this.global.candidateId })
        );
      }
    })

    this.global.userDetails.subscribe(data => {
      if (!data) return;
      this.store.dispatch<Action>(getEligibilityroute({ candidateId: this.global.candidateId }));
    })
    this.store.select(selectorGetTimezones).subscribe((timezones: Timezone[]) => {
      if (timezones.length > 0) {
        this.timezones = timezones
      }
    });
    //  <----geting a eligibility route name ---> 
    this.store.select(selectorGetEligibilityroute).subscribe((route) => {
      if (route) {
        this.examName = []
        if (this.examName.length < 1) {
          this.examName.push(route);
          this.store.dispatch<Action>(
            getExamByERIdId({ eligibilityRouteId: route["id"], personTenantRoleId: this.global.candidateId })
          );
        }
        this.store.select(selectorGetExamId).subscribe((exams: ExamModel[]) => {
          if (exams != null && exams.length > 0) {
            this.examModels = exams;
            if (this._services.rescheduleInformation) {
              this.scheduleagainEnable = (this._services.rescheduleInformation.examStatusId) || (this._services.rescheduleInformation.statusId)
              const data = exams.find((ele) => ele.title == this._services.rescheduleInformation.examName);
              data ? ((this.examTypeDisable = true, this.setSelectedExam(data)), this.selectedExam) : null;
              this.payment = false;
              this.rescheduled = true;
              this.noExamSelected = false;
              this.Eligibility = true;
            }
            else if (!this.selectedExam) {
              this.payment = true;
              this.rescheduled = false;
              this.noExamSelected = true;
              this.Eligibility = true;
            }
            else {
              this.setSelectedExam(null);
              this.steps1 = null;
            }
          }
        });
      } else {
        this.Eligibility = false;
        this.noExamSelected = false;
      }
    });
    this.store.select(selectorGetMonthlyslots).subscribe((data) => {

      if (data.length > 0) {
        this.getMonthSlots = data;
        this.getMonthSlots.forEach((element) => {
          if (!element.isAvailable) {

            this.disableDates = new Date(element.slotDate.slice(0, 10));
            this.disableDates = this.datepipe.transform(this.disableDates, 'yyyy-MM-dd');
            this.myHolidayDates.push(this.disableDates);
          }
        });
      }
    })


    this.store.select(selectorGetTimeslots).subscribe((data: Slot[]) => {
      if (data) {
        this.slotsAvaiable = [];
        let availableSlots = data;
        if (availableSlots.length > 0) {
          availableSlots.forEach((ele: Slot) => {
            let slotstring = `${ele.strSlotDate} ${ele.strSlotTime}`;
            let slotDate = new Date(Date.parse(slotstring));
            let slotmodel = new SlotModel({
              slotId: ele.slotId,
              availableSlots: ele.availableSlots,
              bookedSlots: ele.bookedSlots,
              slotDate: ele.slotDate,
              strSlotDate: ele.strSlotDate,
              strSlotTime: ele.strSlotTime,
              totalSlots: ele.totalSlots,
              slotDateTime: slotDate,
              slotDateUtc: ele.slotDateUtc
            })
            this.slotsAvaiable.push(slotmodel);
          });
          this.slotsAvaiable = this.slotsAvaiable.sort((a, b) => new Date(a.slotDateTime).getTime() - new Date(b.slotDateTime).getTime());
          this.slotsAvaiable.forEach((ele: SlotModel) => {
            const { slotDate } = ele;
            if (new Date(slotDate).getUTCHours() >= 0 && new Date(slotDate).getUTCHours() < 4) {
              this.timeSlot[0].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 4 && new Date(slotDate).getUTCHours() < 8) {
              this.timeSlot[1].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 8 && new Date(slotDate).getUTCHours() < 12) {
              this.timeSlot[2].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 12 && new Date(slotDate).getUTCHours() < 16) {
              this.timeSlot[3].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 16 && new Date(slotDate).getUTCHours() < 20) {
              this.timeSlot[4].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 20 && (new Date(slotDate).getUTCHours() <= 23 && new Date(slotDate).getUTCMinutes() <= 59)) {
              this.timeSlot[5].data.push(ele);
            }
          });
          this.isSelect = true;
          const time = this.timeSlot.filter((ele: any) => ele.data.length > 0);
          time.length > 0 ? this.selectrangeactive(time[0]) : null;
        } else if (this.slotsAvaiable.length == 0) {
          this.errors = "No slots available for the selected date";
          this.snackbar.callSnackbaronWarning(this.errors);
          this.isSelect = false;
        }
      }
    });
    this.store.select(selectorGetCart).subscribe((response) => {
      if (response) {
        setTimeout(() => {
          this.store.dispatch<Action>(getCartItems({ personTenantRoleId: this.global.candidateId }));
        }, 1000);

      }
      else {
        this.global.candidateId ? this.store.dispatch<Action>(getCartItems({ personTenantRoleId: this.global.candidateId })) : null;
      }
      // })
    });

    this.store.select(get_decodeInfo).subscribe((data: DecodedIdentityToken) => {
      if (data)
        this.userdata = `${data.given_name} ${data.family_name}`

    });

    this.store.dispatch(getCartItems({ personTenantRoleId: this.global.candidateId }))
    this.store.select(get_cartItems).subscribe(data => {
      this.cartId = data
    })

    this.store
      .select(selectorGetPersonForm)
      .subscribe((personForms: PersonForm[]) => {
        if (personForms.length > 0) {
          let PersonForm = personForms.filter((x) => x.elapsedDateTime != null && x.formTypeId == FormTypes.Application && x.statusId == FormStatuses.Approved)
          if(PersonForm.length > 0 && personForms[0].elapsedDateTime){
            this.elapsedDateTime = PersonForm[0].elapsedDateTime
            this.maxDate = parseISO(PersonForm[0].elapsedDateTime)
            this.maxDate.setDate(this.maxDate.getDate() - 1)
          }else{
            this.maxDate =null
          }
        


        }
      })



    this.global.No_slot_avaiable.subscribe((data) => {
      if (
        data &&
        data != null &&
        data != "" &&
        data != undefined &&
        this.selectedExam.title == "Nurse Aide Written Exam"
      ) {
        this.dialog
          .open(PopUpComponent, {
            data: {
              title: this.selectedExam.title,
              message: this.Language.english.No_slot_avaiable,
              cancelButton: this.Language.english.no,
              OkButton: this.Language.english.yes,
            },
            disableClose: true,
          })
          .afterClosed()
          .subscribe((confirmed: any) => {
            if (confirmed == true || confirmed.confirmed == true) {
              this.radioselect.setValue("Online");
              this.options(1);
              this.dialog.closeAll();
            } else {
              this.dialog.closeAll();
            }
          });
      }
    });

    let n = Intl.DateTimeFormat().resolvedOptions();
    this.time = moment(new Date())
      .tz(n.timeZone)
      .format("h:mm a z")
    this.Date = moment(new Date()).format("DD/MM/YYYY")


    this.store.select(selectorGetRescheduledResponse).subscribe(data => {
      if (data !=null) {
        this.event_s.unsubscribe()
        var datePipe = new DatePipe("en-US");
        this.time = datePipe.transform(this._services.rescheduleInformation.examDateTime, 'shortTime', '+0000')
        let body = {
          body: `Online ${this.selectedExam.title} scheduled for ${moment(this._services.rescheduleInformation.examDateTime).format('MM/DD/YYYY')} at ${this.time} ${this._services.rescheduleInformation.timeZoneAbbreviation} was rescheduled by ${this.global.roleName}`,
          candidateId: this.global.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: `Online ${this.selectedExam.title} Rescheduled`,
          userId: this.global.candidateId,
          userName: this.userdata,
        }
        this.http.getAddnotes(body).subscribe(data => {
        })
      }else{
         this.event_s.unsubscribe()
      }
    })

  }

  myHolidayFilter = (d: Date): boolean => {
    if (d) {
      let date = moment(d).format("YYYY-MM-DD");
      return !this.myHolidayDates.find((item) => item == date);
    }
  };
  //  <----Showing a content with required select of online or test --->
  public options(id: number): void {
    this.global.No_slot_avaiable.next(null); // Reset when component is destroyed
    if (id == 1) {
      this.isSelect = false;
      this.calendarDisable = false;
      this.Validators.reset()
      this.optionExamType = id
      this.selectedDate = null
    } else {
      this.optionExamType = id
      this.isSelect = false;
      this.calendarDisable = false;
      this.selectedDate = null
    }
  }

  //   <----Selecting a timeZone --->

  public examEvent(tz: Timezone): void {
    this.setSlot();
    this.timeZoneId = tz;
    this.date = false;
    if (this.selectedDate) {
      this.store.dispatch<Action>(
        getTimeSlots({ timezone: this.timeZoneId.id, startDate: this.selectedDate.toDateString(), examId: this.selectedExam.id, offset: this.timeZoneId.offset, candidateId:this.global.userDetails.getValue().personId })
      );
    }
    let nowDate = moment();
    let currentMonth = nowDate.month() != 0 ? nowDate.month() : 12;
    let CurrentYear = nowDate.month() != 0 ? nowDate.year() : nowDate.year() - 1;
    // this.store.dispatch<Action>(getMonthlySlots({ month: currentMonth + 1, year: CurrentYear, timezone: this.timeZoneId.id }));
    this.calendarDisable = true;
  }
  //  <----showing a color selected in calendar --->
  changeCalendar(event: any): void {
    const body = Array.from(
      document.querySelectorAll<HTMLDivElement>(".mat-calendar .mat-calendar-content"));
    const html = `
  <div class="inline-flex" id="calendarFooter"><br>
  <p class="inline-flex" style="font-size: 8px;">
  <span class="inline-flex pl-1"><img src="assets/img/black_icon.svg">      </span> &nbsp; <span>Available</span>     &nbsp;&nbsp;&nbsp;
  <span class="inline-flex pl-3"><img src="assets/img/gray_icon.svg">       </span> &nbsp; <span>Not available</span> &nbsp;&nbsp;&nbsp;
  <span class="inline-flex pl-3"><img src="assets/img/lightGreen_icon.svg"> </span> &nbsp; <span> Today</span>        &nbsp;&nbsp;&nbsp; 
  <span class="inline-flex pl-3"><img src="assets/img/greenTheme_icon.svg"> </span> &nbsp; <span> Selected</span>     &nbsp;&nbsp;&nbsp;
  </p>
  </div>`;
    body.forEach((ele: HTMLDivElement) => {
      const element = document.createElement("div");
      element.setAttribute("class", "availability-status");
      element.innerHTML = html;
      ele.appendChild(element);
    });
  }
  //  <!-- <----Selecting a Date and spliting a data according to select range --->
  public selectDate(event): void {
    this.setSlot();
    this.date = false;
    this.selectedDate = event.value;
    this.store.dispatch<Action>(
      getTimeSlots({ timezone: this.timeZoneId.id, startDate: this.selectedDate.toDateString(), examId: this.selectedExam.id, offset: this.timeZoneId.offset, candidateId:this.global.userDetails.getValue().personId })
    );
  }
  selectrangeactive(event) {
    this.slots = event.data;
    this.step = event.id;
  }
  bookslot(event) {
    this.bookedslot = event.slotId;
    this.examDateTime = event.slotDateUtc
    this.buttonDisable = true;
  }
  public setSelectedExam(selectedExam: ExamModel) {
    sessionStorage.setItem('title',selectedExam.title)
    // Reset the No_slot_avaiable BehaviorSubject when changing exams
    this.global.No_slot_avaiable.next(null);

    if (selectedExam) {
      this.ShowTestCenter = selectedExam.title === "Nurse Aide Skills Exam" && StateAllowTestCenterforOnline.includes(this.global.userDetails.getValue().stateId) ? false : true
      this.scheduleEvent.next(true)
      this.examType = true;
      this.noExamSelected = false;
      this.isSelect = false;
      this.Validators.reset();
      this.selectedDate = null;
      this.selectedExam = selectedExam;
      let isTypeAlreadySet = false;

      this.examTypeModels.forEach(x => {
        let hasMode = selectedExam.supportedExamMode.find(z => z.examModeTypeId === x.id);
        if (hasMode) {
          x.checked = false;
          x.disabled = false;

          if (!isTypeAlreadySet) {
            x.checked = true;
            isTypeAlreadySet = true;
          }
        }
        else {
          x.checked = false;
          x.disabled = true;
        }
      });

      let selectedExamType = this.examTypeModels.find(x => x.checked === true);
      if (this._services.rescheduleInformation != null) {
        this._services.rescheduleInformation.mode = this._services.rescheduleInformation.mode =='Online' && this._services.rescheduleInformation.testCenterDetails.testCenterName !=null && this._services.rescheduleInformation.testCenterName !='' && this._services.rescheduleInformation.testCenterName !=undefined?"Test Center":this._services.rescheduleInformation.mode
        let hasMode = this.examTypeModels.find(
          (x) => this._services.rescheduleInformation.mode === x.name
        );
        this.global.userDetails.getValue().stateId == StateList.WA
          ? this.radioselect.setValue(selectedExamType.name)
          : this.radioselect.setValue(hasMode.name);
        this.global.userDetails.getValue().stateId == StateList.WA
          ? this.options(selectedExamType.id)
          : this.options(hasMode.id);
        this.steps1 = selectedExam.id;
      } else if (selectedExamType != null) {
        this.radioselect.setValue(selectedExamType.name);
        this.options(selectedExamType.id);
        this.steps1 = selectedExam.id;
      }
    } else {
      this.examType = false;
      this.noExamSelected = true;
      this.isSelect = false;
      this.Validators.reset();
      this.selectedDate = null;
    }
  }

  setStep(index: number) {
    this.expanded = index;
  }
  // <----Spliting a data according to select range  --->
  setSlot() {
    this.timeSlot = [
      { title: "12 AM - 04 AM", id: 1, key: "phase1", data: [], timeperiod: "MIDNIGHT", color: "green" },
      { title: "04 AM - 08 AM", id: 2, key: "phase2", data: [], timeperiod: "EARLY MORNING", color: "blue" },
      { title: "08 AM - 12 PM", id: 3, key: "phase3", data: [], timeperiod: "MORNING", color: "blue" },
      { title: "12 PM - 04 PM", id: 4, key: "phase4", data: [], timeperiod: "AFTERNOON", color: "blue" },
      { title: "04 PM - 08 PM", id: 5, key: "phase5", data: [], timeperiod: "EVENING", color: "green" },
      { title: "08 PM - 11:59 PM", id: 6, key: "phase6", data: [], timeperiod: "NIGHT", color: "green" },
    ];
  }
  // <-----add cart and pay now api--->
  getCart(i) {
    this.store.dispatch<Action>(
      getCart({
        details: {
          personTenantRoleId: this.global.candidateId,
          amount: this.selectedExam.price,
          cartItemTypeId: 1,
          currencyId: 1,
          examDetail: {
            candidateId: this.global.candidateId,
            examId: this.selectedExam.id,
            slotId: this.bookedslot,
            timeZone: this.timeZoneId.id,
            offSet: this.timeZoneId.offset,
            examModeId: 1,
            personTenantRoleId: this.global.candidateId,
            examDateTime: this.examDateTime
          },

        },
        isPayment: false,
      })
    );
    setTimeout(() => {
      this.store.dispatch(getExamByERIdId({ eligibilityRouteId: this.examName[0].id, personTenantRoleId: this.global.candidateId })),
      this.cartItems()
    }, 3000);
  }

  cartItems() {
    const dialogRef = this.dialog.open(CartSummaryPopupComponent, {
      width: "360px",
      height: "550px",
    });
  }

  disable(data): any {
    if (this.examModels) {
      return this.examModels.findIndex((x) => (x.isDisabled && x.id == data.id)) > -1
    }
    return false;
  }

  reschedule() {

    this.store.dispatch<Action>(
      reschedule({
        rescheduleBody: {
          candidateId: this.global.candidateId,
          examId: this.selectedExam.id,
          slotId: this.bookedslot,
          timeZone: this.timeZoneId.id,
          offSet: this.timeZoneId.offset,
          examModeId: 1,
          scheduleId: this._services.rescheduleInformation.id,
          personTenantRoleId: this.global.candidateId,
          ExamDateTime: this.examDateTime,
          testCenterName: '',
          testCenterAddress: '',
          testCenterCity: '',
          testCenterState: '',
          testCenterPostalCode: '',
          testCenterId: "",
          isGrievanceFilled: this._services.rescheduleInformation.formTypeId == FormTypes.Grievance ? true : false
        },

      }));

      this.event_s = this.store.select(getLoading).subscribe(data => {
        this.rescheduleloading = data
      })
 
  }

  untilPaymentProcess(item) {
    if (item == 0) {
      if (this.rescheduleloading) {
        return false;
      }
      else {
        return true;
      }

    }
    else if (item == 1) {
      if (this.rescheduleloading) {
        return this.rescheduleloading;
      }
      else {
        return false;
      }
      //   // return true
    }
  }


  getPayment() {
    if (this.cartId.length > 0) {
      this.router.navigateByUrl("/exam-scheduled/payment/1");
    } else {
      this.services.callSnackbaronError("No item in Cart to make a Payment")
    }
  }
  scheduleAagin() {

    let body:schedule={
        candidateId: this.global.candidateId,
        examId:this.selectedExam.id ,
        slotId:this.bookedslot ,
        timeZone: this.timeZoneId.id,
        offSet: this.timeZoneId.offset,
        examModeId: 1,
        personTenantRoleId: this.global.candidateId,
        testCenterId: "",
        accommodationType: "",
        accommodationItems: [
        ],
        clientExamId: 0,
        testCenterName: "",
        testCenterAddress: "",
        testCenterCity: "",
        testCenterState: "",
        testCenterPostalCode: "",
        examDateTime:this.examDateTime,
        scheduleId: this._services.rescheduleInformation.id,
     }

     this.event_s = this.store.select(getLoading).subscribe(data => {
      this.rescheduleloading = data
    })
    this.http.Schedule(body).subscribe((data:any)=>{
      if(data!==null && data.isScheduled){
        this.event_s.unsubscribe()
               var datePipe = new DatePipe("en-US");
              this.time =datePipe.transform(this._services.rescheduleInformation.examDateTime, 'shortTime','+0000')
               let body={
                 body: `Online ${this.selectedExam.title} scheduled for ${moment(this._services.rescheduleInformation.examDateTime).format('MM/DD/YYYY')} at ${this.time} ${this._services.rescheduleInformation.timeZoneAbbreviation} was rescheduled by ${this.global.roleName}`,
                 candidateId:this.global.candidateId,
                 files: [],
                 id: 0,
                 noteTypeid: 5,
                 title: `Online ${this.selectedExam.title} Rescheduled`,
                 userId: this.global.candidateId,
                 userName: this.userdata,
               }
               this.http.getAddnotes(body).subscribe(data=>{
                if(data){
                  this.services.callSnackbaronSuccess(
                    "Exam Rescheduled Successfully"
                  );
                }
               })
        this.router.navigateByUrl('/exam-scheduled')
      }
    }, (err) => {
      if (err) {
        this.event_s?.unsubscribe()
      }
    })


  }

  ngOnDestroy(): void {
    this.store.dispatch(ClearTimeslots());
    this._services.rescheduleInformation = null;
    this.isSelect = false;
    this.global.No_slot_avaiable.next(null);
    this.event_s?.unsubscribe()
  }
}
export const allowSchedule =[70,68,9,77,78,80,81,85,86,87,93,94];

export const AllowReschedulestatus=[1,7];


