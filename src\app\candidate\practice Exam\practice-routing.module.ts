import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PracticeExamComponent } from './practice.component';
import { RegisterExamComponent } from './register_practice_exam/register-practice.component';
// import { AuthGuard } from 'src/app/core/auth.guard';

// import { ScheludedExamComponent } from './scheluded-exam/scheluded-exam.component';

// const routes: Routes = [{
//   path: '', component: ScheduledComponent, children: [
//     { path: 'register', component: RegisterForExamComponent },
//     // { path: 'exam', component: ScheludedExamComponent },
//   ]
// }];
const routes: Routes =
[
    {
        path: '', component: PracticeExamComponent,
        // canActivate: [AuthGuard],
        data: {
          title: 'Practice Exam', 
          breadcrumb: [
            {
              label: 'Home',
              url: '/dashboard'
            },
            {
              label: 'Practice Exam',
              url: ''
            },
        
          ]
        },
      },
      {
        path: 'register_practice', component: RegisterExamComponent,
        // canActivate: [AuthGuard],
        data: {
          title: 'Practice Exam', 
          breadcrumb: [
            {
              label: 'Home',
              url: '/dashboard'
            },
            {
              label: ' Practice Exam',
              url: '/practice_exam'
            },
            {
              label: 'Register For Practice Exam',
              url: ''
            },
      
          ]
        },
      },

];




@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PracticeExamRoutingModule { }
