/* Global spacing if not already defined */
.px-gutter {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.custom-tab-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

.tabs {
  display: flex;
  width: 100%;
  height: 48px;
  overflow: hidden;
  border-color: #7d7d7d66;
  border-width: 0.3px;
  opacity: 1;
}

.tab {
  flex: 1;
  height: 100%;
  text-align: center;
  font-weight: 500;
  line-height: 48px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-right-color: #7d7d7d66;
  border-width: 0.3px;
  opacity: 1;
}

.active-tab {
  color: #0076c1;
  font-weight: 600;
  border-bottom: 3px solid #0076c1;
  background-color: white;
  width: 73px;
}

.inactive-tab {
  background-color: #f9f9f9; /* red */
}

// .tab-btn.active {
//   width:73px;
//   color: #0076C1;
//   border-bottom: 3px  #0076C1;
// }

// .tab-content {
//     border-color: #6D6D6D;
//   border-width: 0.3px;
//   opacity: 1;
// }

/* Sub-tab container */
.sub-tabs {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  background-color: white;
  border-bottom: 0.3px solid #7d7d7d66;
  opacity: 1;
}
.tab-section {
  background-color: #f9f9f9;
}
/* Sub-tab buttons */
.sub-tab {
  font-size: 14px;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  background: transparent;
}

/* Active sub-tab */
.active-sub-tab {
  color: #0076c1;
  font-weight: 500;
  border-bottom: 3px solid #0076c1;
}

// .skill-grid {
//   background-color: #F9F9F9;
//   display: grid;
//   grid-template-columns: repeat(1, 1fr);
//   gap: 1rem;
//   padding: 0 1rem;
// }

.skill-flex {
  background-color: #f9f9f9;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0 1rem;
}

.grid-layout {
  display: grid;
  gap: 1rem;

  // Default: 4 columns
  grid-template-columns: repeat(4, 1fr);

  // Medium screens: 2 columns
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  // Small screens: 1 column
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.grid_layout {
  flex: 1;
  border-radius: 4px;
  color: white;
  opacity: 1;
  border-color: #7d7d7d66;
  border-width: 0.5px;
}

// /* Media queries for skill-grid */
@media (min-width: 640px) and (max-width: 899px) {
  .skill-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 900px) and (max-width: 1023px) {
  .skill-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .skill-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (min-width: 1440px) {
  .skill-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

// .grid_layout{
//   width: 100%;
//   border-radius: 4px;
//   color: white;
//   opacity: 1;
//   border-color: #979797;
//   border-width: 0.5px;
//   box-sizing: border-box
// }

// .card-content {
//   width:336px;
//   height:270px;
//   top:12.26px;
//   left:11.26px;
//   background-color: #FAFAFA;
//   color: var(--text-color4);
//   border-radius: 4px;
//   border-width: 0.3px;
//   opacity: 1;
//   // radius:4px;
//   border-color: #e5e5e5;
// }
.card-content {
  background-color: #fafafa;
  color: var(--text-color4);
  border-radius: 4px;
  border-width: 0.3px;
  opacity: 1;
  border-color: #e5e5e5;
  font-family: "Roboto", sans-serif;
}

.description-text {
  overflow-y: auto;
  // font-weight: 400;
  // font-size: 14px;
  // line-height: 1;
  // letter-spacing: 0px;
  color: #7d7d7d;
  margin: 0;

  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
  font-family: "Roboto", sans-serif;
  padding-left: 16px;
  margin-top: 12px !important;
  max-height: none;
  overflow: hidden;
  transition: max-height 0.3s ease;
  margin-bottom: 10px;
}

.viewmore {
  color: #0076c1;
}

.description-text.collapsed {
  max-height: 80px;
}

.description-text::-webkit-scrollbar {
  width: 4px;
}

.description-text::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 8px;
}

.title {
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0px;
  color: #333;

  width: 286px;
  height: 50px;

  display: -webkit-box;
  // -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 16px;
  padding-top: 8px;
}

.menu-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 20px;
  line-height: 24px;
  cursor: pointer;
  color: #555;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  margin-top: 10px;
}

.menu-toggle:hover {
  background-color: #d1e4f0;
  padding: 1px 2px 1px 1px;
}

.meta {
  padding-bottom: 23.86px;
  padding-right: 44px;
}

.meta-label {
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0;
  color: #c4c4c4;
  padding-left: 16px;
  width: 42px;
  height: 16px;
}

.meta-value {
  color: #0b132a;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0px;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  width: 42px;
  height: 16px;
  padding-left: 16px;
  padding-top: 7px;
}

.meta-value1 {
  color: #1b75bb;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0px;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  width: 42px;
  height: 16px;
  padding-left: 16px;
  padding-top: 7px;
}
// .card_border{
//     border : 1px solid #93979e;
// }

.try-now-btn {
  color: var(--text-color2);
  background-color: #e5f1f9;
  border-radius: 4px;
  padding: 8px 14px 8px 14px;
}

.try-now-btn:hover {
  opacity: 0.9;
}

.try-now {
  color: #1b75bb;
}

//For diff. view port cards adjusting

::ng-deep .no-padding-dialog .mat-dialog-container {
  padding: 0 !important;
}

.menu_pop {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.menu_pop_item {
  color: #4b5563;
  cursor: pointer;
  border-bottom: 1px solid #c1c3c7;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
    color: var(--text-color2);
  }
}

.card-borders {
  border: var(--border);
  border-radius: var(--border-radius);
}

.icon {
  color: #a1a8af;
  font-size: 18px;
}
