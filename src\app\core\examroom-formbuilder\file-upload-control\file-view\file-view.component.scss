.text-color {
    color: var(--text-color2);
}

.button {
    border: 0.5px solid var(--text-color2);
    width: 105px;
    height: 35px;
    box-shadow: none !important;
}

.icon {
    box-shadow: none !important;
}
.colorIcon {
    color: lightgray;
}

.never-disabled {
    pointer-events: all!important;
    z-index: 100000000000!important;
    cursor: pointer;
}

.doc-container {
    margin: 16px 0px;
}

.view-document {
    height: 28rem;
    width: initial !important;
}

.view-image {
    display: flex !important;
    width: initial !important;
    align-self: center !important; 
    justify-content: center !important; 
}

.cont {
    text-align: center !important;
}

.img-container {
    justify-content: center;
    display: flex;
}