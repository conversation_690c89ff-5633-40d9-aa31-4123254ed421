.grid-container {
    display: flex;
    column-gap: 24px;
  }
  
  .grid-column {
    flex: 1;
  }
  
  .grid-item {
    height: 32px;
    text-align: center;
    width: 157px;
  }
  
  .border-primary{
    border:1px solid rgb(var(--color-primary));
    border-radius: 5px;
  }

.bg-zinc100{
    background:#F9F9F9 ;
}

.baground-result{
    background:  #F0F0F0;
}

.right-color{
  color: #00AB72;
}

.wrong-color{
  color: #D02F44;
}

.p-color{
  color: #11263C;
}