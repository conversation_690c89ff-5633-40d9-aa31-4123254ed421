import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import { saveLog } from './logger.actions';

@Injectable()
export class LoggerInterceptor implements HttpInterceptor {

  constructor(private store: Store) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      tap((res : any)=> { 
        if (!res.url) return;
        this.store.dispatch(saveLog({
          log : {
            url : res?.url ?? "", 
            status: res?.status ?? 400
          }
        }))
      }) 
    );
  }
}
