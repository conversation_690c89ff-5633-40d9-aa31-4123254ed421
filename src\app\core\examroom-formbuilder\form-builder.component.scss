.my-12 {
    // margin-top: 0.75rem;
    // margin-bottom: 0.1rem;
    margin-top: 0rem;
    margin-bottom: 0rem;
}

.fb-container {
    // padding-left: 0.25rem!important;
    // padding-right: 0.25rem!important;
    display: flow-root!important;
}

.fb-ht-fixed {
    // height: 80vh;
    overflow: auto;
}

.fb-ht-fixed-sm {
    height: 100%;
}

#wMsg {
    display: none;
}

.add-icon {
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 1.7rem!important;
    cursor: pointer;
}

.editor {
    width: 92%!important;
    margin: 0.5rem;
}

.basic-card {
    margin: 1rem 1rem;
    margin-bottom: 4rem;
    max-width: 100%;
    min-height: fit-content;
    min-width: fit-content;
    border: 1px solid rgba(49, 48, 48, 0.829);
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 0.92rem;
}

.basic-card-children {
    margin: 0.5rem 0.5rem;
    width: stretch;
    height: stretch;
    min-height: fit-content;
    min-width: fit-content;
    border: 1px solid rgba(49, 48, 48, 0.829);
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 0.9rem;
}

.genesis-form-feild {
    width: 27%;
    margin: 0.7rem;
}

.form-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.active-route {
    background: rgba(84, 16, 230, 0.565)
}

.wrapper {
    margin: 25px auto;
    max-width: 600px;
    text-align: center;
    padding: 0 20px;
}

.container {
    width: 45%;
    margin: 0 25px 25px 0;
    display: inline-block;
    vertical-align: top;
}

.movie-list {
    width: 80%;
    border: solid 1px #ccc;
    min-height: 60px;
    display: inline-block;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 25px;
}

.movie-block {
    padding: 20px 10px;
    border-bottom: solid 1px #ccc;
    color: rgba(0, 0, 0, 0.87);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    cursor: move;
    background: white;
    font-size: 14px;
}

.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.movie-block:last-child {
    border: none;
}

.movie-list.cdk-drop-list-dragging .movie-block:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
.forms{
    max-width: 790px;
}