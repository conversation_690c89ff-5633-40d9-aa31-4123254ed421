export interface eligibilityRoute {
  id: number;
  eligibilityName?: string;
  tenantCode?: string;
  eligibilityRouteDetails?: eligibilityRouteDetails;
}

export interface eligibilityRouteDetails {
  eligibilityRouteDetail: string;
  mandatoryInfo: string;
  optionalInfo: string,
}

export enum FormTypes {
  "Accomodation" = 1,
  "Grievance" = 2,
  "Application" = 3,
  "Demographic" = 4,
  "Certificate_Renewal" = 5,
  "Certificate_Reciprocity" = 6,
  "Certificate_Duplicate" = 7,
  "AbuseAlligation" = 9,
  "ExcusedAbsense" =13,
  "RenewalSCMA" = 14,
  "ReciporatingSCMA" = 15,
  "V2-reciporating-SCMAE"=9,
  "V2-renewal-SCMAE"=8,
  "GrievanceEvaluator"=10,
  "Appeal"=16,
  "examtype_appeal"=11,
  "sc_reinstate"=17,
  "SC_Reinstate_Renewal"=12
}

export interface Form {
  formID?: Array<number>,
  formTypeID: Array<number>,
  stateID?: number,
  eligibilityID: number,
  formJSON?: any;
  isSubmitAllowed?: Array<boolean>;
  formCode?:string,
  fees?:number
  version?:number

}

export interface PersonFormLog {
  personFormId: number;
  comment: string;
  name: string;
  actionOn: Date;
  reviewer: string;
  formTypeId: number;
}

export const AllowPayment=[5,8,12]
export const AllowPaymentReciporating=[6,9,10,11]

export interface PersonForm {
  personFormId: number;
  name: string;
  state: string;
  eligiblityRoute: string;
  eligibilityRouteId?: number;
  formCode?: string;
  submittedDate: Date;
  lastUpdatedDate: Date;
  iconUrl: string;
  status: string;
  waitingTime?: any;
  version?:number
}

export interface response {
  response: any;
  formTypeId: Array<FormTypes>;
  personFormId: Array<number>;
  disabled?: Array<boolean>,
  code: string;
  version?:number
}



export interface renewelFee {
  cartItems:cartItems[],
  currencyId: number,
  personTenantRoleId:number,
}

export interface cartItems{
  cartItemTypeId:number
  amount:number,
  examCode:String,
  personEventCartId:number,
  quantity:number
}

export enum FormFee{
  "Certificate_Renewal"=35,
  "Certificate_Reciprocity"=26
}

export enum FormCartItemId{
  "Renewal"=3,
  "Reciprocity"=4,
  "V2-reciporating-scmae"=5,
  "V2-renewal-scmae"=6,
  "V2_greivance_code"=7,
  "V2_greivance_code_appeal"=8,
  "SC_reinstate_cartypeId" = 9
}

export enum RenewelStateList{
  "SC"=7,
  "AL"=6
}




export enum AutoRenewelStateList{
  "SC"=7
}
export enum ReciprocityStateList{
  "AL"=6,
  
}
export enum ReciporatingSCMA{
  "SC"=7,

}



export const RenewelStateLists =[6,7]

export enum StateList{
  "SC"=7,
  "CO" =9,
  "PA" =4,
  "CO_ER8"=56,
  "CO_ER7"=54,
  "VA" =14,
  'AK'=19,
  "WA"=15,
  "DC"=5,
  "MS"=6
}

export const States=[
  4,5,6,7,8,9,10,11,12,13,14,15,16
]


export  interface generateRegistry{
  "certNumber":string |any,
  "certType":number,
  "actionBy":number,
  "stateId":number
  "personId":number
}
export const stateAllowstosavedata=[7,5,6]

export enum registryCerttype{
  "RenewalCertTpe"=2
}
