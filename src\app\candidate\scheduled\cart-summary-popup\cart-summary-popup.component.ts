import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http-services/http.service';
import { FormsWrapperService } from '../../forms-wrapper/forms-wrapper.service';
import { FormTypes } from '../../forms-wrapper/forms-wrapper.types';
import { deleteCartItem, getCartItems, getCartPracticeItems } from '../../state/shared/shared.actions';
import { get_PracticecartItems, get_cartItems } from '../../state/shared/shared.selectors';
import {  getPracticeRegisteredExam, getRegisteredExam, removeCartItem, removePracticeCartItem } from '../state/scheduled.actions';
import { selectorGetCartDeleteStatus, selectorGetCartPracticeDeleteStatus, selectorGetRegisteredexam } from '../state/scheduled.selectors';

@Component({
  selector: 'exai-cart-summary-popup',
  templateUrl: './cart-summary-popup.component.html',
  styleUrls: ['./cart-summary-popup.component.scss']
})
export class CartSummaryPopupComponent implements OnInit {
  listExam: any
  Validator: FormGroup
  total: any
  subtotal: any
  ExamTotal:number
  cart: any
  cert;
  CartTypeItems:string
  certDetailsReciporating
  PracticeListExam:any
  certDetailsRenewal:any;
  registeredExams:any;
  NotAllowScheduleforCheating:boolean = false
  
  constructor(private dialogRef: MatDialogRef<CartSummaryPopupComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: any, private formwrapper: FormsWrapperService, private router: Router, private store: Store, private global: GlobalUserService, private services: HttpService) {
    this.CartTypeItems = data?.message
    this.store.dispatch<Action>(
      getRegisteredExam({ candidateId: this.global.candidateId })
    );
    // this.listExam=data.response
    // this.subtotal=data.subtotal
    // this.total=data.total
    this.store.select(selectorGetRegisteredexam).subscribe( (exams) => {
      this.registeredExams = exams;
     })

     this.getIncidentstatus()

  }

  ngOnInit(): void {
    this.Validator = new FormGroup({
      code: new FormControl("", [Validators.required]),
    });

    this.services.getCertificates(this.global.candidateId).subscribe(data => {
      this.cert = data
      let a = this.cert.filter(x => this.listExam.find(y => y.examTypeId == x.formTypeId && x.statusId == 1))
      this.certDetailsReciporating = a[0]
    })

    this.store.select(get_cartItems).subscribe(cartItems => {
      if (cartItems != null) {
        //this.listExam = cartItems;
        let n = Intl.DateTimeFormat().resolvedOptions()
        this.listExam = JSON.parse(JSON.stringify(cartItems));
        this.listExam = cartItems
        this.listExam = this.listExam.map((ele) => (Object.assign({}, ele, { eventDateN: moment(ele.eventDate).format('MM/DD/YYYY') }, { eventDateTimeOnly: moment(ele.eventDate).tz(n.timeZone).format('h:mm a z') })))
        this.subtotal = this.listExam.reduce((acc, val) => acc += val.amount, 0)
        this.ExamTotal = this.listExam.reduce((acc, val) => acc += val.amount, 0)
        this.total = this.listExam.reduce((acc, val) => acc += val.amount, 0);
      } else {
      }

    })

  }
  PayNow() {
    if (this.formwrapper.userResponse == null && this.listExam.find(item => item.cartItemTypeId == CartItemId.Renewal || item.cartItemTypeId == CartItemId.Reciprocity)) {
      let registryDetails = [];
      registryDetails = this.listExam.filter(item => item.cartItemTypeId == CartItemId.Renewal || item.cartItemTypeId == CartItemId.Reciprocity)
      if (registryDetails.length == 1) {
        this.services.removecart(this.global.candidateId, Number(registryDetails[0].personEventCartId)).subscribe(data => {
          if (data) {
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            )
            if (CartItemId.Reciprocity && this.certDetailsReciporating.formTypeId === FormTypes.Certificate_Reciprocity) {
              this.dialogRef.close()
              setTimeout(() => {
                this.router.navigate(['registry', 'reciprocity-form', FormTypes.Certificate_Reciprocity
                  , this.global.candidateId, this.certDetailsReciporating.eligibilityRouteId, this.global.stateId, 0, this.certDetailsReciporating.personFormId, 0, this.certDetailsReciporating.statusId]);
              }, 3000)

            } else {
              this.dialogRef.close()
              setTimeout(() => {
                this.router.navigate(['registry', 'renewal-form', FormTypes.Certificate_Renewal, this.global.candidateId, this.certDetailsReciporating.eligibilityRouteId, this.global.stateId, 0, this.certDetailsReciporating.personFormId,  this.certDetailsReciporating.statusId,this.certDetailsReciporating.certificateId]);
              }, 3000)

            }
          }
        })
        // this.store.dispatch<Action>(
        //   removeCartItem({ tetantId: this.global.candidateId, cartItemsId: Number(registryDetails[0].personEventCartId) })
        // );
      }
    }

    else {
      this.dialogRef.close()
      this.router.navigateByUrl("/exam-scheduled/payment/1")
    }

  }

  practicePay(){
    this.dialogRef.close()
    this.router.navigateByUrl("/exam-scheduled/payment/2")
  }
  deleteItem(id, i: number): void {
    if (id) {
      !examName.includes(id.examName)? this.store.dispatch<Action>(
        removeCartItem({ tetantId: this.global.candidateId, cartItemsId: id.personEventCartId })
      ) : 
      this.store.dispatch<Action>(
        removePracticeCartItem({ personTenantRoleId: this.global.candidateId, cartItemsId: id.personEventCartId })
      );

      !examName.includes(id.examName)? 
        this.store.select(selectorGetCartDeleteStatus).subscribe((data) => {

        if (data) {

          this.store.dispatch<Action>(deleteCartItem({ index: i }));

          setTimeout(() => {

            this.store.dispatch<Action>(

              getRegisteredExam({ candidateId: this.global.candidateId })

            );
          }, 2000);

          setTimeout(() => {

            if (this.listExam.length == 0) {
              debugger
      
              id.cartItemTypeId == CartItemId.Reciprocity || id.cartItemTypeId == CartItemId.Renewal ? this.router.navigateByUrl('/registry') :this.CartTypeItems !='Practice' ?this.router.navigateByUrl('/exam-scheduled'):this.router.navigateByUrl('/practice_exam')
      
              this.dialogRef.close()
      
            }
      
          }, 3000)

        }

      }):this.store.select(selectorGetCartPracticeDeleteStatus).subscribe((data: any) => {
        if (data) {
          // this.store.dispatch<Action>(deleteCartItem({ index: i }));
          setTimeout(()=>{
            this.store.dispatch<Action>(
              getCartPracticeItems({ personTenantRoleId: this.global.candidateId })
            )
            this.store.dispatch<Action>(
              getPracticeRegisteredExam({ candidateId: this.global.candidateId })
            );
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            );

            !examName.includes(id.examName)?this.router.navigateByUrl('/exam-scheduled'):this.router.navigateByUrl('/practice_exam')
      
            this.dialogRef.close()
          },2000)
        
          
        }
      });



    }



 
  }

  addExam() {
    this.router.navigateByUrl("/exam-scheduled/register");
    this.dialogRef.close();
  }

  getIncidentstatus() {
    this.services.getIncidentStatus(this.global.personId).subscribe((data: boolean) => {
      if (data) {
        this.NotAllowScheduleforCheating = data
      }
    })
  }
}
export enum CartItemId {
  "Renewal" = "3",
  "Reciprocity" = "4"
}

export const examName =['Nurse Aide Bundle II','Nurse Aide Bundle I','Nurse Aide Bundle III']
