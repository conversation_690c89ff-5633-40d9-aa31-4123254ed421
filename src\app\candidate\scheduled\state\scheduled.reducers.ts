import { state } from "@angular/animations";
import { createReducer, on } from "@ngrx/store";
import {
  examCancelled,
  gotCart,
  gotEligibilityRoutes,
  gotExamId,
  gotMakePayment,
  gotMonthlySlots,
  gotRegisteredExam,
  gotRescheduled,
  gotSchedule,
  gotTimeSlots,
  cartItemRemoved,
  gotTimezones,
  isPayment,
  gotVoucher,
  gotVoucherAssign,
  gotVoucherApply,
  gotScheduled,
  gotPersonForm,
  gotPaymentMethod,
  madeCharge,
  gotPaymentCustomerId,
  paymentMethodCreated,
  gotTimeSlotsTestCenter,
  gotRescheduledTC,
  paymentCustomerIdCreated,
  clearChargeResponseState,
  createUpdate,
  clearVocherResponse,
  gotShowRegisterExam,
  getTimeSlotsTestCenter,
  clearTimeSlotsTestCenter,
  ClearTimeslots,
  setPaymentErrorMessage,
  gotClearData,
  setFormProgressBar,
  gotPracticeCart,
  gotPracticeRegisteredExam,
  madePracticeExamCharge,
  cartItemPracticeRemoved,
  gotPracticeRescheduled,
  gotPracticeRetryscheduled,
  gotPracticeExamId,
  examPracticeCancelled,
  gotVoucher_validate_apply,
  getTimeSlotsTestCenterFailure,
} from "./scheduled.actions";
import { initScheduledState, ScheduledState } from "./scheduled.state";

const _scheduledReducer = createReducer<ScheduledState>(
  initScheduledState,
  on(getTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      loading: true,
      timeSlotsTestCenterStatus: "loading",
    };
  }),

  on(gotTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      loading: false,
      timeslotsTestCenter: action.slots,
      timeSlotsTestCenterStatus: "success",
    };
  }),

  on(getTimeSlotsTestCenterFailure, (state, { error }) => {
    return {
      ...state,
      loading: false,
      timeSlotsTestCenterStatus: "error",
    };
  }),

  on(clearTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      timeslotsTestCenter: null,
      timeSlotsTestCenterStatus: "idle",
    };
  }),

  on(gotTimezones, (state, action) => {
    return {
      ...state,
      timezones: action.Timeszones,
    };
  }),
  on(gotTimeSlots, (state, action) => {
    return {
      ...state,
      timeslots: action.Slots,
    };
  }),
  on(getTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      loading: true,
    };
  }),
  on(gotTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      timeslotsTestCenter: action.slots,
      loading: false,
    };
  }),
  on(clearTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      timeslotsTestCenter: null,
    };
  }),
  on(ClearTimeslots, (state, action) => {
    return {
      ...state,
      timeslots: null,
    };
  }),
  on(gotCart, (state, action) => {
    return {
      ...state,
      cart: action.cart,
      isPayment: action.isPayment,
    };
  }),
  on(gotPracticeCart, (state, action) => {
    return {
      ...state,
      PracticeCart: action.cart,
      isPayment: action.isPayment,
    };
  }),
  on(gotMonthlySlots, (state, action) => {
    return {
      ...state,
      monthlySlots: action.monthlySlots,
    };
  }),
  on(gotExamId, (state, action) => {
    return {
      ...state,
      examId: action.examdata,
    };
  }),
  on(gotPracticeExamId, (state, action) => {
    return {
      ...state,
      examIdPractice: action.examdata,
    };
  }),

  on(gotClearData, (state, action) => {
    return {
      ...state,
      clearCartResponse: action.Response,
    };
  }),
  on(gotEligibilityRoutes, (state, action) => {
    return {
      ...state,
      route: action.route,
    };
  }),
  on(gotRegisteredExam, (state, action) => {
    return {
      ...state,
      registeredExams: action.registeredExams,
    };
  }),
  on(gotPracticeRegisteredExam, (state, action) => {
    return {
      ...state,
      registeredPracticeExams: action.registeredPracticeExams,
    };
  }),

  on(gotMakePayment, (state, action) => {
    return {
      ...state,
      makepaymentresponse: action.makePaymentResponse,
    };
  }),
  // on(gotCartItems, (state, action)=>{
  //     return {
  //         ...state,cartItems:action.cartItems
  //     }
  // }),
  on(gotSchedule, (state, action) => {
    return {
      ...state,
      schedule: action.scheduleres,
    };
  }),
  on(examCancelled, (state, action) => {
    return {
      ...state,
      isCancelled: action.isCancelled,
    };
  }),
  on(examPracticeCancelled, (state, action) => {
    return {
      ...state,
      isPracticeCancelled: action.isCancelled,
    };
  }),

  on(cartItemRemoved, (state, action) => {
    return {
      ...state,
      isDeleted: action.isDeleted,
    };
  }),
  on(cartItemPracticeRemoved, (state, action) => {
    return {
      ...state,
      isPracticeDeleted: action.isPracticeDeleted,
    };
  }),
  on(gotRescheduled, (state, action) => {
    return {
      ...state,
      rescheduleResponse: action.rescheduleResponse,
    };
  }),
  on(gotPracticeRescheduled, (state, action) => {
    return {
      ...state,
      PracticerescheduleResponse: action.PracticerescheduleResponse,
    };
  }),

  on(gotPracticeRetryscheduled, (state, action) => {
    return {
      ...state,
      PracticeretryscheduleResponse: action.PracticeretryscheduleResponse,
    };
  }),

  on(gotRescheduledTC, (state, action) => {
    return {
      ...state,
      rescheduleResponse: action.rescheduleResponse,
    };
  }),
  on(isPayment, (state, action) => {
    return {
      ...state,
      isPayment: false,
    };
  }),

  on(gotVoucher, (state, action) => {
    return {
      ...state,
      VocherResponse: action.VocherResponse,
    };
  }),

  on(gotVoucher_validate_apply, (state, action) => {
    return {
      ...state,
      Vocher_validate_apply_Response: action.VocherResponse,
    };
  }),

  on(gotVoucherAssign, (state, action) => {
    return {
      ...state,
      Vocher: action.VocherAssignResponse,
    };
  }),

  on(gotVoucherApply, (state, action) => {
    return {
      ...state,
      VochersApply: action.VocherApplyResponse,
    };
  }),

  on(gotScheduled, (state, action) => {
    return {
      ...state,
      scheduleResponse: action.ScheduledResponse,
    };
  }),

  on(gotPersonForm, (state, action) => {
    return {
      ...state,
      personForms: action.personForms,
    };
  }),
  on(gotPaymentMethod, (state, action) => {
    return {
      ...state,
      paymentMethods: action.paymentmethods,
    };
  }),
  on(madeCharge, (state, action) => {
    return {
      ...state,
      chargeResponse: action.chargeResponse,
    };
  }),

  on(madePracticeExamCharge, (state, action) => {
    return {
      ...state,
      chargePracticeResponse: action.chargePracticeResponse,
    };
  }),
  on(gotPaymentCustomerId, (state, action) => {
    return {
      ...state,
      customerIdObj: action.customerIdObj,
    };
  }),
  on(paymentMethodCreated, (state, action) => {
    return {
      ...state,
      createPaymentMethodResponse: action.response,
    };
  }),
  on(paymentCustomerIdCreated, (state, action) => {
    return {
      ...state,
      createPaymnetCustomerIdResponse: action.response,
    };
  }),
  on(clearChargeResponseState, (state, action) => {
    return {
      ...state,
      paymentMethods: [],
      chargeResponse: null,
      customerIdObj: null,
      createPaymentMethodResponse: null,
      createPaymnetCustomerIdResponse: null,
      scheduleResponse: null,
      chargePracticeResponse: null,
    };
  }),
  on(createUpdate, (state, action) => {
    return {
      ...state,
      VocherUpdateResponse: action.response,
    };
  }),
  on(clearVocherResponse, (state, action) => {
    return {
      ...state,
      VocherResponse: null,
      VochersApply: null,
      Vocher_validate_apply_Response: null,
    };
  }),
  on(gotShowRegisterExam, (state, action) => {
    return {
      ...state,
      showRegisterExamStatus: action.data,
    };
  }),
  on(setPaymentErrorMessage, (state, action) => {
    return {
      ...state,
      paymenterror: action.message,
    };
  }),
  on(setFormProgressBar, (state, action) => {
    return {
      ...state,
      formProgressBar: action.formArray,
    };
  })
);
export function scheduledReducer(state, action) {
  return _scheduledReducer(state, action);
}
