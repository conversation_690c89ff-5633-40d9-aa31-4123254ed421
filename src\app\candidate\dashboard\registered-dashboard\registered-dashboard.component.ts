import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
//import moment from "moment";
import { interval, Subscription } from "rxjs";
import { LanguageService } from "src/app/core/language.service";
import { ScheduledService } from "../../scheduled/scheduled.service";
import { upcomingExam } from "../state/dashboard.models/Upcomingexam";
import Moment from "moment";
import * as moment from 'moment/moment';
import 'moment-timezone';
import { Timer } from "../state/dashboard.models";
import { MatDialog } from "@angular/material/dialog";
import { getCandidateLogin, getShowRegisterExam, getupcomingExam } from "../state/dashboard.actions";
import { Action, Store } from "@ngrx/store";
import { getCandidateLogin$, selectorShowRegisterExam$, selectUpcommmingExam } from "../state/dashboard.selectors";
import { GlobalUserService } from "src/app/core/global-user.service";
import { ConfirmPopupComponent } from "../confirm-popup/confirm-popup.component";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";
import { HttpService } from "src/app/core/http-services/http.service";
import { DatePipe } from "@angular/common";
import { ExamName } from "../../scheduled/state/models/cart";
import { Allowonlyoral } from "src/app/core/examroom-formbuilder/form-builder.types";
@Component({
  selector: "exai-registered-dashboard",
  templateUrl: "./registered-dashboard.component.html",
  styleUrls: ["./registered-dashboard.component.scss"],
})
export class RegisteredDashboardComponent implements OnInit {
  @Output() ExamChanged:EventEmitter<any>=new EventEmitter<any>();
  @Input() upcomingExam: upcomingExam;
  @Input() upcomingExams: upcomingExam[];
  timePDT: string;
  examDateTimePDT;
  showRegisterExam:any;
  private subscription: Subscription;
  registeredDateTime;
  cancel: boolean = true
  payment: boolean = false
  alloworal=Allowonlyoral
  examDateTime;
  examDatetime:string
  public systemCheckStatus: boolean = false;
  timerInfo: Timer = { days: 0, hours: 0, minutes: 0, seconds: 0, default: 0 };
  clientExamIdtoStartExam: number;
  @ViewChild('systemCheck') systemCheck: TemplateRef<any>;
   ExamCode:string
  constructor(public lngSrvc: LanguageService, private router: Router, private _services: ScheduledService, private dialog: MatDialog, private store: Store, private global: GlobalUserService,
    private services: SnackbarService, private httpService: HttpService, private http: HttpClient, private activatedRoute: ActivatedRoute,private dp:DatePipe) {

  }

  ngOnInit(): void {
    this.subscription = interval(1000).subscribe(() => {

      this.getTimeDifference();
    });
     console.log(this.upcomingExam)
     this.ExamCode = JSON.parse(this.upcomingExam.eventDataDetail).ExamCode

    this.examTimePDT();
    this.examDatetime= this.dp.transform((this.upcomingExam.examDateTimeUtc), "MM/dd/YYYY", "+0000",)
    this.registeredDateTime = moment(this.upcomingExam.examDateTime).format("MMMM Do , YYYY ");
    this.examDateTime = moment(this.upcomingExam.examDateTimeUtc).format('MM/DD/YYYY');
    this.systemCheckStatus = this.upcomingExams.filter(x => (x.examMode === "Online") || (x.examMode ==='Test Center' && JSON.parse(x.eventDataDetail).ExamCode ==='CBT-WR')).length > 0;
    this.clientExamIdtoStartExam = this.upcomingExam.clientExamId;

    this.global.userDetails.subscribe(data=>{
      if(data){
        this.store.dispatch<Action>(getShowRegisterExam({ personTenantRoleId: this.global.candidateId }));
      }
    })

    this.store.select(selectorShowRegisterExam$).subscribe((data: any) => {
      if (data) {
        this.showRegisterExam = data;
      }
    });
  }

  examTimePDT() {
    //let n = Intl.DateTimeFormat().resolvedOptions();
    this.examDateTimePDT = moment(this.upcomingExam.examDateTimeUtc).tz(this.upcomingExam.timeZoneAbbreviation).format('h:mm a z');
  }
  // systemTest() {
  //   window.open("https://examroom.ai/systemtest/#");
  // }

  _systemCheck(): void {
    this.dialog.open(this.systemCheck, {
      // maxHeight: '90vh',
      maxWidth: '60vw',
      minWidth: '75vh',
      // minHeight: '80vh',
      autoFocus: false,
      disableClose: true
    })
  }

  getTimeDifference(): void {
    if (this.upcomingExam) {
      const date = new Date(this.upcomingExam.examDateTimeUtc);
      const difference = date.getTime() - new Date().getTime();
      const _seconds = Number(difference / 1000);
      const days = Math.floor(_seconds / (3600 * 24));
      const hours = Math.floor(_seconds % (3600 * 24) / 3600);
      const minutes = Math.floor(_seconds % 3600 / 60);
      const seconds = Math.floor(_seconds % 60);
      _seconds > 0 ? this.timerInfo = {
        days: days,
        hours: hours,
        minutes: minutes,
        seconds: seconds,
        default: _seconds
      } : this.subscription.unsubscribe();
      if (this.timerInfo.days == 0 && this.timerInfo.hours <= 1 && this.timerInfo.minutes <= 59 && this.timerInfo.seconds <= 59) {
        this.payment = true;
        this.cancel = false
      } else {
        this.payment = false
        this.cancel = true
      }
    }
  }

  reschedule(event: any): void {
    if(!event?.candidateId) event.candidateId = this.global.candidateId;
    if(!event?.mode && event?.examMode) event.mode = event.examMode;
    ExamName.includes(event.eligibilityRouteId)? this._services.PracticeInformation = event:this._services.rescheduleInformation = event;
    ExamName.includes(event.eligibilityRouteId)?this.router.navigateByUrl("/practice_exam/register_practice"): this.router.navigateByUrl("/exam-scheduled/register");
  }

  startExam() {
    if(this.upcomingExam.examModeId === 1  || this.ExamCode ==='CBT-WR'){
      const inputDetails = { email: this.global.emailIdToStartExam, role: this.global.roleIDforStartExam, firstName: this.global.given_name, lastName: this.global.family_name, ClientID: this.global.clientIDforStartExam }
      this.httpService.scheduleStartExam(inputDetails).subscribe((data: any) => {
        if (data.Result) {
          // let a = data.LoginURL;
          // var splitted = a.split("candidate", 2);
          // var newArray = splitted.push("candidate")
          // splitted[0] = "https://test.examroom.ai/"
          // var temp = splitted[1];
          // splitted[1] = splitted[2]
          // splitted[2] = temp
          // var newStr = splitted.join(' ')
          // data.LoginURL = this.removeWhitespaces(newStr);
          // setTimeout(() => window.open("http://localhost:8011/#/?id=kKJ09SYafrYddmwFA/8NTf1sWfWMRxZDZiJBL53Kecbqu3iYZfQy+1Y0xpQTL/bjd225kM1jw/xti7zYlH0RgQ==", '_self'), 10);
          // let b="345"
          let a = data.LoginURL+`&ExamID=${this.clientExamIdtoStartExam}`;
          setTimeout(() => window.open(a, '_blank'), 10);
        }
    })
    }else if(this.upcomingExam.examModeId === 2 && this.alloworal.includes(this.upcomingExam.examName)){
      setTimeout(() => window.open(environment.taotaker, '_blank'), 10);
    }
  
  }

  // removeWhitespaces(string, i = 0, res = "") {
  //   if (i >= string.length)
  //     return res
  //   else
  //   if (string[i] == " ")
  //     return this.removeWhitespaces(string, i + 1, res)
  //   else
  //     return this.removeWhitespaces(string, i + 1, res += string[i])
  // }

  // Old code for Start Exam
  // startExam(){
  //       var candidateLoginInputs = {
  //       APIKey: '2020430',
  //       SecretKey:'5A739D1D-2D17-4088-AB54-B1F8C9FE9C90',
  //       email: this.global.emailIdToStartExam,
  //       password: this.global.userId,
  //     }
  //     //V1 api integration for get available slots
  //     this.store.dispatch<Action>(getCandidateLogin({candidateLoginInputs}));
  //     this.store.select(getCandidateLogin$).subscribe(data=>{
  //       if(data){
  //         this.services.callSnackbaronSuccess(`${data.Message}`)
  //         window.open(data.LoginURL);
  //       }else{

  //       }
  //     //  window.open("http://test.examroom.ai/candidate/#/?id=C4lDaU7wVx2ckMoUivAsW7Mmt3Ofp0WzhBa7/HlWNSDENUOERDNAXDQ+M4eI+/IlrCm6o/aB3Fr+sOBPINl9Hg==",'_blank');
  //     // 

  //       })
  //     //end--
  //   // this.store.dispatch<Action>(getCandidateLogin({getCandidateLogin})
  //   //   );
  //      // this.store.dispatch<Action>(getAvailableSlots({getAvailableslots}));
  // }

  getConfirmation(id) {
    const dialogRef = this.dialog.open(ConfirmPopupComponent, {
      data:{message: id,id:3},
    })
    dialogRef.afterClosed().subscribe(data=>{
      if(data){
        this.store.dispatch<Action>(getupcomingExam({ candidateId: this.global.candidateId }));
        this.store.select(selectUpcommmingExam).subscribe(exam=>{
          if(exam){
            this.ExamChanged.emit(exam)
          }
        })
      }
     
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
