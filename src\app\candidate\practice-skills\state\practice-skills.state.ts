import { PracticeBundleSkill } from "./models/practice-skill-bundle.model";
import { PracticeSkillMode } from "./models/practice-skill-mode.model";
import { PracticeSkillResponse } from "./models/practice-skill-response.model";
import { PracticeSkill } from "./models/practice-skill.model";

//
// Skills State
//
export interface PracticeSkillsState {
  skills: PracticeSkill[];
  totalRecords: number;
  pageSize: number;
  pageNo: number;
  loading: boolean;
  error: any;

  // New property for single skill details
  selectedSkill?: PracticeSkill | null;
  selectedSkillLoading: boolean;
  selectedSkillError: any;
}
export const initialPracticeSkillState: PracticeSkillsState = {
  skills: [],
  totalRecords: 0,
  pageSize: 0,
  pageNo: 0,
  loading: false,
  error: null,

  selectedSkill: null,
  selectedSkillLoading: false,
  selectedSkillError: null,
};

//
// Bundles State
//
export interface PracticeSkillBundlesState {
  bundles: PracticeBundleSkill[];
  totalRecords: number;
  pageSize: number;
  pageNo: number;
  loading: boolean;
  error: any;
}

export const initialPracticeSkillBundlesState: PracticeSkillBundlesState = {
  bundles: [],
  totalRecords: 0,
  pageSize: 0,
  pageNo: 0,
  loading: false,
  error: null,
};

//
// Skill by GUID State (for Detail View)
//
export interface PracticeSkillByGuidState {
  skillResponse: PracticeSkillResponse | null;
  loading: boolean;
  error: any;
}
export const initialPracticeSkillByGuidState: PracticeSkillByGuidState = {
  skillResponse: null,
  loading: false,
  error: null,
};

//
// Skill Mode State
//
export interface PracticeSkillModeState {
  modeResponse: PracticeSkillMode;
  loading: boolean;
  error: any;
}
export const initialPracticeSkillModeState: PracticeSkillModeState = {
  modeResponse: {
    data: [],
    totalRecords: 0,
    pageSize: 0,
    pageNo: 0,
    message: "",
    status: "",
  },
  loading: false,
  error: null,
};

//
// Combined Feature State
//
export interface PracticeFeatureState {
  skillsState: PracticeSkillsState;
  bundlesState: PracticeSkillBundlesState;
  skillByGuidState: PracticeSkillByGuidState;
  skillModeState: PracticeSkillModeState;
}
