<div class="px-gutter pt-2 overflow-y-auto" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr"
    gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
        <div class="" fxLayout="column">
            <h6><strong>{{(form ? FormTypesToNames[form.formTypeID[0]]?.toString():'')+' Form'}}</strong></h6>
            <!-- <ng-container *ngIf="false; else normalBreadCrumbs">
               <exai-dynamic-bread-crumbs [crumbs]="breadCrumbsArray" (crumbNavigate)="handleCrumbNavigation($event.crumb)">
                </exai-dynamic-bread-crumbs> -->
            <!-- </ng-container> -->
            <!-- <ng-template #normalBreadCrumbs>  -->
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
                [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
            <!-- </ng-template> -->
        </div>
    </div>
    <div class="w-full mt-2" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1"
        *ngIf="getWhetherAtleastOneEnabled">

        <div class="flex justify-end add-new" fxLayout="">
            <button mat-button class="btn-3 text-xs mx-2" (click)="deleteForm()">
                {{this.lngSrvc.curLangObj.value.cancel}}
            </button>
            <button mat-button class="btn-1 text-xs ml-2" [style.display]="'none'"
                (click)="permanentlySaveResponse = false;" #submitButton id="submitButton">.
            </button>
            <button *ngIf="certStatusId !='4'" mat-button class="btn-2 text-xs mx-2"
                (click)="submit(false, true, false)" #submitButton id="submitButton">
                {{this.lngSrvc.curLangObj.value.saveDraft}}
            </button>
            <!-- /// GrievanceStatus //// -->
            <!-- <div *ngIf="showReciprocityPayment && (form?.formTypeID.includes(2) )">
                    <div *ngIf="enableResPayment; else addtocart1">
                    <button  mat-button class="btn-1 text-xs ml-2"     [ngClass]="{'button-disabled' : checkProgressOfSections}"  (click)="submit(false, true, false)" #submitButton id="submitButton"(click)="paymentPage()">
                        {{this.lngSrvc.curLangObj.value.renewelPayment}}
                    </button>
                    </div>
                    <ng-template #addtocart1>
                        <button  mat-button class="btn-1 text-xs ml-2"  [ngClass]="{'button-disabled' : checkProgressOfSections || GrievanceStatus !='Evaluator' }" (click)="submit(false, true, true)" #submitButton id="submitButton">
                            {{this.lngSrvc.curLangObj.value.addToCart}}
                        </button>
                    </ng-template>
                </div> -->


            <!-- this block is for renewal payment button of selected state -->
            <div
                *ngIf="showRenewelPayment && (form?.formTypeID.includes(5) || form?.formTypeID.includes(14) || form?.formTypeID.includes(17) ); else elseBlock">
                <div *ngIf="enablePayment; else addtocart">
                    <button mat-button class="btn-1 text-xs ml-2"
                        [ngClass]="{'button-disabled' : checkProgressOfSections }" (click)="submit(false, true, false)"
                        #submitButton id="submitButton" (click)="paymentPage()">
                        {{this.lngSrvc.curLangObj.value.renewelPayment}}
                    </button>
                </div>
                <ng-template #addtocart>
                    <button mat-button [disabled]="checkProgressOfSections" class="btn-1 text-xs ml-2"
                        [ngClass]="{'button-disabled' : checkProgressOfSections}" (click)="submit(false, true, true)"
                        #submitButton id="submitButton">
                        {{this.lngSrvc.curLangObj.value.addToCart}}
                    </button>
                </ng-template>
            </div>

            <!-- this block is for resciprocity payment button of selected state -->
            <ng-template #elseBlock>
                <div
                    *ngIf="showReciprocityPayment && (form?.formTypeID.includes(6) ||form?.formTypeID.includes(15) ) ; else elseBlock1">
                    <div *ngIf="enableResPayment; else addtocart1">
                        <button mat-button class="btn-1 text-xs ml-2"
                            [ngClass]="{'button-disabled' : checkProgressOfSections}"
                            (click)="submit(false, true, false)" #submitButton id="submitButton"
                            (click)="paymentPage()">
                            {{this.lngSrvc.curLangObj.value.renewelPayment}}
                        </button>
                    </div>
                    <ng-template #addtocart1>
                        <button mat-button class="btn-1 text-xs ml-2"
                            [ngClass]="{'button-disabled' : checkProgressOfSections}"
                            (click)="submit(false, true, true)" #submitButton id="submitButton">
                            {{this.lngSrvc.curLangObj.value.addToCart}}
                        </button>
                    </ng-template>
                </div>
            </ng-template>

            <!-- this block is for apart from renewal and resciprocity and non selected state-->
            <ng-template #elseBlock1>

                <div id="Div-For-Submit-Button-To-Show">
                   
                    <button mat-button class="btn-1 text-xs ml-2"
                        [ngClass]="{'button-disabled' :  checkProgressOfSections  }" 
                        
                        (click)="isSubmit = true;permanentlySaveResponse = true;" #submitButton id="submitButton">
                    
                        {{this.lngSrvc.curLangObj.value.submit}}
                    </button>

                </div>


            </ng-template>



        </div>

    </div>
    <div class="w-full mt-2" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1"
        *ngIf="withdraw && !getWhetherAtleastOneEnabled">
        <div class="flex justify-end add-new">
            <button mat-button class="btn-1 text-xs ml-2" (click)="deleteForm()">
                Withdraw Application
            </button>
        </div>
    </div>

    <div class="w-full mt-2" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1"
        *ngIf='form?.formTypeID.includes(2) && AppealstatusId === 3'>
        <div class="flex justify-end add-new">
            <button mat-button class="btn-1 text-xs ml-2" (click)="submitAppeal()">
                Submit
            </button>
        </div>
    </div>

</div>

<div *ngIf='form?.formTypeID.includes(2) && AppealstatusId === 3' class="px-gutter py-2">
    <div class="bg-white cardBorder p-4">
        <div>
            <h6 class="mb-2"><strong>Appeal Form</strong></h6>
            <p class="text-xs text-gray">
                This appeal addresses the rejection of a grievance concerning exam procedures. Significant
                irregularities, such as the exam starting late and technical difficulties, adversely impacted
                performance. Evidence, including screenshots and emails, supports these claims. These issues deviated
                from standard procedures. A re-evaluation, retake opportunity, for reconsideration of grading is
                requested.
            </p>
        </div>
        <div class="wwidth">
            <h6 class="mb-2"><strong> Please be specific regarding your appeal for rejected grievance</strong> </h6>
            <form [formGroup]="formGroup">
                <mat-form-field appearance="outline" class="block">
                    <mat-label>Answer</mat-label>
                    <textarea class="flex-wrap" matInput formControlName="description" placeholder="Answer" rows="1">
                  </textarea>
                    <mat-error *ngIf="formGroup.get('description').touched && formGroup.get('description').invalid">
                        Answer is required.
                    </mat-error>
                </mat-form-field>
            </form>


        </div>
    </div>
</div>


<div *ngIf='form?.formTypeID.includes(2) && AppealstatusId === 12' class="px-gutter py-2">
    <div class="bg-white cardBorder p-4">
        <div>
            <h6 class="mb-2"><strong>Appeal Form</strong></h6>
            <p class="text-xs text-gray">
                This appeal addresses the rejection of a grievance concerning exam procedures. Significant
                irregularities, such as the exam starting late and technical difficulties, adversely impacted
                performance. Evidence, including screenshots and emails, supports these claims. These issues deviated
                from standard procedures. A re-evaluation, retake opportunity, for reconsideration of grading is
                requested.
            </p>
        </div>
        <div class="wwidth">
            <h6 class="mb-2"><strong> Please be specific regarding your appeal for rejected grievance</strong> </h6>
            <form>
                <mat-form-field appearance="outline" class="block">
                    <mat-label>Answer</mat-label>
                    <textarea readonly class="flex-wrap" matInput formControlName="description" placeholder="Answer"
                        rows="3" [value]="description">
                  </textarea>
                </mat-form-field>
            </form>
        </div>
    </div>
</div>


<div class="px-gutter py-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr"
    gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
    <div class="shadow-none justify-start touch-auto overflow-auto dashboard"
        [gdColumn]="!(form && !form.formTypeID.includes(2) && !form.formTypeID.includes(13) && !form.formTypeID.includes(4)&& !form.formTypeID.includes(16)) ? '1 / 11 ' : '1 / 9'"
        gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="card shadow-none cardBorder touch-auto overflow-auto" fxFlex="auto">
            <ng-container>
                <exai-form-builder *ngIf="form && form.formJSON != null && !(userResponse$ | async)"
                    [existingForm]="form.formJSON" [showToolbar]="false" [showOnlyForm]="true"
                    (sectionCompleted)="sectionsForProgress.splice($event.sectionIndex,1,$event);changeSectionValidationEvent($event)"
                    (onChange)="handleChange($event)" (formSubmitEvent)="saveResponse($event)"
                    [submitButtonRef]="submitButtonRef"
                    (initalSectionValidationEvent)="initialSectionValidationEvent($event)" [showStatic]="form.formTypeID.includes(2)
                    || form.formTypeID.includes(4) ||form.formTypeID.includes(16) "
                    [element]="{eligibilityRouteId:form.eligibilityID,personEventId,stateId:form.stateID,candidateId,certStatusId}">
                </exai-form-builder>

                <div>
                    <div class="form-cards" id='gallery-1'>
                        <mat-accordion *ngIf="form && form.formJSON != null && !(userResponse$ | async) &&form?.formTypeID.includes(3) && ![15,20,22463,22613].includes(global.userDetails.value.stateId) && !['117','118','63','64','22331'].includes(StateDetails.eligibilityRouteId) ">
                            <!-- <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true"> -->
                                <mat-expansion-panel class="cardBorder  mb-2">
                                    <mat-expansion-panel-header class="matheader">
                                        <mat-panel-title class="text-xs title ">
                                            Testing Preference
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <h6 class="mat-text-header">
                                        1. Are you planning to take your Nurse Aide Skills Exam at an In-facility or Regional
                                        Testing Center?</h6>
                                        <mat-radio-group [formControl]="radioselect" [disabled]="NonEditData">
                                            <ng-container *ngFor="let examType of examTypeModels; let i = index">
                                              <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
                                                (change)="getSelectedRoute(examType.id)">{{ examType.name }}
                                              </mat-radio-button>
                                            </ng-container>
                                            <mat-error class="-mt-2" *ngIf="radioselect.value == null || radioselect.value == '' || radioselect.value == undefined " >
                                                This field is required
                                              </mat-error>
                                          </mat-radio-group>
                                        
                                   
                                    <form [formGroup]="INFValue">
                                        <ng-container *ngIf="INFSelected" >
                                            <h6 class="mat-text-header">
                                                2. What In-facility are you interested in testing at? Kindly include their INF code below if you know it:</h6>
                                                <mat-form-field class="mt-2 ml-2" appearance="outline">
                                                    <input class="form-control   "formControlName="infcode" matInput autocomplete="off" maxlength="8" id='infcode' [disabled]="NonEditData" (input)="onKeydownMain($event.target.value)" (keypress)="onlyNumberKey($event)"  (paste)="paste($event)">
                                                </mat-form-field>
                    
                                            <mat-error *ngFor="let validation of validation_messages.testId">
                                                <mat-error class="error-message -mt-5 mb-2"
                                                  *ngIf="INFValue.get('infcode')?.value?.length < 8">
                                                 {{validation.message}}
                                                </mat-error>
                                          
                                              </mat-error>
                                        </ng-container>
                                    </form>
                                    <ng-container  *ngIf="INFSelected">
                                        <h6 class="-mt-2 mat-text-header">
                                            3. I acknowledge that I am not required to test at this location.</h6>
                                         
        
                                            <mat-radio-group [formControl]="radioselectAcceptance" [disabled]="NonEditData">
                                                <ng-container *ngFor="let examType of Acceptance; let i = index">
                                                  <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"  (change)="getSelectedAccept(examType.id)"
                                                    >{{ examType.name }}
                                                  </mat-radio-button>
                                                </ng-container>
                                                <mat-error class="-mt-2" *ngIf="radioselectAcceptance.value == null || radioselectAcceptance.value == '' || radioselectAcceptance.value == undefined " >
                                                    This field is required
                                                  </mat-error>
                                              </mat-radio-group>
                                    </ng-container>
                                   
                                </mat-expansion-panel>
                            <!-- <mat-expansion-panel class="cardBorder application-expansion1"> -->
                            <!-- <mat-expansion-panel class="cardBorder application-expansion1" *ngIf="AccomodationPerformLogs && AccomodationPerformLogs.length">
                                <mat-expansion-panel-header>
                                    <mat-panel-title class="text-xs font-bold">
                                        {{this.lngSrvc.curLangObj.value.accommodationProcess}}
                                    </mat-panel-title>
                                </mat-expansion-panel-header>
                                <p class="expand">
                                    <exai-progress-bar [performlogs]="AccomodationPerformLogs"></exai-progress-bar>
                                </p>
                            </mat-expansion-panel> -->
                        </mat-accordion>
                    </div>
                </div>
            </ng-container>
           


            <ng-container>
                <exai-form-builder *ngIf="form && form.formJSON != null && (userResponse$ | async)"
                    [existingForm]="form.formJSON" [showToolbar]="false" [showOnlyForm]="true"
                    (sectionCompleted)="sectionsForProgress.splice($event.sectionIndex,1,$event);changeSectionValidationEvent($event)"
                    (onChange)="handleChange($event)" (formSubmitEvent)="saveResponse($event)"
                    [submitButtonRef]="submitButtonRef" [existingUserData]="(userResponse$ | async).response"
                    (initalSectionValidationEvent)="initialSectionValidationEvent($event)"
                    [showStatic]="form.formTypeID.includes(2)|| form.formTypeID.includes(4)"
                    [element]="{eligibilityRouteId:form.eligibilityID,personEventId,stateId:form.stateID,candidateId,certStatusId}"
                    [disabled]="(userResponse$ | async).disabled">
                </exai-form-builder>
                <div>
                    <div class="form-cards" id='gallery-1'>
                        <mat-accordion *ngIf="form && form.formJSON != null && (userResponse$ | async) && (form?.formTypeID.includes(3)) && ![15,20,22463,22613].includes(global.userDetails.value.stateId) && !['117','118','63','64','22331'].includes(StateDetails.eligibilityRouteId)">
                            <!-- <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true"> -->
                                <mat-expansion-panel class="cardBorder  mb-2">
                                    <mat-expansion-panel-header class="matheader">
                                        <mat-panel-title class="text-xs title ">
                                            Testing Preference
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <h6 class="mat-text-header">
                                        1. Are you planning to take your Nurse Aide Skills Exam at an In-facility or Regional
                                        Testing Center?</h6>
                                        <mat-radio-group [formControl]="radioselect" [disabled]="NonEditData">
                                            <ng-container *ngFor="let examType of examTypeModels; let i = index">
                                              <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
                                                (change)="getSelectedRoute(examType.id)">{{ examType.name }}
                                              </mat-radio-button>
                                            </ng-container>
                                            <mat-error class="-mt-2" *ngIf="radioselect.value == null || radioselect.value == '' || radioselect.value == undefined " >
                                                This field is required
                                              </mat-error>
                                          </mat-radio-group>
                                        
                                   
                                    <form [formGroup]="INFValue">
                                        <ng-container *ngIf="INFSelected" >
                                            <h6 class="mat-text-header">
                                                2. What In-facility are you interested in testing at? Kindly include their INF code below if you know it:</h6>
                                                <mat-form-field class="mt-2 ml-2" appearance="outline">
                                                    <input class="form-control   "formControlName="infcode" matInput autocomplete="off" maxlength="8" id='infcode' [disabled]="NonEditData" (input)="onKeydownMain($event.target.value)" (keypress)="onlyNumberKey($event)"  (paste)="paste($event)">
                                                </mat-form-field>
                    
                                            <mat-error *ngFor="let validation of validation_messages.testId">
                                                <mat-error class="error-message -mt-5 mb-2"
                                                  *ngIf="INFValue.get('infcode')?.value?.length < 8">
                                                 {{validation.message}}
                                                </mat-error>
                                          
                                              </mat-error>
                                        </ng-container>
                                    </form>
                                    <ng-container  *ngIf="INFSelected">
                                        <h6 class="-mt-2 mat-text-header">
                                            3. I acknowledge that I am not required to test at this location.</h6>
                                         
        
                                            <mat-radio-group [formControl]="radioselectAcceptance" [disabled]="NonEditData">
                                                <ng-container *ngFor="let examType of Acceptance; let i = index">
                                                  <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"  (change)="getSelectedAccept(examType.id)"
                                                    >{{ examType.name }}
                                                  </mat-radio-button>
                                                </ng-container>
                                                <mat-error class="-mt-2" *ngIf="radioselectAcceptance.value == null || radioselectAcceptance.value == '' || radioselectAcceptance.value == undefined " >
                                                    This field is required
                                                  </mat-error>
                                              </mat-radio-group>
                                    </ng-container>
                                   
                                </mat-expansion-panel>
                            <!-- <mat-expansion-panel class="cardBorder application-expansion1"> -->
                            <!-- <mat-expansion-panel class="cardBorder application-expansion1" *ngIf="AccomodationPerformLogs && AccomodationPerformLogs.length">
                                <mat-expansion-panel-header>
                                    <mat-panel-title class="text-xs font-bold">
                                        {{this.lngSrvc.curLangObj.value.accommodationProcess}}
                                    </mat-panel-title>
                                </mat-expansion-panel-header>
                                <p class="expand">
                                    <exai-progress-bar [performlogs]="AccomodationPerformLogs"></exai-progress-bar>
                                </p>
                            </mat-expansion-panel> -->
                        </mat-accordion>
                    </div>
                </div>
            </ng-container>
            

            <ng-container *ngIf="isAccomodationSelected">
                <div *ngIf="accTypeFormSysFileName.value || showdownload"
                    class="max-w-full py-2 shadow-none cardBorder mx-4 my-4 px-3 flex flex-row justify-center items-center"
                    #accFormDownloadButton id="accFormDownloadButton">
                    Based on your Accomodation Type selection, please download the following form for completion by your
                    evaluator
                    <mat-icon
                        (click)="store.dispatch(downloadAccTypeForm({sysFileName:this.accTypeFormSysFileName.value?this.accTypeFormSysFileName.value:AccomodationValues[0].systemFileName}))"
                        class="cursor-pointer">download</mat-icon>
                </div>
            </ng-container>
        </div>
    </div>

    <div *ngIf="form && !form.formTypeID.includes(2) && !form.formTypeID.includes(13)&& !form.formTypeID.includes(4) && !form.formTypeID.includes(FormTypes.AbuseAlligation)&& !form.formTypeID.includes(16)"
        class="shadow-none justify-start" gdColumn="9/ -1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <!-- <div class="card shadow-none cardBorder p-4 dashboard touch-auto overflow-auto" fxFlex="auto">
            <form-progress-bar [sections]="sectionsForProgress"></form-progress-bar>
        </div> -->
        <div>
            <mat-accordion>
                <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true">
                    <mat-expansion-panel-header>
                        <mat-panel-title class="text-xs font-bold">
                            Application Progress
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <p class="expand">
                        <form-progress-bar [sections]="sectionsForProgress"></form-progress-bar>
                    </p>
                </mat-expansion-panel>
                <!-- <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true"> -->
                <mat-expansion-panel class="cardBorder application-expansion mb-2"
                    *ngIf="ApplicationPerformLogs && ApplicationPerformLogs.length">
                    <mat-expansion-panel-header>
                        <mat-panel-title class="text-xs font-bold">
                            {{(form ? FormTypesToNames[form.formTypeID[0]]?.toString():'')+' Process'}}
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <p class="expand">
                        <exai-progress-bar [performlogs]="ApplicationPerformLogs"></exai-progress-bar>
                    </p>
                </mat-expansion-panel>
                <!-- <mat-expansion-panel class="cardBorder application-expansion1"> -->
                <mat-expansion-panel class="cardBorder application-expansion1"
                    *ngIf="AccomodationPerformLogs && AccomodationPerformLogs.length">
                    <mat-expansion-panel-header>
                        <mat-panel-title class="text-xs font-bold">
                            {{this.lngSrvc.curLangObj.value.accommodationProcess}}
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <p class="expand">
                        <exai-progress-bar [performlogs]="AccomodationPerformLogs"></exai-progress-bar>
                    </p>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>

</div>