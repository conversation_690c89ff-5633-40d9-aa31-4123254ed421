import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { get_userDetails } from 'src/app/candidate/state/shared/shared.selectors';
import { SnackbarService } from '../snackbar.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(private store: Store, private snackbar: SnackbarService,private router: Router){

  }
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> {

      
    return this.store.select(get_userDetails).pipe(
      map((user) => {
        if (user){
          return true;
        }
        this.snackbar.callSnackbaronWarning("you are not logged in ")
        // this.router.navigateByUrl("")

      })
    );
  }
  
}
