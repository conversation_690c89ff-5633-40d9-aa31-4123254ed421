<div class="shadow-none card cardBorder justify-start touch-auto overflow-auto dashboard h-full" gdColumn="2/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1">
    <div class="eligibility-desc mb-3">
        <div class="eligibility1 touch-auto overflow-auto">
            <div class="exam ">
                <h2 class="px-5 py-1 pt-4 text-xs font-bold fontColor1">
                    Support Ticket
                </h2>
            </div>
            <form [formGroup]='formGroup' (ngSubmit)="submit()">
                <div fxLayout="column" class="payment px-4">
                    <mat-form-field appearance='outline' class="">
                        <mat-label class="text-xs">Summary</mat-label>
                        <input matInput formControlName='subject' name="subject" id='subject' [ngClass]="{ 'is-invalid': formGroup.get('subject').touched && formGroup.get('subject').invalid }">
                        <mat-error class="flex">
                            <div *ngIf="formGroup.get('subject').touched && formGroup.get('subject').invalid" class='invalid-feedback '>
                                <div *ngIf="formGroup.get('subject').errors.required">Summary is Required &nbsp;
                                </div>
                            </div>
                            <div *ngIf="formGroup.get('subject').touched && formGroup.get('subject').invalid" class='invalid-feedback'>
                                <div *ngIf="formGroup.get('subject').errors.required">
                                    Maximum number of characters is 50
                                </div>
                            </div>
                        </mat-error>
                    </mat-form-field>
                    <mat-form-field class="" appearance='outline'>
                        <mat-label class="text-xs">Description</mat-label>
                        <textarea class="help-txtarea" matInput formControlName='description' placeholder='Description'></textarea>
                        <mat-error>
                            <div *ngIf="formGroup.get('description').touched && formGroup.get('description').invalid" class='invalid-feedback' [ngClass]="{ 'is-invalid': formGroup.get('description').touched && formGroup.get('description').invalid }">
                                <div *ngIf="formGroup.get('description').errors.required">Description is Required
                                </div>
                            </div>
                        </mat-error>
                    </mat-form-field>
                    <form [formGroup]="formGroupUpload">
                        <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="formGroupUpload" [model]="formGroupModel[0]">
                        </dynamic-material-form-control>
                    </form>
                    <!-- <input type="file" (change)="handleFileInput($event)" formControlName="attachment"> -->
                    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                        <button mat-button class="btn-3 add t-xs" type="button" (click)='cancel()'>Cancel</button>
                        <button class="btn-1 t-xs" type="submit" mat-button [disabled]='!formGroup.valid'>Submit</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>