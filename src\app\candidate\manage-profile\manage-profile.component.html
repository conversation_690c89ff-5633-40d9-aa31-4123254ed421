<div class="px-gutter " gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full " gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <h5 class=" pt-2 titleFont"><strong>Manage Profile</strong></h5>
        </div>
        <!-- <div class="-mt-1"> -->
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
            [fontSize]="'0.65rem'">
        </app-ng-dynamic-breadcrumb>
        <!-- </div> -->
    </div>
    <div class="pt-2" gdRows="1fr 1fr " gdRows.lt-md="1fr" gdRows.lt-sm="1fr" gdGap="13px">
        <div gdRow="1 / 2" gdRow.lt-md="1/2 " gdRow.lt-sm="1">
            <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr"
                gdColumns.lt-sm="1fr" gdGap="13px">
                <div gdColumn="1 / 8" gdColumn.lt-md="1/2 " gdColumn.lt-sm="1">
                    <div class="card shadow-none cardBorder  mt-2" fxFlex="auto">
                        <div gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="15px"
                            exaiContainer>
                            <div class="justify-start w-full" gdColumn="1 / 2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1">
                                <div class="" fxLayout="column">
                                    <h6 class="pt-4 mx-5 font-bold">General Information</h6>

                                    <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr" gdGap="12px" exaiContainer>
                                        <div class="mx-5 justify-start w-full" gdColumn="1 / 2">
                                            <img class="photo"
                                                [src]="personDetails?.profileImageUrl ? personDetails.profileImageUrl : profileUrl" />
                                            <div>
                                                <div class="change text-xs pt-2" (click)="pic.click()">
                                                    Change Photo
                                                    <input #pic type="file" id="changePhoto" accept=".jpg,.jpeg,.png,.JPEG,.JPG.,.PNG"
                                                        (change)="profilePicUpload($event)" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="justify-start" gdColumn="2 / -1" gdColumn.lt-md="2/-1">
                                            <div gdRows="1fr 1fr 1fr " gdGap="8px">
                                                <div class="text-xs text1" gdRow="1/2">Name:</div>
                                                <div class="text-xs text" gdRow="1/2">
                                                    {{ personDetails?.firstName }} {{ personDetails?.middleName }} {{
                                                    personDetails?.lastName }}
                                                </div>
                                                <div class="text-xs text1" gdRow="2/3">
                                                    Date Of Birth:
                                                </div>
                                                <div class="text-xs text" gdRow="2/3">
                                                    <!-- {{ personDetails?.dateofBirth | date: 'MM/dd/yyyy' }}  -->
                                                    {{ this.personDetails?.dateofBirth? this.formattedDOB : "N/A"}}

                                                </div>
                                                <div class="text-xs text1" gdRow="3/-1">Gender:</div>
                                                <div class="text-xs text" gdRow="3/-1">
                                                    {{ personDetails?.gender }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="justify-start w-full " gdColumn="2 / -1" gdColumn.lt-md="1 / 2"
                                gdColumn.lt-sm="1">
                                <div class="pt-4 mx-2" fxLayout="column">
                                    <h6 class=" font-bold">Contact Information</h6>
                                    <div class="pt-2" gdColumns="1fr" gdGap="12px" exaiContainer>
                                        <div class="justify-start w-full">
                                            <div gdRows="1fr 1fr 1fr 1fr" gdGap="8px">
                                                <div class="text-xs text1" gdRow="1/2">Address:</div>
                                                <div class="text-xs text" gdRow="1/2">
                                                    {{ personDetails?.address }}
                                                </div>
                                                <div class="text-xs text1" gdRow="2/3">
                                                    <label> Phone Number:</label>
                                                </div>
                                                <div class="text-xs text" gdRow="2/3">
                                                    {{ personDetails?.phoneNumber }}
                                                </div>
                                                <div class="text-xs text1" gdRow="3/4">
                                                    <label> Email:</label>
                                                </div>
                                                <div class="text-xs text" gdRow="3/4">
                                                    {{ personDetails?.emailId }}
                                                </div>
                                                <div class="text-xs text1" gdRow="4/-1">
                                                    <label> SSN:</label>
                                                </div>
                                                <div class="text-xs text" gdRow="4/-1">
                                                    {{ personDetails?.ssn }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div></div>
                        </div>
                        <div class="flex justify-end pb-3 px-3">
                            <button class="btn-4 text-xs" mat-button type="button" *ngIf='disableEditProfileBtn'
                                (click)="editProfile()">
                                Edit Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="" gdRow="2/ -1" gdRow.lt-md="1fr" gdRow.lt-sm="1fr" *ngIf="personForm.length > 0">
            <h6 class="mx-1 pb-1 text">Previous Corrections</h6>
            <div fxLayout="row" fxLayoutGap="16px grid">
                <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                    *ngFor="let item of items">
                    <div class="card shadow-none cardBorder touch-auto overflow-auto" fxFlex="auto">
                        <div class="bg-color">
                            <div class="flex justify-between px-4 pt-1 pb-2" fxLayout="row">
                                <div class="">
                                    <h6 class="text_size1">
                                        <strong>Candidate Correction Form</strong>
                                    </h6>
                                </div>
                            </div>
                        </div>
                        <div fxLayout="column">
                            <h4 class="px-4 status text-xs text1">Current Status</h4>
                            <img src="{{ item.iconUrl }}" class="inline iconSize mx-4" />
                            <span class="t-xs ml-2 active2 -mt-3 mx-5 pl-8 pb-3">
                                {{ item.status }}
                            </span>

                            <span *ngIf="item.statusId !== formstatusEnum.Drafted"
                                class="t-xs ml-2 active2 -mt-3 mx-5 pl-8 pb-3">{{item.submittedDate | date: "MMMM dd ,yyyy"}} /{{item.submittedDate |date:'shortTime' }}
                                {{item.timeZoneAbbreviation}}</span>

                        </div>
                        <div class="mx-3 pb-4" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
                            exaiContainer>
                            <div gdColumn="1/-1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1/-1">
                                <div class="h4 status t-xs text1 pl-3" *ngIf="item.statusId != 7">Fields Requested for
                                    Change</div>
                                <div class="t-xs pl-3 pt-1 trunc" title="{{ item.fields }}">
                                    {{ item.fields }}
                                </div>
                                <div class="">
                                    <h6 class="text_size1"><strong></strong></h6>
                                </div>
                            </div>
                        </div>
                        <div class="mx-3 pb-4" gdColumns="1fr 1fr 1fr 1fr 1fr "
                            gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr"
                            exaiContainer *ngIf="item.status === 'Change Request' || item.status == 'Rejected'">
                            <div gdColumn="1/-1" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                                <div class="" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
                                    <div class="t-xs status px-3">
                                        {{item.status == 'Change Request' ? 'Change Request Reason' : 'Rejection Reason'
                                        }}
                                    </div>
                                </div>
                                <div class="status1 t-xs px-3">{{ item.comment }}</div>
                            </div>
                        </div>
                        <div fxLayout="row" class="pl-4 pb-2 pr-2" fxLayoutAlign="end center" fxLayoutGap="8px"
                            *ngIf="item.status == 'Drafted' || item.status === 'Change Request'">
                            <button mat-button color="var(--text-color2)" class="btn-4 text-xs"
                                (click)="editCorrectionForm(item)">
                                Edit Correction Form
                            </button>
                        </div>
                        <div fxLayout="row" class="pl-4 pb-2 pr-2" fxLayoutAlign="end center" fxLayoutGap="8px"
                            *ngIf="item.status == 'Pending' || item.status == 'Submitted'">
                            <button mat-button color="var(--text-color2)" class="btn-4 text-xs"
                                (click)="editCorrectionForm(item)">
                                View Summary
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- </div> -->
        <!-- </div>
      </div> -->
    </div>
</div>
<!-- </div> -->