<div>
    <button  *ngIf="!disabled" (click)="fileUpload()" [style.margin-bottom]="'0.6rem'" mat-button class="btn-2 text-xs">
        <!-- <img class="-mb-6 pt-3" src="assets/img/Icons/upload.svg" width="16px" height="15px"> -->
        <!-- <mat-icon class="text-color material-icons-outlined">cloud_upload</mat-icon> -->
        <span class="text-color mr-2 text-base material-icons-outlined">
            <mat-icon>cloud_upload</mat-icon>
            </span>
        <span class="text-color justify-items-center">{{displayKey}}</span>
    </button>
    <!-- <mat-form-field class="w-1/6 ml-3" appearance="outline" [style.position]="'relative'" [ngStyle]="{'display' : file != null && showFileList ? 'block' : 'none'}">
        <input matInput [formControl]="fileAttr" readonly />
        <div style="position:absolute;top:0px;right:-2.5rem">
            <button (click)="removeEvent()" mat-button class="hover:bg-gray-100 text-gray-800 font-semibold border border-gray-400 rounded  m-0.5 w-1/2">
                
                <mat-icon class="colorIcon">delete</mat-icon>
            </button>
            <button (click)="upload()" *ngIf="!autoUpload && url" mat-button class="hover:bg-gray-100 text-gray-800 font-semibold border border-gray-400 rounded shadow m-0.5 w-1/2">
                <mat-icon>upload</mat-icon>
            </button>
        </div>
        <input type="file" #fileInput [style.display]="'none'" (change)="uploadFileEvt($event.target.files)" [multiple]="multiple" [accept]="accept.join(',')" />
    </mat-form-field> -->
    <span class="" [style.position]="'relative'" [ngStyle]="{'display' : file != null && showFileList ? 'block' : 'none'}">
        <!-- <input matInput [formControl]="fileAttr" readonly /> -->
        <div>

            <p class="text-xs f-medium" *ngFor="let fileName of file;let fileIndex = index">
                <!-- <mat-icon class="text-base align-text-top">file_copy</mat-icon> -->
                {{fileName.split('|')[0]}}
                <button *ngIf="!disabled && fileName.split('|')[0] !=undefined && fileName.split('|')[0] !=null && fileName.split('|')[0] !=''" (click)="removeEvent(fileIndex)"
                    class="text-gray-800 font-semibold border-gray-400 rounded  m-0.5">
                    <mat-icon class="cursor-pointer text-base align-text-top" *ngIf="fileName.split('|')[0] !=undefined && fileName.split('|')[0] !=null && fileName.split('|')[0] !=''">close</mat-icon>
                </button>
                <button *ngIf="fileName" (click)="getToggleFileView()"
                    class=" text-gray-800 font-semibold border-gray-400 rounded  m-0.5 never-disabled">
                    <mat-icon class="cursor-pointer text-base align-text-top">visibility</mat-icon>
                </button>
               
                <app-file-auto-view *ngIf="toggleFileView" [fileName]="fileName" (toggleFileViewEvent)="getToggleFileView($event)"></app-file-auto-view>

                <!-- <button (click)="upload()" *ngIf="!autoUpload && url" mat-button class="hover:bg-gray-100 text-gray-800 font-semibold border border-gray-400 rounded shadow m-0.5 w-1/2">
                    <mat-icon>upload</mat-icon>
                </button> -->
            </p>
        </div>
        <input type="file" #fileInput [style.display]="'none'" (change)="uploadFileEvt($event.target.files)" [multiple]="multiple" [accept]="accept.join(',')" />
    </span>
</div>