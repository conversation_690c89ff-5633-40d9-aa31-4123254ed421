
import { Component, Inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import { getPracticeRegisteredExam, getRegisteredExam } from '../scheduled/state/scheduled.actions';
import { GlobalUserService } from 'src/app/core/global-user.service';
import moment from 'moment';
import { selectorGetRegisteredexam, selectorPracticeGetRegisteredexam } from '../scheduled/state/scheduled.selectors';
import { RegisteredExamsModel } from 'src/app/core/Dto/registered-exams.model';
import { HttpErrorResponse } from '@angular/common/http';
import { DatePipe } from '@angular/common';
import { ScheduledService } from '../scheduled/scheduled.service';
import { ConfirmationPopupComponent } from '../scheduled/confirmation-popup/confirmation-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { Excusedlist } from '../excused-absense/state/excused.model';
import { GrievanceFormService } from '../grievance-form/grievance-form.service';
import {FormTypes } from "src/app/core/Dto/enum";
import { ExaminationResultComponent } from '../examination-result/examination-result.component';

@Component({
  selector: 'exai-practice_exam',
  templateUrl: './practice.component.html',
  styleUrls: ['./practice.component.scss']
})
export class PracticeExamComponent {
  statusIcon = statusIcon;
  listExam: RegisteredExamsModel[];
  errors: any;
  exam: boolean = false;
  details: boolean = false;
  timeslot: boolean = false;
  registerExams: any

  constructor(private router: Router, private store: Store, public global: GlobalUserService, private dp: DatePipe, private _service: ScheduledService,private dialog:MatDialog,   private service: GrievanceFormService,

  ) {

    this.global.userDetails.subscribe((data: any) => {
      if (!data) return;
   
      this.store.dispatch<Action>(
        getPracticeRegisteredExam({ candidateId: this.global.candidateId })
      );
      setTimeout(() => {
        this.store.select(selectorGetRegisteredexam).subscribe(
          (data: RegisteredExamsModel[]) => {
            this.registerExams = data
          },
          (err: HttpErrorResponse) => {

          }
    
    
        );
        this.store.select(selectorPracticeGetRegisteredexam).subscribe(
          (data: RegisteredExamsModel[]) => {
            const exams = data;
            if (exams.length > 0) {
              this.exam = true;
              this.details = false;
              this.timeslot = true;

              let n = Intl.DateTimeFormat().resolvedOptions();
              const _exams = exams.map((ele) =>
                Object.assign(
                  {},
                  ele,
                  {
                    registeredDateTime: moment(ele.registeredDateTime)
                      .tz(n.timeZone)
                      .format("MMMM Do, YYYY / h:mm A"),
                  },
                  {
                    examDateTimenew: moment(ele.examDateTime).format("MM/DD/YYYY"),
                  },

                  {
                    examDatetime: this.dp.transform((ele.examDateTimeUtc), "MM/dd/YYYY", "+0000",)
                  }

                )
              );

              // Can't cancel and Reschedule the exam within 9 days start
              _exams.forEach((ele, i: number) => {
                if (ele.mode !== "Online") {
                  function isDateGreaterThan48Hours(inputDate) {
                    const currentDate = new Date();
                    const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                    const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
                    return diffInHours > 216;
                  }
                  const inputDate = new Date(ele.examDateTimeUtc);
                  const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
                  if (isGreaterThan48Hours) {
                    _exams[i].reschedule = true;
                    _exams[i].cancel = true;
                  } else {
                    _exams[i].reschedule = false;
                    _exams[i].cancel = false;
                  }
                }
                else if (ele.mode === "Online") {
                  function isDateGreaterThan48Hours(inputDate) {
                    const currentDate = new Date();
                    const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                    const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

                    return diffInHours > 48;
                  }
                  const inputDate = new Date(ele.examDateTimeUtc);
                  const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
                  if (isGreaterThan48Hours) {
                    _exams[i].reschedule = true;
                    _exams[i].cancel = true;
                  } else {
                    _exams[i].reschedule = false;
                    _exams[i].cancel = false;
                  }
                }
              });
              // Can't cancel and Reschedule the exam within 9 days end
              this.listExam = _exams;
            } else if (exams.length == 0) {
              this.details = true;
              this.exam = false;
              this.timeslot = false;
            }
          },
          (err: HttpErrorResponse) => {
            this.errors = err.error;
            this.details = true;
            this.exam = false;
            this.timeslot = false;
          }
        );
      }, 1500)
    });
  }
  clickRoute(id:number) {
   id == 1? this.router.navigateByUrl("practice_exam/register_practice"):this.router.navigateByUrl("/exam-scheduled/payment/2")
  }

  reschedule(event: any): void {
    this._service.PracticeInformation = event;
    this.router.navigateByUrl("practice_exam/register_practice");
  }

  absence(item: RegisteredExamsModel, personFormId: any): void {
    this.global.absensePersonEventId = item.id;
    let absenseForm: Excusedlist = {
      personFormId: personFormId,
      formTypeId: 0,
      formId: 0,
      examName: item.examName,
      name: "",
      state: "",
      eligiblityRoute: "",
      stateName: "",
      eligiblityRouteName: item.eligibilityRouteName,
      iconUrl: item.iconUrl,
      comment: "",
      examDate: item.examDateTime,
      submittedDate: "",
      status: item.examStatus,
      statusId: item.examStatusId,
      mode: item.mode,
      examId: item.examId,
      isExcuseAbsenceSubmitted: false,
      id: item.id,
    };
    this.service.viewabsense = absenseForm;
    
    if (personFormId == 0 || item.isExcuseAbsenceFilled == false) {
      // this.router.navigateByUrl("grievance-form/report-grievance");

      this.router.navigate([
        "absense-form",
        "Absence-form-view",
        FormTypes.ExcusedAbsense,
        this.global.candidateId,
        item.eligibilityRouteId,
        this.global.stateId,
        item.id,
        ''
      ]);
    } else {
      if (item.isExcuseAbsenceSubmitted == false) {
        // this.router.navigateByUrl("grievance-form/report-grievance");
        this.router.navigate([
          "absense-form",
          "Absence-form-view",
          FormTypes.ExcusedAbsense,
          this.global.candidateId,
          item.eligibilityRouteId,
          this.global.stateId,
          item.id,
          item.personFormId,
          0
        ]);
        
      } 
      else {
        // this.router.navigateByUrl("grievance-form/view-grievance");
        this.router.navigate([
          "absense-form",
          "Absence-form-view",
          FormTypes.ExcusedAbsense,
          this.global.candidateId,
          0,
          this.global.stateId,
          item.id,
          personFormId,
          0,
        ]);
      }
    }
  }

  getConfirmation(id: number) {
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: id,
    });
    dialogRef.afterClosed().subscribe((result) => { });
  }

  showResult(item) {
    const dialogRef = this.dialog.open(ExaminationResultComponent, {
        height:'100%',
        width:'935px',
        data: item
       });
    dialogRef.afterClosed().subscribe((result) => { });
  }
}

export enum statusIcon {
  "Waiting for Proctor" = "assets/img/Group 354.svg",
  "Exam Scheduled" = "assets/img/Group 355.svg",
  "Event Assigned" = "assets/img/Icons/approved.svg",
  "No Exam" = "assets/img/NoExam-Blue.svg",
  "Exam Cancelled" = "assets/img/Icons/approved.svg",
  "Event Rescheduled" = "assets/img/Icons/approved.svg",
  "Payment Pending" = "assets/img/Icons/pending.svg",
  "Event Completed" = "assets/img/Icons/approved.svg",
  "Added to cart" = 'assets/img/Icons/approved.svg'
}
