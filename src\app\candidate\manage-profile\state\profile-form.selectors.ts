import { createFeatureSelector, createSelector } from "@ngrx/store";
import { ProfileState } from "./profile-form.model";
import { MANAGE_PROFILE_STATE } from "./profile-form.state";

const getProfileState = createFeatureSelector<ProfileState>(MANAGE_PROFILE_STATE);


export const selectForm = createSelector(
  getProfileState, (state) => {
    return state.demographicForm;
  });

export const selectPersonDetails = createSelector(getProfileState, (state) => {
  return state.personDetails;
})

export const getPersonDetail = createSelector(getProfileState, (state) => {
  return state.personDetails;
})

export const getSubmitDemographicData = createSelector(getProfileState, (state) => {
  return state.submitDemographicform;
})

export const getSavedResId = createSelector(getProfileState, (state) => {
  return state.savedDemographicResponseId;
})

export const getCorrectionForm = createSelector(getProfileState, (state) => {
  return state.personForm;
})

export const correctionFormData = createSelector(getProfileState, (state) => {
  return state.getCorrectionData;
})

export const deletedCorrectionFormData = createSelector(getProfileState, (state) => {
  return state.deletedDemographicForm;
})

export const correctionLogs = createSelector(getProfileState, (state) => {
  return state.personFormLogs;
})

export const successUpdate = createSelector(getProfileState, (state) => {
  return state.updateSuccess;
})

export const disableEditProfile = createSelector(getProfileState, (state) => {
  return state.disableEditProfiles;
})

export const getUpcomingExams = createSelector(getProfileState, (state) => {
  return state.upcomingExams;
})