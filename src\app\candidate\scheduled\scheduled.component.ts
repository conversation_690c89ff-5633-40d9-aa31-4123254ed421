import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { HttpErrorResponse } from "@angular/common/http";
import { HttpService } from "src/app/core/http-services/http.service";
import { Action, Store } from "@ngrx/store";
import {
  getClearCart,
  getPersonForm,
  getRegisteredExam,
  getShowRegisterExam,
} from "./state/scheduled.actions";
import {
  selectorclearCartData,
  selectorGetPersonForm,
  selectorGetRegisteredexam,
  selectorGetRescheduledResponse,
  selectorShowRegisterExam$,
} from "./state/scheduled.selectors";
import { MatSnackBar } from "@angular/material/snack-bar";
import { ConfirmationPopupComponent } from "./confirmation-popup/confirmation-popup.component";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { ScheduledService } from "./scheduled.service";
import { GlobalUserService } from "src/app/core/global-user.service";
import { GrievanceFormService } from "../grievance-form/grievance-form.service";
import * as moment from "moment/moment";
import "moment-timezone";
import { FormTypes } from "src/app/core/Dto/enum";
import { ClearCartResponse, clearCartResponse, Exam, PersonForm } from "./state/models/Exam";
import { RegisteredExamsModel } from "src/app/core/Dto/registered-exams.model";
import {
  GrevienceModuleState,
  GrievanceFormsList,
} from "../grievance-form/state/grievance.model";
import { clearGrievanceState, loadAll } from "../grievance-form/state/grievance.actions";
import { Observable } from "rxjs";
import { selectorLoadAllSuccess } from "../grievance-form/state/grievance.selectors";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { SnackbarService } from "src/app/core/snackbar.service";
import { ImgPopUpComponent } from "src/app/core/img-pop-up/img-pop-up.component";
import { get_cartItems } from "../state/shared/shared.selectors";
import { CartItem } from "./state/models/cartItem";
import { getCartItems } from "../state/shared/shared.actions";
import { toUpdateSnn } from "../dashboard/dashboard-static";
import { ConfirmPopupComponent } from "../dashboard/confirm-popup/confirm-popup.component";
import { TestDirectionsDialogComponent } from "./test-directions-dialog/test-directions-dialog.component";
import { DatePipe } from "@angular/common";
import { Excusedlist } from "../excused-absense/state/excused.model";

@Component({
  selector: "exai-scheduled",
  templateUrl: "./scheduled.component.html",
  styleUrls: ["./scheduled.component.scss"],
})
export class ScheduledComponent implements OnInit, OnDestroy {
  statusIcon = statusIcon;
  rescheduleallow = AllowReschedulestatus;
  gridColumns = 4;
  listExam: RegisteredExamsModel[];
  errors: any;
  exam: boolean = false;
  details: boolean = false;
  timeslot: boolean = false;
  examTitle = "Colorado Nurse Aide Testing Application - E1";
  scheduledExamServices: any;
  exams: Array<{ id: number; name: string; information?: any }> = [];
  scheduledExamServices1: any;
  currentStatusDateWritten;
  RescheduleButton: boolean = true
  examDate;
  examname;
  button: boolean;
  // disablecancelBtnDash: boolean = true;
  // disableRescheduleBtn: boolean = true
  AccomodationCount
  NotAllowScheduleforCheating: boolean = false
  public disableSubmit: boolean = false;
  grivieanceForm$: Observable<Array<GrievanceFormsList>>;
  showRegisterExam: ShowRegisterExamModel;
  bothApproved: boolean = false;
  information: any;
  statusExam: any;
  registerExams: any
  exampending: PersonForm;
  cart: CartItem[]
  constructor(
    private httpService: HttpService,
    private router: Router,
    private store: Store,
    private _snackbar: MatSnackBar,
    private dialog: MatDialog,
    private _service: ScheduledService,
    public global: GlobalUserService,
    private service: GrievanceFormService,
    private snackbar: SnackbarService,
    private dp: DatePipe
  ) {

    this.getIncidentstatus()

    
    this.store.dispatch<Action>(
      getRegisteredExam({ candidateId: this.global.candidateId })
    );
    this.store.dispatch(
      getPersonForm({
        candidateId: this.global.candidateId,
        formTypeId1: FormTypes.Accomodation,
        formTypeId2: FormTypes.Application,
      })
    );
    this.global.userDetails.subscribe((data: any) => {
      if (!data) return;
      this.subscriptions();
    });
    this.store.dispatch<Action>(
      getShowRegisterExam({ personTenantRoleId: this.global.candidateId })
    );
 
  }

  ngOnInit(): void { this.getConfirmationSSN() }
  subscriptions(): void {


    this.store.select(selectorGetRescheduledResponse).subscribe((res) => { });
    this.store.select(selectorGetRegisteredexam).subscribe(
      (data: RegisteredExamsModel[]) => {
        this.registerExams = data
        const exams = data;
        if (exams.length > 0) {
          this.exam = true;
          this.details = false;
          this.timeslot = true;

          let n = Intl.DateTimeFormat().resolvedOptions();
          const _exams = exams.map((ele) =>
            Object.assign(
              {},
              ele,
              {
                registeredDateTime: moment(ele.registeredDateTime)
                  .tz(n.timeZone)
                  .format("MMMM Do, YYYY / h:mm A"),
              },
              {
                examDateTimenew: moment(ele.examDateTime).format("MM/DD/YYYY"),
              },
              {
                examDateTimePDT: moment(ele.examDateTimeUtc)
                  .tz(ele.timeZoneAbbreviation)
                  .format("h:mm a z"),
              },
              {
                examDatetime: this.dp.transform((ele.examDateTimeUtc), "MM/dd/YYYY", "+0000",)
              }

            )
          );

          // Can't cancel and Reschedule the exam within 9 days start
          _exams.forEach((ele, i: number) => {
            if (ele.mode !== "Online") {
              ele.examStatus = (ele.examStatusId === 97 || ele.examStatusId === 88 || ele.examStatusId == 89) && ele.isMisconductValidate == false?"Exam Under Review":ele.isMisconductValidate == true && ele.examStatusId !=96 && ele.examStatusId !=8  ?"Confirmed violation of exam rules":ele.examStatus
              function isDateGreaterThan48Hours(inputDate) {
                const currentDate = new Date();
                const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
                return diffInHours > 216;
              }
              const inputDate = new Date(ele.examDateTimeUtc);
              const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
              if (isGreaterThan48Hours) {
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }


              /// showing the show result view after 24 hours for all test center///

              function isDateGreaterThan24Hours(inputDate: string) {
                const datePipe = new DatePipe('en-US');

                // Parse the input date string into a Date object
                const parsedInputDate = new Date(datePipe.transform(inputDate, 'yyyy-MM-ddTHH:mm:ss'));
                const today = new Date();
                // Calculate the difference in milliseconds between today and parsedInputDate
                const timeDifferenceInMilliseconds = today.getTime() - parsedInputDate.getTime();
                const timeDifference = today.getTime() - parsedInputDate.getTime();
                const twentyFourHoursInMilliseconds = 24 * 60 * 60 * 1000;
                const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
                const timeDifference1 = parsedInputDate.getTime() - today.getTime();
                const isGreaterThan24Hours = timeDifferenceInMilliseconds > twentyFourHoursInMilliseconds;
                const isAllowed = daysDifference >= 30 || timeDifference1 >= 30 ? false : true


                return { isGreaterThan24Hours, isAllowed };

              }
              const inputDates = ele.examDateTimeUtc // Replace with your input date
            if(ele.examStatusId === 10){
              const result = isDateGreaterThan24Hours(inputDates)
              if (result.isAllowed && result.isGreaterThan24Hours) {
                _exams[i].allowShowResult = true;
                _exams[i].allowGrievance = true
              } else if (result.isGreaterThan24Hours && result.isAllowed == false) {
                _exams[i].allowShowResult = true;
              }
              else {
                _exams[i].allowShowResult = false;
                _exams[i].allowGrievance = false

              }
            }
          

            }
            else if (ele.mode === "Online") {
              ele.examStatus = (ele.examStatusId === 97 || ele.examStatusId === 88 || ele.examStatusId == 89) && ele.isMisconductValidate == false?"Exam Under Review":ele.isMisconductValidate == true && ele.examStatusId !=96 && ele.examStatusId !=8  ?"Confirmed violation of exam rules":ele.examStatus
              function isDateGreaterThan48Hours(inputDate) {
                const currentDate = new Date();
                const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

                return diffInHours > 48;
              }
              const inputDate = new Date(ele.examDateTimeUtc);
              const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
              if (isGreaterThan48Hours) {
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }
            }











          });
          // Can't cancel and Reschedule the exam within 9 days end
          this.listExam = _exams;
        } else if (exams.length == 0) {
          this.details = true;
          this.exam = false;
          this.timeslot = false;
        }

        // showing a Reschedule button when exam cancelled
        let a = this.registerExams.filter(x => (x.mode !== 'Test Center' && x.examStatusId === this.global.ScheduledStatusId))
        this.RescheduleButton = a.length > 0 ? false : true
      },
      (err: HttpErrorResponse) => {
        this.errors = err.error;
        this.details = true;
        this.exam = false;
        this.timeslot = false;
      }


    );

    this.store
      .select(selectorShowRegisterExam$)
      .subscribe((data: ShowRegisterExamModel) => {
        if (data) {
          this.showRegisterExam = data;
          this.disableSubmit = (!data.showRegister);
          this.disableSubmit = (!data.showRegister) ? !(data.isAccommodationApproved && data?.isApplicationApproved) : this.disableSubmit;
        }
      });

    this.store
      .select(selectorGetPersonForm)
      .subscribe((personForms: PersonForm[]) => {
        if (personForms) {
          this.AccomodationCount = personForms.filter(x => x.formTypeId == FormTypes.Accomodation && x.status == "Pending").length;
        }
      })

           // }
           this.bothApproved = this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false ?false:this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved?false:true
           this.errors = (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false) || (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved)? 'Your application is approved and you may now register for the exam': 'Approved Application Required'

    this.store.select(get_cartItems).subscribe((cartItems) => {
      this.cart = cartItems
    })



  }

  getConfirmation(id: number) {
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: id,

    });
    dialogRef.afterClosed().subscribe((result) => { });
  }
  getConfirmation1(item) {
    const dialogRef = this.dialog.open(ImgPopUpComponent, { data: item });
    dialogRef.afterClosed().subscribe((result) => { });
  }

  viewGrievance(item: RegisteredExamsModel, personFormId: any): void {
    this.global.grievancePersonEventId = item.id;
    let grievanceForm: GrievanceFormsList = {
      personFormId: personFormId,
      formTypeId: 0,
      formId: 0,
      examName: item.examName,
      name: "",
      state: "",
      eligiblityRoute: "",
      stateName: "",
      eligiblityRouteName: item.eligibilityRouteName,
      iconUrl: item.iconUrl,
      comment: "",
      examDate: item.examDateTime,
      submittedDate: "",
      status: item.examStatus,
      statusId: item.examStatusId,
      mode: item.mode,
      examId: item.examId,
      isGrievanceFormSubmitted: false,
      id: item.id,
      appealResponse: [],
      grievanceResponse: []
    };
    this.service.viewGrievance = grievanceForm;

    if (personFormId == 0 || item.isGrievanceFilled == false) {
      // this.router.navigateByUrl("grievance-form/report-grievance");

      this.router.navigate([
        "grievance-form",
        "report-grievance",
        FormTypes.Grievance,
        this.global.candidateId,
        0,
        this.global.stateId,
        item.id,
        item.mode
      ]);
    } else {
      if (item.isGrievanceFormSubmitted == false) {
        // this.router.navigateByUrl("grievance-form/report-grievance");
        this.router.navigate([
          "grievance-form",
          "report-grievance",
          FormTypes.Grievance,
          this.global.candidateId,
          item.eligibilityRouteId,
          this.global.stateId,
          item.id,
          item.personFormId,
          0
        ]);

      }
      else {
        // this.router.navigateByUrl("grievance-form/view-grievance");
        this.router.navigate([
          "grievance-form",
          "report-grievance",
          FormTypes.Grievance,
          this.global.candidateId,
          0,
          this.global.stateId,
          item.id,
          personFormId,
          0,
        ]);
      }
    }
  }
  absence(item: RegisteredExamsModel, personFormId: any): void {

    this.global.absensePersonEventId = item.id;
    let absenseForm: Excusedlist = {
      personFormId: personFormId,
      formTypeId: 0,
      formId: 0,
      examName: item.examName,
      name: "",
      state: "",
      eligiblityRoute: "",
      stateName: "",
      eligiblityRouteName: item.eligibilityRouteName,
      iconUrl: item.iconUrl,
      comment: "",
      examDate: item.examDateTime,
      submittedDate: "",
      status: item.examStatus,
      statusId: item.examStatusId,
      mode: item.mode,
      examId: item.examId,
      isExcuseAbsenceSubmitted: false,
      id: item.id,
    };
    this.service.viewabsense = absenseForm;

    if (personFormId == 0 || item.isExcuseAbsenceFilled == false) {
      // this.router.navigateByUrl("grievance-form/report-grievance");

      this.router.navigate([
        "absense-form",
        "Absence-form-view",
        FormTypes.ExcusedAbsense,
        this.global.candidateId,
        0,
        this.global.stateId,
        item.id,
        ''
      ]);
    } else {
      if (item.isExcuseAbsenceSubmitted == false) {
        // this.router.navigateByUrl("grievance-form/report-grievance");
        this.router.navigate([
          "absense-form",
          "Absence-form-view",
          FormTypes.ExcusedAbsense,
          this.global.candidateId,
          item.eligibilityRouteId,
          this.global.stateId,
          item.id,
          item.personFormId,
          0
        ]);

      }
      else {
        // this.router.navigateByUrl("grievance-form/view-grievance");
        this.router.navigate([
          "absense-form",
          "Absence-form-view",
          FormTypes.ExcusedAbsense,
          this.global.candidateId,
          0,
          this.global.stateId,
          item.id,
          personFormId,
          0,
        ]);
      }
    }
  }


  clickRoute() {

    this.router.navigateByUrl("exam-scheduled/register");

  }
  clearCart() {

    let body = {}
    this.store.dispatch(getClearCart({ personId: this.global.userId, body }))
    this.store.select(selectorclearCartData).subscribe((data: ClearCartResponse) => {
      if (data) {
        setTimeout(() => {
          this.store.dispatch(getRegisteredExam({ candidateId: this.global.candidateId }))
          this.store.dispatch(getCartItems({ personTenantRoleId: this.global.candidateId }))
          this.snackbar.callSnackbaronSuccess("Cleared CartItems SuccessFully")
        }, 2000)
      }
    })
  }

  reschedule(event: any): void {

      this._service.rescheduleInformation = event;
      this.global.No_slot_avaiable.next(null)
      this.router.navigateByUrl("exam-scheduled/register");
    

  }
  payNow() {
    if (this.cart.length > 0) {
      this.router.navigateByUrl("exam-scheduled/payment/1");
    } else {
      this.snackbar.callSnackbaronWarning("Please contact Credentia Support Team");
    }
  }

  getConfirmationSSN() {
    this.httpService.getAccountPersonDetails().subscribe((personDetails: any) => {
      var stateSSNNotMandatory = ['PA']
      if (personDetails.fullSSN.length < 9 && !(stateSSNNotMandatory.includes(personDetails.clientStateCode))) {
        const dialogRef = this.dialog.open(ConfirmPopupComponent, {
          disableClose: true,
          data: { ssn: personDetails },
        });
      }
    })
  }

  getIncidentstatus() {
    this.httpService.getIncidentStatus(this.global.userDetails.value.personId).subscribe((data: boolean) => {
      if (data) {
        this.NotAllowScheduleforCheating = data
      }
    })
  }

  openTestCenterDirections(data) {
    const dialogRef = this.dialog.open(TestDirectionsDialogComponent, { panelClass: ['test-center'], data });
    dialogRef.afterClosed().subscribe((result) => { });
  }

  enableTestCenterDirections(item): boolean {
    if (!item) return false;
    const { mode, examDateTime } = item;
    if (!mode || !examDateTime) return false;
    const today = new Date()
    let newDate = this.dp.transform(today, 'yyyy-MM-dd');
    let ExamDate = this.dp.transform(examDateTime, 'yyyy-MM-dd');
    if (mode == 'Test Center' && ExamDate >= newDate) return true;
    return false;
  }

  ngOnDestroy() {
    // this.service.viewGrievance = null;
  }
}

export enum statusIcon {
  "Waiting for Proctor" = "assets/img/Group 354.svg",
  "Exam Scheduled" = "assets/img/Group 355.svg",
  "Event Assigned" = "assets/img/Icons/approved.svg",
  "No Exam" = "assets/img/NoExam-Blue.svg",
  "Exam Cancelled" = "assets/img/Icons/approved.svg",
  "Event Rescheduled" = "assets/img/Icons/approved.svg",
  "Payment Pending" = "assets/img/Icons/pending.svg",
  "Event Completed" = "assets/img/Icons/approved.svg",
}
export const AllowReschedulestatus=[79,80,81,84,85,86,87,77,78,96]
