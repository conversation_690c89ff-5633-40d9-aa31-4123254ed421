import { DatePipe } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import { id } from 'date-fns/locale';
import moment from 'moment';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http-services/http.service';
import { LanguageService } from 'src/app/core/language.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { cancelExam, cancelPracticeExam, getPracticeRegisteredExam, getRegisteredExam } from '../state/scheduled.actions';
import { selectorGetIsCancelled, selectorGetPracticeIsCancelled } from '../state/scheduled.selectors';
import { ScheduledState } from '../state/scheduled.state';
import { ExamName } from '../state/models/cart';

@Component({
  selector: 'exai-confirmation-popup',
  templateUrl: './confirmation-popup.component.html',
  styleUrls: ['./confirmation-popup.component.scss'],
})
export class ConfirmationPopupComponent implements OnInit {
  id: any;
  mode:string
  examName: any;
  time 
  Date
  candidateName:string
  testcenterName:string
  examTime:string
  timezoneAbbrevation:string
  listExam: any = [];
  constructor(
    private router: Router,
    private dialogRef: MatDialogRef<ConfirmationPopupComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: any,
    public lngSrvc: LanguageService,
    private store: Store,
    private _snackbar: MatSnackBar,
    private global: GlobalUserService,
    private services: SnackbarService,
    private http:HttpService
  ) {
    this.id = data.id;
    this.examName = data.examName;
    this.mode = data.mode;
    this.candidateName = data.candidateName;
    this.testcenterName =data.testCenterDetails.testCenterName;
    this.examTime = data.examDateTime;
    this.timezoneAbbrevation = data.timeZoneAbbreviation;
  }

  ngOnInit(): void { 
  }

  cancel() {
    this.dialogRef.close();
  }

  public cancelledExam(item): void {
    
    ExamName.includes(this.data.eligibilityRouteId)?this.store.dispatch<Action>(
      cancelPracticeExam({
        examScheduleId: this.id,
        candidateId: this.global.candidateId,
      })
    ): this.store.dispatch<Action>(
        cancelExam({
          examScheduleId: this.id,
          candidateId: this.global.candidateId,
        })
      );
    ExamName.includes(this.data.eligibilityRouteId)?this.store.select(selectorGetPracticeIsCancelled).subscribe((iscancelled) => {
      if (iscancelled != null) {
        var datePipe = new DatePipe("en-US");
        this.Date = moment(this.examTime).format("MM/DD/YYYY")
        this.time =datePipe.transform(this.examTime, 'shortTime','+0000')
        let body={
          body: ` ${this.mode} ${this.examName} scheduled ${this.testcenterName? " for " + this.testcenterName:""} on ${this.Date} at ${this.time} ${this.timezoneAbbrevation} was cancelled by ${this.global.roleName}`,
          candidateId:this.global.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: `${this.mode} ${this.examName} Cancelled`,
          userId:this.global.candidateId,
          userName:this.candidateName,
        }
        this.http.getAddnotes(body).subscribe(data=>{
        })
        setTimeout(() => {
          this.store.dispatch<Action>(
            getPracticeRegisteredExam({ candidateId: this.global.candidateId })
          );
        }, 1000);
      }
    }):  this.store.select(selectorGetIsCancelled).subscribe((iscancelled) => {
      if (iscancelled != null) {
        var datePipe = new DatePipe("en-US");
        this.Date = moment(this.examTime).format("MM/DD/YYYY")
        this.time =datePipe.transform(this.examTime, 'shortTime','+0000')
        let body={
          body: ` ${this.mode} ${this.examName} scheduled ${this.testcenterName? " for " + this.testcenterName:""} on ${this.Date} at ${this.time} ${this.timezoneAbbrevation} was cancelled by ${this.global.roleName}`,
          candidateId:this.global.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: `${this.mode} ${this.examName} Cancelled`,
          userId:this.global.candidateId,
          userName:this.candidateName,
        }
        this.http.getAddnotes(body).subscribe(data=>{
        })
        setTimeout(() => {
          this.store.dispatch<Action>(
            getRegisteredExam({ candidateId: this.global.candidateId })
          );
        }, 1000);
      }
    });









  }










}
