import { Component, Input, OnInit } from '@angular/core';
import { sectionCompleteEvent } from 'src/app/core/examroom-formbuilder/form-builder.types';
import { HttpService } from 'src/app/core/http-services/http.service';

@Component({
  selector: 'registry-form-progress-bar',
  templateUrl: './progress-bar.component.html',
  styleUrls: ['./progress-bar.component.scss'],
})

export class RegistryProgressBarComponent implements OnInit {

  constructor(private http: HttpService) { }

  @Input() sections?:Array<sectionCompleteEvent> = []
  ngOnInit(): void {
  }
  trimString(string){
    return string.replace(/\s/g, '').length > 0
  }
}