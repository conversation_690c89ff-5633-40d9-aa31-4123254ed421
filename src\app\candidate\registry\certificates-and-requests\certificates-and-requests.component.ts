import { HostListener } from "@angular/core";
import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { Store, Action } from '@ngrx/store';
// import { CdkDragDrop } from "@angular/cdk/drag-drop";


import { GlobalUserService } from './../../../core/global-user.service';
import {
  clearForm,
  DO_Check_Registry,
  loadAllCertificates,
  loadAllRequests,
  makerequestnull,
} from '../state/registry.actions';
import {
  CertificateModel,
  RegistryModuleState,
  RegistryStatus,
  RequestModel,
  StateModel
} from './../state/registry.model';
import {
  selectorDo_check,
  selectorLoadAllCertificates,
  selectorLoadAllRequests,
} from '../state/registry.selectors';
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { FormTypes } from "src/app/core/Dto/enum";
import { StateList } from "../../forms-wrapper/forms-wrapper.types";
import { DatePipe } from "@angular/common";

@Component({
  selector: 'exai-certificates-and-requests',
  templateUrl: './certificates-and-requests.component.html',
  styleUrls: ['./certificates-and-requests.component.scss']
})
export class CertificatesAndRequestsComponent implements OnInit {
  screenHeight: number;
  screenWidth: number;

  disableDragAndDrop: boolean = false;

  certificates$: Observable<Array<CertificateModel>>
  listCertificates: CertificateModel[] = [];
  filteredCertificates: CertificateModel[] = [];

  requests$: Observable<Array<RequestModel>>
  listRequests: RequestModel[] = [];

  textForSearch: string = "";
  Reciporating: boolean = false
  ReciporatingMACE: boolean = false

  userHasCertificate: boolean = false;

  constructor(private global: GlobalUserService, private store: Store<RegistryModuleState>, private dialog: MatDialog, private router: Router,private datePipe:DatePipe) {
    this.global.userDetails.subscribe((data: any) => {
      if (!data) return;
      this.subscriptions();
    })
  }


  ngOnInit(): void {
    this.store.dispatch<Action>(clearForm())
    this.getCertificates();
  
  }

  /**
   * 
   * Subscribing to actions : loadAllCertificates, loadAllRequests
   * @memberof CertificatesAndRequestsComponent
   */
  subscriptions(): void {
  }

  tabClick(event) {
    if (event.tab.textLabel === "Requests") {
      this.checkIfUpdatedRequests();
      this.getRequests();
    } else {
      this.checkIfUpdatedRequests();
      this.getCertificates();
    }
  }

  checkIfUpdatedRequests() {
    this.store.select(selectorLoadAllRequests).subscribe((data: Array<any>) => {
      this.listRequests = data;
    }, (err: HttpErrorResponse) => {
    })
  }

  getCertificates() {
    this.store.dispatch<Action>(loadAllCertificates({ pageNumber: 1, pageSize: 10, personId: this.global.userId }))
    this.certificates$ = this.store.select(selectorLoadAllCertificates)
    this.certificates$.subscribe((data: any) => {
      if (data != null && data != undefined) {
        this.listCertificates = this.filteredCertificates = data.result;
        this.listCertificates?.length > 0 ? this.userHasCertificate = true : this.userHasCertificate = false;
        if (this.listCertificates != null && this.listCertificates != undefined) {
         this.HideReciporatingButton()
        }
      }

    }, (err: HttpErrorResponse) => {
    })
  }

  HideReciporatingButton() {
    if(this.global.stateId == StateList.PA || this.global.stateId == StateList.SC){
      if (this.listCertificates.length > 0 && (this.global.stateId == StateList.PA || this.global.stateId == StateList.SC)) {
        this.store.dispatch<Action>(DO_Check_Registry({ candidateId: this.global.candidateId }))
        this.store.select(selectorDo_check).subscribe(status => {
          if (status) {
            this.Reciporating = status && this.listCertificates.length > 0 && this.global.stateId == StateList.PA ? false : true
          }else{
            let ReciporatingRecords = this.listCertificates.filter((x)=>{
              if((x.CertType == 1 && !ExamRenewal.includes(x.RegistryName)) || (x.CertType == 4 && x.CertificateType =="Reciprocity") ){
               return x
              }
           })
           let ReciporatingMACERecords = this.listCertificates.filter((x)=>{
            if((x.CertType == 1 && ExamRenewal.includes(x.RegistryName)) || (x.CertificateType =="MACE-Reciprocity" && x.CertType == 14)){
             return x
            }
         })
            this.Reciporating = ReciporatingRecords.length > 0?false:true
            this.ReciporatingMACE = this.global.stateId == StateList.SC && ReciporatingMACERecords.length == 0 ?true:false
          }
        })
      }
     else if(this.listCertificates.length == 0){
        this.Reciporating = true
        this.ReciporatingMACE = this.global.stateId == StateList.SC ?true:false
      }
    } 
    else if(this.global.stateId == StateList.DC || this.global.stateId == StateList.MS ){
      let Certificate = this.listCertificates.filter((x)=>{
          return RegistryStatus.includes(x.RegistryStatusId)
      })
      this.Reciporating = Certificate.length > 0?false:true
    }
    else{
      this.Reciporating = true
    }
  
  }
  getRequests() {
    this.store.dispatch<Action>(makerequestnull());
    this.store.dispatch<Action>(loadAllRequests({ candidateId: this.global.candidateId }));
    this.store.select(selectorLoadAllRequests).subscribe((data: Array<any>) => {
      if (data) {
        this.listRequests = data;

      }


    }, (err: HttpErrorResponse) => {
    })
  }

  /**
   * 
   * Used in search input. Filters certificates by Certificate number and Eligibility route.
   * @memberof CertificatesAndRequestsComponent
   */
  searchFilter() {
    this.filteredCertificates = [];
    this.listCertificates.forEach(element => {
      if (element.CertNumber.includes(this.textForSearch) || element.EligibilityRoute.includes(this.textForSearch))
        this.filteredCertificates.push(element);
    })
  }


  openStatesDialog(Value) {
    Value!=null && Value !=''?this.router.navigate(['registry', 'reciprocity-form', FormTypes.ReciporatingSCMA, this.global.candidateId, 0, this.global.stateId, this.global.stateId,'']):this.router.navigate(['registry', 'reciprocity-form', FormTypes.CertificateReciprocity, this.global.candidateId, 0, this.global.stateId, this.global.stateId,'']);
  }

}

export const ExamRenewal =[
  'M1 - South Carolina State Approved Medication Assistant Trained Candidate',
 "M2- Lapsed or Expired less than 24 months South Carolina Medication Assistant Registry Applicants"
]