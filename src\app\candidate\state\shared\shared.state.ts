import { DecodedIdentityToken, UserDetails } from "../../candiate.types";
import { CartItem } from "../../scheduled/state/models/cartItem";

export interface SharedState {
  showLoading: boolean;
  errorMessage: string;
  cartItems:CartItem[],
  userdetails: UserDetails,
  decodeInfo: DecodedIdentityToken
  PracticecartItems:CartItem[]
}

export const initialState: SharedState = {
  showLoading: false,
  errorMessage: '',
  cartItems:[],
  userdetails:null,
  decodeInfo:null,
  PracticecartItems:null
};
