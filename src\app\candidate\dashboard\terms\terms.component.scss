// Terms and policies
.verticalLine {
    height: inherit;
    border-right: 0.6px solid var(--card-border);
    padding-right: 0.75em;
}

.lastUpdated {
    text-align: end;
    color: var(--login-icons);
}

.header {
    height: inherit;
    overflow: auto;
    height: calc(500px - 48px - 24px);
    color: var(--header);
}

.termsContent {
    // height: calc(100vh - 30vh);
}

.terms1,
.terms2,
.terms3,
.terms4 {
    color: var(--forgot-password-heading);
    &:hover {
        color: var(--primary);
        font-weight: 500;
        cursor: pointer;
    }
}

.terms1.active {
    color: var(--primary);
    font-weight: 500;
}

.terms2.active {
    color: var(--primary);
    font-weight: 500;
}

.terms3.active {
    color: var(--primary);
    font-weight: 500;
}

.terms4.active {
    color: var(--primary);
    font-weight: 500;
}

.closeDialog {
    font-size: large;
    color: var(--login-icons);
}
.custom {
    font-weight: 600!important;
    font-size: 18px;
    color: #015991;
}

.custom1 {
    font-size: 14px;
    font-weight: 500;
    color: #0076c1;
}

.link1 {
    color : #0076c1;
}

// p {
//     // text-align: justify!important;
//     font-size: 12px;
//     color: var(--card-border);
// }

li,ul,p {
    font-size: 12px;
    color: var(--card-border);
}
.text-xs{
    font-size: 14px;
    margin-top: 0 !important;
}

.tx-xs1{
    font-size: 18px;
}

.textDeccoration{
    text-decoration: underline;
}

.hrzl {
    border: 0.1px solid #0063a0e0;
    width: 100%;
    margin-bottom: 8px;
}

br {
    margin-bottom: 0.25rem!important;
}
