import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { skip } from 'rxjs/operators';
import { sectionCompleteEvent } from 'src/app/core/examroom-formbuilder/form-builder.types';

@Component({
  selector: 'form-progress-bar',
  templateUrl: './progress-bar.component.html',
  styleUrls: ['./progress-bar.component.scss'],
})

export class ProgressBarComponent {

  constructor(
    private store: Store,
    private router: Router) { }

  @Input() sections?:Array<sectionCompleteEvent> = []
}