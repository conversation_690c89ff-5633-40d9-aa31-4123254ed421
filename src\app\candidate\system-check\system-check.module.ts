import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SystemCheckComponent } from './system-check.component';
import { DevicesComponent } from './devices/devices.component';
import { CheckComponent } from './check/check.component';
import { ReportComponent } from './report/report.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ContainerModule } from '../../../@exai/directives/container/container.module';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

const COMPONENTS = [
  SystemCheckComponent,
  DevicesComponent,
  CheckComponent,
  ReportComponent
]

@NgModule({
  declarations: [
    ...COMPONENTS
  ],
  exports: [
    ...COMPONENTS
  ],
  imports: [
    CommonModule,
    FlexLayoutModule,
    ContainerModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule
  ],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
})
export class SystemCheckModule { }
