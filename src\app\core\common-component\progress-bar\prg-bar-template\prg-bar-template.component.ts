import { Component, Input, OnInit } from "@angular/core";
import { PersonFormLog, prgLogCandi } from "../progress-bar.types";

@Component({
  selector: "app-prg-bar-template",
  templateUrl: "./prg-bar-template.component.html",
  styleUrls: ["./prg-bar-template.component.scss"],
})
export class PrgBarTemplateComponent implements OnInit {
  isTwo: boolean;

  constructor() {}
  @Input() header: string;
  @Input() performlogs: prgLogCandi[] | PersonFormLog[] | any;
  panelOpenState = false;
  ngOnInit(): void {
    if (this.performlogs[0].logs) {
      this.isTwo = true;
    }
  }
}
