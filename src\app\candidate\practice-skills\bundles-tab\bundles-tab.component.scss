
.title{
    color: #1f2937;;
}
.card-content {
  background-color: #FAFAFA;
  color: var(--text-color4);
  border-radius: 4px;
  border-width: 0.3px;
  opacity: 1;
  border-color: #e5e5e5;
}

.meta-label {
  color: var(--text-edit);
}

.meta-value {
  color: var(--text-color2);
}

.try-now-btn {
  color: var(--text-color2);
  background-color: #E5F1F9;
  border-radius: 4px;
  padding:8px 14px 8px 14px;
}

.try-now-btn:hover {
  opacity: 0.9;
}


//For diff. view port cards adjusting 
.bundle-grid {
  background-color: #F9F9F9;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  padding: 0 1rem;
}

.card_border{
  border-radius: 4px;
  color:white;
  opacity: 1;
  border-color:   #7d7d7d66;
  border-width: 0.5px;
}

/* Default: Mobile */
.bundle-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr); /* Mobile */
  gap: 1rem;
}

/* Tablet: 640px to 899px */
@media (min-width: 640px) and (max-width: 899px) {
  .bundle-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Small Laptop: 900px to 1023px */
@media (min-width: 900px) and (max-width: 1023px) {
  .bundle-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Laptop/Desktop: 1024px to 1439px */
@media (min-width: 1024px) and (max-width: 1439px) {
  .bundle-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

/* Large Screen: 1440px and above */
@media (min-width: 1440px) {
  .bundle-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

.menu_pop{
  background-color: #ffffff;
  border: 1px solid #e5e7eb; 
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.menu_pop_item{
 color: #4b5563; 
  cursor: pointer;
  border-bottom: 1px solid #c1c3c7; ;
  transition: background-color 0.2s;
    &:hover {
    background-color: #f3f4f6; 
    color: var(--text-color2);
  }
}
.icon {
    color: #a1a8af;
    font-size: 18px;
}
.menu-toggle{
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;

  &:hover {
  background-color: #d1e4f0;
  }
}
.bundle-includes {
  padding-left: 1rem;

  .includes-list {
    list-style-type: disc;
    padding-left: 1.25rem; 
    font-size: 0.75rem; 
    color: #4b5563; 

    li {
      margin-bottom: 0.25rem;
    }
  }
}
