<h1 mat-dialog-title class="active2 justify-center">Test Center Directions</h1>
<div mat-dialog-content>
  <div class="content pt-2" *ngIf="directions; else: loading">
    <div gdColumns="1fr 1fr 1fr 1fr" gdGap="0.5rem">
      <div class="flex flex-col">
        <span class="active2">Adress</span>
        <span>{{ directions?.testSiteAddress }}</span>
      </div>
      <div class="flex flex-col">
        <span class="active2">City</span>
        <span>{{ directions?.city }}</span>
      </div>
      <div class="flex flex-col">
        <span class="active2">State</span>
        <span>{{ directions?.state }}</span>
      </div>
      <div class="flex flex-col">
        <span class="active2">Postal Code</span>
        <span>{{ directions?.postalCode }}</span>
      </div>
    </div>

    <div class="flex flex-col pt-3">
      <span class="active2">Directions</span>
     
      <p *ngIf="!isUrl(directions.directions)" class="direction" [innerHtml]="replaceTags(directions?.directions) | safeHmtl"></p>
      <a class="link" *ngIf="isUrl(directions.directions)" [href]="directions.directions" target="_blank">
        {{directions.directions}}
      </a>
      <!-- <a *ngIf='extractQueryParameter(directions?.directions,"query")' href='directions?.directions' class="direction" ></a> -->

    </div>

    <div class="flex justify-end pt-3">
      <button mat-button color="primary" (click)="download()">Download</button>
    </div>
  </div>

  <ng-template #loading>
    <div class="card-div flex justify-center">
      <div class="spinner w-full">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
    </div>
  </ng-template>
</div>