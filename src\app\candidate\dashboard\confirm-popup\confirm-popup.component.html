<div *ngIf="data?.id == 3" class="p-2 touch-auto overflow-auto" gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr " gdGap="2px" exaiContainer>
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div *ngIf="data.message" fxLayout="column">
            <div class="flex justify-end cursor-pointer" fxLayout="row">
                <mat-icon class="text-sm flex justify-end"  (click)="close()" mat-dialog-close>close</mat-icon>
            </div>
        </div>
    </div>
    <div class="" gdColumn="1 /-1 " gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
       
    <div *ngIf="data.ssn;else elseBlock">
        <div class="custom" >
        <div class="" gdColumn="1/ -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
            <div fxLayout="column">
                <div class="flex justify-center submit" fxLayout="row">
                    <h3> <b>Please Confirm Your SSN</b></h3>
                </div>
            </div>
        </div>
        <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
            <div class="pb-4 flex justify-center t-xs confirm" fxLayout="row">
                <h6>XXX-XX-{{personDetails.fullSSN}}</h6>
            </div>
        </div>

        <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
            <div class="pb-4 flex justify-center t-xs confirm" fxLayout="row">
                <form [formGroup]="modifyssn">
                <mat-form-field class="pl-4" appearance='outline'>
                    <mat-label>SSN</mat-label>
                    <input class="w-96" matInput formControlName="ssn" placeholder="Enter Full SSN">
                    <mat-error>
                        <div class="invalid-feedback">
                          <div>Only Nine Number is Required</div>
                        </div>
                      </mat-error>
                </mat-form-field>
                </form>
            </div>
        </div>
        <div class="custom1">
            <button class=" flex justify-center btn-1 text-xs" mat-button type="button" (click)="submit()">
                Submit
              </button>
              </div>
    </div>
    </div>
</div>
    <ng-template #elseBlock>

        <div  class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
            <div fxLayout="column">
                <div class="flex justify-center submit" fxLayout="row">
                    <h3> <b>{{examName}}</b></h3>
                </div>
            </div>
        </div>
        <div    class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
            <div class="pb-4 flex justify-center t-xs confirm" fxLayout="row">
                <h6>Are you sure you want to cancel this exam</h6>
            </div>
        </div>
        <div   class="pb-4" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="16px">
            <div class="" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
                <div class="" fxLayout="column">
                    <button mat-dialog-close class="btn-1" mat-button  (click)="cancelExam(data.item);close()">Yes</button>
    
                </div>
            </div>
            <div class="" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="2 / -1">
                <div class="" fxLayout="column">
                    <button class="btn-3" mat-button mat-dialog-close >No</button>
                </div>
            </div>
        </div>
      </ng-template>


</div>


<div *ngIf="data?.id == 2" class="p-2 touch-auto overflow-auto" gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr " gdGap="2px" exaiContainer>
<div class=" h-full " >
    <form [formGroup]="form">
        <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>First Name</mat-label>
                <input class="text-xs" id="fName" matInput type="text" placeholder="First name" formControlName="firstName">
                <mat-error *ngFor="let validation of validation_messages.firstName">
                    <mat-error class="error-message"
                        *ngIf="form.get('firstName').hasError(validation.type) && (form.get('firstName').dirty || form.get('firstName').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>Middle Name</mat-label>
                <input class="text-xs" id="mName" matInput type="text" placeholder=" Middle name" formControlName="middleName">
            </mat-form-field>
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>Last Name</mat-label>
                <input class="text-xs" id="lName" matInput type="text" placeholder="Last name" formControlName="lastName">
                <mat-error *ngFor="let validation of validation_messages.lastName">
                    <mat-error class="error-message"
                        *ngIf="form.get('lastName').hasError(validation.type) && (form.get('lastName').dirty || form.get('lastName').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
        </div>
        <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
          <mat-form-field fxFlex="auto"  appearance="outline"
          >
          <mat-label>Date of Birth</mat-label>
          <input matInput  [matDatepicker]="picker" formControlName="dob"
              placeholder='Date of Birth' [disabled]="form.get('dob').disabled" [max]="maxDate"   [required]="true">
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker ></mat-datepicker>
          <mat-error *ngFor="let validation of validation_messages.dob">
            <mat-error class="error-message"
                *ngIf="form.get('dob').hasError(validation.type) && (form.get('dob').dirty || form.get('dob').touched)">
                {{validation.message }}</mat-error>
             
        </mat-error>
      </mat-form-field>

      <mat-form-field fxFlex="auto"  appearance="outline">
        <mat-label>Gender</mat-label>
        <mat-select formControlName="gender" placeholder="Gender">
          <mat-option *ngFor="let option of options" [value]="option.value">
            {{ option.label }}
          </mat-option>
        </mat-select>
        <mat-error *ngFor="let validation of validation_messages.gender">
            <mat-error class="error-message"
                *ngIf="form.get('gender').hasError(validation.type) && (form.get('gender').dirty || form.get('gender').touched)">
                {{validation.message}}</mat-error>
        </mat-error>
      </mat-form-field>
          </div>

          <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>Address</mat-label>
                <input class="text-xs" id="address" matInput type="text" placeholder="Address (Number and Street)" formControlName="address">
                <mat-error *ngFor="let validation of validation_messages.address">
                    <mat-error class="error-message"
                        *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
        </div>

        <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>City</mat-label>
                <input class="text-xs" id="city" matInput type="text" placeholder="City" formControlName="city">
                <mat-error *ngFor="let validation of validation_messages.city">
                    <mat-error class="error-message"
                        *ngIf="form.get('city').hasError(validation.type) && (form.get('city').dirty || form.get('city').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>Zip Code</mat-label>
                <input class="text-xs" id="zipcode" matInput type="text" placeholder="Zip Code" formControlName="zipCode">
                <mat-error *ngFor="let validation of validation_messages.zipCode">
                    <mat-error class="error-message"
                        *ngIf="form.get('zipCode').hasError(validation.type) && (form.get('zipCode').dirty || form.get('zipCode').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
        </div>
        <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>State</mat-label>
                <mat-select formControlName="state" placeholder="State">
                  <mat-option *ngFor="let option of state" [value]="option.stateName">
                    {{ option.stateName }}
                  </mat-option>
                </mat-select>
                <mat-error *ngFor="let validation of validation_messages.state">
                    <mat-error class="error-message"
                        *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
              </mat-form-field>
        </div>
        <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>SSN</mat-label>
                <input class="text-xs" id="ssn" matInput type="text" placeholder="SSN" formControlName="ssn">
                <mat-error *ngFor="let validation of  validation_messages.ssn">
                    <mat-error class="error-message"
                        *ngIf="form.get('ssn').hasError(validation.type)  && (form.get('ssn').dirty || form.get('ssn').touched)">
                        {{ validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
            <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline"
                                    >
                                    <mat-label *ngIf="global.userDetails.getValue().stateCode == 'CO'; else ssnLabel"> SSN or ITIN</mat-label>
                                    <ng-template #ssnLabel>
                                        <mat-label>Confirm SSN</mat-label>
                                    </ng-template>
                                    <input type="text" (keypress)="keyPressNumbers($event)" class="form-control"
                                    [autocomplete]="false"
                                    type="password" 
                                        matInput [placeholder]="global.userDetails.getValue().stateCode == 'CO'? 'Confirm SSN or ITIN' : global.Confirm_Ssn" formControlName="confirmSsn" [matTooltip]="'Legal '+global.SSN">
                                        
                                            <mat-error class="error-message mb-2"
                                        *ngIf="form.get('confirmSsn').touched && !form.get('confirmSsn').valid && global.userDetails.getValue().stateCode == 'CO'"
                                        >
                                        {{global.SsnItin_not_matched}}
                                    </mat-error>
                                        <mat-error class="error-message mb-2"
                                    *ngIf="form.get('confirmSsn').touched && !form.get('confirmSsn').valid && global.userDetails.getValue().stateCode != 'CO'"
                                    >
                                    {{global.Ssn_not_matched}}
                                </mat-error>
                                    

                                    
                                </mat-form-field>
        </div>
        <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field fxFlex='auto' class="phNumberInput"
            appearance="outline">
            <mat-label>Phone Number</mat-label>
            <ngx-mat-intl-tel-input  phNumberInput matInput
                placeholder="Phone Number" [preferredCountries]="['us']"
                [enablePlaceholder]="false" [enableSearch]="true" name="phone"
                formControlName="phoneNumber" >
            </ngx-mat-intl-tel-input>
            <mat-error *ngFor="let validation of validation_messages.phoneNumber">
                <mat-error class="error-message"
                    *ngIf="form.get('phoneNumber').hasError(validation.type) && (form.get('phoneNumber').dirty || form.get('phoneNumber').touched)">
                    {{validation.message}}</mat-error>
            </mat-error>
        </mat-form-field>
            <mat-form-field fxFlex="auto"  appearance="outline">
                <mat-label>Email</mat-label>
                <input class="text-xs" id="email" matInput type="email" placeholder="Email" formControlName="_email">
                <mat-error *ngFor="let validation of validation_messages._email">
                    <mat-error class="error-message"
                        *ngIf="form.get('_email').hasError(validation.type) && (form.get('_email').dirty || form.get('_email').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </mat-form-field>
        </div>
    </form>

    <div class="mt-4 addingtextMargin" fxLayout="row" fxLayoutAlign="center center">
        <mat-checkbox (change)='showOptions($event)'   class="align-co" color='primary' >
    </mat-checkbox> <div class="terms ml-2 mt-1">By clicking Sign Up, you agree to our <a (click)="openDialog()"
        >Terms and Data Policy</a>
</div>
    </div>

    <div fxLayout="row" fxLayoutAlign="center center">
        <button (click)="registered()" mat-flat-button color="primary" [disabled]="!isChecked || !checkStatus() "
            class="loginBtn"  
            >Submit</button>
    </div>
 

    

  </div>
</div>
