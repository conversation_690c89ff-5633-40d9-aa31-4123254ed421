import { RegistryEffects } from './state/registry.effects';
import { REGISTRY_MODULE_STATE } from './state/registry.selectors';
import { reducer } from './state/registry.reducer';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { FormsModule } from '@angular/forms';

//import { ContainerModule } from './../../../../../examroom-login/src/@exai/directives/container/container.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDialogModule } from "@angular/material/dialog";

import { RegistryRoutingModule } from './registry-routing.module';
import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';

import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatStepperModule } from '@angular/material/stepper';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';

import { FormBuilderModule } from "src/app/core/examroom-formbuilder/form-builder.module";


import { FlexLayoutModule } from '@angular/flex-layout';
import { DragDropModule } from '@angular/cdk/drag-drop';

import { RegistryComponent } from './registry.component';
import { CertificatesAndRequestsComponent } from './certificates-and-requests/certificates-and-requests.component';
import { CrCardsComponent } from './cr-cards/cr-cards.component';
import { ViewCertificateComponent } from './view-certificate/view-certificate.component';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { RegistryStatePopupComponent } from './registry-state-popup/registry-state-popup.component';
import { RegistryProgressBarComponent } from './progress-bar/progress-bar.component';
import { RegistryProgressStepComponent } from './progress-bar/progress-step/progress-step.component';
import { RegistryProgressBarModule } from './registry-progress-bar/progress-bar.module';
import { CommonComponentModule } from 'src/app/core/common-component/common-component.module';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { MatTooltipModule } from '@angular/material/tooltip';


@NgModule({
  declarations: [
    RegistryComponent,
    CertificatesAndRequestsComponent,
    CrCardsComponent,
    ViewCertificateComponent,
    RegistryStatePopupComponent,
    RegistryProgressBarComponent,
    RegistryProgressStepComponent
  ],
  imports: [
    CommonModule,
    RegistryRoutingModule,
    NgDynamicBreadcrumbModule,
    RegistryProgressBarModule,
    FormBuilderModule,
    FlexLayoutModule,
    ContainerModule,
    FormsModule,
    DragDropModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatMenuModule,
    MatStepperModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatListModule,
    MatExpansionModule,
    CommonComponentModule,
    MatTooltipModule,
    StoreModule.forFeature(REGISTRY_MODULE_STATE, reducer),
    EffectsModule.forFeature([RegistryEffects]),
  ],
  providers: [
    DatePipe, 
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptorService,
      multi: true,
      
    }
  ]
})
export class RegistryModule { }
