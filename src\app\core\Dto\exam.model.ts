export class ExamModel{
    public constructor(init?:Partial<ExamModel>) {
        Object.assign(this, init);
    }

    id: number = 0;
    title: string = '';
    price: number;
    testDuration: number;
    supportedExamMode: ExamModeModel[] = [];
    isDisabled:boolean
    attempts?:number
    preSelectType?:number
    preselect?:boolean
}

export class ExamModeModel{
    public constructor(init?:Partial<ExamModeModel>) {
        Object.assign(this, init);
    }

    examModeTypeId: number = 0;
    examCode: string = '';
}