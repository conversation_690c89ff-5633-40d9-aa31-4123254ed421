.config-panel {
  padding: var(--padding-16) var(--padding);
}

.heading {
  margin-bottom: var(--padding);
}

.section {
  border-bottom: 1px solid var(--foreground-divider);
  margin-bottom: var(--padding-16);
  padding-bottom: var(--padding-16);

  &:last-child {
    border-bottom: none;
  }
}

.section-content {
  margin-inline-start: var(--padding-12);

  .subheading {
    margin-top: var(--padding);
  }
}

.subheading {
  @apply my-4 uppercase text-xs text-secondary font-medium;
}

.layout + .layout {
  margin-top: var(--padding);
}

.layout-image {
  &:hover {
    .layout-image-overlay {
      background: rgba(0, 0, 0, 0.7);
      opacity: 1;
      visibility: visible;
    }
  }

  .layout-image-overlay {
    border-radius: var(--border-radius);
    bottom: 0;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: var(--trans-ease-out);
    visibility: hidden;
    width: 100%;

    button {
      padding: 0 8px;
    }
  }
}

.exai-color-picker {
  transition: var(--trans-ease-out);

  &:hover,
  &.selected {
    background: currentColor !important;

    p,
    div {
      color: white;
    }
  }

  p {
    transition: var(--trans-ease-out);
  }
}

.color {
  align-items: center;
  border-radius: 50%;
  box-shadow: var(--elevation-z8);
  display: flex;
  flex-direction: row;
  height: 36px;
  justify-content: center;
  margin-inline-end: var(--padding-16);
  text-align: center;
  vertical-align: middle;
  width: 36px;

  &.light {
    background: white;
    color: #000;
  }

  &.dark {
    background: #303030;
    color: white;
  }

  &.flat {
    background: #f5f5f5;
    color: #000;
  }
}

mat-slide-toggle + mat-slide-toggle,
mat-slide-toggle + mat-checkbox,
mat-checkbox + mat-slide-toggle,
mat-checkbox + mat-checkbox {
  display: block;
  margin-top: var(--padding-12);
}

.style-name {
  font: var(--font-body-2);
}
