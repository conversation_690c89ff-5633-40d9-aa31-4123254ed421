import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { ManageProfileRoutingModule } from './manage-profile-routing.module';
import { ManageProfileComponent } from './manage-profile.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
// import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { BreadcrumbsModule } from 'src/@exai/components/breadcrumbs/breadcrumbs.module';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { ProfileEditComponent } from './profile-edit/profile-edit.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { FormBuilderModule } from 'src/app/core/examroom-formbuilder/form-builder.module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { MngProfileReducer } from './state/profile-form.reducer';
import { MngProfileEffects } from './state/profile-form-effects';
import { NgxMatIntlTelInputModule } from 'ngx-mat-intl-tel-input';
import { MANAGE_PROFILE_STATE } from './state/profile-form.state';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
@NgModule({
  declarations: [
    ManageProfileComponent,
    ProfileEditComponent,
  ],
  imports: [
    CommonModule,
    ManageProfileRoutingModule,
    MatCardModule,
    FlexLayoutModule,
    ContainerModule,
    // HttpClientModule,
    BreadcrumbsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatIconModule,
    BreadcrumbsModule,
    FormsModule,
    ReactiveFormsModule,
    NgDynamicBreadcrumbModule,
    NgxMatIntlTelInputModule,
    MatIconModule,
    MatSelectModule,
    FormBuilderModule,
    MatTooltipModule,
    StoreModule.forFeature(MANAGE_PROFILE_STATE, MngProfileReducer),
    EffectsModule.forFeature([MngProfileEffects]),
  ],
  providers: [
    // { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})

export class ManageProfileModule { }
