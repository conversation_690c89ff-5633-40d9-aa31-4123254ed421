import { Component, HostListener, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { bundleList } from '../practice-skills.data';
import { BundleAttemptsDialogComponent } from '../bundle-attempts-dialog/bundle-attempts-dialog.component';

@Component({
  selector: 'exai-bundles-tab',
  templateUrl: './bundles-tab.component.html',
  styleUrls: ['./bundles-tab.component.scss']
})
export class BundlesTabComponent implements OnInit {

  bundles = bundleList;
  selectedBundle: any = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private _dialog: MatDialog
  ) { }

  ngOnInit(): void {
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.menu_pop') && !target.closest('.menu-toggle')) {
      this.selectedBundle = null;
    }
  }

  openMenu(skill: any) {
    this.selectedBundle = this.selectedBundle === skill ? null : skill;
  }
  viewBundle(bundle: any) {
    this.router.navigate(['bundles', bundle.id], {
      relativeTo: this.route,
      state: { bundleData: bundle }
    });
  }
  addToCart(skill: any) {
    console.log('Add to cart:', skill);
    this.selectedBundle = null;
  }
  openAddAttemptDialog(skill: any) {
    this._dialog.open(BundleAttemptsDialogComponent, {
      width: '600px',
      data: { bundle: skill },
      panelClass: 'custom-dialog-container'
    });
  }

}
