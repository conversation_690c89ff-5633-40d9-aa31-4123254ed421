export interface Exam {
  id: number;
  examId: number;
  candidateId: number;
  examName: string;
  candidateEmailId: string;
  candidateName: string;
  examDateTime: string;
  examDateTimeUtc: string;
  timeZoneOffset: any;
  examStatusId: number;
  examStatus: string;
  isDisabled?:boolean
}

  export interface PersonForm{
    personFormId: number;
    name: string;
    state: string;
    eligiblityRoute: string;
    eligibilityRouteId?: number;
    formCode?: string;
    submittedDate;
    lastUpdatedDate: Date;
    iconUrl: string;
    status: string;
    waitingTime?: any;
    formTypeId:any;
    elapsedDateTime:string;
    statusId:number
  }
  export interface ClearCartResponse{
    clearData:clearCartResponse[]
  }

  export interface clearCartResponse {
    personEventId: 0,
    isRemovedFromCart: true,
    isPaymentDone: true,
    appliedVoucherCode: "string"
  }

export interface schedule {
    candidateId: number,
    examId: number,
    slotId: number,
    timeZone: string,
    offSet?: string,
    examModeId: number,
    personTenantRoleId: number,
    testCenterId: string,
    accommodationType: string,
    accommodationItems: [],
    clientExamId: number,
    testCenterName: string,
    testCenterAddress: string,
    testCenterCity: string,
    testCenterState: string,
    testCenterPostalCode: string,
    examDateTime?: string,
    scheduleId:number,
    Slotime?:string,
    ExamDateTime?:string,
    testSiteId?:string,
    TestCenterDirections?:string
    testCenterCode?:string
    examClientEventId?:number
    examCode?:string
  }



  