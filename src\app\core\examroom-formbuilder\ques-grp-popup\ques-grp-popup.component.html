<div id="questionGroupPopup" class="card p-6 fb-ht-fixed" style="position:relative" fxFlex fxFlex.xs="auto">
    <div style="position:absolute; top:10px;right:10px;cursor:pointer;" (click)="this.dialogRef.close()">
        <mat-icon>close</mat-icon>
    </div>
    <h4 class="mt-0 mb-6">Ques Grp Builder</h4>

    <div *ngIf="(displayIndex$ | async) === 0">
        <mat-tab-group mat-align-tabs="center" [style.height]="'62vh'" class="basic-card touch-auto overflow-auto">
            <mat-tab label="Basic Details">
                <form [formGroup]="basicQuestionGrpDetails">
                    <div class="row">
                        <!--No point in showing the id if the user cannot even edit it-->
                        <!-- <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Ques Grp Id</mat-label>
                            <input matInput formControlName="id" readonly>
                        </mat-form-field> -->
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Ques Grp Name</mat-label>
                            <input matInput formControlName="name">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Ques Grp Label</mat-label>
                            <input matInput formControlName="label">
                        </mat-form-field>
                        <!-- <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Number of feild in Row:</mat-label>
                            <input matInput formControlName="numFeildsInRow" type="number">
                            <mat-select formControlName="numFeildsInRow">
                                <mat-option [value]="2">2</mat-option>
                                <mat-option [value]="3">3</mat-option>
                                <mat-option [value]="4">4</mat-option>
                                <mat-option [value]="5">5</mat-option>
                                <mat-option [value]="5">6</mat-option>
                                <mat-option [value]="5">12</mat-option>
                            </mat-select>
                        </mat-form-field> -->
                        <mat-checkbox formControlName="replicable" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>Replicable?</span>
                            <mat-hint>Use this only when absolutely necessary</mat-hint>
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="basicQuestionGrpDetails.value.replicable">
                            <mat-label>Initial Count</mat-label>
                            <input matInput formControlName="initialCount" type="number">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="basicQuestionGrpDetails.value.replicable">
                            <mat-label>Total Count</mat-label>
                            <input matInput formControlName="totalCount" type="number">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="basicQuestionGrpDetails.value.replicable">
                            <mat-label>Remove Label</mat-label>
                            <input matInput formControlName="removeLabel" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="basicQuestionGrpDetails.value.replicable">
                            <mat-label>Add Label</mat-label>
                            <input matInput formControlName="addLabel" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="basicQuestionGrpDetails.value.replicable">
                            <mat-label>Related Question Group ID</mat-label>
                            <mat-select formControlName="parentModelID">
                                <mat-option *ngFor="let id of formBuilderService.getAllQuestionGroupIDsOfAParticualarSection(data.sectionIndex)" [value]="id">{{id}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-checkbox formControlName="AbRelated" class="genesis-form-feild">Make AB Related?
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="basicQuestionGrpDetails.value.AbRelated">
                            <mat-label>Related Feild ID</mat-label>
                            <mat-select formControlName="relatedFeildID">
                                <mat-option *ngFor="let id of formBuilderService.getAllQuestionGroupIDsOfAParticualarSection(data.sectionIndex)" [value]="id">{{id}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-checkbox formControlName="showInstructions" class="genesis-form-feild">Show Instructions?
                        </mat-checkbox>
                        <mat-form-field *ngIf="basicQuestionGrpDetails.value.showInstructions" appearance="outline" class="editor">
                            <mat-label> Instructions</mat-label>
                            <!-- <textarea *ngIf="basicFormOrSectionDetails.value.showInstructionInIButton" matInput formControlName="instructions" placeholder="Enter Instructions Here" cols="80" rows="10"></textarea> -->
                            <input matInput id="wMsg">
                            <ngx-editor-menu class="editor" style="overflow:hidden;" [editor]="editor" [toolbar]="toolbar">
                            </ngx-editor-menu>
                            <ngx-editor class="editor" style="height: 3rem !important;" [editor]="editor" formControlName="instructions">
                            </ngx-editor>
                        </mat-form-field>
                    </div>
                </form>
            </mat-tab>
            <mat-tab label="Css/Styling">
                <mat-accordion style="width:90%" class="m-2">
                    <mat-expansion-panel style="width:90%" class="m-4">
                        <mat-expansion-panel-header>
                            <strong> Element </strong>
                        </mat-expansion-panel-header>
                        <exai-layout-form [formgroup]="LayoutFormQuesGrp.controls[0]"></exai-layout-form>
                    </mat-expansion-panel>
                    <mat-expansion-panel style="width:90%" class="m-4">
                        <mat-expansion-panel-header>
                            <strong> Grid </strong>
                        </mat-expansion-panel-header>
                        <exai-layout-form [formgroup]="LayoutFormQuesGrp.controls[1]"></exai-layout-form>
                    </mat-expansion-panel>
                </mat-accordion>

            </mat-tab>
        </mat-tab-group>
        <div class="row" style="display:flex; flex-direction: row; align-items:flex-end;justify-content:center">
            <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" [disabled]="this.basicQuestionGrpDetails.invalid" (click)="next()">
                Next
            </button>
        </div>
    </div>
    <div *ngIf="(displayIndex$ | async) === 1">
        <div class="row" style="display:flex; flex-direction: row; align-items:center;justify-content:flex-start">
            <mat-icon class="mt-0 mb-6 text-2xl cursor-pointer" (click)="displayIndex.next(0)">keyboard_arrow_left
            </mat-icon>
            <h4 class="mt-0 mb-6" style="display:flex; flex-direction: row; align-items:center;">
                Rendered Question Group</h4>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <button mat-button class="mt-0 mb-6 bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" [matMenuTriggerFor]="feildsMenu">
                Add Feilds
            </button>
            <mat-menu #feildsMenu="matMenu">
                <button mat-menu-item *ngFor="let feildType of feildTypes; let i = index" (click)="itemSelected(feildType)">{{feildType.displayName}}</button>
            </mat-menu>
        </div>
        <div [style.height]="'62vh'" class="basic-card touch-auto overflow-auto flex align-start">
            <mat-accordion style="width:90%" class="m-2" multi cdkDropList (cdkDropListDropped)="drop($event)">
                <mat-expansion-panel style="width:90%" class="m-4" *ngFor="let feild of allSelectedFeildType;let i = index">
                    <mat-expansion-panel-header class="relative" cdkDrag>
                        <strong> Type: </strong> &nbsp;&nbsp;{{feild.displayName}} | &nbsp;&nbsp; <strong> Label:
                        </strong> &nbsp;&nbsp;{{allFeildDetails[i].label}}
                        <mat-icon class="mt-0 mb-1 text-2l cursor-pointer absolute" style="top:0.5rem; right:0.125rem" (click)="removeFeild(i)">
                            close</mat-icon>
                        <mat-icon class="mt-0 mb-1 text-2l cursor-pointer absolute" style="top:0.5rem; right:1.725rem" (click)="editFeildDetails(i)">
                            edit</mat-icon>
                    </mat-expansion-panel-header>
                    <pre>{{ feild | json}}</pre>
                    <pre>{{allFeildDetails[i] | json}}</pre>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
        <button (click)="close()" mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow">
            Done
        </button>
    </div>
    <div *ngIf="(displayIndex$ | async) === 2">
        <div class="row" style="display:flex; flex-direction: row; align-items:center;justify-content:flex-start">
            <!-- <mat-icon class="mt-0 mb-6 text-2xl cursor-pointer" (click)="backFromFeild()">keyboard_arrow_left</mat-icon> -->
            <h4 class="mt-0 mb-6" style="display:flex; flex-direction: row; align-items:center;">
                {{isUserEditingFeild ? 'Save':'Add' }}&nbsp;{{this.curFeildType.displayName}}</h4>
        </div>
        <mat-tab-group mat-align-tabs="center" [style.height]="'62vh'" class="basic-card touch-auto overflow-auto">
            <mat-tab label="Basic Details">
                <form [formGroup]="feildDetails">
                    <!-- <div class="row">
                            No point in showing the id if the user cannot even edit it
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Feild Id*</mat-label>
                            <input matInput formControlName="id" readonly>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Feild Name*</mat-label>
                            <input matInput formControlName="name">
                        </mat-form-field>
                    </div> -->
                    <div class="row">
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Feild Label*</mat-label>
                            <input matInput formControlName="label">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Placeholder*</mat-label>
                            <input matInput formControlName="placeholder" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Identifier</mat-label>
                            <mat-select formControlName="identifier">
                                <mat-option *ngFor="let identifier of identifiers" [value]="identifier">{{identifier}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Update On</mat-label>
                            <mat-select formControlName="updateOn" required>
                                <mat-option [value]="'Change'" selected>Change</mat-option>
                                <mat-option [value]="'Blur'">Blur</mat-option>
                                <mat-option [value]="'Submit'">Submit</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Orientation</mat-label>
                            <mat-select formControlName="orientation" required>
                                <mat-option [value]="'horizontal'" selected>Horizontal</mat-option>
                                <mat-option [value]="'vertical'">Vertical</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Colspan</mat-label>
                            <input matInput formControlName="colspan" type="number">
                        </mat-form-field>
                        <mat-checkbox formControlName="required" class="genesis-form-feild">
                            Required?
                        </mat-checkbox>
                        <mat-checkbox formControlName="hidden" class="genesis-form-feild">
                            Hidden?
                        </mat-checkbox>
                        <mat-checkbox formControlName="disable" class="genesis-form-feild">
                            Disable?
                        </mat-checkbox>
                        <mat-checkbox formControlName="mergeToNextRow" class="genesis-form-feild">
                            MergeToNextRow?
                        </mat-checkbox>
                        <mat-checkbox formControlName="prefillValue" class="genesis-form-feild">
                            Prefill Value?
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="feildDetails.value.prefillValue">
                            <mat-label>Element Property To Be Extracted For Prefill</mat-label>
                            <input matInput formControlName="elementPropertyToBeExtractedForPrefill" type="text">
                        </mat-form-field>
                        <mat-checkbox formControlName="fetchOptionsFromApi" class="genesis-form-feild" *ngIf="[6,5].includes(this.curFeildType.id)">
                            Fetch Options From API?
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="feildDetails.value.hidden">
                            <mat-label>Until</mat-label>
                            <mat-select formControlName="relatedFeildId">
                                <mat-option *ngFor="let feild of feildThatCouldBeRelatedTo" [value]="feild.feildId">
                                    Label:&nbsp;{{feild.label}}
                                </mat-option>
                            </mat-select>
                            <mat-hint>Select a feild of this question group...</mat-hint>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="feildDetails.value.hidden">
                            <mat-label>Has Value</mat-label>
                            <input matInput formControlName="relatedFeildValueAtWhichThisFeildHasToBeShown" type="text">
                            <mat-hint>Enter the value of the related feild on which this feild has to be shown
                            </mat-hint>
                        </mat-form-field>
                    </div>
                </form>
                <!-- Options is for radiogroup and select/multi select-->
                <div class="row">
                    <div *ngIf="this.curFeildType.id == 8 || ([6,5].includes(this.curFeildType.id) && !(feildDetails.value.fetchOptionsFromApi))">
                        <p>Options</p>
                        <div class="row" *ngFor="let group of optionsDetails.controls; let i = index">
                            <form [formGroup]="group">
                                <mat-form-field appearance="outline" class="genesis-form-feild">
                                    <mat-label>Label</mat-label>
                                    <input matInput formControlName="label" type="text">
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="genesis-form-feild">
                                    <mat-label>Value</mat-label>
                                    <input matInput formControlName="value" type="text">
                                </mat-form-field>
                                <mat-checkbox [checked]="i==0" formControlName="defaultValue" class="genesis-form-feild">
                                    Default Value?
                                </mat-checkbox>
                                <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="this.optionsDetails.removeAt(i)">
                                    Remove Option
                                </button>
                            </form>
                        </div>
                        <div class="row">
                            <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="addOption()">
                                Add Option
                            </button>
                        </div>
                    </div>
                    <!-- <div class="row" *ngIf="[6,5].includes(this.curFeildType.id) && (feildDetails.value.fetchOptionsFromApi)">
                        <form [formGroup]="feildDetails">
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Api Url</mat-label>
                                <input matInput formControlName="apiUrl" type="text">
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Method</mat-label>
                                <mat-select formControlName="method" required>
                                    <mat-option [value]="'POST'">Post</mat-option>
                                    <mat-option [value]="'GET'" selected>Get</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </form>
                    </div> -->
                    <div class="row" *ngIf="[6,5].includes(this.curFeildType.id)">
                        <form [formGroup]="feildDetails">
                            <mat-checkbox formControlName="filterable" class="genesis-form-feild">
                                Filterable?
                            </mat-checkbox>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Prefix</mat-label>
                                <input matInput formControlName="prefix">
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Suffix</mat-label>
                                <input matInput formControlName="suffix">
                            </mat-form-field>
                        </form>
                    </div>
                </div>
                <!-- The following is for slider, and the max is also for rating ... but not working properly as of yet -->
                <div class="row" *ngIf="[11,13].includes(this.curFeildType.id)">
                    <form [formGroup]="feildDetails">
                        <mat-form-field *ngIf="[11].includes(this.curFeildType.id)" appearance="outline" class="genesis-form-feild">
                            <mat-label>Min</mat-label>
                            <input matInput formControlName="min" type="number">
                        </mat-form-field>
                        <mat-form-field *ngIf="[11,13].includes(this.curFeildType.id)" appearance="outline" class="genesis-form-feild">
                            <mat-label>Max</mat-label>
                            <input matInput formControlName="max" type="number">
                        </mat-form-field>
                        <mat-form-field *ngIf="[11].includes(this.curFeildType.id)" appearance="outline" class="genesis-form-feild">
                            <mat-label>Step</mat-label>
                            <input matInput formControlName="step" type="number">
                        </mat-form-field>
                        <mat-checkbox *ngIf="[11].includes(this.curFeildType.id)" formControlName="vertical">
                            Vertical?
                        </mat-checkbox>
                    </form>
                </div>
                <div class="row" *ngIf="[14].includes(this.curFeildType.id)">
                    <form [formGroup]="feildDetails">
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>cols</mat-label>
                            <input matInput formControlName="cols" type="number">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Rows</mat-label>
                            <input matInput formControlName="rows" type="number">
                        </mat-form-field>
                        <mat-checkbox formControlName="vertical">
                            Wrap?
                        </mat-checkbox>
                    </form>
                </div>
                <div class="row" *ngIf="[12].includes(this.curFeildType.id)">
                    <form [formGroup]="feildDetails">
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Off Label</mat-label>
                            <input matInput formControlName="offLabel" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>On Label</mat-label>
                            <input matInput formControlName="onLabel" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Label Position</mat-label>
                            <mat-select formControlName="labelPosition" required>
                                <mat-option [value]="'before'">Before</mat-option>
                                <mat-option [value]="'after'">After</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </form>
                </div>
                <div class="row" *ngIf="[9].includes(this.curFeildType.id)">
                    <form [formGroup]="feildDetails">
                       
                        <mat-checkbox formControlName="pastDisabled" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>Disable Past Dates?</span>
                        </mat-checkbox>
                        <mat-checkbox formControlName="displayCurrentDate" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>Display Current Date?</span>
                        </mat-checkbox>
                         <mat-checkbox formControlName="displayClosedDate" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>Display closed Complete Custom Date?</span>
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="feildDetails.value.pastDisabled">
                            <mat-label>Past OffSet</mat-label>
                            <input matInput formControlName="pastOffset" type="number">
                            <mat-hint>The dates beyond this many days in the past will be disabled</mat-hint>
                        </mat-form-field>
                        <mat-checkbox formControlName="futureDisabled" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>Disable future Dates?</span>
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="feildDetails.value.futureDisabled">
                            <mat-label>Future OffSet</mat-label>
                            <input matInput formControlName="futureOffset" type="number">
                            <mat-hint>The dates beyond this many days in the future will be disabled</mat-hint>
                        </mat-form-field>
                        <mat-checkbox formControlName="todateDisabled" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>From Date and To Date</span>
                        </mat-checkbox>
                        <mat-checkbox formControlName="disableBasedOnApiRes" class="genesis-form-feild" class="ml-3 flex flex-row">
                            <span>Disable Based on Api Dates?</span>
                        </mat-checkbox>
                    </form>
                </div>
                <ng-container *ngIf="([9].includes(this.curFeildType.id) && feildDetails.value.disableBasedOnApiRes) || ([6,5].includes(this.curFeildType.id) && feildDetails.value.fetchOptionsFromApi)">
                    <p>Request Details</p>
                    <form [formGroup]="feildDetails">
                        <mat-checkbox formControlName="fetchOptionsBasedOnResponseAnotherField" class="genesis-form-feild">
                            Fetch Options Based On Response Of Another Field?
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="feildDetails.value.fetchOptionsBasedOnResponseAnotherField">
                            <mat-label>Related Field</mat-label>
                            <mat-select formControlName="fetchOptionsRelatedFeildId">
                                <mat-option *ngFor="let feild of allFeildDetails" [value]="feild.id">
                                    Label:&nbsp;{{feild.label}}
                                </mat-option>
                            </mat-select>
                            <mat-hint>Select a feild of this question group...</mat-hint>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Fetch Url</mat-label>
                            <input matInput formControlName="fetchUrl" type="text">
                            <mat-hint>Provide details here to make and api call</mat-hint>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Method</mat-label>
                            <mat-select formControlName="method" required>
                                <mat-option [value]="'post'">Post</mat-option>
                                <mat-option [value]="'get'" selected>Get</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </form>
                    <p>Request Params</p>
                    <div class="row" *ngFor="let group of requestParams.controls; let paramIndex = index">
                        <form [formGroup]="group">
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Param Type</mat-label>
                                <mat-select formControlName="paramType">
                                    <mat-option *ngFor="let pt of paramTypesKeys" [value]="paramTypes[pt]">{{pt}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Param Name</mat-label>
                                <input matInput formControlName="paramName" type="text">
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Param Value</mat-label>
                                <input matInput formControlName="paramValue" type="text">
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="group.value.paramType == paramTypes.Required">
                                <mat-label>Required Param Position</mat-label>
                                <input matInput formControlName="position" type="number">
                            </mat-form-field>
                            <mat-checkbox formControlName="extractedFromGlobal" class="genesis-form-feild" *ngIf="!group.value.extractedFromElement">
                                Extract Value of Param From Some Global Property?
                            </mat-checkbox>
                            <mat-checkbox formControlName="extractedFromElement" class="genesis-form-feild" *ngIf="!group.value.extractedFromGlobal">
                                Extract Value of Param From Some Local Property?
                            </mat-checkbox>
                            <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="group.value.extractedFromGlobal || group.value.extractedFromElement">
                                <mat-label>Property/Key to be Extracted..</mat-label>
                                <input matInput formControlName="elementPropertyToBeExtracted" type="text">
                            </mat-form-field>
                            <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="this.requestParams.removeAt(paramIndex)">
                                    Remove Param
                                </button>
                        </form>
                    </div>
                    <div class="row">
                        <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="addParam()">
                            Add Param
                        </button>
                    </div>
                </ng-container>
                <div class="row" *ngIf="[10].includes(this.curFeildType.id)">
                    <form [formGroup]="feildDetails">
                        <div class="row">
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Upload Url</mat-label>
                                <input matInput formControlName="url">
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Remove Url</mat-label>
                                <input matInput formControlName="removeUrl">
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>Accept</mat-label>
                                <input matInput formControlName="accept">
                                <mat-hint>Enter list of file types to accept as comma separated list</mat-hint>
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>MinSize</mat-label>
                                <input matInput formControlName="minSize" type="number">
                                <mat-hint>Min Size of the file allowed</mat-hint>
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="genesis-form-feild">
                                <mat-label>MaxSize</mat-label>
                                <input matInput formControlName="maxSize" type="number">
                                <mat-hint>Max size of the file allowed</mat-hint>
                            </mat-form-field>
                        </div>
                        <div class="row">
                            <mat-checkbox formControlName="multiple" class="genesis-form-feild">
                                Allow Multiple?
                            </mat-checkbox>
                            <mat-checkbox formControlName="showFileList" class="genesis-form-feild">
                                Show File List?
                            </mat-checkbox>
                            <mat-checkbox formControlName="autoUpload" class="genesis-form-feild">
                                Auto Upload?
                            </mat-checkbox>
                        </div>
                    </form>
                </div>
            </mat-tab>
            <mat-tab label="Css/Styling">
                <mat-accordion style="width:90%" class="m-2">
                    <mat-expansion-panel style="width:90%" class="m-4">
                        <mat-expansion-panel-header>
                            <strong> Element </strong>
                        </mat-expansion-panel-header>
                        <exai-layout-form [formgroup]="LayoutFormFeild.controls[0]"></exai-layout-form>
                    </mat-expansion-panel>
                    <mat-expansion-panel style="width:90%" class="m-4">
                        <mat-expansion-panel-header>
                            <strong> Grid </strong>
                        </mat-expansion-panel-header>
                        <exai-layout-form [formgroup]="LayoutFormFeild.controls[1]"></exai-layout-form>
                    </mat-expansion-panel>
                </mat-accordion>

            </mat-tab>
            <mat-tab label="Validations">
                Validations
                <ng-container class="w-full" *ngFor="let group of basicValidationsForm.controls; let i = index;">
                    <form [formGroup]="group">
                        <mat-checkbox formControlName="validationSelected" class="genesis-form-feild">
                            {{this.basicValidations[i]}}
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label> Param</mat-label>
                            <input matInput formControlName="inputVal" [type]="'pattern' == basicValidations[i] || 'email'== basicValidations[i]  ? 'text':'number'">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Error Message</mat-label>
                            <input matInput formControlName="errorMessage" type="text">
                        </mat-form-field>
                    </form>
                </ng-container>

            </mat-tab>
        </mat-tab-group>

        <div class="row">
            <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="addFeildtoQuestionGroup()" [disabled]="feildDetails.invalid">
                {{isUserEditingFeild ? 'Save':'Add' }} Feild
            </button>
        </div>
    </div>
</div>