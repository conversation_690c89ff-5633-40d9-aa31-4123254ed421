import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from './dashboard.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { SecondaryToolbarModule } from 'src/@exai/components/secondary-toolbar/secondary-toolbar.module';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { IconModule } from '@visurel/iconify-angular';
import { RegisteredDashboardComponent } from './registered-dashboard/registered-dashboard.component';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { DASHBOARD_STATE_NAME } from './state/dashboard.selectors';
import { dashboardReducer } from './state/dashboard.reducers';
import { DashboardEffects } from './state/dashboard.effects';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { ConfirmPopupComponent } from './confirm-popup/confirm-popup.component';
import { MatMenuModule } from '@angular/material/menu';
import { SystemCheckModule } from '../system-check/system-check.module';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMatIntlTelInputModule } from 'ngx-mat-intl-tel-input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TermsDialogComponent } from './terms/terms.component';

@NgModule({
  declarations: [
    DashboardComponent,
    RegisteredDashboardComponent,
    ConfirmPopupComponent,
    TermsDialogComponent
  ],
  imports: [
    CommonModule,
    DashboardRoutingModule,
    FlexLayoutModule,
    SecondaryToolbarModule,
    PageLayoutModule,
    ContainerModule,
    IconModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatButtonModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatDialogModule,
    MatMenuModule,
    ReactiveFormsModule,
    MatTooltipModule,
    StoreModule.forFeature(DASHBOARD_STATE_NAME, dashboardReducer),
    EffectsModule.forFeature([DashboardEffects]),
    SystemCheckModule,
    MatNativeDateModule,
    MatDatepickerModule,
    NgxMatIntlTelInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,

  ],
  providers: [
    DatePipe,
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class DashboardModule { }
