import { createAction, props } from "@ngrx/store";
import { ExamModel } from "src/app/core/Dto/exam.model";
import { RegisteredExamsModel } from "src/app/core/Dto/registered-exams.model";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { VoucherModel } from "src/app/core/Dto/VoucherModel";
import { ActiveForm } from "./models/activeForm";
import { Cart, Details } from "./models/cart";
import { CartItem } from "./models/cartItem";
import { chargeBody, chargeResponse } from "./models/charge";
import { customerId } from "./models/customerId";
import { ClearCartResponse, Exam, PersonForm, schedule } from "./models/Exam";
import {
  MakePaymentBody,
  MakePaymentResponse,
  Reschedule,
  Vocher,
  VocherResponse,
  Voucher_validate_apply,
} from "./models/makePayment";
import { MonthlySlot } from "./models/monthlySlot";
import {
  CreatePaymentCustomerIdBody,
  CreatePaymentCustomerIdResponse,
  CreatePaymentMethodRespnse,
  paymentMethod,
} from "./models/paymentMethods";
import { Schedule } from "./models/schedule";
import { slot, Slot } from "./models/slot";
import { Timezone } from "./models/timezone.model";
import {
  VochersApply,
  VocherUpdate,
  VocherUpdateResponse,
} from "./models/vocher";

// Timezones
export const getTimezones = createAction("[Timezones] GET Timezones");

export const gotTimezones = createAction(
  "[Timezones] GOT Timezones",
  props<{ Timeszones: Timezone[] }>()
);

// Timeslots
export const getTimeSlotsTestCenterFailure = createAction(
  "[TimeSlots] Get TimeSlots Test Center Failure",
  props<{ error: any }>()
);

export const getTimeSlots = createAction(
  "[TimeSlots] GET TimeSlots",
  props<{
    examId: number;
    timezone: string;
    startDate: string;
    endDate?: string;
    offset: string;
    candidateId:number
  }>()
);

export const gotTimeSlots = createAction(
  "[TimeSlots] GOT TimeSlots",
  props<{ Slots: any[] }>()
);

export const getClearCart = createAction(
  "[getClearCart] GET getClearCart",
  props<{ personId: number; body: any }>()
);

export const gotClearData = createAction(
  "[gotClearData] GOT gotClearData",
  props<{ Response: ClearCartResponse }>()
);

export const getTimeSlotsTestCenter = createAction(
  "[TimeSlots] GET TimeSlots for TestCenter",
  props<{
    timezone: string;
    examId: number;
    startDate: string;
    endDate?: string;
    testCenterName: string;
    testCenterId?: number | string;
    testCenterAddress?: string;
    radius: number;
    candidateId?: number;
    isOnline?: boolean;
  }>()
);

export const gotTimeSlotsTestCenter = createAction(
  "[TimeSlots] GOT TimeSlots  for TestCenter",
  props<{ slots: slot[] }>()
);

// Monthslots
export const getMonthlySlots = createAction(
  "[MonthlySlots] GET MonthlySlots",
  props<{ month: number; year: number; timezone: string }>()
);

export const gotMonthlySlots = createAction(
  "[MonthlySlots] GOT MonthlySlots",
  props<{ monthlySlots: MonthlySlot[] }>()
);

// Cart
export const getCart = createAction(
  "[Cart] GET Cart",
  props<{ details: Details; isPayment?: boolean }>()
);

export const gotCart = createAction(
  "[Cart] GOT Cart",
  props<{ cart: Cart; isPayment?: boolean }>()
);

export const addCartTC = createAction(
  "[Cart] ADD Cart",
  props<{ body: any }>()
);

// export const addedCart = createAction('[Cart] ADD Cart',
//   props<{ addedCart: any }>())

export const removeCartItem = createAction(
  "[Cart] deleteCartitems",
  props<{ tetantId: number; cartItemsId: number }>()
);

export const cartItemRemoved = createAction(
  "[Cart] deletedCartitems",
  props<{ isDeleted: boolean }>()
);

// Exam
export const getExamByERIdId = createAction(
  "[Exam] GET Exam by ERId",
  props<{ eligibilityRouteId: number; personTenantRoleId: number }>()
);

export const gotExamId = createAction(
  "[Exam] GOT ExamId",
  props<{ examdata: ExamModel[] }>()
);

export const getRegisteredExam = createAction(
  "[Exam] GET RegisteredExam",
  props<{ candidateId: number }>()
);

export const gotRegisteredExam = createAction(
  "[Exam] GOT RegisteredExam",
  props<{ registeredExams: RegisteredExamsModel[] }>()
);

export const cancelExam = createAction(
  "[Exam] Cancel Exam",
  props<{ examScheduleId: number; candidateId: number }>()
);

export const examCancelled = createAction(
  "[Exam] Exam Cancelled",
  props<{ isCancelled: boolean }>()
);

// Eligibility route
export const getEligibilityroute = createAction(
  "[EligibilityRoute] GET EligibilityRoute",
  props<{ candidateId: number }>()
);

export const gotEligibilityRoutes = createAction(
  "[EligibilityRoute] GOT EligibilityRoute",
  props<{ route: string }>()
);

// Payment
export const getMakePayment = createAction(
  "[Payment] GET MakePayment",
  props<{ makepaymentBody: MakePaymentBody; tentantId: number }>()
);

export const gotMakePayment = createAction(
  "[Payment] GOT MakesPayment",
  props<{ makePaymentResponse: MakePaymentResponse }>()
);

export const isPayment = createAction(
  "[Payment] isPayment action",
  props<{ isPayment: boolean }>()
);

export const getPaymentMethod = createAction(
  "[GET Payment ] getPayment method",
  props<{ customerId: string }>()
);

export const gotPaymentMethod = createAction(
  "[GOT Payment] gotPayment method",
  props<{ paymentmethods: paymentMethod[] }>()
);

export const makeCharge = createAction(
  "[Payment] Make charge ",
  props<{ chargeBodu: chargeBody }>()
);

export const madeCharge = createAction(
  "[Payment] Made charge",
  props<{ chargeResponse: chargeResponse }>()
);

export const getPaymentCustomerId = createAction(
  "[Payment] GET Customer Id",
  props<{ PersonTenantRoleId: number }>()
);

export const gotPaymentCustomerId = createAction(
  "[Payment] GOT Customer Id",
  props<{ customerIdObj: customerId }>()
);

// Schedule
export const getSchedule = createAction(
  "[Schedule] GET Schedule",
  props<{ schedule: Schedule }>()
);

export const gotSchedule = createAction(
  "[Schedule] GOT Schedule",
  props<{ scheduleres: any }>()
);

export const reschedule = createAction(
  "[Schedule] GET Rescheduled",
  props<{ rescheduleBody: Reschedule }>()
);

export const gotRescheduled = createAction(
  "[Schedule] GOT Rescheduled",
  props<{ rescheduleResponse: number }>()
);

export const rescheduleTC = createAction(
  "[Schedule] GET RescheduledTestCener",
  props<{ rescheduleBody: any }>()
);

export const gotRescheduledTC = createAction(
  "[Schedule] GET RescheduledTestCenter",
  props<{ rescheduleResponse: number }>()
);

export const getScheduled = createAction(
  "[Schedule] GET Scheduled",
  props<{ personTentantRole: number; cartId: number; PersonID: number }>()
);

export const gotScheduled = createAction(
  "[Schedule] GOT Scheduled",
  props<{ ScheduledResponse: any }>()
);

export const clearScheduledState = createAction(
  "[Schedule] Clearing Scheduled Store"
);

// Voucher
export const getVoucher = createAction(
  "[Voucher] GET Voucher",
  props<{ VocherValidator: Vocher }>()
);

export const gotVoucher = createAction(
  "[Voucher] GOT Voucher",
  props<{ VocherResponse: VocherResponse }>()
);
///single voucher apply
export const getVoucher_validate_apply = createAction(
  "[Voucher_validate_apply] GET Voucher_validate_apply",
  props<{ VocherValidator: Voucher_validate_apply }>()
);

export const gotVoucher_validate_apply = createAction(
  "[Voucher_validate_apply] GOT Voucher_validate_apply",
  props<{ VocherResponse: number }>()
);

export const getVoucherAssign = createAction(
  "[Voucher] GET VoucherAssign",
  props<{ personId: number }>()
);
export const gotVoucherAssign = createAction(
  "[Voucher] GOT VoucherAssign",
  props<{ VocherAssignResponse: VoucherModel[] }>()
);

export const getVoucherApply = createAction(
  "[Voucher] GET VoucherApply",
  props<{ VocherDetails: VochersApply }>()
);
export const gotVoucherApply = createAction(
  "[Voucher] GOT VoucherApply",
  props<{ VocherApplyResponse: number }>()
);

// Person form
export const getPersonForm = createAction(
  "[PersonForm] GET personform",
  props<{ candidateId: number; formTypeId1: number; formTypeId2: number }>()
);

export const gotPersonForm = createAction(
  "[PersonForm] GOT personforme",
  props<{ personForms: PersonForm[] }>()
);

export const createPaymentMethod = createAction(
  "[PaymentMethod] create payment method",
  props<{ CreatePaymentMethod }>()
);

export const paymentMethodCreated = createAction(
  "[PaymentMethod] payment method created",
  props<{ response: CreatePaymentMethodRespnse }>()
);

export const createPaymentCustomerId = createAction(
  "[PaymentCustomerId] create customer id",
  props<{ body: CreatePaymentCustomerIdBody }>()
);

export const paymentCustomerIdCreated = createAction(
  "[PaymentCustomerIdCreated]  customer id created",
  props<{ response: CreatePaymentCustomerIdResponse }>()
);

export const createupdate = createAction(
  "[update] create update vocher",
  props<{ VocherUpdateDetails: VocherUpdate }>()
);
export const createUpdate = createAction(
  "[updates] create Update Vocher",
  props<{ response: VocherUpdateResponse }>()
);

export const clearChargeResponseState = createAction(
  "[PaymentChargeResponseState] clear charge response state"
);
export const clearVocherResponse = createAction(
  "[VocherResponse] clear Vocher response state"
);

export const getShowRegisterExam = createAction(
  "[GET SHOW REGISTERED EXAM]",
  props<{ personTenantRoleId: number }>()
);

export const gotShowRegisterExam = createAction(
  "[GOT SHOW REGISTERED EXAM]",
  props<{ data: ShowRegisterExamModel }>()
);

export const deleteCard = createAction(
  "[Delete Card] delete card",
  props<{ id: string }>()
);

export const cardDeleted = createAction("[Card Deleted]  card deleted");

export const clearTimeSlotsTestCenter = createAction(
  "[CLEAR TIMESLOTS FOR TEST CENTER] Clear Data"
);
export const ClearTimeslots = createAction(
  "[CLEAR TIMESLOTS FOR ONLINE] Clear Data"
);

export const setPaymentErrorMessage = createAction(
  "[Payment Error]",
  props<{ message: any }>()
);

export const setFormProgressBar = createAction(
  "[setFormProgressBar]",
  props<{ formArray: any }>()
);

export const ReschedulePracticeExam = createAction(
  "[Practice Reschedule] get reschedule practice",
  props<{ PracticerescheduleBody: Reschedule }>()
);
export const gotPracticeRescheduled = createAction(
  "[Schedule] GOT Rescheduled",
  props<{ PracticerescheduleResponse: number }>()
);

export const PracticeretrySchedule = createAction(
  "[Practice Retry schedule ] get retry schedule",
  props<{ retryScheduleResponse: schedule }>()
);
export const gotPracticeRetryscheduled = createAction(
  "[Retry Schedule] GOT Retry scheduled",
  props<{ PracticeretryscheduleResponse: number }>()
);

export const makePracticeExamCharge = createAction(
  "[Payment] Make Practice charge ",
  props<{ chargePracticeBodu: chargeBody }>()
);

export const madePracticeExamCharge = createAction(
  "[Payment] Made Practice charge",
  props<{ chargePracticeResponse: chargeResponse }>()
);

export const cancelPracticeExam = createAction(
  "[Exam] Practice Cancel Exam",
  props<{ examScheduleId: number; candidateId: number }>()
);

export const examPracticeCancelled = createAction(
  "[Exam] Practice Exam Cancelled",
  props<{ isCancelled: boolean }>()
);

export const gotPracticeRegisteredExam = createAction(
  "[Exam] GOT Practice RegisteredExam",
  props<{ registeredPracticeExams: RegisteredExamsModel[] }>()
);

export const getPracticeRegisteredExam = createAction(
  "[ Exam] GET Practice RegisteredExam",
  props<{ candidateId: number }>()
);

export const getPracticeExamByERIdId = createAction(
  "[Exam] GET Exam Practice by ERId",
  props<{ eligibilityRouteId: number; personTenantRoleId: number }>()
);

export const gotPracticeExamId = createAction(
  "[Exam] GOT Practice ExamId",
  props<{ examdata: ExamModel[] }>()
);
export const removePracticeCartItem = createAction(
  "[Cart] deletePracticeCartitems",
  props<{ personTenantRoleId: number; cartItemsId: number }>()
);

export const cartItemPracticeRemoved = createAction(
  "[Cart] deletedPracticeCartitems",
  props<{ isPracticeDeleted: boolean }>()
);

export const gotPracticeCart = createAction(
  "[Practice Cart] GOT Practice Cart",
  props<{ cart: Cart; isPayment?: boolean }>()
);

export const getPracticeCart = createAction(
  "[ Practice Cart] GET Practice Cart",
  props<{ details: Details; isPayment?: boolean }>()
);
