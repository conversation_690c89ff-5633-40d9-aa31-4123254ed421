<svg xmlns="http://www.w3.org/2000/svg" width="311.394" height="316.964" viewBox="0 0 311.394 316.964">
  <g id="Group_38" data-name="Group 38" transform="translate(-301.288 -308.6)">
    <g id="Group_15" data-name="Group 15" transform="translate(456.072 443.997)">
      <path id="Path_85" data-name="Path 85" d="M1062.58,1075.817l39.775,15.974,49.235-122.6-33.621-13.5a6.631,6.631,0,0,0-8.625,3.682Z" transform="translate(-1057.9 -955.167)" fill="#7c9af2"/>
      <g id="Group_13" data-name="Group 13" transform="translate(0 9.01)">
        <rect id="Rectangle_7" data-name="Rectangle 7" width="77.345" height="108.747" transform="matrix(-0.985, -0.175, 0.175, -0.985, 76.158, 120.579)" fill="#bed1f9"/>
        <rect id="Rectangle_8" data-name="Rectangle 8" width="34.539" height="0.776" transform="translate(72.092 21.164) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_9" data-name="Rectangle 9" width="49.051" height="0.775" transform="translate(79.58 20.494) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_10" data-name="Rectangle 10" width="59.51" height="0.775" transform="translate(82.965 31.36) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_11" data-name="Rectangle 11" width="59.51" height="0.776" transform="translate(82.314 35.033) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_12" data-name="Rectangle 12" width="59.51" height="0.775" transform="translate(81.012 42.377) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_13" data-name="Rectangle 13" width="59.51" height="0.775" transform="matrix(-0.985, -0.175, 0.175, -0.985, 80.361, 46.049)" fill="#55769f"/>
        <rect id="Rectangle_14" data-name="Rectangle 14" width="59.51" height="0.775" transform="translate(79.71 49.721) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_15" data-name="Rectangle 15" width="59.51" height="0.775" transform="translate(76.847 65.873) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_16" data-name="Rectangle 16" width="59.51" height="0.776" transform="translate(76.171 69.681) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_17" data-name="Rectangle 17" width="59.51" height="0.775" transform="translate(75.496 73.489) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_18" data-name="Rectangle 18" width="59.51" height="0.775" transform="translate(74.821 77.297) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_19" data-name="Rectangle 19" width="59.51" height="0.776" transform="translate(74.146 81.105) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_20" data-name="Rectangle 20" width="59.51" height="0.775" transform="translate(73.471 84.913) rotate(-169.947)" fill="#55769f"/>
        <rect id="Rectangle_21" data-name="Rectangle 21" width="59.51" height="0.775" transform="matrix(-0.985, -0.175, 0.175, -0.985, 72.796, 88.721)" fill="#55769f"/>
      </g>
      <g id="Group_14" data-name="Group 14" transform="translate(2.281 6.336)">
        <rect id="Rectangle_22" data-name="Rectangle 22" width="77.345" height="108.747" transform="matrix(-0.967, -0.255, 0.255, -0.967, 74.798, 124.851)" fill="#dde8fe"/>
        <rect id="Rectangle_23" data-name="Rectangle 23" width="34.539" height="0.775" transform="translate(78.878 25.437) rotate(-165.256)" fill="#92b5f9"/>
        <rect id="Rectangle_24" data-name="Rectangle 24" width="49.051" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 86.395, 25.382)" fill="#92b5f9"/>
        <rect id="Rectangle_25" data-name="Rectangle 25" width="59.51" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 88.88, 36.489)" fill="#92b5f9"/>
        <rect id="Rectangle_26" data-name="Rectangle 26" width="59.51" height="0.776" transform="translate(87.931 40.095) rotate(-165.255)" fill="#92b5f9"/>
        <rect id="Rectangle_27" data-name="Rectangle 27" width="59.51" height="0.775" transform="translate(86.032 47.308) rotate(-165.256)" fill="#92b5f9"/>
        <rect id="Rectangle_28" data-name="Rectangle 28" width="59.51" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 85.083, 50.915)" fill="#92b5f9"/>
        <rect id="Rectangle_29" data-name="Rectangle 29" width="59.51" height="0.775" transform="translate(84.134 54.522) rotate(-165.256)" fill="#92b5f9"/>
        <rect id="Rectangle_30" data-name="Rectangle 30" width="59.51" height="0.775" transform="translate(79.959 70.385) rotate(-165.256)" fill="#92b5f9"/>
        <rect id="Rectangle_31" data-name="Rectangle 31" width="59.51" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 78.975, 74.125)" fill="#92b5f9"/>
        <rect id="Rectangle_32" data-name="Rectangle 32" width="59.51" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 77.991, 77.865)" fill="#92b5f9"/>
        <rect id="Rectangle_33" data-name="Rectangle 33" width="59.51" height="0.775" transform="translate(77.007 81.605) rotate(-165.256)" fill="#92b5f9"/>
        <rect id="Rectangle_34" data-name="Rectangle 34" width="59.51" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 76.022, 85.345)" fill="#55769f"/>
        <rect id="Rectangle_35" data-name="Rectangle 35" width="59.51" height="0.775" transform="matrix(-0.967, -0.255, 0.255, -0.967, 75.038, 89.085)" fill="#55769f"/>
        <rect id="Rectangle_36" data-name="Rectangle 36" width="59.51" height="0.775" transform="translate(74.054 92.825) rotate(-165.256)" fill="#55769f"/>
      </g>
      <path id="Path_86" data-name="Path 86" d="M1134.63,1081.707l80.076,32.159,46.7-116.292-67.288-41.509a7.164,7.164,0,0,0-10.409,3.427Z" transform="translate(-1114.858 -954.995)" fill="#92b5f9"/>
    </g>
    <g id="Group_16" data-name="Group 16" transform="translate(511.482 534.703)">
      <path id="Path_87" data-name="Path 87" d="M1380.226,1411.835l14.508-4.25c2.554-10.558,4.088-19.55,4.088-19.55-17.505.706-30.977,6.063-41.339,13.679l-2.144,17.244-4.628-11.561c-24.463,23.329-27.325,59.343-27.325,59.343s49.237-19.265,58.622-27.2c3.624-3.065,6.956-11.442,9.681-20.571C1386.791,1416.327,1380.226,1411.835,1380.226,1411.835Z" transform="translate(-1319.487 -1388.035)" fill="#fa7167"/>
      <path id="Path_88" data-name="Path 88" d="M1398.822,1388.035s-57.621,45.2-75.436,78.7c0,0,49.237-19.265,58.622-27.2,3.624-3.065,6.956-11.442,9.681-20.571-4.9-2.638-11.463-7.13-11.463-7.13l14.508-4.25C1397.288,1397.028,1398.822,1388.035,1398.822,1388.035Z" transform="translate(-1319.487 -1388.035)" fill="#e8605d"/>
      <path id="Path_89" data-name="Path 89" d="M1394,1589.239a69.3,69.3,0,0,0-28.723-2.181c-1.656,3.8-3.733,8.315-3.733,8.315l-2.324-7.224c-33.25,7.482-54.448,36.925-54.448,36.925,22.3,6.79,39.428,5.66,52.426.955.088-2.774.159-6.895.159-6.895l3.954,5.233a59.432,59.432,0,0,0,15.37-10.018c-2.077-2.708-4.339-5.833-4.339-5.833l8.177,2.086A64.335,64.335,0,0,0,1394,1589.239Z" transform="translate(-1304.773 -1544.878)" fill="#fa7167"/>
      <path id="Path_90" data-name="Path 90" d="M1357.2,1636.607c.088-2.774.159-6.9.159-6.9l3.954,5.233a59.432,59.432,0,0,0,15.37-10.019c-2.077-2.708-4.339-5.833-4.339-5.833l8.177,2.086a64.334,64.334,0,0,0,13.48-21.364c-39.473,6.04-89.229,35.836-89.229,35.836C1327.072,1642.442,1344.2,1641.312,1357.2,1636.607Z" transform="translate(-1304.773 -1555.455)" fill="#e8605d"/>
    </g>
    <g id="Group_17" data-name="Group 17" transform="translate(341.626 489.889)">
      <path id="Path_91" data-name="Path 91" d="M509.092,1175.069h-5.476a5.724,5.724,0,0,1,3.236-.979h137.18c7.177,0,13,5.157,13,11.519v7.317c0,6.362-5.819,11.52-13,11.52l-135.646-.041Z" transform="translate(-501.574 -1174.09)" fill="#e8605d"/>
      <path id="Path_92" data-name="Path 92" d="M1132.032,1192.927v-7.317c0-6.362-5.818-11.519-13-11.519h2.946c7.177,0,13,5.157,13,11.519v7.317c0,6.362-5.818,11.52-13,11.52h-2.907C1126.235,1204.427,1132.032,1199.277,1132.032,1192.927Z" transform="translate(-988.086 -1174.09)" fill="#dde8fe"/>
      <path id="Path_93" data-name="Path 93" d="M978.651,1192.927v-7.317c0-6.362-5.818-11.519-13-11.519H968.6c7.177,0,13,5.157,13,11.519v7.317c0,6.362-5.819,11.52-13,11.52h-2.907C972.853,1204.427,978.651,1199.277,978.651,1192.927Z" transform="translate(-866.832 -1174.09)" fill="#dde8fe"/>
      <path id="Path_94" data-name="Path 94" d="M579.264,1174.22c6.864,0,12.427,4.932,12.427,11.016v8.269c0,6.084-5.564,11.017-12.427,11.017h-85.4v-1.85h.353l21.71-1.823,4.388-15.26s-8.47-8.432-9.022-8.65-17.077-.877-17.077-.877l-.353,0v-1.839Z" transform="translate(-493.866 -1174.193)" fill="#fa7167"/>
      <path id="Path_95" data-name="Path 95" d="M500.679,1196.3c0-8.922-5.2-13.247-5.2-13.247h83.312c6.3,0,11.415,4.531,11.415,10.119v6.365c0,5.588-5.111,10.119-11.415,10.119H495.477S500.679,1207.531,500.679,1196.3Z" transform="translate(-495.14 -1181.173)" fill="#dde8fe"/>
      <path id="Path_96" data-name="Path 96" d="M590.2,1193.169v6.365a9.052,9.052,0,0,1-.333,2.427c-2.81-6.094-8.195-11.964-18.446-13.851-23.935-4.406-75.947-5.059-75.947-5.059h83.312C585.093,1183.05,590.2,1187.581,590.2,1193.169Z" transform="translate(-495.14 -1181.173)" fill="#daeef7" style="mix-blend-mode: multiply;isolation: isolate"/>
    </g>
    <g id="Group_18" data-name="Group 18" transform="translate(342.894 550.318)">
      <path id="Path_97" data-name="Path 97" d="M517.151,1463.808h-6.2a6.052,6.052,0,0,1,3.662-1.226H669.878A14.566,14.566,0,0,1,684.586,1477v9.159a14.566,14.566,0,0,1-14.708,14.419l-153.525-.051Z" transform="translate(-508.642 -1462.582)" fill="#0b0754"/>
      <path id="Path_98" data-name="Path 98" d="M596.573,1462.744a13.93,13.93,0,0,1,14.065,13.789v10.351a13.93,13.93,0,0,1-14.065,13.789H499.918v-2.315h.4l24.572-2.281,4.966-19.1s-9.586-10.555-10.211-10.827-19.327-1.1-19.327-1.1l-.4,0v-2.3Z" transform="translate(-499.918 -1462.71)" fill="#4951ec"/>
      <path id="Path_99" data-name="Path 99" d="M507.63,1490.378c0-11.167-5.887-16.581-5.887-16.581h94.292a12.8,12.8,0,0,1,12.92,12.666v7.968a12.8,12.8,0,0,1-12.92,12.666H501.743S507.63,1504.44,507.63,1490.378Z" transform="translate(-501.361 -1471.448)" fill="#dde8fe"/>
      <path id="Path_100" data-name="Path 100" d="M608.955,1486.463v7.968a12.459,12.459,0,0,1-.377,3.037c-3.18-7.628-9.276-14.975-20.878-17.338-27.09-5.516-85.957-6.333-85.957-6.333h94.292A12.8,12.8,0,0,1,608.955,1486.463Z" transform="translate(-501.361 -1471.448)" fill="#bed1f9" style="mix-blend-mode: multiply;isolation: isolate"/>
    </g>
    <g id="Group_19" data-name="Group 19" transform="translate(356.455 520.168)">
      <path id="Path_101" data-name="Path 101" d="M732.158,1319.617h6.2a7.139,7.139,0,0,0-3.664-.973H579.372c-8.126,0-14.714,5.122-14.714,11.441v7.267c0,6.319,6.588,11.441,14.714,11.441l153.585-.04Z" transform="translate(-564.658 -1318.644)" fill="#4951ec"/>
      <path id="Path_102" data-name="Path 102" d="M890.232,1318.773c-7.771,0-14.071,4.9-14.071,10.941v8.213c0,6.043,6.3,10.941,14.071,10.941h96.692v-1.837h-.4l-24.581-1.81-4.968-15.156s9.589-8.375,10.214-8.591,19.335-.871,19.335-.871l.4,0v-1.827Z" transform="translate(-810.912 -1318.746)" fill="#0b0754"/>
      <path id="Path_103" data-name="Path 103" d="M992.455,1340.7a16.7,16.7,0,0,1,5.889-13.157h-94.33c-7.138,0-12.925,4.5-12.925,10.05v6.322c0,5.55,5.787,10.05,12.925,10.05h94.33S992.455,1351.858,992.455,1340.7Z" transform="translate(-822.714 -1325.679)" fill="#dde8fe"/>
      <path id="Path_104" data-name="Path 104" d="M891.09,1337.593v6.322a7.952,7.952,0,0,0,.377,2.41c3.181-6.053,9.279-11.883,20.886-13.757,27.1-4.376,85.991-5.025,85.991-5.025h-94.33C896.877,1327.543,891.09,1332.042,891.09,1337.593Z" transform="translate(-822.714 -1325.679)" fill="#daeef7" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_105" data-name="Path 105" d="M644.835,1337.353v-7.267c0-6.319,6.588-11.441,14.714-11.441H644.8c-8.126,0-14.714,5.122-14.714,11.441v7.267c0,6.319,6.588,11.441,14.714,11.441l14.554,0C651.318,1348.709,644.835,1343.62,644.835,1337.353Z" transform="translate(-616.381 -1318.644)" fill="#fa7167"/>
    </g>
    <path id="Path_106" data-name="Path 106" d="M620.641,1645.441h7.773a9.806,9.806,0,0,0-4.593-1.087H429.1c-10.188,0-18.447,5.724-18.447,12.783v8.12c0,7.06,8.259,12.784,18.447,12.784L621.643,1678Z" transform="translate(-86.455 -1055.961)" fill="#92b5f9"/>
    <path id="Path_107" data-name="Path 107" d="M818.815,1644.5c-9.743,0-17.641,5.474-17.641,12.225v9.176c0,6.752,7.9,12.225,17.641,12.225h121.22v-2.053h-.5l-30.817-2.023-6.229-16.934s12.022-9.357,12.806-9.6,24.24-.974,24.24-.974l.5,0V1644.5Z" transform="translate(-395.178 -1056.074)" fill="#7c9af2"/>
    <path id="Path_108" data-name="Path 108" d="M946.968,1669a18.136,18.136,0,0,1,7.383-14.7H836.093c-8.949,0-16.2,5.027-16.2,11.229v7.064c0,6.2,7.255,11.229,16.2,11.229H954.35S946.968,1681.464,946.968,1669Z" transform="translate(-409.973 -1063.821)" fill="#dde8fe"/>
    <path id="Path_109" data-name="Path 109" d="M819.889,1665.526v7.064a8.011,8.011,0,0,0,.473,2.693c3.988-6.763,11.633-13.276,26.184-15.371,33.975-4.89,107.8-5.614,107.8-5.614H836.093C827.144,1654.3,819.889,1659.324,819.889,1665.526Z" transform="translate(-409.973 -1063.821)" fill="#bed1f9" style="mix-blend-mode: multiply;isolation: isolate"/>
    <path id="Rectangle_37" data-name="Rectangle 37" d="M1.859,0h281.7a1.858,1.858,0,0,1,1.858,1.858v0a1.858,1.858,0,0,1-1.858,1.858H1.859A1.859,1.859,0,0,1,0,1.859v0A1.859,1.859,0,0,1,1.859,0Z" transform="translate(586.704 625.564) rotate(180)" fill="#4951ec"/>
    <path id="Path_110" data-name="Path 110" d="M1438.588,1101.878l-54.518-31.182-7.369,14.6,55.784,29.512Z" transform="translate(-850.153 -602.463)" fill="#7c9af2"/>
    <path id="Path_111" data-name="Path 111" d="M1444.012,681.505s.43,4.24,3.776,4.533c-3.346.293-3.776,4.534-3.776,4.534s-.43-4.24-3.776-4.534C1443.582,685.745,1444.012,681.505,1444.012,681.505Z" transform="translate(-900.379 -294.795)" fill="#92b5f9"/>
    <path id="Path_112" data-name="Path 112" d="M1718.508,1271.592s.645,6.36,5.664,6.8c-5.019.44-5.664,6.8-5.664,6.8s-.645-6.36-5.664-6.8C1717.863,1277.952,1718.508,1271.592,1718.508,1271.592Z" transform="translate(-1115.885 -761.279)" fill="#fa7167"/>
    <path id="Path_113" data-name="Path 113" d="M1645.772,515.975s.573,5.648,5.03,6.038c-4.457.391-5.03,6.038-5.03,6.038s-.573-5.648-5.03-6.038C1645.2,521.623,1645.772,515.975,1645.772,515.975Z" transform="translate(-1058.886 -163.937)" fill="#dde8fe"/>
    <path id="Path_114" data-name="Path 114" d="M385.509,1391.148s.385,3.8,3.384,4.063c-3,.263-3.384,4.063-3.384,4.063s-.386-3.8-3.385-4.063C385.123,1394.948,385.509,1391.148,385.509,1391.148Z" transform="translate(-63.904 -855.793)" fill="#92b5f9"/>
    <path id="Path_115" data-name="Path 115" d="M1556.4,881.172s.385,3.792,3.377,4.055c-2.993.262-3.377,4.055-3.377,4.055s-.385-3.793-3.377-4.055C1556.013,884.964,1556.4,881.172,1556.4,881.172Z" transform="translate(-989.539 -452.638)" fill="#fa7167"/>
    <path id="Path_116" data-name="Path 116" d="M1307.671,405.958s.385,3.792,3.377,4.055c-2.993.262-3.377,4.055-3.377,4.055s-.385-3.793-3.377-4.055C1307.287,409.75,1307.671,405.958,1307.671,405.958Z" transform="translate(-792.912 -76.965)" fill="#fa7167"/>
    <path id="Path_117" data-name="Path 117" d="M554.8,636.418s.5,4.962,4.419,5.306c-3.916.343-4.419,5.306-4.419,5.306s-.5-4.962-4.419-5.306C554.294,641.38,554.8,636.418,554.8,636.418Z" transform="translate(-196.914 -259.152)" fill="#fa7167"/>
    <path id="Path_118" data-name="Path 118" d="M386.213,1046.8s.737,7.271,6.475,7.774c-5.738.5-6.475,7.774-6.475,7.774s-.738-7.271-6.475-7.774C385.476,1054.069,386.213,1046.8,386.213,1046.8Z" transform="translate(-62.017 -583.572)" fill="#fa7167"/>
    <path id="Path_119" data-name="Path 119" d="M1229.241,731.436s.573,5.648,5.029,6.039c-4.457.391-5.029,6.038-5.029,6.038s-.573-5.648-5.03-6.038C1228.668,737.084,1229.241,731.436,1229.241,731.436Z" transform="translate(-729.603 -334.267)" fill="#fa7167"/>
    <path id="Path_120" data-name="Path 120" d="M1744.914,1022.5s.573,5.648,5.029,6.038c-4.457.391-5.029,6.038-5.029,6.038s-.573-5.648-5.03-6.038C1744.341,1028.152,1744.914,1022.5,1744.914,1022.5Z" transform="translate(-1137.261 -564.366)" fill="#92b5f9"/>
    <path id="Path_121" data-name="Path 121" d="M414.234,469.244s.408,4.023,3.582,4.3c-3.175.278-3.582,4.3-3.582,4.3s-.408-4.023-3.583-4.3C413.826,473.267,414.234,469.244,414.234,469.244Z" transform="translate(-86.455 -126.995)" fill="#92b5f9"/>
    <path id="Path_122" data-name="Path 122" d="M608.431,944.036s.654,6.443,5.738,6.889c-5.084.446-5.738,6.889-5.738,6.889s-.653-6.443-5.738-6.889C607.778,950.479,608.431,944.036,608.431,944.036Z" transform="translate(-238.271 -502.335)" fill="#92b5f9"/>
    <path id="Path_123" data-name="Path 123" d="M346,743.751s.447,5.111,3.918,5.465c-3.472.354-3.918,5.465-3.918,5.465s-.446-5.112-3.918-5.465C345.554,748.862,346,743.751,346,743.751Z" transform="translate(-32.249 -344.002)" fill="#dde8fe"/>
    <path id="Path_124" data-name="Path 124" d="M742.832,353.626s.448,5.126,3.93,5.481c-3.482.355-3.93,5.481-3.93,5.481s-.448-5.126-3.93-5.481C742.384,358.752,742.832,353.626,742.832,353.626Z" transform="translate(-345.949 -35.595)" fill="#dde8fe"/>
    <g id="Group_34" data-name="Group 34" transform="translate(421.843 350.844)">
      <g id="Group_33" data-name="Group 33" transform="translate(0 0)">
        <path id="Path_125" data-name="Path 125" d="M1044.561,1157.2v3.1a.464.464,0,0,0,.463.464h7.239a.526.526,0,0,0,.282-.969l-4.062-2.6A3.839,3.839,0,0,1,1044.561,1157.2Z" transform="translate(-1009.426 -1021.692)" fill="#4951ec"/>
        <path id="Path_126" data-name="Path 126" d="M961.172,514.342a.143.143,0,0,1-.193-.162c.181-.861.734-3.574.526-3.67-.248-.114-.8,2.3-1.053,2.328s.348-2.31.063-2.389-.76,2.072-.891,2.08.1-2.1-.037-2.248-.664,2.075-.821,2.13-.3-1.6-.448-1.6-.238-.03-.972,3.788a5.3,5.3,0,0,1-.291,1.728l.551,1.124,1.708.057s3.711-3.358,3.68-3.562S962.8,513.7,961.172,514.342Z" transform="translate(-940.249 -510.274)" fill="#fb9390"/>
        <path id="Path_127" data-name="Path 127" d="M1055.183,520.288a.143.143,0,0,0,.245-.056c.231-.849.975-3.516,1.2-3.507.273.011-.334,2.411-.123,2.552s.744-2.215,1.033-2.155-.268,2.191-.155,2.258.866-1.917,1.057-1.984-.354,2.149-.24,2.27,1-1.284,1.129-1.222.226.082-.861,3.815a5.3,5.3,0,0,0-.528,1.67l-1,.75-1.546-.728s-1.773-4.679-1.652-4.847S1054.024,518.978,1055.183,520.288Z" transform="translate(-1016.68 -515.374)" fill="#fb9390"/>
        <path id="Path_128" data-name="Path 128" d="M972.008,672.947l.027,6.528-6.341-.978,1.058-7.34Z" transform="translate(-947.079 -637.458)" fill="#fb9390"/>
        <path id="Path_129" data-name="Path 129" d="M968.418,674.536l.487-3.379,5.262,2.409,0,1.289A9.568,9.568,0,0,1,968.418,674.536Z" transform="translate(-949.233 -637.458)" fill="#ef7f7f" opacity="0.6"/>
        <path id="Path_130" data-name="Path 130" d="M906.985,1155.555v3.4a.509.509,0,0,1-.509.509h-7.964a.576.576,0,0,1-.31-1.062l4.468-2.848A4.235,4.235,0,0,0,906.985,1155.555Z" transform="translate(-893.513 -1020.392)" fill="#4951ec"/>
        <g id="Group_22" data-name="Group 22" transform="translate(4.356 72.292)">
          <g id="Group_21" data-name="Group 21">
            <g id="Group_20" data-name="Group 20">
              <path id="Path_131" data-name="Path 131" d="M899.462,860.487c.016-.727-1.877,1.417-1.842.837.743-12.077,24.34-1.951,24.34-1.951l5.76,4.227,6.5,55.973s-3.53,1.022-5.852.186l-14.4-47.964-1.771-.681A100.079,100.079,0,0,1,899.462,860.487Z" transform="translate(-897.62 -855.399)" fill="#3a3f46"/>
            </g>
          </g>
        </g>
        <g id="Group_32" data-name="Group 32" transform="translate(4.279 75.472)">
          <g id="Group_31" data-name="Group 31">
            <g id="Group_23" data-name="Group 23" transform="translate(0.031 3.237)">
              <path id="Path_132" data-name="Path 132" d="M897.423,886.036c-.007.141-.013.286-.019.434C897.41,886.322,897.416,886.177,897.423,886.036Z" transform="translate(-897.404 -886.036)" fill="#06bfad"/>
            </g>
            <g id="Group_24" data-name="Group 24" transform="translate(0.015 3.678)">
              <path id="Path_133" data-name="Path 133" d="M897.342,888.144c-.006.151-.011.307-.016.465C897.331,888.451,897.336,888.3,897.342,888.144Z" transform="translate(-897.326 -888.144)" fill="#06bfad"/>
            </g>
            <g id="Group_25" data-name="Group 25" transform="translate(0 4.205)">
              <path id="Path_134" data-name="Path 134" d="M897.268,890.66c0,.165-.009.333-.013.505Q897.261,890.907,897.268,890.66Z" transform="translate(-897.255 -890.66)" fill="#06bfad"/>
            </g>
            <g id="Group_26" data-name="Group 26" transform="translate(0.055 2.744)">
              <path id="Path_135" data-name="Path 135" d="M897.538,883.684c-.008.13-.015.266-.022.4C897.523,883.95,897.53,883.814,897.538,883.684Z" transform="translate(-897.516 -883.684)" fill="#06bfad"/>
            </g>
            <g id="Group_27" data-name="Group 27" transform="translate(0.058)">
              <path id="Path_136" data-name="Path 136" d="M897.555,870.584c4.862,5.131,14.576,12.533,14.576,12.533l1.771.681-1.771-.681-5.475,48.459s-3.9.743-6.131-.465C900.525,931.112,897.237,884.984,897.555,870.584Z" transform="translate(-897.533 -870.584)" fill="#3a3f46"/>
            </g>
            <g id="Group_28" data-name="Group 28" transform="translate(33.458 60.992)">
              <path id="Path_137" data-name="Path 137" d="M1060.207,1161.766a15.2,15.2,0,0,1-3.222.506,15.2,15.2,0,0,0,3.222-.506Z" transform="translate(-1056.985 -1161.766)" fill="#06bfad"/>
            </g>
            <g id="Group_29" data-name="Group 29" transform="translate(31.508 61.367)">
              <path id="Path_138" data-name="Path 138" d="M1047.678,1163.553c.157.032.317.058.478.078C1047.995,1163.611,1047.835,1163.585,1047.678,1163.553Z" transform="translate(-1047.678 -1163.553)" fill="#06bfad"/>
            </g>
            <g id="Group_30" data-name="Group 30" transform="translate(32.229 61.472)">
              <path id="Path_139" data-name="Path 139" d="M1052.349,1164.081a9.522,9.522,0,0,1-1.228-.027A9.522,9.522,0,0,0,1052.349,1164.081Z" transform="translate(-1051.121 -1164.054)" fill="#06bfad"/>
            </g>
          </g>
        </g>
        <path id="Path_140" data-name="Path 140" d="M989.886,868.621l.928,7.988c-2.456.951-8.442,1.967-12.952-7.9-1.422-3.112-1.788-5.351-1.526-6.959a64.074,64.074,0,0,1,7.79,2.643Z" transform="translate(-955.43 -788.129)" fill="#3a3f46"/>
        <path id="Path_141" data-name="Path 141" d="M905.054,703.925c.273,0,4.36-9.264,4.36-9.264,3.5,2.452,8.242.75,8.242.75s19.095,34.942,6.289,40.391-24.884-1.181-24.884-1.181-2.725-7.084-2.725-7.356S904.781,703.925,905.054,703.925Z" transform="translate(-892.248 -656.038)" fill="#dde8fe"/>
        <path id="Path_142" data-name="Path 142" d="M1006.233,546.1s.827,16.578-.726,19.617c-5.154,10.081-11.557,11.1-11.557,11.1s-2.2,35.26-.386,43.706c0,0,6.04,1.862,14.94-2.316,0,0-6.812-27.336-6.448-30.787,0,0,10.9-9.718,11.262-21.161a77.27,77.27,0,0,0-2.634-21.615Z" transform="translate(-968.544 -537.445)" fill="#209e91"/>
        <path id="Path_143" data-name="Path 143" d="M885.135,608.8a160.938,160.938,0,0,1,3.678-25.565c3-12.442,5.177-17.619,5.177-17.619-4.019-4.427-3.352-8.43-3-13.623.409-5.994,6.266-17.982,6.266-17.982l-3.95-2.588c-18.936,22.75-8.446,40.868-8.446,40.868s-7.084,25.066-8.037,32.694A57.666,57.666,0,0,0,885.135,608.8Z" transform="translate(-876.825 -526.991)" fill="#209e91"/>
        <path id="Path_144" data-name="Path 144" d="M1026.6,733.568a4.189,4.189,0,0,1-.761-.723,7.688,7.688,0,0,1-1.507-3.28,9.532,9.532,0,0,0,1.621,3.184,3.864,3.864,0,0,0,.885.809l-.054.049c0,.023,0,.047-.006.072a2.07,2.07,0,0,1-1.44-.338,6.569,6.569,0,0,1-1.211-.869,8.751,8.751,0,0,0,1.255.781A2.708,2.708,0,0,0,1026.6,733.568Z" transform="translate(-993.276 -683.631)" fill="#fff"/>
        <path id="Path_145" data-name="Path 145" d="M915.368,711.108a4.2,4.2,0,0,0,.865-.8,9.273,9.273,0,0,0,1.621-3.184,7.692,7.692,0,0,1-1.507,3.28,4.192,4.192,0,0,1-.761.723,1.714,1.714,0,0,0,.533-.062,3.5,3.5,0,0,0,.684-.254,8.584,8.584,0,0,0,1.255-.782,6.593,6.593,0,0,1-1.211.869,3.542,3.542,0,0,1-.7.292,1.471,1.471,0,0,1-.776.04l-.145-.039Z" transform="translate(-907.185 -665.894)" fill="#fff"/>
        <path id="Path_146" data-name="Path 146" d="M950.964,614.109l-.383,4.48.708.445,1.309-5.455Z" transform="translate(-935.132 -591.94)" fill="#0b0754"/>
        <path id="Path_147" data-name="Path 147" d="M945.7,629.335a1.534,1.534,0,1,1-1.822-1.556A1.715,1.715,0,0,1,945.7,629.335Z" transform="translate(-928.919 -603.149)" fill="#ef7f7f"/>
        <path id="Path_148" data-name="Path 148" d="M950.834,608.628a14.8,14.8,0,0,1-.063-3.763c.025-.245.035-.49.041-.735a7.993,7.993,0,1,1,15.828,1.759,1.727,1.727,0,0,1,1.55.186,2.41,2.41,0,0,1-2.724,3.834,1.531,1.531,0,0,1-.306-.292,7.5,7.5,0,0,1-8.654,5.275A7.358,7.358,0,0,1,950.834,608.628Z" transform="translate(-935.211 -578.3)" fill="#fb9390"/>
        <path id="Path_149" data-name="Path 149" d="M992.6,636.312a.7.7,0,1,0,.634-.435A.7.7,0,0,0,992.6,636.312Z" transform="translate(-968.311 -609.568)" fill="#0b0754"/>
        <path id="Path_150" data-name="Path 150" d="M964.253,632.068a.7.7,0,1,0,.635-.435A.7.7,0,0,0,964.253,632.068Z" transform="translate(-945.899 -606.212)" fill="#0b0754"/>
        <path id="Path_151" data-name="Path 151" d="M972.159,658.583a4.582,4.582,0,0,1-1.121.385,3.537,3.537,0,0,1-1.168.045,3.014,3.014,0,0,1-1.1-.383,4.2,4.2,0,0,1-.912-.754,3.223,3.223,0,0,0,.855.845,2.755,2.755,0,0,0,1.135.43,3.159,3.159,0,0,0,1.211-.079A3.464,3.464,0,0,0,972.159,658.583Z" transform="translate(-948.791 -626.958)" fill="#ef7f7f"/>
        <path id="Path_152" data-name="Path 152" d="M990.1,629.058a6.546,6.546,0,0,1,1.043-.112,3.447,3.447,0,0,1,.97.115,3.2,3.2,0,0,1,.891.387,10.026,10.026,0,0,1,.854.613,2.311,2.311,0,0,0-.612-.929,2.271,2.271,0,0,0-1-.561,2.376,2.376,0,0,0-1.149-.02A2.2,2.2,0,0,0,990.1,629.058Z" transform="translate(-966.376 -603.73)" fill="#0b0754"/>
        <path id="Path_153" data-name="Path 153" d="M962.211,624.672a2.2,2.2,0,0,0-.778-.8,2.379,2.379,0,0,0-1.095-.35,2.273,2.273,0,0,0-1.131.209,2.321,2.321,0,0,0-.877.684,9.664,9.664,0,0,1,1.006-.307,3.2,3.2,0,0,1,.968-.081,3.447,3.447,0,0,1,.955.2A6.537,6.537,0,0,1,962.211,624.672Z" transform="translate(-941.257 -599.8)" fill="#0b0754"/>
        <path id="Path_154" data-name="Path 154" d="M974.926,633.214a10.088,10.088,0,0,0-.91.986,9.481,9.481,0,0,0-.782,1.1,2.621,2.621,0,0,0-.343.808c-.023.146-.012.289.233.53a3.231,3.231,0,0,0,.527.437,4.339,4.339,0,0,0,.584.335,7.354,7.354,0,0,1-.949-.912,1.29,1.29,0,0,1-.154-.239.518.518,0,0,1,.047-.249,3.572,3.572,0,0,1,.29-.57c.221-.376.472-.741.717-1.11S974.686,633.593,974.926,633.214Z" transform="translate(-952.763 -607.462)" fill="#ef7f7f"/>
        <path id="Path_155" data-name="Path 155" d="M948.627,581.447s-2.354-.453.545-1.874,10.005-2.248,13.72.934c0,0,1.249-1.6,2.372-.9s-1.169,1.483-1.169,1.483,1.569.3,1.871,1.174c.224.644-.736.453-1.251.31.853.351,3.359,2.719.1,9.187-.576-.27-4.3-5.585-4.3-5.585s-7.491,2.461-6.125.42c0,0-8.73,2.2-6.614-1.762C947.777,584.835,944.559,584.351,948.627,581.447Z" transform="translate(-931.96 -564.062)" fill="#0b0754"/>
        <path id="Path_156" data-name="Path 156" d="M1016.078,606.121a1.535,1.535,0,0,0-1.428-.112c-.863.32-1.412,2.257-1.412,2.257s-2.827,2.315-1.448-7.774c1.354-9.912,3.229,1.65,3.229,1.65Z" transform="translate(-983.22 -578.421)" fill="#0b0754"/>
      </g>
    </g>
    <g id="Group_37" data-name="Group 37" transform="translate(437.136 308.6)">
      <path id="Path_157" data-name="Path 157" d="M1000.441,481.988l-12.925-.921a1.187,1.187,0,0,1-1.1-1.268h0a1.187,1.187,0,0,1,1.268-1.1l12.925.921a1.187,1.187,0,0,1,1.1,1.268h0A1.188,1.188,0,0,1,1000.441,481.988Z" transform="translate(-978.751 -443.067)" fill="#33c2b3"/>
      <path id="Path_158" data-name="Path 158" d="M967.8,308.6l4.517,11.072L984,322.228l-9.135,7.717,1.179,11.9-10.162-6.3-10.953,4.8,2.854-11.612-7.948-8.934,11.926-.874Z" transform="translate(-949.835 -308.6)" fill="#33c2b3"/>
      <g id="Group_36" data-name="Group 36" transform="translate(0 0)">
        <g id="Group_35" data-name="Group 35">
          <path id="Path_159" data-name="Path 159" d="M1031.242,308.6l-1.157,16.247,5.674-5.175Z" transform="translate(-1013.275 -308.6)" fill="#209e91"/>
          <path id="Path_160" data-name="Path 160" d="M1047.441,373.663l-17.356,2.618,8.222,5.1Z" transform="translate(-1013.275 -360.034)" fill="#209e91"/>
          <path id="Path_161" data-name="Path 161" d="M966.645,363.8l-4.883-5.927-11.926.874Z" transform="translate(-949.835 -347.549)" fill="#209e91"/>
          <path id="Path_162" data-name="Path 162" d="M974.156,401.657l11.715-15.494-8.861,3.882Z" transform="translate(-969.062 -369.916)" fill="#209e91"/>
          <path id="Path_163" data-name="Path 163" d="M1036.609,403.162l-9.4-17-.762,10.7Z" transform="translate(-1010.4 -369.916)" fill="#209e91"/>
        </g>
      </g>
      <rect id="Rectangle_38" data-name="Rectangle 38" width="19.686" height="6.919" transform="translate(5.391 38.013) rotate(4.076)" fill="#fa7167"/>
      <rect id="Rectangle_39" data-name="Rectangle 39" width="0.756" height="19.686" transform="translate(4.899 44.913) rotate(-85.916)" fill="#cdd6e0" style="mix-blend-mode: multiply;isolation: isolate"/>
      <rect id="Rectangle_40" data-name="Rectangle 40" width="0.756" height="19.686" transform="matrix(0.071, -0.997, 0.997, 0.071, 5.351, 38.575)" fill="#cdd6e0" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_164" data-name="Path 164" d="M992.385,486.415l-19.342-1.378a.889.889,0,0,1-.823-.95h0a.889.889,0,0,1,.95-.824l19.342,1.378a.889.889,0,0,1,.823.95h0A.889.889,0,0,1,992.385,486.415Z" transform="translate(-967.529 -446.676)" fill="#209e91"/>
      <path id="Path_165" data-name="Path 165" d="M989.455,525.2l-20.638-1.47a.239.239,0,0,1-.221-.256l.092-1.3a.239.239,0,0,1,.255-.221l20.638,1.47a.239.239,0,0,1,.222.255l-.093,1.3A.239.239,0,0,1,989.455,525.2Z" transform="translate(-964.666 -477.262)" fill="#fa7167"/>
      <path id="Path_166" data-name="Path 166" d="M1004.07,504.2l-7.467-.532a1.685,1.685,0,0,1-1.56-1.8h0a1.684,1.684,0,0,1,1.8-1.56l7.467.532a1.685,1.685,0,0,1,1.56,1.8h0A1.685,1.685,0,0,1,1004.07,504.2Z" transform="translate(-985.57 -460.148)" fill="#bed1f9"/>
      <path id="Path_167" data-name="Path 167" d="M998.763,505.239a1.124,1.124,0,0,1-1.022-1.338,1.155,1.155,0,0,1,1.229-.9l7.371.525a1.156,1.156,0,0,1,1.09,1.064,1.124,1.124,0,0,1-1.2,1.18Z" transform="translate(-987.69 -462.28)" fill="#d9d9d9" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_168" data-name="Path 168" d="M1017.332,447.242l-4.154-.3.028-.385a20.855,20.855,0,0,1,2.752-9.334h0a20.218,20.218,0,0,1,1.4,9.63Z" transform="translate(-999.91 -410.284)" fill="#33c2b3"/>
    </g>
  </g>
</svg>
