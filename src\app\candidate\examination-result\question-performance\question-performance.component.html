<div class="text-center mb-4 mt-2 text-sm p-color">
    Question Performance
</div>
<div class="grid-container mb-5">
    <div *ngFor="let col of data ; let i = index" class="grid-column">
        <div *ngFor="let value of col ;let j=index" class="grid-item flex">
            <div class="w-1/2 flex items-center justify-center text-gray" 
            [ngClass]="j%2==1 ? 'bg-zinc100' : 'baground-result'">
                Q.{{ (i * col.length) + j + 1 }}
            </div>
            <div class="w-1/2  flex items-center justify-center"
                [ngClass]=" j%2==0 ? 'bg-zinc100' : 'baground-result'  ">
                <ng-container *ngIf="value.isCorrect == 'incorrect'">
                    <mat-icon class="wrong-color text-lg pt-1">cancel</mat-icon>
                </ng-container>
                <ng-container *ngIf="value.isCorrect == 'correct'">
                    <mat-icon class="right-color text-lg pt-1">check_circle</mat-icon>
                </ng-container>
            </div>
        </div>
    </div>
</div>

<div class="flex justify-center mt-5">
    <div class="flex items-center mr-5 p-color text-sm">
        <mat-icon class="right-color mr-3 text-lg pt-1">check_circle</mat-icon> Correct Answer
    </div>
    <div class="flex items-center p-color text-sm">
        <mat-icon class="wrong-color mr-3 text-lg pt-1">cancel</mat-icon> Wrong Answer
    </div>
</div>

<div class="border-primary p-6 mt-5 baground-result">
    <div class="flex mb-3">
        <div class="p-color text-sm">
            Total Score 
        </div>
        <div class="ml-auto p-color text-sm">
            {{ scores.examDetails.score }}/40
        </div>
    </div>
    <div class="flex">
        <div class="p-color text-sm">
            Percentage
        </div>
        <div class=" ml-auto p-color text-sm">
            {{ scores.examDetails.percentage }}%
        </div>
    </div>
</div>