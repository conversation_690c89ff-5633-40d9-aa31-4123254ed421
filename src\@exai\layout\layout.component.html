<div [class.boxed]="isBoxed$ | async" [class.horizontal-layout]="!(isLayoutVertical$ | async)" [class.is-mobile]="!(isDesktop$ | async)" [class.vertical-layout]="isLayoutVertical$ | async" [class.has-fixed-footer]="(isFooterFixed$ | async) && isFooterVisible$ | async"
    [class.has-footer]="isFooterVisible$ | async" [class.scroll-disabled]="scrollDisabled$ | async" [class.toolbar-fixed]="isToolbarFixed$ | async" [class.sidenav-collapsed]="sidenavCollapsed$ | async" [class.content-container]="containerEnabled$ | async"
    [class.with-search]="searchOpen$ | async" class="page-container">

    <exai-progress-bar1></exai-progress-bar1>

    <exai-search></exai-search>
    <ng-container *ngTemplateOutlet="loading"></ng-container>

    <ng-container *ngTemplateOutlet="toolbarRef"></ng-container>
    <mat-sidenav-container class="sidenav-container">
        <mat-sidenav #sidenav [disableClose]="isDesktop$ | async" [fixedInViewport]="!(isDesktop$ | async)" [mode]="!(isDesktop$ | async) || (isLayoutVertical$ | async) ? 'over' : 'side'" [opened]="(isDesktop$ | async) && !(isLayoutVertical$ | async)" class="sidenav">
            <ng-container *ngTemplateOutlet="sidenavRef"></ng-container>
        </mat-sidenav>
        <mat-sidenav #quickpanel [fixedInViewport]="!(isDesktop$ | async)" class="quickpanel" mode="over" position="end">
            <ng-container *ngTemplateOutlet="quickpanelRef"></ng-container>
        </mat-sidenav>
        <mat-sidenav-content class="sidenav-content">
            <main class="content">
                <!-- <button (mouseover)="helpButtonExpanded.next(true)" (mouseout)="helpButtonExpanded.next(true)" mat-button
                 class="flex flex-row justify-items-center my-1 bg-white hover:bg-gray-100 text-gray-800 font-semibold py-1 px-1 border border-gray-400 rounded shadow help-btn">
                  <ng-container *ngIf="!(helpButtonExpanded$ | async)">
                    <mat-icon class="chat">chat</mat-icon>
                  </ng-container>
                  <ng-container *ngIf="(helpButtonExpanded$ | async)">
                    <div class="">
                        <mat-icon class="chat1 mr-2">chat</mat-icon>
                        <span mat-button class="exai-link t-xs"
                              (click)="this.chatPanelExpanded.next(!this.chatPanelExpanded.value)">
                          Live Chat                 
                        </span>
                    </div>
                  </ng-container>
                </button>
                <ng-container *ngIf="(chatPanelExpanded$ | async)">
                    <div class="chatContainer bg-white">
                        <ng-container *ngTemplateOutlet="chatPanelRef"></ng-container>
                    </div>
                </ng-container> -->
                <router-outlet></router-outlet>
            </main>
        </mat-sidenav-content>
    </mat-sidenav-container>
    <ng-container *ngTemplateOutlet="footerRef"></ng-container>
</div>