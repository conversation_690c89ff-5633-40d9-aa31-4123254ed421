import { BehaviorSubject, Observable } from "rxjs";
import Peer from 'peerjs';

export interface message {
  type: 'config' | 'message',
  sender: sender;
  senderType: 'agent' | 'clientORcandidate',
  timestamp: Date,
  text: string;
}

export interface sender {
  senderName?: string;
  senderPeerjsID?: string;
  senderMetaData?: object;
}

export enum configMsgTexts {
  removeCandidateScreenShare = 'removeCandidateScreenShare',
  closeChatConnectionCompletely = 'closeChatConnectionCompletely'
}


export class chatConnectionAtSuppCen {
  chatConnectionStatus: BehaviorSubject<string> = new BehaviorSubject<string>('');
  $chatConnectionStatus: Observable<string> = this.chatConnectionStatus.asObservable();
  screenShareConnectionStatus: BehaviorSubject<string> = new BehaviorSubject<string>('');
  $screenShareConnectionStatus: Observable<string> = this.screenShareConnectionStatus.asObservable();
  clientORcandidatePeerjsID: string = null;
  clientORcandidateName: string = null;
  agentName: string = null;
  agentPeerjsID: string = null;
  chatConnection: Peer.DataConnection = null;
  screenShareConnection: Peer.MediaConnection = null;
  clientORcandidateRemoteStream: BehaviorSubject<MediaStream>;
  conversation: Array<message> = [];
  constructor(object: any) {
    this.chatConnectionStatus = new BehaviorSubject<string>(object.chatConnectionStatus);
    this.$chatConnectionStatus = this.chatConnectionStatus.asObservable();
    this.screenShareConnectionStatus = new BehaviorSubject<string>(object.screenShareConnectionStatus);
    this.$screenShareConnectionStatus = this.screenShareConnectionStatus.asObservable();
    this.clientORcandidatePeerjsID = object.clientORcandidatePeerjsID;
    this.agentPeerjsID = object.agentPeerjsID;
    this.clientORcandidateName = object.clientORcandidateName;
    this.agentName = object.agentName;
    this.chatConnection = object.chatConnection;
    this.screenShareConnection = object.screenShareConnection;
    this.clientORcandidateRemoteStream = new BehaviorSubject<MediaStream>(object.clientORcandidateRemoteStream);
    this.conversation = object.conversation;
  }
}

export interface closeChatConn{
  closerId: string;
}