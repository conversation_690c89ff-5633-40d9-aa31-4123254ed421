import { BreakpointObserver } from '@angular/cdk/layout';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatDialog, MatDialogConfig, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import moment from 'moment';
import { forkJoin, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { environment } from 'src/environments/environment';
import { FormBuilderService } from '../form-builder.service';
import { sectionName } from '../form-builder.types';
import { FormViePopupComponent } from './form-vie-popup/form-vie-popup.component';
import { CommentPopupComponent } from './comment-popup/comment-popup.component'

@Component({
  selector: 'app-form-link-attach',
  templateUrl: './form-link-attach.component.html',
  styleUrls: ['./form-link-attach.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FormLinkAttachComponent),
      multi: true
    }
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormLinkAttachComponent implements OnInit, OnDestroy {
  @Input() disabled: boolean;
  @Input() model: any;
  outputValue: any;
  finalOutputValue: Array<any>;
  enableFormList: boolean = false;
  addedFormListId: Array<any> = [];
  addedFormList: Array<any> = [];
  availableForm: any = [];
  candidateDetails;
  actionForm = [];
  recentLinkClickEventSelector;
  routeParamData: any;
  tempCandidateId;
  sectionName = sectionName.ProblemreportSction4;
  getFormResponse: Subject<any> = new Subject();
  getNextActionDataData: Subject<any> = new Subject();
  displayedColumns: Array<string> = ['Date', 'type', 'description', 'additional', 'Received', 'Due', 'Comment', 'File', 'Action'];
  gotDetails: Array<any> = [];
  count = 0
  dataSource: Array<any> = [];
  multiplecall: boolean = true
  files = []
  @ViewChild('callAPIDialog') callAPIDialog: TemplateRef<any>;
  private unsubscribe: Subject<any> = new Subject();
  addforms: Array<any>=[];


  writeValue(value: any) {
    let a;
    if (value !== undefined && value != null && value != "") {
      // JSON.parse(value)[0].forEach(x=>{
      //   this.addedFormListId.push({formName:x.formName,personformId:x.personformId,description:x.description,date:x.date})
      // })
      //the below method is called only when filled form is opened
      this.routeParamData.personFormId || this.formService.cellclickedDetails.id ? this.getCandidateFormList(null) : this.getCandidateFormList("uploadall")
      JSON.parse(value)[1].forEach(x => {
        this.actionForm.push({ personformId: x.personformId })
      })
      this.getFormResponse.subscribe(id => {
          if (id) {
            id.forEach(element => {
              this.http.get(environment.baseUrl + `client/api/form/personform/list?personFormId=${element?.personformId}`).subscribe((formresponse: any) => {
                let value: any = Object.values(JSON.parse(formresponse[0].formResponse)[0].problem_report_action_form_section_1_q1s1)
                if (this.gotDetails.find(x => x.formId == element.personformId)) {
    
                }
                else {
                  formresponse.forEach(x => {
                    if (x.active == true) {
                      this.files = []
                      this.gotDetails.push({ formId: element.personformId, response: value });
                      let a = Array.isArray(value[14])
                      if (a == true) {
                        for (let i = 0; i < value[14].length; i++) {
                          this.files.push(value[14]?.split('|')[i].replace(/,/g, '').split('/')[0])
                        }
                      } else {
                        for (let i = 0; i < value[14]?.split('|').length; i++) {
                          this.files.push(value[14]?.split('|')[i].replace(/,/g, '').split('/')[0])
                        }
                      }
                      this.files = this.files.reduce((unique, o) => {
                        if (!unique.some(obj => obj === o)) {
                          unique.push(o);
                        }
                        return unique;
                      }, []);
    
                      this.dataSource.push({
                        date: value[1],
                        type: value[3],
                        description: value[6],
                        additional: value[7],
                        Received: value[9],
                        Due: value[11],
                        Comment: value[13],
                        Files: this.files,
                        personformId: element.personformId,
                      })
                      this.dataSource = [...this.dataSource];
                      this.cdr.detectChanges();
                      this.getNextActionDataData.next(this.count++);
                    }
                  })
                }
    
    
                // else {
                // }
              })
              
            });
          
          }
        })
        for(let i=0;i<this.actionForm.length;i++){
           this.addforms.push(this.actionForm[i])
        }
        this.getFormResponse.next(this.addforms)
      this.getNextActionDataData.subscribe(count => {
        if (this.actionForm[count])
          this.getFormResponse.next(this.actionForm[this.count])
      })

      this.outputValue = this.addedFormListId;
    }
    else
      this.outputValue = [];
  }
  propagateChange = (_: any) => { };

  registerOnChange(fn) {
    this.propagateChange = fn;
  }

  registerOnTouched() { }
  constructor(private cdr: ChangeDetectorRef, public dialog: MatDialog,
    private formService: FormBuilderService, private breakpointobserver: BreakpointObserver,
    private http: HttpClient, private activatedRoute: ActivatedRoute, private snackbar: SnackbarService, private store: Store) {
    this.candidateDetails = this.formService.selectedCandidateDetails;
  }

  ngOnInit(): void {
    this.formService.attachedForm = [];
    this.activatedRoute.paramMap.subscribe((data: any) => {
      if (data) {
        this.routeParamData = data.params;
      }
    })
    this.formService.addForms.pipe(takeUntil(this.unsubscribe)).subscribe(data => {
      if (data) {
        this.availableForm = [];
        this.getFormListData(false);
      }
    })
  }

  typeOf(value) {
    var formats = [
      moment.ISO_8601,
      "MM/DD/YYYY  :)  HH*mm*ss"];
    return moment(value, formats, true).isValid();
  }

  getFormListData(enableList) {
    this.availableForm = [];
    this.enableFormList = enableList;
    if (this.formService.selectedCandidateDetails) {
      this.http.get(environment.baseUrl + `client/api/form/GetProblemReports?candidateId=${this.formService.selectedCandidateDetails?.candidateId}&formTypeId=11&pageNumber=1&pageSize=1000`).subscribe((formlist: any) => {
        if (formlist.result.length > 0) {
          if (!enableList) {
            this.addedFormListId = [];
            this.addedFormList = [];
          }
          formlist.result.forEach(name => {
            if (name.id != this.routeParamData.personFormId) {
              if (!enableList) {
                this.addedFormListId.push({ formName: name.problemType, personformId: name.id, description: name.reportSummary, date: name.openedDate, status: name.problemReportStatus, abuseDescription: name.abuseDescription });
                this.addedFormList.push(name.problemType + "|" + name.id);
              }
              if (enableList) {
                this.availableForm.push({ formName: name.problemType, personformId: name.id });
              }
            }
          })
          this.addedFormListId.forEach(item => {
            this.availableForm = this.availableForm.filter(x => x.personformId != Number(JSON.parse(item.personformId)))
          })
          this.availableForm = this.availableForm?.filter(x => x.personformId != this.routeParamData.personFormId)
        }
        else if (formlist.result.length == 0) {
          this.addedFormListId = [];
          this.addedFormList = [];
        }
        this.outputValue = this.addedFormListId;
        this.finalOutputValue = [
          this.outputValue,
          this.actionForm
        ]
        this.propagateChange(JSON.stringify(this.finalOutputValue));
        this.cdr.markForCheck();
        this.cdr.detectChanges();
      })
    }
  }

  getCandidateFormList(userValue) {
    this.http.get(environment.baseUrl + `client/api/form/GetProblemReports?candidateId=${this.formService.selectedCandidateDetails?.candidateId}&formTypeId=11&pageNumber=1&pageSize=1000`).subscribe((formlist: any) => {
      if (formlist.result.length > 0) {
        this.addedFormListId = [];

        formlist.result.forEach(name => {
          if (userValue) {
            this.addedFormListId.push({ formName: name.problemType, personformId: name.id, description: name.reportSummary, date: name.openedDate, status: name.problemReportStatus, abuseDescription: name.abuseDescription });
          }
          else if (this.routeParamData.personFormId && name.id != this.routeParamData.personFormId) {
            this.addedFormListId.push({ formName: name.problemType, personformId: name.id, description: name.reportSummary, date: name.openedDate, status: name.problemReportStatus, abuseDescription: name.abuseDescription });
          }
          else if (this.formService.cellclickedDetails.id && name.id != this.formService.cellclickedDetails.id) {
            this.addedFormListId.push({ formName: name.problemType, personformId: name.id, description: name.reportSummary, date: name.openedDate, status: name.problemReportStatus, abuseDescription: name.abuseDescription });
          }

          else if (this.recentLinkClickEventSelector?.personFormId && name.id != this.recentLinkClickEventSelector?.personFormId) {
            this.addedFormListId.push({ formName: name.problemType, personformId: name.id, description: name.reportSummary, date: name.openedDate, status: name.problemReportStatus, abuseDescription: name.abuseDescription });

          }
          this.cdr.markForCheck();
          this.cdr.detectChanges();
        })
      }
    });
  }

  addForms(item) {
    this.addedFormList.push(item.formName + "|" + item.personformId);
    this.addedFormListId.push({ formName: item.formName, personformId: item.personformId })
    this.outputValue = this.addedFormListId;
    this.availableForm = this.availableForm.filter(a => a.personformId != item.personformId);
    this.finalOutputValue = [
      this.outputValue,
      this.actionForm
    ]
    this.propagateChange(JSON.stringify(this.finalOutputValue));
    // this.propagateChange(this.outputValue.map(x=>JSON.stringify(x)).join(','));
    this.cdr.markForCheck();
  }

  viewForm(item, formTypeID, status) {
    this.getFormResponse.next(null)
    let formJson;
    let userResponse
    forkJoin(item ? [
      this.http.get(environment.baseUrl + `client/api/form/formsbyformtypeid?formTypeId=${formTypeID}&clientId=3&stateId=4`),
      this.http.get(environment.baseUrl + `client/api/form/personform/list?personFormId=${item.personformId}`),
    ] : [this.http.get(environment.baseUrl + `client/api/form/formsbyformtypeid?formTypeId=${formTypeID}&clientId=3&stateId=4`)])
      .subscribe((x: Array<any>) => {
        formJson = x[0],
          userResponse = x[1] ? x[1][0] : [];
        const dialogConfig = new MatDialogConfig();
        dialogConfig.hasBackdrop = true;
        this.breakpointobserver
          .observe(['(min-width : 1024px)']).subscribe((result: { matches: any }) => {
            if (result.matches) {
              dialogConfig.minWidth = '60vw';
              dialogConfig.minHeight = '85vh';
              dialogConfig.maxWidth = '70vw';
              dialogConfig.maxHeight = "85vh";
            } else {
              dialogConfig.minWidth = '90vw';
              dialogConfig.minHeight = '90vh';
            }
          });
        if (formJson != undefined && userResponse != undefined) {
          dialogConfig.data = [];
          dialogConfig.data.push(formJson, userResponse, this.model.parent.id, status)
          const dialogRef = this.dialog.open(FormViePopupComponent, dialogConfig);
          dialogRef.afterClosed().subscribe((data: any) => {
            if (data) {
              this.formService.attachedForm.forEach(items => {
                userResponse.length == 0 ? this.actionForm.push(items) : '';
              })
              this.getFormResponse.subscribe(id => {
                if (id) {
                  this.multiplecall = true
                  this.http.get(environment.baseUrl + `client/api/form/personform/list?personFormId=${id.personformId}`).subscribe((formresponse: any) => {
                    if (this.multiplecall) {
                      this.files = []
                      let value: any = Object.values(JSON.parse(formresponse[0].formResponse)[0].problem_report_action_form_section_1_q1s1);
                      if (this.gotDetails.find(x => x.formId == id.personformId)) {
                        let index = this.gotDetails.findIndex(x => x.formId == id.personformId);
                        this.gotDetails[index] = { formId: data.personformId, response: value }
                        let a = Array.isArray(value[14])
                        if (a == true && value[14] != null) {
                          for (let i = 0; i < value[14].length; i++) {
                            this.files.push(value[14]?.split('|')[i].replace(/,/g, '').split('/')[0])
                          }
                        } else if (value[14] != null && value[14] != "") {
                          for (let i = 0; i < value[14]?.split('|').length; i++) {
                            this.files.push(value[14]?.split('|')[i].replace(/,/g, '').split('/')[0])
                          }
                        } else {
                          this.files = []
                        }
                        this.files = this.files.reduce((unique, o) => {
                          if (!unique.some(obj => obj === o)) {
                            unique.push(o);
                          }
                          return unique;
                        }, []);
                        this.dataSource[index] = ({
                          date: value[1],
                          type: value[3],
                          description: value[6],
                          additional: value[7],
                          Received: value[9],
                          Due: value[11],
                          Comment: value[13],
                          Files: this.files,
                          personformId: id.personformId
                        })
                        this.dataSource = [...this.dataSource];
                        this.cdr.detectChanges();
                        this.cdr.markForCheck();
                      }
                      else {
                        this.gotDetails.push({ formId: id.personformId, response: value })
                        this.dataSource.push({
                          date: value[1],
                          type: value[3],
                          description: value[6],
                          additional: value[7],
                          Received: value[9],
                          Due: value[11],
                          Comment: value[13],
                          Files: value[14],
                          personformId: id.personformId
                        })
                        this.dataSource = [...this.dataSource];
                        this.cdr.detectChanges();
                        this.cdr.markForCheck();
                        // this.getNextActionDataData.next(this.count++)
                      }
                      this.multiplecall = false
                    }
                  })
                }
              })
              this.getFormResponse.next(data)
              // this.getNextActionDataData.subscribe(count=>{
              //   if(this.actionForm[count])
              //   this.getFormResponse.next(this.actionForm[count])
              // })

              this.finalOutputValue = [
                this.outputValue,
                this.actionForm
              ]
              this.propagateChange(JSON.stringify(this.finalOutputValue));
              this.cdr.detectChanges();
            }
          })
        }
      })

  }

  removeFormlist(item) {
    this.addedFormList = this.addedFormList.filter(y => y != item.formName + "|" + item.personformId)
    this.addedFormListId = this.addedFormListId.filter(x => x.personformId != item.personformId);
    this.availableForm.push({ formName: item.formName, personformId: item.personformId });
    this.outputValue = this.addedFormListId;
    this.finalOutputValue = [
      this.outputValue,
      this.actionForm
    ]
    this.propagateChange(JSON.stringify(this.finalOutputValue));
    // this.propagateChange(this.outputValue.map(x=>JSON.stringify(x)).join(','));
    this.cdr.markForCheck();
    this.cdr.detectChanges();
  }

  deleteAttchedForm(formId) {
    let dialogRef = this.dialog.open(this.callAPIDialog);
    dialogRef.afterClosed().subscribe(result => {
      if (result == 'yes') {
        if (this.formService.selectedCandidateDetails?.candidateId) {
          this.http.delete(environment.baseUrl + `candidate/api/form/personform?candidateId=${this.formService.selectedCandidateDetails?.candidateId}&personFormId=${formId.personformId}`).subscribe(deleteresponse => {
            this.actionForm = this.actionForm.filter(x => x.personformId != formId.personformId);
            this.dataSource = this.dataSource.filter(x => x.personformId != formId.personformId);
            this.dataSource = [...this.dataSource]
            this.finalOutputValue = [
              this.outputValue,
              this.actionForm
            ]
            this.propagateChange(JSON.stringify(this.finalOutputValue));
            this.cdr.detectChanges();
          })
        }
        else {
          this.snackbar.callSnackbaronWarning("Please select a candidate name to delete action Forms")
        }
      }
    })
  }


  closeList() {
    this.enableFormList = false;
  }
  ngOnDestroy() {
    this.unsubscribe.next();
    this.unsubscribe.complete();
    this.formService.selectedCandidateDetails = null;

  }

  showComment(comment) {
    this.dialog.open(CommentPopupComponent, {
      data: comment
    })


  }
}
