import { createReducer, on } from '@ngrx/store';
import { store } from './excused.store';
import * as GreivenceActions from './excused.action';
import { gotupcomingExam } from './excused.action';
import { ExcusedAbsense } from './excused.model';

export const reducer = createReducer(
  store,
  on(GreivenceActions.loadAllSuccess, (state, action) => {
    return {
      ...state,
      AbsenseFormList: action.data,
    };
  }),
  on(GreivenceActions.loadAllFailure, (state, action) => {
    return {
      ...state,
      error: action.error,
    };
  }),
  on(GreivenceActions.loadAbsenseformSuccess, (state, action) => {
    return {
      ...state,
      reportGrievance: action.data,
    };
  }),
  on(GreivenceActions.loadReportGrievanceFailure, (state, action) => {
    return {
      ...state,
      error: action.error,
    };
  }),
  on(GreivenceActions.postReportGrievanceSuccess, (state, action) => {
    return {
      ...state,
      saveGrievance: action.data,
    };
  }),
  on(GreivenceActions.postReportGrievanceFailure, (state, action) => {
    return {
      ...state,
      error: action.error,
    };
  }),
  on(GreivenceActions.postSaveDraftReportGrievanceSuccess, (state, action) => {
    return {
      ...state,
      saveDraftGrievance: action.data,
    };
  }),
  on(GreivenceActions.postSaveDraftReportGrievanceFailure, (state, action) => {
    return {
      ...state,
      error: action.error,
    };
  }),
  on(GreivenceActions.postViewGrievanceSuccess, (state, action) => {
    return {
      ...state,
      viewFormStatus: action.data,
    };
  }),
  on(GreivenceActions.postViewGrievanceFailure, (state, action) => {
    return {
      ...state,
      error: action.error,
    };
  }),
  on(GreivenceActions.getPersonProgressSuccess, (state, action) => {
    return {
      ...state,
      viewFormProgress: action.data,
    };
  }),
  on(GreivenceActions.getPersonProgressFailure, (state, action) => {
    return {
      ...state,
      error: action.error,
    };
  }),
  on(GreivenceActions.Examcancelled, (state, action) => {
    return {
      ...state,
      deletedGrievanceForm: action.isCancelled,
    };
  }),

  on(gotupcomingExam, (state, action) => {
    return {
      ...state,
      upcomingExam: action.upcomingExam,
    };
  }),

  on(GreivenceActions.clearGrievanceState, (state, action) => {
    return {
      ...state,
      grevienceFormList: [],
      excusedAbsense: new ExcusedAbsense(),
      saveGrievance: null,
      saveDraftGrievance: null,
      viewFormStatus: null,
      viewFormProgress: [],
      deletedGrievanceForm: null,
    };
  })
);
