<div class="relative">
    <span class="menu-toggle p-2 cursor-pointer" (click)="onToggleClick()">⋮</span>
    <div *ngIf="isOpen" class="menu_pop absolute top-6 z-50 w-40 bg-white shadow-md rounded border"
        [ngClass]="{ 'right-0': align === 'right', 'left-0': align === 'left' }">
        <div *ngFor="let item of menuItems"
            class="menu_pop_item p-2 cursor-pointer flex items-center gap-2 hover:bg-gray-100"
            (click)="onItemClick(item)">
            <mat-icon *ngIf="item.icon" class="text-base pt-1">{{ item.icon }}</mat-icon>
            <span class="text-sm">{{ item.label }}</span>
        </div>
    </div>
</div>