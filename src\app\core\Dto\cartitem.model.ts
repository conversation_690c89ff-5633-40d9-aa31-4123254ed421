export class CartItemModel {
    public constructor(init?: Partial<CartItemModel>) {
        Object.assign(this, init);
    }

    personEventCartId?: string = '';
    cartId: number = 0;
    examName?: string = '';
    examCode?: string = null;
    examTypeId: number = 0;
    examNameDesc?: string = '';
    examMode?: string = '';
    timeZoneCode?: string = '';
    examDateAndTime: Date;
    eventDate: Date;
    eventDateUtc: Date;
    durationInMinutes: number = 0;
    amount: number = 0;
    personEventId: number = 0;
    cartStatusId: number = 0;
    cartStatus?: string = '';
    timeZoneOffset?: string = '';
    eligibilityRouteName?: string = '';
    examId: number = 0;
}