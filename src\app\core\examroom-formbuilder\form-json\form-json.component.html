<div class="card p-6 fb-ht-fixed" style="width: 95vw;position: relative;" fxFlex fxFlex.xs="auto">
    <h4 class="mt-0 mb-6">Built Form Json</h4>
    <mat-tab-group mat-align-tabs="center" class="touch-auto overflow-auto flex align-top">
        <mat-tab label="Form JSON(needed to render the form)">
            <pre style="margin-bottom: 4rem;">
                <mat-icon style="position: absolute; top:20px; right:50px;font-size:1.4rem;cursor:pointer"
                    [matTooltip]="'Copy to Clipboard'" mat-button [cdkCopyToClipboard]="formBuilderService.genesisForm | json">
                    file_copy
                </mat-icon>
                {{formBuilderService.genesisForm | json}}
                </pre>
        </mat-tab>
        <mat-tab label="Editing Meta Data(needed to render a form inside Form Builder)">
            <pre style="margin-bottom: 4rem;">
                                        <mat-icon style="position: absolute; top:20px; right:50px;font-size:1.4rem;cursor:pointer" [matTooltip]="'Copy to Clipboard'" mat-button [cdkCopyToClipboard]="formBuilderService.editData | json">
                                                    file_copy
                                        </mat-icon> {{formBuilderService.editData | json}}
                    </pre>
        </mat-tab>
    </mat-tab-group>
</div>