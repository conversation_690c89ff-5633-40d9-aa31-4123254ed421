import { createFeatureSelector, createSelector } from "@ngrx/store";
import { CandidateChatState } from "./candidate-chat.state";

export const CANDIDATE_CHAT_STATE_NAME = "CandidateChatState";

const getCandidateChatState = createFeatureSelector<CandidateChatState>(CANDIDATE_CHAT_STATE_NAME);

export const selectSupportStaffInfo = createSelector(
  getCandidateChatState, (state) => {
    return state.connectedSupportStaffInfo;
  }
)

export const selectErrorMessage = createSelector(getCandidateChatState, (state) => {
  return state.errorMessage;
})