import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
// import { AuthGuard } from 'src/app/core/auth.guard';
import { PaymentComponent } from './payment/payment.component';
import { RegisterForExamComponent } from './register-for-exam/register-for-exam.component';
import { ScheduledComponent } from './scheduled.component';
// import { ScheludedExamComponent } from './scheluded-exam/scheluded-exam.component';

// const routes: Routes = [{
//   path: '', component: ScheduledComponent, children: [
//     { path: 'register', component: RegisterForExamComponent },
//     // { path: 'exam', component: ScheludedExamComponent },
//   ]
// }];
const routes: Routes =
[
  
  {
  path: '', component: ScheduledComponent,
  // canActivate: [AuthGuard],
  data: {
    title: 'Exam Schedule', 
    breadcrumb: [
      {
        label: 'Home',
        url: '/dashboard'
      },
      {
        label: 'Exam Schedule',
        url: ''
      },
  
    ]
  },
},
{
  path: 'register', component: RegisterForExamComponent,
  // canActivate: [AuthGuard],
  data: {
    title: 'Exam Schedule', 
    breadcrumb: [
      {
        label: 'Home',
        url: '/dashboard'
      },
      {
        label: 'Exam Schedule',
        url: '/exam-scheduled'
      },
      {
        label: 'Register For Exam',
        url: ''
      },

    ]
  },
},
{
  path: 'payment/:id', component: PaymentComponent,
  // canActivate: [AuthGuard],
  data: {
    title: 'Exam Schedule', 
    // breadcrumb: [
    //   {
    //     label: 'Home',
    //     url: '/dashboard'
    //   },
    //   {
    //     label: 'Exam Schedule',
    //     url: '/exam-scheduled'
    //   },
    //   {
    //     label: 'Register For Exam',
    //     url: '/exam-scheduled/register'
    //   },
    //   {
    //     label: 'Payment',
    //     url: ''
    //   },

    // ]
  },
},
];




@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ScheduledRoutingModule { }
