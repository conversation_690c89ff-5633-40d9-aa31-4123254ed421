import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, NavigationEnd, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { get_userDetails } from 'src/app/candidate/state/shared/shared.selectors';
import { environment } from 'src/environments/environment';
import { SnackbarService } from './snackbar.service';
import { URL } from 'src/app/core/url';
import { AppService } from '../app.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(private router: Router, private _appService: AppService) { }

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    if (this._appService.authorizedInformation && this._appService.userInfomration) {
      return true;
    } else {
      const url = state.url;
      const query = next.queryParams;
      query && 'token' in query ? this._appService.userAuthorize(query.token,query.email,query.firstName,query.lastName) : null;
      return false;
    }
  }

}