.addressDetails {
    height: calc(80vh - 55vh);
    width: calc(150vh - 55vh);

}

.addressDetails1 {
    width: calc(148vh - 55vh);
}
.InformationDetails{
  transform-origin: 50% 20.875px 0px;
  opacity: 0.8;
  min-width: calc(100% + 12px);
  transform: scaleY(1)
}

.matoptions{
  font-size: x-small !important;
    line-height: 1.5em !important;
    height: 5em !important;
}


.custom{
    padding-top:30px;
    
}

.borderclass{
    border:2px solid black;
}
.example-form {
    min-width: 150px;
    max-width: 500px;
    
    
  }

  .addressDetails {
    height: calc(100vh - 55vh);
}
  .example-form1 {
   position: sticky;
    
  }
  
  .input_width{
    width:17rem;
  }
  .text-xs{
    // font-size: 0.67rem !important;
    // font-weight: bold;
  }
  .text-xs1{
    font-size: 0.65rem !important;
  }
  .border1{
    border: 1px solid gray;
  }
  ::ng-deep mat-form-field{

    max-width: none !important;
    
    }

    .alert{
      color: red;
      margin-top: -23px;
    }