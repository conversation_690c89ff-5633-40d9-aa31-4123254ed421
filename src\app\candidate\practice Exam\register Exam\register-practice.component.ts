import { Component, Inject, Input, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Action, Store } from '@ngrx/store';
import { ExamTypeModel } from 'src/app/core/Dto/exam-type.model';
import { PracticeretrySchedule, ReschedulePracticeExam, getExamByERIdId, getPracticeCart, getPracticeExamByERIdId, getPracticeRegisteredExam, getTimeSlots, getTimezones, gotPracticeExamId } from '../../scheduled/state/scheduled.actions';
import { selectorGetPracticeCart, selectorGetPracticeRescheduledResponse, selectorGetTimeslots, selectorGetTimezones, selectorPracticeGetExamId, selectorPracticeGetRegisteredexam } from '../../scheduled/state/scheduled.selectors';
import { Timezone } from '../../scheduled/state/models/timezone.model';
import moment, { Moment } from 'moment';
import { Cart } from '../../scheduled/state/models/cart';
import { Observable, Subject, Subscription, interval } from 'rxjs';
import { Exam } from '../../scheduled/state/models/Exam';
import { MatAccordion } from '@angular/material/expansion';
import { MatCalendar } from '@angular/material/datepicker';
import { SlotModel } from 'src/app/core/Dto/slot.model';
import { ExamModel } from 'src/app/core/Dto/exam.model';
import { Slot } from '../../scheduled/state/models/slot';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpErrorResponse } from '@angular/common/http';
import { RegisteredExamsModel } from 'src/app/core/Dto/registered-exams.model';
import { get_PracticecartItems } from '../../state/shared/shared.selectors';
import { getCartItems, getCartPracticeItems } from '../../state/shared/shared.actions';
import { FormTypes } from '../../forms-wrapper/forms-wrapper.types';
import { DatePipe } from '@angular/common';
import { ScheduledService } from '../../scheduled/scheduled.service';


@Component({
  selector: 'exai-register_exam',
  templateUrl: './register-practice.component.html',
  styleUrls: ['./register-practice.component.scss']
})
export class RegisterExamComponent implements OnInit {
  @ViewChild("calendar") calendar: MatCalendar<Moment>;
  @Input() max: any;
  scheduleagainEnable: number
  rescheduleloading: boolean = false;
  scheduleloading: boolean = false;
  value = 0;
  loading = false;
  reschedule: boolean = true
  rescheduleload = false
  scheduleload = false
  ExamID:ExamModel[]=[]
  Validators: FormGroup
  ShowTestCenter: boolean = false
  date: boolean = false;
  examType: boolean = false;
  noExamSelected: boolean = true;
  calendarDisable: boolean = false;
  examTypeDisable: boolean = false;
  isSelect: boolean = false;
  payment: boolean = true;
  selectedDate: Date = null;
  slotsAvaiable: SlotModel[] = [];
  timeSlot: Array<any>;
  timeZoneId: Timezone;
  selectedExam: ExamModel;
  bookedslot: any;
  slots = [];
  errors: any;
  step: any = 0;
  steps1: number = 0;
  cart: Cart;
  timezones: Timezone[];
  form: FormGroup;
  radioselect = new FormControl("Online");
  registerExams
  minDate = new Date();
  buttonDisable: boolean = false;
  examDateTime: any;
  public examTypeModels: ExamTypeModel[] = new ExamTypeModel().getDefaultValues();
  listExam: Array<object> = []
  ScheduleMessage:string


  constructor(private store: Store, private fb: FormBuilder, private snackbar: SnackbarService, public global: GlobalUserService, private _services: ScheduledService

  ) {

    this.global.userDetails.subscribe((data: any) => {
      if (!data) return;
      this.store.dispatch<Action>(getPracticeExamByERIdId({eligibilityRouteId:121,personTenantRoleId:this.global.candidateId}))
      this.store.dispatch<Action>(getTimezones());
      this.store.dispatch<Action>(
        getPracticeRegisteredExam({ candidateId: this.global.candidateId })
      );
      this.store.select(selectorPracticeGetRegisteredexam).subscribe(
        (data: RegisteredExamsModel[]) => {
          this.registerExams = data

          // showing a Reschedule button when exam cancelled

        },
        (err: HttpErrorResponse) => {

        }


      );


    });
  }

  subscriptions(): void {

  }

  ngOnInit(): void {

    this.form = this.fb.group({
      CardNumber: new FormControl("", [Validators.required]),
    });
    this.Validators = this.fb.group({
      date: new FormControl("", [Validators.required]),
      timezone: new FormControl("", [Validators.required]),

    });
    this.form.controls["CardNumber"].disable();
    this.setSlot();

    this.store.select(selectorPracticeGetExamId).subscribe((data)=>{
           if(data.length > 0){
            this.ExamID = data
            if (this._services.PracticeInformation && this.registerExams.length > 0) {
              this.scheduleagainEnable = (this._services.PracticeInformation.examStatusId) || (this._services.PracticeInformation.statusId)
              this.ScheduleMessage =(this._services.PracticeInformation.examStatusId === 1 && this._services.PracticeInformation.showStatus ===1) || this._services.PracticeInformation.examStatusId === 10 ?"Schedule":"Reschedule"
              const data = this.ExamID.find((ele: any) => ele.id == this._services.PracticeInformation.examId);
              data ? ((this.examTypeDisable = true, this.setSelectedExam(data)), this.selectedExam) : null;
              this.payment = false;
              this.reschedule = true;
              this.noExamSelected = false;
            }
            else if(this.registerExams.length == 0 && this.ExamID.find((ele: any) => ele.preSelectType !=null && ele.preselect == true) ){
              const data = this.ExamID.find((ele: any) => ele.preSelectType !=null && ele.preselect == true);
              data ? ((this.setSelectedExam(data)), this.selectedExam) : null;
              this.payment = true;
              this.noExamSelected =  data.title !=undefined? false:true;
            }
            else if (!this.selectedExam) {
              this.payment = true;
              this.reschedule = false;
              this.noExamSelected = true;
        
            }
            else {
              this.setSelectedExam(null);
              this.steps1 = null;
            }
           }
    })
    this.store.select(selectorGetTimezones).subscribe((timezones: Timezone[]) => {
      if (timezones.length > 0) {
        this.timezones = timezones
      }
    });

    this.store.select(selectorGetTimeslots).subscribe((data: Slot[]) => {
      if (data) {
        this.slotsAvaiable = [];
        let availableSlots = data;
        if (availableSlots.length > 0) {
          availableSlots.forEach((ele: Slot) => {
            let slotstring = `${ele.strSlotDate} ${ele.strSlotTime}`;
            let slotDate = new Date(Date.parse(slotstring));
            let slotmodel = new SlotModel({
              slotId: ele.slotId,
              availableSlots: ele.availableSlots,
              bookedSlots: ele.bookedSlots,
              slotDate: ele.slotDate,
              strSlotDate: ele.strSlotDate,
              strSlotTime: ele.strSlotTime,
              totalSlots: ele.totalSlots,
              slotDateTime: slotDate,
              slotDateUtc: ele.slotDateUtc
            })
            this.slotsAvaiable.push(slotmodel);
          });
          this.slotsAvaiable = this.slotsAvaiable.sort((a, b) => new Date(a.slotDateTime).getTime() - new Date(b.slotDateTime).getTime());
          this.slotsAvaiable.forEach((ele: SlotModel) => {
            const { slotDate } = ele;
            if (new Date(slotDate).getUTCHours() >= 0 && new Date(slotDate).getUTCHours() < 4) {
              this.timeSlot[0].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 4 && new Date(slotDate).getUTCHours() < 8) {
              this.timeSlot[1].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 8 && new Date(slotDate).getUTCHours() < 12) {
              this.timeSlot[2].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 12 && new Date(slotDate).getUTCHours() < 16) {
              this.timeSlot[3].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 16 && new Date(slotDate).getUTCHours() < 20) {
              this.timeSlot[4].data.push(ele);
            } else if (new Date(slotDate).getUTCHours() >= 20 && (new Date(slotDate).getUTCHours() <= 23 && new Date(slotDate).getUTCMinutes() <= 59)) {
              this.timeSlot[5].data.push(ele);
            }
          });
          this.isSelect = true;
          const time = this.timeSlot.filter((ele: any) => ele.data.length > 0);
          time.length > 0 ? this.selectrangeactive(time[0]) : null;
        } else if (this.slotsAvaiable.length == 0) {
          this.errors = "No slots available for the selected date";
          this.snackbar.callSnackbaronWarning(this.errors);
          this.isSelect = false;
        }
      }
    });

    this.store.select(selectorGetPracticeCart).subscribe((data) => {
      if (data) {
        setTimeout(() => {
          this.store.dispatch<Action>(getCartItems({ personTenantRoleId: this.global.candidateId }));
          this.store.dispatch<Action>(getPracticeRegisteredExam({ candidateId: this.global.candidateId }))
          this.examType = false
          this.noExamSelected = true
        }, 1000);
        setTimeout(()=>{
          this.store.dispatch<Action>(getPracticeExamByERIdId({eligibilityRouteId:121,personTenantRoleId:this.global.candidateId}))
          this.store.select(selectorPracticeGetExamId).subscribe((data)=>{
                 if(data.length > 0){
                  this.ExamID = data
                 }
                })
        },1500)
      }
    })






  }

  public setSelectedExam(selectedExam) {
    if (selectedExam) {
      this.examType = true;
      this.noExamSelected = false;
      this.isSelect = false;
      this.Validators.reset();
      this.selectedDate = null;
      this.selectedExam = selectedExam;
      let isTypeAlreadySet = false;

      this.examTypeModels.forEach(x => {
        let hasMode = selectedExam.supportedExamMode.find(z => z.examModeTypeId === x.id);
        if (hasMode) {
          x.checked = false;
          x.disabled = false;

          if (!isTypeAlreadySet) {
            x.checked = true;
            isTypeAlreadySet = true;
          }
        }
        else {
          x.checked = false;
          x.disabled = true;
        }
      });

      let selectedExamType = this.examTypeModels.find(x => x.checked === true);

      if (selectedExamType != null) {
        this.options(selectedExamType.id);
        this.steps1 = selectedExam.id;
      }
    } else {
      this.examType = false;
      this.noExamSelected = true;
      this.isSelect = false;
      this.Validators.reset();
      this.selectedDate = null;
    }
  }

  public examEvent(tz: Timezone): void {
    this.setSlot();
    this.timeZoneId = tz;
    this.date = false;
    if (this.selectedDate) {
      this.store.dispatch<Action>(
        getTimeSlots({ timezone: this.timeZoneId.Id, startDate: this.selectedDate.toDateString(), examId: this.selectedExam.id, offset: this.timeZoneId.Offset,candidateId:this.global.userDetails.getValue().personTenantRoleId})
      );
    }
    this.calendarDisable = true;
  }
  //  <----showing a color selected in calendar --->
  changeCalendar(event: any): void {
    const body = Array.from(
      document.querySelectorAll<HTMLDivElement>(".mat-calendar .mat-calendar-content"));
    const html = `
  <div class="inline-flex" id="calendarFooter"><br>
  <p class="inline-flex" style="font-size: 8px;">
  <span class="inline-flex pl-1"><img src="assets/img/black_icon.svg">      </span> &nbsp; <span>Available</span>     &nbsp;&nbsp;&nbsp;
  <span class="inline-flex pl-3"><img src="assets/img/gray_icon.svg">       </span> &nbsp; <span>Not available</span> &nbsp;&nbsp;&nbsp;
  <span class="inline-flex pl-3"><img src="assets/img/lightGreen_icon.svg"> </span> &nbsp; <span> Today</span>        &nbsp;&nbsp;&nbsp; 
  <span class="inline-flex pl-3"><img src="assets/img/greenTheme_icon.svg"> </span> &nbsp; <span> Selected</span>     &nbsp;&nbsp;&nbsp;
  </p>
  </div>`;
    body.forEach((ele: HTMLDivElement) => {
      const element = document.createElement("div");
      element.setAttribute("class", "availability-status");
      element.innerHTML = html;
      ele.appendChild(element);
    });
  }
  //  <!-- <----Selecting a Date and spliting a data according to select range --->
  public selectDate(event): void {
    this.setSlot();
    this.date = false;
    this.selectedDate = event.value;
    this.store.dispatch<Action>(
      getTimeSlots({ timezone: this.timeZoneId.Id, startDate: this.selectedDate.toDateString(), examId: this.selectedExam.id, offset: this.timeZoneId.Offset,candidateId:this.global.userDetails.getValue().personId })
    );
  }

  getCart(i) {
    this.store.dispatch<Action>(
      getPracticeCart({
        details: {
          personTenantRoleId: this.global.candidateId,
          amount: this.selectedExam.price,
          cartItemTypeId: 1,
          currencyId: 1,
          examDetail: {
            candidateId: this.global.candidateId,
            examId: this.selectedExam.id,
            slotId: this.bookedslot,
            timeZone: this.timeZoneId.Id,
            offSet: this.timeZoneId.Offset,
            examModeId: 1,
            personTenantRoleId: this.global.candidateId,
            examDateTime: this.examDateTime,
            totalAttempts: Schedule[0] === this.selectedExam.title?1:Schedule[1] === this.selectedExam.title?2:Schedule[2] === this.selectedExam.title?3:null

          },

        },
        isPayment: false,
      })
    );
  }

  bookslot(event) {
    this.bookedslot = event.slotId;
    this.examDateTime = event.slotDateUtc
    this.buttonDisable = true;
  }

  selectrangeactive(event) {
    this.slots = event.data;
    this.step = event.id;
  }

  public options(id: number): void {
    if (id == 1) {
      this.isSelect = false;
      this.calendarDisable = false;
      this.Validators.reset()
      this.selectedDate = null
    } else {
      this.isSelect = false;
      this.calendarDisable = false;
      this.selectedDate = null
    }
  }

  disable(data) {
    if (this.ExamID) {
     return  this.ExamID.findIndex((x) => (x.isDisabled && x.id  == data.id)) > -1
   }
   return false;
 }

  Practicereschedule() {
    this.rescheduleloading = true
    const subs$: Subscription = interval(800).subscribe(res => {
      this.value = this.value + 10;
      if (this.value === 150) {
        subs$.unsubscribe();
        this.rescheduleloading = false
        this.value = 0;

      }
    });
    this.store.dispatch<Action>(
      ReschedulePracticeExam({
        PracticerescheduleBody: {
          candidateId: this.global.candidateId,
          examId: this.selectedExam.id,
          slotId: this.bookedslot,
          timeZone: this.timeZoneId.Id,
          offSet: this.timeZoneId.Offset,
          examModeId: 1,
          scheduleId: this._services.PracticeInformation.id,
          personTenantRoleId: this.global.candidateId,
          ExamDateTime: this.examDateTime,
          testCenterName: '',
          testCenterAddress: '',
          testCenterCity: '',
          testCenterState: '',
          testCenterPostalCode: '',
          testCenterId: "",
          isGrievanceFilled: this._services.PracticeInformation.formTypeId == FormTypes.Grievance ? true : false
        },

      }));
    this.store.select(selectorGetPracticeRescheduledResponse).subscribe(data => {
      if (data !=null) {
        (this._services.PracticeInformation.examStatusId === 1 && this._services.PracticeInformation.showStatus ===1) || this._services.PracticeInformation.examStatusId === 10?this.snackbar.callSnackbaronSuccess(
         " Exam Scheduled successfully"
        ): this.snackbar.callSnackbaronSuccess(
          "Exam Rescheduled Successfully"
        );
      
      }
    })
  }

  PracticescheduleAagin() {
    this.scheduleloading = true
    const subs$: Subscription = interval(800).subscribe(res => {
      this.value = this.value + 10;
      if (this.value === 150) {
        subs$.unsubscribe();
        this.scheduleloading = false
        this.value = 0;

      }
    });
    this.store.dispatch<Action>(
      PracticeretrySchedule({
        retryScheduleResponse: {
          candidateId: this.global.candidateId,
          examId: this.selectedExam.id,
          slotId: this.bookedslot,
          timeZone: this.timeZoneId.Id,
          offSet: this.timeZoneId.Offset,
          examModeId: 1,
          personTenantRoleId: this.global.candidateId,
          testCenterId: "",
          accommodationType: "",
          accommodationItems: [
          ],
          clientExamId: 0,
          testCenterName: "",
          testCenterAddress: "",
          testCenterCity: "",
          testCenterState: "",
          testCenterPostalCode: "",
          examDateTime: this.examDateTime,
          scheduleId: this._services.PracticeInformation.id,
        },

      }));
  }

  setSlot() {
    this.timeSlot = [
      { title: "12 AM - 04 AM", id: 1, key: "phase1", data: [], timeperiod: "MIDNIGHT", color: "green" },
      { title: "04 AM - 08 AM", id: 2, key: "phase2", data: [], timeperiod: "EARLY MORNING", color: "blue" },
      { title: "08 AM - 12 PM", id: 3, key: "phase3", data: [], timeperiod: "MORNING", color: "blue" },
      { title: "12 PM - 04 PM", id: 4, key: "phase4", data: [], timeperiod: "AFTERNOON", color: "blue" },
      { title: "04 PM - 08 PM", id: 5, key: "phase5", data: [], timeperiod: "EVENING", color: "green" },
      { title: "08 PM - 11:59 PM", id: 6, key: "phase6", data: [], timeperiod: "NIGHT", color: "green" },
    ];
  }
}

export const Schedule= [
  "Nurse Aide Written Exam",
  "Nurse Aide Written Exam Attempt Two",
  "Nurse Aide Written Exam Attempt Three"
 
]