<div *ngIf="showToolbar" class="my-12 fb-container" fxLayout="row" fxLayout.xs="column" fxLayoutAlign="start start" fxLayoutAlign.xs="start stretch" fxLayoutGap="14px">
    <div class="card px-6 fb-ht-fixed-sm" fxFlex fxFlex.xs="auto">
        <div class="flex">
            <button [color]="router.url.includes('trueFormBuilder') ? 'primary': ''" mat-button (click)="navigate('trueFormBuilder')" class="mat-elevation-z5 hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow m-3 w-3/12">
                Form Builder
            </button>
            <button [color]="router.url.includes('dynFormComponent') ? 'primary': ''" mat-button (click)="navigate('dynFormComponent')" class="mat-elevation-z5 hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow m-3 w-3/12">
                Built Form
            </button>
            <button [color]="router.url.includes('BuiltFormJson') ? 'primary': ''" mat-button *ngIf="showJSONS" (click)="navigate('BuiltFormJson')" class="mat-elevation-z5 hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow m-3 w-3/12">
                Built Form Json
            </button>
            <button [color]="router.url.includes('formJsonOutput') ? 'primary': ''" mat-button *ngIf="showJSONS" (click)="navigate('formJsonOutput')" class="mat-elevation-z5 hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow m-3 w-3/12">
                Built Form Output
            </button>
            <button *ngIf="showSave" [ngClass]="{'disabled':!formBuilderService.genesisForm}" mat-button (click)="handleSaveEvent()" class="mat-elevation-z5 hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow m-3 w-3/12 flex flex-row align-center justify-center">
                Save Form &nbsp; &nbsp; &nbsp;<mat-icon>save</mat-icon>
            </button>
        </div>
    </div>
</div>
<div class="my-12 fb-container forms" fxLayout="row" fxLayout.xs="column" fxLayoutAlign="start start" fxLayoutAlign.xs="start stretch" fxLayoutGap="14px">
    <ng-container *ngIf="(router.url.includes('dynFormComponent') || showOnlyForm); else others">
        <exai-dynamic-form-component #dynamicForm [genesisForm]="existingForm" [data]="existingUserData" [element]="element" [elementId]="elementId" (validatedSubmission)="handleSubmit($event)" (onChange)="onChange($event)" (onBlur)="onBlur($event)" (onFocus)="onFocus($event)"
            (onMatEvent)="onMatEvent($event)" (sectionCompleted)="sectionCompleted.emit($event);" [submitButtonRef]="submitButtonRef" [disabled]="disabled" [openAll]="openAll" (initalSectionValidationEvent)="initalSectionValidationEvent.emit($event)" [showStatic]="showStatic">
        </exai-dynamic-form-component>
    </ng-container>
    <ng-template #others>
        <router-outlet></router-outlet>
    </ng-template>
</div>