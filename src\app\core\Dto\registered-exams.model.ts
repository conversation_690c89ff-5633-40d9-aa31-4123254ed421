export class RegisteredExamsModel {
    public constructor(init?: Partial<RegisteredExamsModel>) {
        Object.assign(this, init);
    }

    allowPayment: boolean = false;
    allowReschedule: boolean = false;
    allowShowResult: boolean = false;
    allowAppeal:boolean = false;
    candidateEmailId: string = '';
    candidateId: number = 0;
    candidateName: string = '';
    eligibilityRouteName: string = '';
    examDateTime: Date = new Date();
    examDateTimeUtc: Date = new Date();
    examId: number = 0;
    examName: string = '';
    examStatus: string = '';
    examStatusId: number = 0;
    eligibilityRouteId:string
    iconUrl: string = '';
    id: number = 0;
    isExcuseAbsenceFilled:boolean=false;
    isExcuseAbsenceSubmitted:boolean=false;
    isGrievanceFilled: boolean = false;
    isGrievanceFormSubmitted: boolean = false;
    mode: string = '';
    personFormId: number = 0;
    timeZone: string = '';
    timeZoneOffset: string = '';
    registeredDateTime: string = '';
    isPassed: boolean = false;
    timeZoneAbbreviation: string = '';
    examMode?:string =''
}

