import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { ApplicationRoutingModule } from "./application-routing.module";
import { ApplicationComponent } from "./application.component";
import { FlexLayoutModule } from "@angular/flex-layout";
import { SecondaryToolbarModule } from "src/@exai/components/secondary-toolbar/secondary-toolbar.module";
import { PageLayoutModule } from "src/@exai/components/page-layout/page-layout.module";
import { ContainerModule } from "src/@exai/directives/container/container.module";
import { MatTabsModule } from "@angular/material/tabs";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { IconModule } from "@visurel/iconify-angular";
import { BreadcrumbsModule } from "src/@exai/components/breadcrumbs/breadcrumbs.module";
import { MatSelectModule } from "@angular/material/select";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CdkStepperModule } from "@angular/cdk/stepper";
import { MatExpansionModule } from "@angular/material/expansion";
import { MatStepperModule } from "@angular/material/stepper";
import { NgDynamicBreadcrumbModule } from "ng-dynamic-breadcrumb";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { PopUpComponent } from "./application-form/pop-up/pop-up.component";
import { MatDialogModule } from "@angular/material/dialog";
import { CdkAccordionModule } from "@angular/cdk/accordion";
import { ApplicationFilledComponent } from "./application-filled/application-filled.component";
import { FormBuilderModule } from "src/app/core/examroom-formbuilder/form-builder.module";
import { MatTooltipModule } from "@angular/material/tooltip";
import { StoreModule } from "@ngrx/store";
import { EffectsModule } from "@ngrx/effects";
import { applicationReducer } from "./state/application.reducers";
import { ApplicationEffects } from "./state/application.effects";
import { APPLICATION_STATE_NAME } from "./state/application.selectors";
import { CommonComponentModule } from "src/app/core/common-component/common-component.module";
import {MatCheckboxModule} from '@angular/material/checkbox';
import { HTTP_INTERCEPTORS } from "@angular/common/http";
import { TokenInterceptorService } from "src/app/core/token-interceptor.service";

@NgModule({
  declarations: [
    ApplicationComponent,
    PopUpComponent,
    ApplicationFilledComponent,
  ],
  imports: [
    CommonModule,
    ApplicationRoutingModule,
    FlexLayoutModule,
    SecondaryToolbarModule,
    PageLayoutModule,
    ContainerModule,
    MatTabsModule,
    MatStepperModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    IconModule,
    BreadcrumbsModule,
    MatSelectModule,
    MatExpansionModule,
    FormsModule,
    ReactiveFormsModule,
    CdkStepperModule,
    MatIconModule,
    NgDynamicBreadcrumbModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatFormFieldModule,
    MatDialogModule,
    CdkAccordionModule,
    FormBuilderModule,
    MatTooltipModule,
    CommonComponentModule,
    StoreModule.forFeature(APPLICATION_STATE_NAME, applicationReducer),
    EffectsModule.forFeature([ApplicationEffects]),
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class ApplicationModule {}
