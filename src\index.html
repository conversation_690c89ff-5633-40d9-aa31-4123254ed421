<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Credentia | Candidate</title>
    <base href="/">

    <meta content="width=device-width, initial-scale=1" name="viewport">
    <link rel="icon" type="image/svg" href="./assets/img/Icons/Cred-logo.svg">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" rel="stylesheet" crossorigin="anonymous">
    <script src="https://unpkg.com/@lottiefiles/lottie-player@0.4.0/dist/lottie-player.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://enterprise.opentok.com/v2.17.3/js/opentok.min.js"></script>
</head>

<body dir="ltr">
    <style>
        #exai-splash-screen {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #e5e5e5a8;
            z-index: 99999;
            pointer-events: none;
        }
        
        #exai-splash-screen>.wrapper {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            -webkit-transform: translate(-50%, -50%);
        }
        
        @-webkit-keyframes ball-scale-multiple {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                opacity: 0;
            }
            5% {
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 0;
            }
        }
        
        @keyframes ball-scale-multiple {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                opacity: 0;
            }
            5% {
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 0;
            }
        }
        
        .ball-scale-multiple {
            position: relative;
            -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
        }
        
        .ball-scale-multiple>div:nth-child(2) {
            -webkit-animation-delay: -0.4s;
            animation-delay: -0.4s;
        }
        
        .ball-scale-multiple>div:nth-child(3) {
            -webkit-animation-delay: -0.2s;
            animation-delay: -0.2s;
        }
        
        .ball-scale-multiple>div {
            /* background-color: #fff; */
            background-color: var(--text-color2);
            border-radius: 100%;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            position: absolute;
            left: -30px;
            top: 0;
            opacity: 0;
            margin: 0;
            width: 60px;
            height: 60px;
            -webkit-animation: ball-scale-multiple 1s 0s linear infinite;
            animation: ball-scale-multiple 1s 0s linear infinite;
        }
    </style>

    <div id="exai-splash-screen">
        <div class="wrapper">
            <div class="ball-scale-multiple">
                <div></div>
                <div></div>
                <div></div>
            </div>
        </div>
    </div>

    <exai-root></exai-root>
</body>

<!-- <script> (function(){ var s = document.createElement('script'); var h = document.querySelector('head') || document.body; s.src = 'https://acsbapp.com/apps/app/dist/js/app.js'; s.async = true; s.onload = function(){ acsbJS.init({ statementLink : '', footerHtml : '', hideMobile : false, hideTrigger : false, disableBgProcess : false, language : 'en', position : 'right', leadColor : '#146FF8', triggerColor : '#146FF8', triggerRadius : '50%', triggerPositionX : 'right', triggerPositionY : 'bottom', triggerIcon : 'people', triggerSize : 'bottom', triggerOffsetX : 20, triggerOffsetY : 20, mobile : { triggerSize : 'small', triggerPositionX : 'right', triggerPositionY : 'bottom', triggerOffsetX : 20, triggerOffsetY : 20, triggerRadius : '20' } }); }; h.appendChild(s); })();</script>
<script type="text/javascript" id="zsiqchat">var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq || {widgetcode: "40915a4c5456914be61a309ec0c88b6d29ad5b49d8ff0601c799d777ed78f9c5604ca47c4374703bf25bd6a1af755ac1", values:{},ready:function(){}};var d=document;s=d.createElement("script");s.type="text/javascript";s.id="zsiqscript";s.defer=true;s.src="https://salesiq.zoho.com/widget";t=d.getElementsByTagName("script")[0];t.parentNode.insertBefore(s,t);</script> -->
</html>