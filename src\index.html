<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Credentia | Login</title>
    <base href="/" />

    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <link rel="icon" type="image/svg" href="./assets/img/demo/Cred-logo.svg" />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css?family=Material+Icons%7CMaterial+Icons+Outlined%7CMaterial+Icons+Two+Tone%7CMaterial+Icons+Round%7CMaterial+Icons+Sharp"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://d17nz991552y2g.cloudfront.net/app/js/jqueryandencoder.ffa5afd5124fbedceea9.js"></script>
    <script src="https://static.opentok.com/v2/js/opentok.js"></script>
  </head>

  <body dir="ltr">
    <style>
      #exai-splash-screen {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #e5e5e5a8;
        z-index: 99999;
        pointer-events: none;
      }

      #exai-splash-screen > .wrapper {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
      }

      @-webkit-keyframes ball-scale-multiple {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
          opacity: 0;
        }

        5% {
          opacity: 1;
        }

        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
          opacity: 0;
        }
      }

      @keyframes ball-scale-multiple {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
          opacity: 0;
        }

        5% {
          opacity: 1;
        }

        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
          opacity: 0;
        }
      }

      .ball-scale-multiple {
        position: relative;
        -webkit-transform: translateY(-30px);
        transform: translateY(-30px);
      }

      .ball-scale-multiple > div:nth-child(2) {
        -webkit-animation-delay: -0.4s;
        animation-delay: -0.4s;
      }

      .ball-scale-multiple > div:nth-child(3) {
        -webkit-animation-delay: -0.2s;
        animation-delay: -0.2s;
      }

      .ball-scale-multiple > div {
        background-color: var(--primary);
        border-radius: 100%;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
        position: absolute;
        left: -30px;
        top: 0;
        opacity: 0;
        margin: 0;
        width: 60px;
        height: 60px;
        -webkit-animation: ball-scale-multiple 1s 0s linear infinite;
        animation: ball-scale-multiple 1s 0s linear infinite;
      }

      ::part(acsb-trigger) {
        color: rgba(0, 0, 0, 0.87) !important;
        border-color: rgba(82, 63, 105, 0.06) !important;
        border-style: solid !important;
        right: inherit !important;
      }

      .zsiq_custommain .zsiq_unrdcnt,
      .zsiq_theme1 .zsiq_unrdcnt {
        display: block !important;
        left: 43px !important;
        border: 1px solid #fff;
      }
      .siqico-call:before,
      .siqico-mincall:before {
        content: "8" !important;
      }
      .zsiq_theme1 div.zsiq_cnt {
        background-color: #0071ce !important;
      }
      .zsiq_theme1 .zsiq_cnt p,
      .zsiq_theme1 div.zsiq_cnt {
        color: #fff !important;
      }
      div.zsiq_flt_rel {
        background-color: #0066cc !important;
      }
      .zsiq_theme1 .zsiq_cnt:after {
        background-color: #0071ce !important;
        box-shadow: 0 0 0 0 #eee !important;
      }

      .acsb-widget .acsb-flex.acsb-flex-center {
        display: none !important;
      }
    </style>

    <style>
      #divslct {
        border: 1px solid #ccc;
        padding-bottom: 20px;
        position: fixed;
        z-index: 1000;
        right: 20px;
        bottom: 20px;
        height: 182px;
        width: 230px;
        display: none;
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        transition: all 0.3s ease;
      }
      #aslct {
        position: fixed;
        right: -6px;
        bottom: -10px;
        z-index: 1000;
        /*background-color: #499b90;*/
        color: #fff;
        padding: 15px 20px;
        border-radius: 50px;
        cursor: pointer;
        font-weight: bold;
        /*box-shadow: 0 4px 8px rgba(0,0,0,0.1);*/
        transition: background-color 0.3s ease;
      }
      #aslct img {
        border-radius: 50px;
        width: 57px;
        height: 57px;
      }
      #aslct:hover {
        /*background-color: #3a7d73;*/
        /*box-shadow: 0 4px 8px rgba(0,0,0,0.1);*/
      }
      #slct {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-top: 10px;
        border-left: solid 1px red;
      }

      .close-btn {
        float: right;
        background: none;
        border: none;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
      }
      .chatHeader {
        padding: 17px 10px 17px 10px;
        background: #2182df;
        border-radius: 10px 10px 0px 0px;
      }
      .close-btn:hover {
        color: #499b90;
      }
      select {
        appearance: none;
        background: #fff
          url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="#000" stroke-width="2" d="M2 7 L12 17 L22 7"/></svg>')
          no-repeat right 10px center;
        background-size: 12px;
      }
      .popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: none;
        z-index: 1000;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        background: #00000029;
      }

      input,
      select {
        -moz-appearance: none;
        appearance: none;
        background-color: #fff;
        border-width: 1px;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        line-height: 1.5rem;
        padding-left: 6px;
      }

      textarea {
        padding: 0;
        -moz-appearance: none;
        appearance: none;
        background-color: #fff;
        border-width: 1px;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        line-height: 1.5rem;
      }

      button,
      input,
      optgroup,
      select,
      textarea {
        font-family: inherit;
        padding-left: 6px;
      }

      .required-label:after {
        content: " *";
        color: red;
      }
    </style>

    <div id="exai-splash-screen">
      <div class="wrapper">
        <div class="ball-scale-multiple">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>

    <div id="divslct">
      <div class="chatHeader">
        <button style="color: white" class="close-btn" onclick="cls();">
          ×
        </button>
        <label style="color: white" for="slct">Chat With Us Now</label>
      </div>
      <div style="padding: 8px; padding-top: 0px">
        <select id="slct" onchange="redr();">
          <option value="">Select State</option>
          <option value="PA">PA</option>
          <option value="Other">Other</option>
        </select>
      </div>
    </div>
    <div class="popup" id="popup">
      <div
        class="relative bg-white rounded-lg shadow dark:bg-gray-700 w-full max-w-md"
      >
        <!-- Modal header -->
        <div
          class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600"
        >
          <h3 class="text-xl font-medium text-gray-900">Notification</h3>
          <button
            type="button"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
            onclick="hidePopup()"
          >
            <svg
              class="w-3 h-3"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 14 14"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
              ></path>
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <div class="p-4 md:p-5 space-y-4">
          <h4 class="text-lg font-normal text-gray-900" id="popup-content">
            You have Selected <span>State</span>. Please Click Confirm for
            Confirmation.
          </h4>
        </div>
        <!-- Modal footer -->
        <div
          class="flex items-center gap-4 p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600 justify-end"
          id="popup-footer"
        >
          <a
            href="javascript:;"
            onclick="notconfrmd();"
            class="text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
            >Cancel</a
          >
          <a
            href="javascript:;"
            onclick="confrmd();"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            >Confirm</a
          >
        </div>
      </div>
    </div>

    <!-- Email Modal -->

    <div
      id="emailModal"
      class="fixed z-[1000000] inset-0 overflow-y-auto hidden"
    >
      <div
        class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
      >
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span
          class="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
          >&#8203;</span
        >
        <div
          class="inline-block align-bottom rounded-lg text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
        >
          <div class="container mx-auto">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
              <div
                class="px-4 py-5 sm:px-6"
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                "
              >
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Support Ticket Web Form
                </h3>
                <button
                  type="button"
                  class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                  onclick="hidePopupEmail()"
                >
                  <svg
                    class="w-3 h-3"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 14 14"
                  >
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                    ></path>
                  </svg>
                </button>
              </div>

              <div class="border-t border-gray-200">
                <form
                  name="zsWebToCase_547474000041204025"
                  id="zsWebToCase_547474000041204025"
                  action="https://tickets.examroom.ai/support/WebToCase"
                  method="POST"
                  class="p-8"
                  onSubmit="return zsValidateMandatoryFields()"
                  enctype="multipart/form-data"
                >
                  <input
                    type="hidden"
                    name="xnQsjsdp"
                    value="edbsnbc421d47abd6727c9bb03195cca06104"
                  />
                  <input
                    type="hidden"
                    name="xmIwtLD"
                    value="edbsn7cfddf5755adface8ac676438233573b20543c6fbb09f74a37a1b87043b03447"
                  />
                  <input type="hidden" name="xJdfEaS" value="" />
                  <input type="hidden" name="actionType" value="Q2FzZXM=" />
                  <input type="hidden" id="property(module)" value="Cases" />
                  <input
                    type="hidden"
                    id="dependent_field_values_Cases"
                    value='&#x7b;"JSON_VALUES"&#x3a;&#x7b;&#x7d;,"JSON_SELECT_VALUES"&#x3a;&#x7b;&#x7d;,"JSON_MAP_DEP_LABELS"&#x3a;&#x5b;&#x5d;&#x7d;'
                  />
                  <input
                    type="hidden"
                    name="returnURL"
                    value="https&#x3a;&#x2f;&#x2f;credentiauat.examroom.ai&#x2f;support&#x2f;"
                  />

                  <div id="step1">
                    <!-- First Name -->
                    <div class="mb-4">
                      <label
                        for="first-name"
                        class="required-label block text-sm font-medium text-gray-700"
                      >
                        First Name</label
                      >
                      <input
                        type="text"
                        name="First Name"
                        id="first-name"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>

                    <!-- Last Name -->
                    <div class="mb-4">
                      <label
                        for="last-name"
                        class="required-label block text-sm font-medium text-gray-700"
                      >
                        Last Name</label
                      >
                      <input
                        type="text"
                        name="Contact Name"
                        id="last-name"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>

                    <!-- Subject -->
                    <div class="mb-4">
                      <label
                        for="subject"
                        class="required-label block text-sm font-medium text-gray-700"
                        >Subject</label
                      >
                      <input
                        type="text"
                        name="Subject"
                        id="subject"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>

                    <!-- Email -->
                    <div class="mb-4">
                      <label
                        for="email"
                        class="required-label block text-sm font-medium text-gray-700"
                      >
                        Email</label
                      >
                      <input
                        type="text"
                        name="Email"
                        id="email"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>

                    <!-- Category -->
                    <div class="mb-4">
                      <label
                        for="category"
                        class="required-label block text-sm font-medium text-gray-700"
                        >Category</label
                      >
                      <select
                        name="Category"
                        class="mt-1 block w-full h-10 px-4 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                      >
                        <option value="">Select Category</option>
                        <option value="Applications">Applications</option>
                        <option value="Vouchers">Vouchers</option>
                        <option value="Email Update">Email Update</option>
                        <option value="Exam Detail">Exam Detail</option>
                        <option value="Exam Scheduling Issue">
                          Exam Scheduling Issue
                        </option>
                        <option value="Update Exam Status">
                          Update Exam Status
                        </option>
                        <option value="CNA365 System Issues">
                          CNA365 System Issues
                        </option>
                        <option value="Onboarding or Offboarding">
                          Onboarding or Offboarding
                        </option>
                        <option value="Activation">Activation</option>
                        <option value="Incorrect State or Disable Account">
                          Incorrect State or Disable Account
                        </option>
                        <option value="Reports">Reports</option>
                        <option value="Scores">Scores</option>
                        <option value="Training Program">
                          Training Program
                        </option>
                        <option value="Registry">Registry</option>
                        <option value="Exam Payments">Exam Payments</option>
                        <option value="Accommodations">Accommodations</option>
                        <option value="Grievance Request">
                          Grievance Request
                        </option>
                        <option value="Testing Applications">
                          Testing Applications
                        </option>
                        <option value="Letter of Good Standing/Verification">
                          Letter of Good Standing/Verification
                        </option>
                        <option value="Score Results (Ship and Score)">
                          Score Results (Ship and Score)
                        </option>
                        <option value="Score Results (Full Service States)">
                          Score Results (Full Service States)
                        </option>
                        <option value="PA Active Nurse Aide Registry List">
                          PA Active Nurse Aide Registry List
                        </option>
                        <option value="Facilities">Facilities</option>
                        <option value="Evaluator Support">
                          Evaluator Support
                        </option>
                        <option value="State Client Support">
                          State Client Support
                        </option>
                        <option value="Careers/Recruiting">
                          Careers/Recruiting
                        </option>
                        <option value="Training Instructors">
                          Training Instructors
                        </option>
                        <option value="Evaluator Skills Questions">
                          Evaluator Skills Questions
                        </option>
                      </select>
                    </div>

                    <div class="py-3 text-right">
                      <button
                        type="button"
                        onclick="cancel()"
                        class="text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                      >
                        Cancel
                      </button>

                      <button
                        type="button"
                        onclick="step2()"
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-500 hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Next
                      </button>
                    </div>
                  </div>

                  <div id="step2" class="hidden">
                    <!-- State -->
                    <div class="mb-4 relative">
                      <label
                        for="first-name"
                        class="required-label mb-2 block text-sm font-medium text-gray-700"
                        >Select State</label
                      >

                      <div
                        id="chipsInput"
                        class="flex flex-wrap items-center border border-gray-300 rounded-md gap-2 shadow-sm p-2 focus-within:border-blue-500"
                      >
                        <!-- Chips will be inserted here -->
                        <input
                          type="text"
                          placeholder="Search.."
                          id="myInput"
                          onkeyup="filterFunction()"
                          autocomplete="off"
                          class="flex-1 pl-1 p-0 border-0 border-b-1 hover:outline-none !shadow-none !ring-0 !outline-none"
                        />
                      </div>
                      <div
                        id="myDropdown"
                        class="dropdown-content hidden absolute mt-1 left-0 right-0 z-10 bg-white shadow-lg max-h-60 overflow-auto border border-gray-200 rounded-md"
                      >
                        <!-- Dropdown items -->
                        <div
                          onclick="addChip('Alabama')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Alabama
                        </div>
                        <div
                          onclick="addChip('Alaska')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Alaska
                        </div>
                        <div
                          onclick="addChip('California')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          California
                        </div>
                        <div
                          onclick="addChip('Colorado')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Colorado
                        </div>
                        <div
                          onclick="addChip('District of Columbia')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          District of Columbia
                        </div>
                        <div
                          onclick="addChip('Georgia')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Georgia
                        </div>
                        <div
                          onclick="addChip('Maryland')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Maryland
                        </div>
                        <div
                          onclick="addChip('North Carolina')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          North Carolina
                        </div>
                        <div
                          onclick="addChip('Nevada')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Nevada
                        </div>
                        <div
                          onclick="addChip('Pennsylvania')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Pennsylvania
                        </div>
                        <div
                          onclick="addChip('Rhode Island')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Rhode Island
                        </div>
                        <div
                          onclick="addChip('South Carolina')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          South Carolina
                        </div>
                        <div
                          onclick="addChip('Virginia')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Virginia
                        </div>
                        <div
                          onclick="addChip('Washington')"
                          class="cursor-pointer px-4 py-2 hover:bg-gray-100"
                        >
                          Washington
                        </div>
                      </div>
                    </div>
                    <div style="display: none">
                      <select
                        name="State"
                        multiple=""
                        onchange="setDependent(this, false)"
                        id="CASECF41"
                        class="manfieldbdr"
                      >
                        <option value="Alabama" selected="selected">
                          Alabama
                        </option>
                        <option value="California">California</option>
                        <option value="Colorado">Colorado</option>
                        <option value="District of Columbia">
                          District of Columbia
                        </option>
                        <option value="Georgia">Georgia</option>
                        <option value="Maryland">Maryland</option>
                        <option value="North Carolina">North Carolina</option>
                        <option value="Nevada">Nevada</option>
                        <option value="Pennsylvania">Pennsylvania</option>
                        <option value="Rhode Island">Rhode Island</option>
                        <option value="South carolina">South carolina</option>
                        <option value="Virginia">Virginia</option>
                        <option value="Washington">Washington</option>
                        <option value="Alaska">Alaska</option>
                      </select>
                    </div>

                    <!-- Candidate ID Number -->
                    <div class="mb-4">
                      <label
                        for="candidate-id"
                        class="block text-sm font-medium text-gray-700"
                        >Candidate ID</label
                      >
                      <input
                        type="text"
                        name="Candidate ID Number"
                        id="candidate-id"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>

                    <!-- Priority -->
                    <div class="mb-4">
                      <label
                        for="priority"
                        class="required-label block text-sm font-medium text-gray-700"
                        >Priority</label
                      >
                      <select
                        id="priority"
                        name="Priority"
                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                      </select>
                    </div>

                    <!-- Description -->
                    <div class="mb-4">
                      <label
                        for="description"
                        class="required-label block text-sm font-medium text-gray-700"
                        >Description</label
                      >
                      <textarea
                        id="description"
                        minlength="50"
                        name="Description"
                        rows="4"
                        class="first-letter:mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      ></textarea>
                    </div>

                    <!-- Attachment -->
                    <div class="mb-4">
                      <label
                        for="attachment_1"
                        class="block text-sm font-medium text-gray-700"
                      >
                        Attachment
                      </label>
                      <div
                        id="drop_zone"
                        class="mt-1 flex justify-center px-6 cursor-pointer pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 hover:bg-gray-50"
                      >
                        <div class="space-y-1 text-center">
                          <svg
                            class="mx-auto h-12 w-12 text-gray-400"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                            aria-hidden="true"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v24a4 4 0 004 4h24a4 4 0 004-4V20M28 8l14 14M28 8v14h14m-10 4h6m-3-3v6m-9 5H8"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            ></path>
                          </svg>
                          <div class="flex text-sm text-gray-600">
                            <p
                              class="relative cursor-pointer rounded-md font-medium text-blue-500 hover:text-blue-500 focus-within:outline-none"
                            >
                              <span>Upload a file or drag and drop</span>
                              <input
                                id="attachment_1"
                                name="attachment_1"
                                type="file"
                                class="sr-only"
                              />
                            </p>
                          </div>
                          <p class="text-xs text-gray-500">
                            PNG, JPG, GIF up to 2MB
                          </p>
                        </div>
                      </div>
                    </div>
                    <!-- Progress Bar: Hidden initially -->
                    <div id="progressBarContainer" class="hidden">
                      <div
                        class="bg-gray-200 rounded-full h-2.5 dark:bg-gray-700"
                      >
                        <div
                          id="progressBar"
                          class="bg-blue-600 h-2.5 rounded-full"
                          style="width: 0%"
                        ></div>
                      </div>
                    </div>
                    <!-- File Preview: Hidden initially -->
                    <div
                      id="previewContainer"
                      class="relative hidden mt-4 mb-4"
                    >
                      <button
                        type="button"
                        id="removeFileBtn"
                        class="absolute top-0 right-0 bg-gray-300 rounded-full p-1 m-1"
                      >
                        <svg
                          class="h-6 w-6 text-white"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                      <p class="text-sm font-semibold text-gray-700">
                        Preview:
                      </p>
                      <img
                        id="filePreview"
                        src=""
                        alt="File Preview"
                        class="max-w-xs mt-2 rounded-lg border border-gray-300"
                      />
                    </div>

                    <div id="zsCaptchaLoading">
                      <strong>Loading...<br /><br /></strong>
                    </div>
                    <div id="zsCaptcha" style="display: none">
                      <img src="#" id="zsCaptchaUrl" name="zsCaptchaImage" /><a
                        href="javascript:;"
                        style="
                          color: #00a3fe;
                          cursor: pointer;
                          margin-left: 10px;
                          vertical-align: middle;
                          text-decoration: none;
                        "
                        class="zsFontClass"
                        onclick="zsRegenerateCaptcha()"
                        >Refresh</a
                      >
                    </div>
                    <div>
                      <input type="text" name="zsWebFormCaptchaWord" />
                      <input type="hidden" name="zsCaptchaSrc" value="" />
                    </div>

                    <!-- Submit Button -->
                    <div class="py-3 text-right">
                      <button
                        type="button"
                        onclick="step1()"
                        class="inline-flex float-left justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gray-400 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Back
                      </button>
                      <button
                        type="submit"
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-500 hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Submit
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="aslct" onclick="redr();">
      <img src="https://credentia-rosy.vercel.app/storage/chatlogo-1.png" />
    </div>

    <exai-root></exai-root>
  </body>


  <script>
    (function () {
      var s = document.createElement("script");
      var h = document.querySelector("head") || document.body;
      s.src = "https://acsbapp.com/apps/app/dist/js/app.js";
      s.async = true;
      s.onload = function () {
        acsbJS.init({
          statementLink: "",
          footerHtml: `<a href='https://examroom.ai/'>Accessibility
  by ExamRoom.AI</a>`,
          hideMobile: false,
          hideTrigger: false,
          disableBgProcess: false,
          language: "en",
          position: "left",
          leadColor: "#146FF8",
          triggerColor: "#146FF8",
          triggerRadius: "50%",
          triggerPositionX: "right",
          triggerPositionY: "bottom",
          triggerIcon: "people",
          triggerSize: "bottom",
          triggerOffsetX: 20,
          triggerOffsetY: 20,
          mobile: {
            triggerSize: "small",
            triggerPositionX: "right",
            triggerPositionY: "bottom",
            triggerOffsetX: 20,
            triggerOffsetY: 20,
            triggerRadius: "20",
          },
        });
      };
      h.appendChild(s);
    })();
  </script>

  <script>
    function goToDeshboard() {
      window.location.href = "/login";
    }
    function validateFormWithCaptcha(event) {
      event.preventDefault();
      var recaptchaResponse = grecaptcha.getResponse();

      if (recaptchaResponse.length === 0) {
        alert("Please complete the reCAPTCHA.");
        return false;
      }

      // If reCAPTCHA is validated, proceed with form submission
      document.getElementById("zsWebToCase_547474000041204025").submit();
    }
  </script>
</html>
