.my-12 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.fb-container {
    padding-left: 1rem!important;
    padding-right: 1rem!important;
}

.fb-ht-fixed {
    height: 80vh;
    overflow: auto;
}

.fb-ht-fixed-sm {
    height: 15vh;
    overflow: auto;
}

#wMsg {
    display: none;
}

.add-icon {
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 1.7rem!important;
    cursor: pointer;
}

.editor {
    width: 92%!important;
    margin: 0.5rem;
}

.basic-card {
    margin: 1rem 1rem;
    margin-bottom: 4rem;
    max-width: 100%;
    min-height: fit-content;
    min-width: fit-content;
    border: 1px solid rgba(49, 48, 48, 0.829);
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 0.92rem;
}

.basic-card-children {
    margin: 0.5rem 0.5rem;
    width: stretch;
    height: stretch;
    min-height: fit-content;
    min-width: fit-content;
    border: 1px solid rgba(49, 48, 48, 0.829);
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 0.9rem;
}

.genesis-form-feild {
    width: 27%;
    margin: 0.7rem;
}

.form-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}