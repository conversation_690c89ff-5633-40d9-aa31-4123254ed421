export interface Performance{
    contentDomain: string,
    totalScore: number,
    obtainedScore: number,
    percentage: number
}
  
export interface ExamDetails{
    name: string,
    id: number,
    examMode: string,
    email: string,
    examName: string,
    score: number,
    percentage: number
}

export interface Score{
    number: number,
    isCorrect: string
    
}

export interface ExamResult{
    examDetails:ExamDetails,
    scores:Score[],
    performance:Performance[]

}