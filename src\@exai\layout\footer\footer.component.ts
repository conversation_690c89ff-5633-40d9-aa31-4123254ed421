import { Component, Input, OnD<PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import icShoppingBasket from '@iconify/icons-ic/twotone-shopping-basket';
import { BehaviorSubject, Observable } from 'rxjs';

@Component({
  selector: "exai-footer",
  templateUrl: "./footer.component.html",
  styleUrls: ["./footer.component.scss"],
})
export class FooterComponent implements OnInit, OnDestroy {
  present_year
  helpButtonExpanded: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  helpButtonExpanded$: Observable<boolean> = this.helpButtonExpanded.asObservable();
  
  @Input() customTemplate: TemplateRef<any>;
  icShoppingBasket = icShoppingBasket;
  
  @Input() chatPanelRef: TemplateRef<any>;
  @Input() chatPanelExpanded: BehaviorSubject<boolean>;
  @Input() chatPanelExpanded$: Observable<boolean>;

  constructor() {
    const d = new Date();
    this.present_year = d.getFullYear();
  }

  ngOnInit() {
  }

  ngOnDestroy(): void {}
}
