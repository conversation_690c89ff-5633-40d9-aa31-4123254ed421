<div class="pr-4">
    <progress-stepper-registry *ngFor="let step of start,let i=index,let last =last">
        <progress-step-registry [reviewer]="step.reviewer" [name]="step.name" [comment]="step.comment"
            [actionOn]="step.actionOn">
        </progress-step-registry>
    </progress-stepper-registry>

    <progress-label-registry class="showmore cursor-pointer" (click)="Show()" (click)="toggle()"
        *ngIf="start.length || middle.length || end.length">
        <div class="flex justify-start item-center">
            <mat-icon *ngIf="showmore">more_vert</mat-icon>
            <span class="pl-4 mat-body-1 font-bold">
                <ul *ngIf="showmore">{{this.show_hide}}</ul>
            </span>
        </div>
    </progress-label-registry>
    <div class="open-close-container overflow-hidden" [@openClose]="isOpen ? true : false">
        <progress-stepper-registry *ngFor="let step of middle,let i=index,let last =last">
            <progress-step-registry [reviewer]="step.reviewer" [name]="step.name" [comment]="step.comment"
                [actionOn]="step.actionOn">
            </progress-step-registry>
        </progress-stepper-registry>
    </div>

    <progress-stepper-registry *ngFor="let step of end,let i=index,let last =last">
        <progress-step-registry [reviewer]="step.reviewer" [isLast]="last" [name]="step.name" [comment]="step.comment"
            [actionOn]="step.actionOn">
        </progress-step-registry>
    </progress-stepper-registry>
</div>