import { createAction, props } from "@ngrx/store";
import { PracticeSkillResponse } from "./models/practice-skill-response.model";
import {
  PracticeBundleSkill,
  PracticeSkillBundleResponse,
} from "./models/practice-skill-bundle.model";
import { PracticeSkillMode as PracticeSkillModeResponse } from "./models/practice-skill-mode.model";

//Action methods for Practice-skill

export const loadPracticeSkills = createAction(
  "[Practice Skills] Load",
  props<{ pageNo: number; pageSize: number }>()
);

export const loadPracticeSkillsSuccess = createAction(
  "[Practice Skills] Load Success",
  props<{ response: PracticeSkillResponse }>()
);

export const loadPracticeSkillsFailure = createAction(
  "[Practice Skills] Load Failure",
  props<{ error: any }>()
);

export const loadPracticeSkillsMode = createAction(
  "[Practice Skills] Load Mode"
);

export const loadPracticeSkillsModeSuccess = createAction(
  "[Practice Skills] Load Mode Success",
  props<{ response: PracticeSkillModeResponse }>()
);

export const loadPracticeSkillsModeFailure = createAction(
  "[Practice Skills] Load Mode Failure",
  props<{ error: any }>()
);

//Action methods for Practice-skill By id
export const loadPracticeSkillByGuid = createAction(
  "[Practice Skill] Load By Guid",
  props<{ practiceSkillGuid: string }>()
);

export const loadPracticeSkillByGuidSuccess = createAction(
  "[Practice Skill] Load By Guid Success",
  props<{ skillResponse: PracticeSkillResponse }>()
);

export const loadPracticeSkillByGuidFailure = createAction(
  "[Practice Skill] Load By Guid Failure",
  props<{ error: any }>()
);

//Action methods for BundledPractice skill
export const loadPracticeSkillBundles = createAction(
  "[PracticeSkillBundle] Load Pracgtice Skill Bundles",
  props<{ pageNo: number; pageSize: number }>()
);

export const loadPracticeSkillBundlesSuccess = createAction(
  "[PracticeSkillBundle] Load Practice Skill Bundles Success",
  props<{ response: PracticeSkillBundleResponse }>()
);

export const loadPracticeSkillBundlesFailure = createAction(
  "[PracticeSkillBundle] Load Practice Skill Bundles Failure",
  props<{ error: any }>()
);
