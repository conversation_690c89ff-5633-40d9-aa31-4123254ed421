<!-- <div class="text-xs font-bold pb-4" fxLayout="row" fxLayoutAlign="start">
    Application Progress
</div> -->
<div class="">
    <ng-container *ngFor="let section of sections,let sectionIndex=index">
        <registry-form-progress-step [sectionName]="(section.sectionDesc && trimString(section.sectionDesc) ) ? section.sectionDesc : section.sectionName" [status]="section.status" [index]="sectionIndex" [size]="sections.length">
        </registry-form-progress-step>
    </ng-container>
</div>