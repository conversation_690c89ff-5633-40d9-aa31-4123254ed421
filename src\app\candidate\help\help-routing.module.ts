import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TicketsComponent } from './tickets/tickets.component';
import { TicketDetailsComponent } from './ticket-details/ticket-details.component';
import { HelpComponent } from './help.component';
import { SelectCategoryComponent } from './select-category/select-category.component';
import { SupportTicketComponent } from './support-ticket/support-ticket.component';

const routes: Routes = [
  {
    path: '',
    component: HelpComponent,
    data: {
      title: 'help',                  
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Help',
          url: ''
        }
      ],
    },
    children: [
      {
        path: '', component: SelectCategoryComponent
      },
      {
        path: 'raise-ticket', component: SupportTicketComponent
      },
      {
        path: ':category', component: TicketsComponent
      },
      {
        path: ':id/get-help', component: TicketDetailsComponent
      }
    ]
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HelpRoutingModule { }

