import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ScheduledComponent } from '../scheduled/scheduled.component';
import { GrievanceFormComponent } from './grievance-form.component';

const routes: Routes = [
  {
    path: '',
    component: GrievanceFormComponent,
    // canActivate: [AuthGuard],
    data: {
      title: 'Grievance',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Grievance Form',
          url: ''
        },
      ]
    },
  },
  {
    path: 'report-grievance',
    loadChildren: () => import('../forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
    // canActivate: [AuthGuard],
    data: {
      title: 'Grievance',
      breadcrumb: [
        {
          label: 'Home',
          url: '/dashboard'
        },
        {
          label: 'Grievance Form',
          url: '/grievance-form'
        },
        {
          label: 'Report Grievance Form',
          url: ""
        }

      ]
    },
  },
  { path: 'examSchedule', component: ScheduledComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class GrievanceFormRoutingModule { }
