
import { SharedState } from './shared/shared.state';
import { SHARED_STATE_NAME } from './shared/shared.selectors';
import { SharedReducer } from './shared/shared.reducers';
import { routerReducer, RouterReducerState } from '@ngrx/router-store';
import { SCHEDULED_STATE_NAME } from '../scheduled/state/scheduled.selectors';
import { scheduledReducer } from '../scheduled/state/scheduled.reducers';

export interface CandidateAppState {
  [SHARED_STATE_NAME]: SharedState;
  router: RouterReducerState;
  
}

export const appReducer = {
  [SHARED_STATE_NAME]: SharedReducer,
  [SCHEDULED_STATE_NAME]: scheduledReducer,
  router: routerReducer,
};




