export class ExamTypeModel {
    public constructor(init?: Partial<ExamTypeModel>) {
        Object.assign(this, init);
    }

    id: number = 0;
    name: string = '';
    checked: boolean = false;
    disabled: boolean = false;
    public getDefaultValues(): ExamTypeModel[] {
        let examTypes: ExamTypeModel[] = [];
        examTypes.push(new ExamTypeModel({
            id: 1, name: 'Online', checked: false, disabled: false
        }));

        examTypes.push(new ExamTypeModel({
            id: 2, name: 'Test Center', checked: false, disabled: false
        }));
        return examTypes;
    }
}