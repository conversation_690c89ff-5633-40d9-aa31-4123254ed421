import { createReducer, on } from "@ngrx/store";
import * as PracticeSkillActions from "./practice-skills.actions";
import {
  initialPracticeSkillBundlesState,
  initialPracticeSkillByGuidState,
  initialPracticeSkillModeState,
  initialPracticeSkillState,
  PracticeSkillByGuidState,
  PracticeSkillModeState,
} from "./practice-skills.state";

/**
 * practiceSkillsReducer to manage the state of Practice skill
 * Handle loading, success, and failure states.
 */
export const practiceSkillsReducer = createReducer(
  initialPracticeSkillState,

  on(PracticeSkillActions.loadPracticeSkills, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(PracticeSkillActions.loadPracticeSkillsSuccess, (state, { response }) => ({
    ...state,
    skills: response.data,
    totalRecords: response.totalRecords,
    pageSize: response.pageSize,
    pageNo: response.pageNo,
    loading: false,
  })),

  on(PracticeSkillActions.loadPracticeSkillsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  }))
);

/**
 * practiceSkillByGuidReducer
 */
export const practiceSkillByGuidReducer = createReducer(
  initialPracticeSkillByGuidState,

  on(
    PracticeSkillActions.loadPracticeSkillByGuid,
    (state): PracticeSkillByGuidState => ({
      ...state,
      loading: true,
      error: null,
    })
  ),

  on(
    PracticeSkillActions.loadPracticeSkillByGuidSuccess,
    (state, { skillResponse }): PracticeSkillByGuidState => ({
      ...state,
      skillResponse,
      loading: false,
    })
  ),

  on(
    PracticeSkillActions.loadPracticeSkillByGuidFailure,
    (state, { error }): PracticeSkillByGuidState => ({
      ...state,
      error,
      loading: false,
    })
  )
);

/**
 * practiceSkillModeReducer
 */
export const practiceSkillModeReducer = createReducer(
  initialPracticeSkillModeState,

  on(
    PracticeSkillActions.loadPracticeSkillsMode,
    (state): PracticeSkillModeState => ({
      ...state,
      loading: true,
      error: null,
    })
  ),

  on(
    PracticeSkillActions.loadPracticeSkillsModeSuccess,
    (state, { response }): PracticeSkillModeState => ({
      ...state,
      modeResponse: response,
      loading: false,
    })
  ),

  on(
    PracticeSkillActions.loadPracticeSkillsModeFailure,
    (state, { error }): PracticeSkillModeState => ({
      ...state,
      error,
      loading: false,
    })
  )
);

/**
 * practiceSkillBundlesReducer
 */
export const practiceSkillBundlesReducer = createReducer(
  initialPracticeSkillBundlesState,

  on(
    PracticeSkillActions.loadPracticeSkillBundles,
    (state, { pageNo, pageSize }) => ({
      ...state,
      loading: true,
      pageNo,
      pageSize,
      error: null,
    })
  ),

  on(
    PracticeSkillActions.loadPracticeSkillBundlesSuccess,
    (state, { response }) => ({
      ...state,
      bundles: response.data,
      totalRecords: response.totalRecords,
      pageSize: response.pageSize,
      pageNo: response.pageNo,
      loading: false,
    })
  ),

  on(
    PracticeSkillActions.loadPracticeSkillBundlesFailure,
    (state, { error }) => ({
      ...state,
      loading: false,
      error,
    })
  )
);
