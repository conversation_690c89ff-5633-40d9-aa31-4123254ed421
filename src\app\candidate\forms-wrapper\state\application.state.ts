import { Form } from '../forms-wrapper.types';
import { PersonFormLog } from 'src/app/core/common-component/progress-bar/progress-bar.types';

export interface ApplicationState {
  form: Form;
  userResponse: Array<any>,
  candidateId: number;
  latestPersonFormId: number;
  personEventId: number;
  personFormLogs:PersonFormLog[],
  renewelFee:any;
  registrySave:any;
}

export const intialApplicationState: ApplicationState = {
  form: null,
  userResponse: null,
  candidateId: null,
  latestPersonFormId: null,
  personEventId: null,
  personFormLogs:[],
  renewelFee:null,
  registrySave:null
}