import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { createEffect, Actions, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { switchMap, map, catchError, tap, mergeMap, concatMap } from 'rxjs/operators';
import { addRenewlTocart, deletedUserResponse, deleteUserResponse, downloadAccTypeForm, downloadedAccTypeForm, getFormJson, getPersonFormLogs, getRegistryResponse, getUserResponse, gotFormJson, gotPersonFormLogs, gotUserResponse, RenewelCartResponse, savedUserResponse, saveUserResponse, setEligilityRouteOnLoad, settingEligilityRouteOnLoad } from './application.actions';
import { environment } from 'src/environments/environment';
import { setErrorMessage } from '../../state/shared/shared.actions';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import lodash from 'lodash';
import { FormTypes, ReciprocityStateList, RenewelStateLists } from '../forms-wrapper.types';
import { StateLists } from 'src/app/core/examroom-formbuilder/form-builder.types';


@Injectable({
  providedIn: 'root',
})
export class ApplicationEffects {
  Coformoldversion:number|null
  Details=[]
  formId:number
  Grievamcestatus:string
  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private globalUserService: GlobalUserService,
    private router:Router,private snackbar: SnackbarService,
  ) { 
    this.globalUserService.userstatus.subscribe((data)=>{
      if(data !=null){
        this.Details.push(data)
        this.Coformoldversion = data.personFormVersion
             this.formId= data.formId
      }
    })

  }

  effectivelyGetForms$ = createEffect(() => this.actions$.pipe(
    ofType(getFormJson),
    mergeMap((action) => {
      return this.httpClient
        .get<any>(
      ((this.Coformoldversion == 0  || this.Coformoldversion == null) && (this.globalUserService.userDetails.getValue().stateId == StateLists.CO || this.globalUserService.userDetails.getValue().stateId == StateLists.VA ) && this.Details.length > 0 && action.formTypeID.includes(3)?
      (environment.baseUrl +
          `client/api/form/formbyformtypeidforversion?formTypeId=${action.formTypeID}&` + (action.eligibilityID ?`eligibilityId=${action.eligibilityID}&`:``)+`clientId=${this.globalUserService.clientId}&stateId=${action.stateID}`):
          environment.baseUrl +
          `candidate/api/form/formsbyformtypeid?formTypeId=${action.formTypeID}&` + (action.eligibilityID ?`eligibilityId=${action.eligibilityID}&`:``)+`clientId=${this.globalUserService.clientId}&stateId=${action.stateID}`)
          )
        .pipe(
          map(form =>
            gotFormJson({
              formID: [form.id],
              formTypeID: action.formTypeID,
              stateID: action.stateID,
              eligibilityID: action.eligibilityID,
              formJSON: lodash.cloneDeep(JSON.parse(form.formJson)),
              isSubmitAllowed:action.isSubmitAllowed,
              formCode:form.formCode,
              fees:form.fees,
            })),
        );
    }),
  ));
  effectivelySaveResponse$ = createEffect(() => this.actions$.pipe(
    ofType(saveUserResponse),
    concatMap((action) => {
      return this.httpClient
        .post<any>(
          environment.baseUrl +
          `candidate/api/form/savepersonform`, action.userResponse
        )
        .pipe(
          mergeMap(savedResponseId =>[
            savedUserResponse({
              personFormId: savedResponseId,
            }),
            getRegistryResponse({
              registryRespone:{savedResponseId:savedResponseId,isSubmit:action.userResponse}
            })
          ]),
          tap(() => {
            // the get person forms api is called first and the accomodation form save status is not shown 
            setTimeout(() => {
              if (action.userResponse.isSubmit || !action.userResponse.isSubmit ) {
                switch (action.formTypeID) {
                  case FormTypes.Application:
                  case FormTypes.Accomodation: this.router.navigate(['application']);
                    break;
                  case FormTypes.Grievance: this.globalUserService.Grievanceroute =="Evaluator"?null: this.router.navigate(['grievance-form']);
                    break;
                    case FormTypes.ExcusedAbsense: this.router.navigate(['absense-form']);
                    break;
                  case FormTypes.Demographic: this.router.navigate(['manage-profile']);
                    break;
                  case FormTypes.Certificate_Duplicate:
                    case FormTypes.RenewalSCMA:action.formTypeID ==FormTypes.RenewalSCMA && RenewelStateLists.includes(this.globalUserService.stateId)?null: this.router.navigate(['registry']);
                    break;
                    case FormTypes.Certificate_Renewal: action.formTypeID ==FormTypes.Certificate_Renewal && RenewelStateLists.includes(this.globalUserService.stateId)?null: this.router.navigate(['registry']);
                       break;
                      case FormTypes.ReciporatingSCMA: action.formTypeID  == FormTypes.ReciporatingSCMA && RenewelStateLists.includes(this.globalUserService.stateId)?null: this.router.navigate(['registry']);
                      break;
                    case FormTypes.Certificate_Reciprocity: action.formTypeID  == FormTypes.Certificate_Reciprocity && this.globalUserService.stateId in ReciprocityStateList?null: this.router.navigate(['registry']);
                      break;
                  default: this.router.navigate(['dashboard']);
                }
              }
            }, 800);
            if (action.formTypeID != (FormTypes.Accomodation))  {
              this.snackbar.callSnackbaronSuccess('Successfully Response Saved.');
            }
            else{
              this.snackbar.callSnackbaronSuccess('Successfully  Response Saved.');
            }
          }),
          catchError((err) => {
            this.snackbar.callSnackbaronError(err.error)
            return of(setErrorMessage({ message: err }))
          })
        );
    }),
  ));

  effectivelyGetPersonFormLogs$ = createEffect(() => this.actions$.pipe(
    ofType(getPersonFormLogs),
    switchMap((action) => {
      return this.httpClient
        .get<any>(environment.baseUrl + `candidate/api/form/personformlogs?` + (action.code && action.code != '0' ? `code=${action.code}` : `personFormId=${action.personFormId}`))
        .pipe(
          map(personFormLogs =>
            gotPersonFormLogs({
              personFormLogs: personFormLogs
            })),
        );
    }),
  ));

  effectivelyGetUserResponse$ = createEffect(() => this.actions$.pipe(
    ofType(getUserResponse),
    switchMap((action) => {
      return this.httpClient
        .get<any>(environment.baseUrl + `candidate/api/form/personform/list?`+(action.code && action.code !='0' ? `code=${action.code}` : `personFormId=${action.personFormId}`))
        .pipe(
          map(userResponse =>
            gotUserResponse({ userResponse: userResponse } )
          ),
        );
    }),
  ))

  effectivelyDownloadFile$ = createEffect(() => this.actions$.pipe(
    ofType(downloadAccTypeForm),
    switchMap((action) => {
      return this.httpClient
        .get<any>(environment.baseUrl + `formmsvc/api/File/url?systemFileName=${action.sysFileName.split('|')[1]}`)
        .pipe(
          map(response =>
            downloadedAccTypeForm({url:response.url})),
          tap((response) => {
            window.open(response.url);
          }),
        );
    }),
  ))

  effectivelySetEligiblityRoute$ = createEffect(() => this.actions$.pipe(
    ofType(settingEligilityRouteOnLoad),
    switchMap((action) => {
      return this.httpClient
        .post<any>(environment.baseUrl + "candidate/api/EligibilityRoute/person-er/add", action)
        .pipe(
          map(response =>
            setEligilityRouteOnLoad())
        )
    }),
  ))

  effectivelyDeleteUserResponse$ = createEffect(() => this.actions$.pipe(
    ofType(deleteUserResponse),
    concatMap((action) => {
      return this.httpClient
        .delete<any>(environment.baseUrl + `candidate/api/form/personform?candidateId=${action.candidateId}&personFormId=${action.personFormId}`)
        .pipe(
          map(Response =>
            deletedUserResponse()
          ),
          tap(() => {
            if (action.formTypeID != FormTypes.Accomodation)
              this.snackbar.callSnackbaronSuccess('Successfully deleted');
            if (action.route) {
                switch (action.formTypeID) {
                  case FormTypes.Application:
                  case FormTypes.Accomodation: this.router.navigate(['application']);
                    break;
                  case FormTypes.Grievance: this.router.navigate(['grievance-form']);
                    break;
                  case FormTypes.Demographic: this.router.navigate(['manage-profile']);
                    break;
                  case FormTypes.Certificate_Duplicate:
                  case FormTypes.Certificate_Renewal:
                  case FormTypes.ReciporatingSCMA:
                    case FormTypes.RenewalSCMA:
                  case FormTypes.Certificate_Reciprocity: this.router.navigate(['registry']);
                    break;
                  default: this.router.navigate(['dashboard']);
                }
            }
          }),
        );
    }),
  ))

  getRenewelCartResponse$ = createEffect(() => this.actions$.pipe(
    ofType(addRenewlTocart),
    switchMap((action) => {
      return this.httpClient
        .post<any>(`${environment.baseUrl}candidate/api/Exam/cart/vouchers`, action.renewelCartDetails)
        .pipe(
          map(response =>
            RenewelCartResponse({renewelAddedDetails:response})),
        );
    }),
  ))
}
