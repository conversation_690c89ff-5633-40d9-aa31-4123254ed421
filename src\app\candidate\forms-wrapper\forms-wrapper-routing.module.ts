import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FormsWrapperComponent } from './forms-wrapper.component';

const routes: Routes = [
  { path: '', component: FormsWrapperComponent },
  { path: ':formTypeId/:candidateId/:eligibilityRouteId/:stateId/:personEventId/:mode', component: FormsWrapperComponent },
  { path: ':formTypeId/:candidateId/:eligibilityRouteId/:stateId/:personEventId/:certNumber', component: FormsWrapperComponent },
  { path: ':formTypeId/:candidateId/:eligibilityRouteId/:stateId/:personEventId/:personFormId/:code', component: FormsWrapperComponent },
  { path: ':formTypeId/:candidateId/:eligibilityRouteId/:stateId/:personEventId/:personFormId/:/:certStatusId', component: FormsWrapperComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FormsWrapperRoutingModule { }
