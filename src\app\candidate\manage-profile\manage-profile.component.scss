.text{
  
        color: var(--text-color1);
}

.img, video {
  max-width: 4% !important;
  height: auto;
}
.bg-color {
  background-color: var(--background-base2); 
}
.text1{
    color: var(--text-profile);
}
.change{
 overflow-y: hidden;
}

.add-new {
    background-color: var(--button-background) !important;
    color: var(--button-color) !important;
    padding: 0rem 2rem;
}
.report-button{
  width: 90px;
    height: 25px;

}
  .mat-stroked-button.mat-primary{
    background-color:  var(--background-base3) !important;

   padding: 0rem 0rem 1rem;
    }
  .change{
 
    color: var(--text-color2);
    font-size: 10px;
    cursor: pointer !important; 
  }
  
   .photo{
    // font-size: 20rem!important;
    max-height: 67%!important;
    max-width:73% !important ;
    width:100px;
    height: 80px;
   }

   #changePhoto {
     display: none;
     cursor: pointer !important; 
   }
  
    .mat-card {
      max-width: 400px;
    }
    
  
  ::ng-deep{
  .img, video {
    max-width: 4% !important;
    height: auto;
  }
}
.iconSize {
  width: 12px;
  height: 12px;
}
.trunc{
  word-break: break-all;
  white-space: nowrap; 
  max-width: 200px; 
  overflow: hidden;
  text-overflow: ellipsis;
}
.titleFont {
  font-size: 1.1em;
  // font-weight: bolder;
  font-family: "Roboto", sans-serif;
}

.status {
  color: var(--text-profile);
}
.status1 {
  color: var(--text-color1);
}