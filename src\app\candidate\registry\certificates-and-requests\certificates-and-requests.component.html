<div class="px-gutter pt-2" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="2px" exaiContainer>
    <div class="w-full flex justify-start" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
        <div class="" fxLayout="column">
            <h5><strong>Registration</strong></h5>
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
    </div>

    <div class="flex justify-end" fxLayoutGap="5px" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
        <div class="flex justify-end" fxLayout="auto" *ngIf="userHasCertificate">
            <div class="font-bold t-xs flex items-center">
                <mat-icon>filter_list</mat-icon>
            </div>
            <mat-form-field appearance="outline" class="certificate-search" fxFlex="250px" fxFlex.lt-md="180px" fxFlex.lt-sm="100px">
              
                <input class="text-xs" (ngModelChange)="searchFilter()" [(ngModel)]="textForSearch" matInput placeholder="" autocomplete="off" />
                <mat-icon mat-icon-button class="time" matSuffix> search </mat-icon>
            </mat-form-field>
        </div>
        <div class="flex items-center mr-5" fxLayout="auto" >
            <button class="add btn-4 font-bold t-xs" (click)="openStatesDialog('')" mat-button *ngIf="Reciporating">
                Nurse Aide Reciprocity Request
            </button>
            <button class="add btn-4 font-bold t-xs ml-2 buttonMA" (click)="openStatesDialog('MACE')" mat-button *ngIf="ReciporatingMACE" >
               Medication Assistant Reciprocity Request 
            </button>
        </div>

    </div>
</div>

<div class="px-gutter pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="py-2" gdGap="16px" exaiContainer>
        <div class="content touch-auto overflow-auto -mt-5">
            <mat-tab-group mat-align-tabs="start" dynamicHeight class="touch-auto overflow-auto custom-tab" animationDuration="0ms" (selectedTabChange)="tabClick($event)">
                <ng-container>
                <mat-tab label="Registration" class="custom-tab">
                    <div fxLayout="row wrap" fxLayoutGap="16px grid" cdkDropListGroup *ngIf="filteredCertificates?.length > 0  && userHasCertificate">
                        <div class="shadow-none justify-start mt-4" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%" *ngFor="let item of filteredCertificates; let i = index">
                            <exai-cr-cards class="h-full" [data]="item" [requestData]="listRequests" ></exai-cr-cards>
                        </div>
                    </div>

                    <div class="px-gutter pt-2" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
                        <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
                            <div class="card shadow-none cardBorder h-full" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%" *ngIf="!userHasCertificate" fxFlex="auto">
                                <div class="flex justify-center">
                                    <img class="mt-8 justify-center size" src="assets/img/register-exam1.svg" /><br />
                                </div>
                                <div class="textColor pb-6">
                                    <section class="mr-5 ml-5">
                                        <div class="backGround_color">
                                            <span class="welc-note flex justify-center text-center text-xs mx-4 mt-4">
                                                <span class="p-2">
                                                    Please complete your exam from Exam Schedule section to recieve a Certificate.
                                                </span>

                                            </span>
                                            <br />
                                        </div>
                                    </section>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-tab>

                <mat-tab label="Requests" > 
                    <div fxLayout="row wrap" fxLayoutGap="16px grid" cdkDropListGroup *ngIf=" listRequests && listRequests.length > 0">
                        <div class="shadow-none justify-start mt-4" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%" *ngFor="let item of listRequests; let i = index">
                            <exai-cr-cards class="h-full" [data]="item" [requestData]="listRequests" ></exai-cr-cards>
                        </div>
                    </div>
                </mat-tab>
            </ng-container>
            </mat-tab-group>
        </div>
    </div>

</div>