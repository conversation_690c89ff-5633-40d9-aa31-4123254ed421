import { Component, OnInit } from '@angular/core';
import { SystemCheckService } from './system-check.service';

@Component({
  selector: 'exai-system-check',
  templateUrl: './system-check.component.html',
  styleUrls: ['./system-check.component.scss']
})
export class SystemCheckComponent implements OnInit {
  step: number = 1;

  constructor(private _service: SystemCheckService) {
    this._service.errorStatus.subscribe((data: number) => {
      if (!data) return;
      this.step = data;
    })
  }

  ngOnInit(): void {
  }

  deviceSelection(step: number): void {
    this.step = step;
  }

}
