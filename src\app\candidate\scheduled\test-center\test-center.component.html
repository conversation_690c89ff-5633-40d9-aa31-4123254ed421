<div  [ngClass]="{hide1:untilPaymentProcess(0)}" class="px-gutter" exaiContainer>
  <div>
  <mat-spinner class="spinner"></mat-spinner>
  <div class="loading">
      <div class="flex justify-center test-base fontcustom">
          We're processing...
      </div>
      <div class="flex justify-center text-xs font-bold">
          This process may take a few seconds, so please be patient.
      </div>
  </div>
  </div>
  </div>


<div [ngClass]="{hide:untilPaymentProcess(1) }" >
  <div class="-mt-5 t-x flex w-full " exaiContainer>
    <div class="shadow-none justify-start w-full" gdColumn="1 / 3" gdColumn.lt-md="1" gdColumn.lt-sm="1">
      <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" class="px-4 pt-1 exam" fxLayoutGap.lt-sm="0">
  
  
        <form [formGroup]="Validators" #form="ngForm" class="w-full">
          <div class="shadow-none justify-start w-full" gdColumn="1 / 3" gdColumn.lt-md="1" gdColumn.lt-sm="1">
            <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px" class="pt-2 exam flex-wrap"
              fxLayoutGap.lt-sm="0" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr" gdColumns-lt.sm="1fr">
              <!-- TIME ZONE PICKER -->
              <!-- <mat-form-field class="mat-field-1-4" appearance="outline">
                <mat-label class="text-xs  fontColor2">Select Time Zone</mat-label>
                <mat-select class="text-xs" formControlName="timezone">
                  <mat-option class="text-xs" *ngFor="let time of timezones" [value]="time">
                    {{ time.timeZoneStandardName }}
                  </mat-option>
                </mat-select>
              </mat-form-field> -->
              <!-- END TIME ZONE PICKER -->
  
              <!-- DATE PICKER -->
  
              <div class="exam">
                <h2 class="text-xs font-bold ">
                  2. Select Your Exam Date Range
                </h2>
              </div>
              <div gdColumn="1 / 2" gdColumn.lt-md="1 /2" gdColumn.lt-sm="1" fxLayout="row" fxLayout.lt-sm="column"
                fxLayoutGap="12px">
                <mat-form-field class="mt-2" appearance="outline">
                  <mat-label class="text-xs fontColor2">Select Your Exam Date Range</mat-label>
                  <mat-date-range-input [formGroup]="range" [rangePicker]="picker" [min]="minDate" [max]="maxDate">
                    <input class="text-xs" matStartDate formControlName="start" autocomplete="off" required
                      placeholder="Start date">
                    <input class="text-xs" matEndDate formControlName="end" autocomplete="off" required
                      placeholder="End date">
                  </mat-date-range-input>
                  <mat-datepicker-toggle matSuffix [for]="picker"
                    (click)="changeCalendar($event)"></mat-datepicker-toggle>
                  <mat-date-range-picker #picker (opened)="onPickerOpened()" 
                  (closed)="onPickerClosed()"></mat-date-range-picker>
                </mat-form-field>
              </div>
  
              <!-- LOCATION PICKER -->
  
              <!-- END LOCATION PICKER -->
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <!-- <div class="exam" >
    <h2 class="px-5 text-xs font-bold fontColor1">
        Select Your Test Center Criteria to Search
    </h2>
  </div> -->
  <div class="exam">
    <h2 class="text-xs px-4 font-bold ">
      3. Search by Mileage Radius or Test Center
    </h2>
  </div>
  <mat-radio-group [formControl]="radioselect">
    <ng-container *ngFor="let examType of examTypeModels; let i = index">
      <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
        (change)="getSelectedRoute(examType.id)">{{ examType.name }}
      </mat-radio-button>
    </ng-container>
  </mat-radio-group>
  
  
  <div class=" pt-2 px-3 exam flex-wrap" gdColumns="1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr"
    gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer [formGroup]="testCenterLocation">
    <div class="flex " gdColumn="2 / 5" gdColumn.lt-md="2 / 5" gdColumn.lt-sm="1" fxLayout="column"
      fxLayout.lt-sm="column" *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
      <div class="text-center mt-2 tx font-bold">
        Search for Test Center Near You by Mileage Radius
      </div>
      <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
        <mat-form-field fxFlex='50%' class="exai-flex-form-field mt-2" appearance="outline">
          <mat-label class="text-xs fontColor2">Your Address</mat-label>
          <input class="form-control text-xs" formControlName="testAddress" placeholder="Your Address:"
            autocomplete="off" matInput >
        </mat-form-field>
        <mat-form-field fxFlex='50%' class="exai-flex-form-field mt-2" appearance="outline">
          <mat-label class="text-xs fontColor2">Mileage Radius</mat-label>
          <input class="form-control text-xs" formControlName="testRadius" placeholder="Radius:" type="number" min="0"
            autocomplete="off" matInput>
        </mat-form-field>
      </div>
      <div class="flex justify-center">
        <button [disabled]="testCenterLocation.status === 'INVALID' || isSearching || !form.form.valid"
          class="btn-1 mb-2 text-xs" mat-raised-button type="button" (click)="getTimeSlots()">
          <ng-container *ngIf="isSearching; else searchText">
            <div class="pulse-dots">
              <div class="dot dot1"></div>
              <div class="dot dot2"></div>
              <div class="dot dot3"></div>
              <div class="dot dot4"></div>
            </div>
          </ng-container>
          <ng-template #searchText>Search by Radius</ng-template>
        </button>
  
      </div>
  
  
    </div>
  
    <!-- <div class="flex text-sm font-bold justify-center items-center" gdColumn="3 / 4" gdColumn.lt-md="3 / 4"
      gdColumn.lt-sm="1" fxLayout="row" fxLayout.lt-sm="column">
      OR
    </div> -->
    <div class="flex " gdColumn="2 / 5" gdColumn.lt-md="2 / 5" gdColumn.lt-sm="1" fxLayout="column"
      fxLayout.lt-sm="column" *ngIf="radioselect.value == 'Search a Test Center by Code'">
      <div class="text-center mt-2 tx font-bold">
        Search for a Test Center Near You by Code
      </div>
      <mat-label class="INFLABEL">Test Center Code</mat-label>
      <mat-form-field class="mt-2" appearance="outline">
  
  
        <span class="INFTEXT mb-1  " id="basic-addon3">INF</span>
        <input class="form-control INF " formControlName="testId" matInput autocomplete="off" maxlength="5" id='testId'
          (keypress)="onlyNumberKey($event)" (paste)="paste($event)">
  
      </mat-form-field>
  
      <mat-error *ngFor="let validation of validation_messages.testId">
        <mat-error class="error-message  -mt-1"
          *ngIf="testCenterLocation.get('testId').hasError(validation.type) && (testCenterLocation.get('testId').dirty || testCenterLocation.get('testId').touched)">
          {{validation.message}}
        </mat-error>
  
      </mat-error>
  
  
  
  
       
          <!-- <span class="INFTEXT mb-1  " id="basic-addon3">INF</span>
          <input class="form-control INF " formControlName="testId" matInput 
          autocomplete="off"  maxlength="5"   id='testId' (paste)="paste($event)" (keypress)="onlyNumberKey($event)"> -->
         
        <!-- </mat-form-field> -->
  
  
  
      <div class="flex justify-center">
        <button [disabled]="isSearching" class="btn-1 mb-2 text-xs" mat-raised-button type="button"
          (click)="getTimeSlots()">
          <ng-container *ngIf="isSearching; else searchText">
            <div class="pulse-dots">
              <div class="dot dot1"></div>
              <div class="dot dot2"></div>
              <div class="dot dot3"></div>
              <div class="dot dot4"></div>
            </div>
          </ng-container>
          <ng-template #searchText>Search by Code</ng-template>
        </button>
      </div>
    </div>
  </div>
  <hr class="mr-5 ml-5 line" />
  <mat-radio-group *ngIf="slotsDates.length > 0 && TSMSLOTS ==true" [formControl]="radioSelectTypeSlotViewselect">
    <ng-container *ngFor="let examType of SelectedTypeSlotView; let i = index">
      <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
        (change)="getSelectedTypeSlotViewRoute(examType.id)">{{ examType.name }}
      </mat-radio-button>
    </ng-container>
  </mat-radio-group>
  <hr class="mr-5 ml-5 line" />
   <ng-container  *ngIf="slotsDates.length > 0 && TSMSLOTS ==true && radioSelectTypeSlotViewselect.value == 'Map View'">
    <div class="map" >
      <agm-map [latitude]=latitude
      [longitude]=longitude [zoom]="5"
       >
  
  
  
        <!-- [latitude]="latitude"
      [longitude]="longitude"
      [zoom]="zoom" -->
        <ng-container *ngFor="let item of slotsDates ">
          <div *ngFor="let items of item.testCenters">
            {{items.longitude}}
            <agm-marker [latitude]=items.latitude (mouseOver)="onMouseOver(infoWindow, $event)" [iconUrl]=items.iconUrl
              (mouseOut)="onMouseOut(infoWindow, $event)" (markerClick)="onMarkerClickEvent($event,items)"
              [longitude]=items.longitude>
              <agm-info-window [disableAutoPan]="false" #infoWindow>
                <div>
                  <h4>{{items.testCenter}}</h4>
                  <!-- <h4 *ngIf="marker.staff_Id">Staff Id : {{marker.staff_Id}}</h4>
                <h4 *ngIf="marker.distanceToEvaluator">Distance : {{marker.distanceToEvaluator}}</h4> -->
                  <!-- <h4>{{marker.latitudeLongitude[0].lat}}</h4>
                <h5>{{marker.latitudeLongitude[0].lng}}</h5> -->
                </div>
                <!-- <div mat-dialog-title fxLayout="row" fxLayoutAlign="space-between center">
                <div>Question</div>
                <button type="button" mat-icon-button (click)="close('No answer')" tabindex="-1">
                    <mat-icon [icIcon]="icClose"></mat-icon>
                </button>
            </div> -->
  
              </agm-info-window>
  
              <!-- [latitude]="latitude"
          [longitude]="longitude" -->
            </agm-marker>
          </div>
  
        </ng-container>
  
      </agm-map>
    </div>
  </ng-container>
  
  
  
  <div class="m-4  flex  overflow-auto  w-full " *ngIf="isSelectDate">
  
    <ng-container *ngIf="TSMSLOTS !=true && radioSelectTypeSlotViewselect.value == 'List View'">
      <div *ngFor="let aSlotDate of slotsDates">
        <button id="{{'button'}}" class=" px-4 mr-3 mb-3 pt-3 pb-3 my-2 state slots2 buttom6"
          [ngClass]="{ active: step == aSlotDate.strSlotDate }"
          (click)="setTestCentersForSelectedDate(aSlotDate.slotDate);selectedDateTime(aSlotDate);selectedDate(aSlotDate)"
          mat-stroked-button color="light">
          {{ aSlotDate.strSlotDate}}<br />
        </button>
      </div>
  
    </ng-container>
    <ng-container *ngIf="TSMSLOTS ==true && radioSelectTypeSlotViewselect.value == 'Map View' && markerclickLan != null ">
      <div *ngFor="let aSlotDate of FilterTestcenterSlot">
        <button id="{{'button'}}" class=" px-4 mr-3 mb-3 pt-3 pb-3 my-2 state slots2 buttom6"
          [ngClass]="{ active: (step | date: 'MM/dd/yyyy':'+0000')==(aSlotDate.day| date: 'MM/dd/yyyy':'+0000') }"
          (click)="setTestCentersForSelectedDate(aSlotDate.day);selectedDateTime(aSlotDate);selectedDate(aSlotDate)"
          mat-stroked-button color="light">
          {{ aSlotDate.day | date: "MM/dd/yyyy":'+0000'}}<br />
        </button>
      </div>
  
    </ng-container>
    <ng-container *ngIf="TSMSLOTS ==true && radioSelectTypeSlotViewselect.value == 'List View'">
      <div *ngFor="let aSlotDate of slotsDates">
        <button *ngIf="aSlotDate.day !=''" id="{{'button'}}" class=" px-4 mr-3 mb-3 pt-3 pb-3 my-2 state slots2 buttom6"
          [ngClass]="{ active: (step | date: 'MM/dd/yyyy':'+0000')==(aSlotDate.day| date: 'MM/dd/yyyy':'+0000') }"
          (click)="setTestCentersForSelectedDate(aSlotDate.day);selectedDateTime(aSlotDate);selectedDate(aSlotDate)"
          mat-stroked-button color="light">
          {{ aSlotDate.day | date: "MM/dd/yyyy":'+0000'}}<br />
        </button>
      </div>
  
    </ng-container>
    <!-- KILOMETERS OR MILES PICKER -->
    <!-- <div class="ml-auto mr-4" fxLayoutAlign="end center">
      <p class="pt-2 -mt-3 text-xs font-bold fontColor1">Show distance in</p>
      <span>
        <mat-radio-group [formControl]="paymentModeStatus">
          <mat-radio-button class="mt-2 px-3 t-xs mb-2 text-xs font-bold" [checked]="n.checked" [disabled]="n.disabled"
            *ngFor="let n of showDistance; let i = index" changes [value]="n.checked">{{ n.name }}
          </mat-radio-button>
        </mat-radio-group>
      </span>
    </div> -->
    <!-- END KILOMETERS OR MILES PICKER -->
  </div>
  <hr class="mr-5 ml-5 line" />
  <!--  TEST CENTERS -->
  <div class="mat-testCenter1 overflow-auto mb-2" *ngIf="isSelectDate">
    <div class="px-4 ">
      <div class="accord1" *ngFor="let item of selectedDateTestCenters,let i = index">
        <mat-expansion-panel
          *ngIf="TSMSLOTS ==true && markerclickLan == null &&radioSelectTypeSlotViewselect.value == 'List View' "
          class="mt-4" [expanded]="expanded === 0" (opened)="setStep(0);" hideToggle>
          <mat-expansion-panel-header class="header">
            <mat-panel-title class="titled t-xs minimise" matTooltip={{item.testSiteName}}>
              {{item.testCenter}}
            </mat-panel-title>
            <mat-panel-title class=" ml-3 titled  minimise" matTooltip={{item.testSiteAddress}}>
              {{item.address}}
            </mat-panel-title>
            <mat-panel-title *ngIf="item.getMap !=''" class="ml-3 titled" matTooltip={{item.getMap}}>
              <a class="link" (click)="getDirection(item.getMap)">GetDirections</a>
            </mat-panel-title>
            <mat-panel-title class=" ml-3 titled" *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
             Distance {{item.distance | number: '1.0-0'}} miles
           </mat-panel-title>
          </mat-expansion-panel-header>
          <hr class="-mr-6 -ml-6" />
          <div class=" flex overflow-auto ml-3 mt-3">
            <ng-container>
              <div *ngFor="let time of item.siteSlot">
                <button
                  *ngIf="(time.examdate | date: 'MM/dd/yyyy':'+0000')==(selectedTestCenterSoltDate| date: 'MM/dd/yyyy':'+0000')"
                  class=" mr-3 mb-3 state slots2 buttom6" mat-stroked-button color="light" (click)=" event(item,time)"
                  [ngClass]="{active: isActive(time)}">
                  {{time.slotime }}
                </button>
                <div [ngClass]="(time.examCode =='VS-PR' || time.examCode =='CBT-WR')?'highlight':'highlight1'" class="ml-3 -mt-2 t-xs ">{{time.examCode
                  =='VS-PR'?' In Person Virtual':time.examCode
                  =='CBT-WR'?' In Person CBT ':'In Person'}}</div>
              </div>
            </ng-container>
          </div>
          <div *ngIf="checkbox" class="p-3">
            <mat-checkbox (change)='showOptions($event)'></mat-checkbox><span class="ml-1">{{AcceptCondidtion}}</span>
          </div>
        </mat-expansion-panel>
  
        <mat-expansion-panel
          *ngIf="TSMSLOTS ==true && markerclickLan != null && radioSelectTypeSlotViewselect.value != 'List View'"
          class="mt-4" [expanded]="expanded === 0" (opened)="setStep(0);" hideToggle>
          <mat-expansion-panel-header class="header">
            <mat-panel-title class="titled t-xs minimise" matTooltip={{item.testSiteName}}>
              {{item.testCenter}}
            </mat-panel-title>
            <mat-panel-title class=" ml-3 titled  minimise" matTooltip={{item.testSiteAddress}}>
              {{item.address}}
            </mat-panel-title>
            <mat-panel-title *ngIf="item.getMap !=''" class="ml-3 titled" matTooltip={{item.getMap}}>
              <a class="link" (click)="getDirection(item.getMap)">GetDirections</a>
            </mat-panel-title>
            <!-- <mat-panel-title *ngIf="item.directions ==''" class="ml-3 titled" matTooltip="No Directions Found">
              <a class="link">GetDirections</a>
            </mat-panel-title> -->
            <mat-panel-title class=" ml-3 titled" *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
              Distance {{item.distance | number: '1.0-0'}} miles
           </mat-panel-title>
          </mat-expansion-panel-header>
          <hr class="-mr-6 -ml-6" />
          <div class=" flex overflow-auto  -ml-4 mt-3">
            <ng-container>
              <div *ngFor="let time of item.siteSlot">
                <button
                  *ngIf="(time.examdate | date: 'MM/dd/yyyy':'+0000')==(selectedTestCenterSoltDate| date: 'MM/dd/yyyy':'+0000')"
                  class=" mr-3 mb-3 state slots2 buttom6" mat-stroked-button color="light" (click)=" event(item,time)"
                  [ngClass]="{active: isActive(time)}">
                  {{time.examdate | date:'shortTime':'+0000' }}
                </button>
              </div>
            </ng-container>
          </div>
        </mat-expansion-panel>
  
        <mat-expansion-panel *ngIf="TSMSLOTS !=true && radioSelectTypeSlotViewselect.value == 'List View'" class="mt-4"
          [expanded]="expanded === 0" (opened)="setStep(0);" hideToggle>
          <mat-expansion-panel-header class="header">
            <mat-panel-title class="titled t-xs minimise" matTooltip={{item.testSiteName}}>
              {{item.testSiteName}}
            </mat-panel-title>
            <mat-panel-title class=" ml-3 titled  minimise" matTooltip={{item.testSiteAddress}}>
              {{item.testSiteAddress}}
            </mat-panel-title>
            <mat-panel-title *ngIf="item.directions !=''" class="ml-3 titled" matTooltip={{item.directions}}>
              <a class="link">GetDirections</a>
            </mat-panel-title>
            <mat-panel-title *ngIf="item.directions ==''" class="ml-3 titled" matTooltip="No Directions Found">
              <a class="link">GetDirections</a>
            </mat-panel-title>
            <mat-panel-title class=" ml-3 titled" *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
              Distance {{item.distanceTo | number: '1.0-0'}} miles
           </mat-panel-title>
          </mat-expansion-panel-header>
          <hr class="-mr-6 -ml-6" />
          <div class=" flex overflow-auto  -ml-4 mt-3">
            <ng-container>
              <div *ngFor="let time of item.slots">
                <button *ngIf="time.strSlotDate==selectedTestCenterSoltDate" class=" mr-3 mb-3 state slots2 buttom6"
                  mat-stroked-button color="light" (click)=" event(item,time)" [ngClass]="{active: isActive(time)}">
                  {{ time.strSlotTime }}
                </button>
              </div>
            </ng-container>
          </div>
        </mat-expansion-panel>
  
      </div>
    </div>
  </div>
  <!-- END TEST CENTERS -->
  
  <!-- PAYMENT BOTTOM PART -->
  <div fxLayout="row" fxLayoutAlign="end center" *ngIf="displayPaymentMethods" class="flex justify-end" fxLayoutGap="8px">
    <button mat-raised-button type="button" *ngIf="Addcart" class="button1 pt-1 mr-4" (click)="addToCart()"
      [disabled]="!displayPaymentMethods">
      Add Cart
    </button>
    <!-- <button mat-stroked-button class="button12 pt-1 mr-4 payment " *ngIf="payment" type="button" (click)="getPayment()"
      [disabled]="!displayPaymentMethods">
      Pay Now
    </button> -->
    <div >
      <button mat-raised-button  class="btn-1 pt-1 mr-4 "
      *ngIf="((rescheduled && scheduleagainEnable==this.global.ScheduledStatusId) || (rescheduled && scheduleagainEnable==this.global.Exam_Approved)) && rescheduleloading== false"
      type="button" (click)="reschedule()" [disabled]="!displayPaymentMethods || rescheduleloading">
      Reschedule
    </button>
  </div>
    
    <div >
      <button mat-raised-button  class="btn-1 pt-1 mr-4"
      *ngIf="(allowRetryschedule.includes(scheduleagainEnable)) && rescheduleloading == false "
      type="button" (click)="scheduleAagin()" [disabled]="rescheduleloading">
      Schedule
    </button>
  </div>
  </div>
  
  <!-- END PAYMENT BOTTOM PART -->
</div>
