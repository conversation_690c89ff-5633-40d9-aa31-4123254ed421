import { NgModule } from '@angular/core';
import { ExtraOptions, RouterModule, Routes } from '@angular/router';
import { AccessDeniedComponent } from './candidate/common/access-denied/access-denied.component';
import { NotFoundComponent } from './candidate/common/not-found/not-found.component';
const routes: Routes = [
  { path: '', loadChildren: () => import('./candidate/candidate.module').then(m => m.CandidateModule) },
  { path: 'denied', component: AccessDeniedComponent },
  { path: "**", component: NotFoundComponent },
];

const config: ExtraOptions = {
  useHash: false
}

@NgModule({
  imports: [RouterModule.forRoot(routes, config)],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
