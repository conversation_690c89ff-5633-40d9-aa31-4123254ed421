<div class="pb-4">
    <div *ngIf="header" class="pb-4 pt-4 text-xs font-semibold">{{header}}</div>
    <ng-container *ngFor="let step of start,let i=index,let last =last">
        <progress-step [performlog]=step>
        </progress-step>
    </ng-container>

    <ng-container class="showmore cursor-pointer" 
        *ngIf="start.length || middle.length || end.length">
        <div class="flex justify-start item-center">
            <mat-icon *ngIf="showmore">more_vert</mat-icon>
            <span class="pl-4 mat-body-1 text-xs cursor-pointer">
                <ul  *ngIf="showmore" (click)="Show()" (click)="toggle()">{{this.show_hide}}</ul>
            </span>
        </div>
    </ng-container>
    <div class="open-close-container overflow-hidden" [@openClose]="isOpen ? true : false">
        <ng-container *ngFor="let step of middle,let i=index,let last =last">
            <progress-step [performlog]="step">
            </progress-step>
        </ng-container>
    </div>

    <ng-container *ngFor="let step of end,let i=index,let last =last">
        <progress-step  [isLast]="last" [performlog]="step">
        </progress-step>
    </ng-container>
</div>