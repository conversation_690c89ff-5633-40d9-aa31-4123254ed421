.title{
    background-color: var(--background-base2);
    @apply p-4;
}
.button-help{
    border:1px solid orange;
    border-radius:5px;
    color:orange;
}
.label-tckt{
    color: var(--text-toggle);
}

.dashboard {
    overflow: auto;
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 5.25rem);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 5.25rem);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 5.25rem);
    }
}