.pulse-dots {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 30px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
  animation: pulse 1.2s infinite ease-in-out;
}

.dot1 {
  animation-delay: -0.16s;
}

.dot2 {
  animation-delay: -0.32s;
}

.dot3 {
  animation-delay: -0.48s;
}

.dot4 {
  animation-delay: -0.64s;
}

@keyframes pulse {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}



.slot {
  display: contents;
  height: 50px;
}

::ng-deep .mat-button-focus-overlay {
  background: none !important;
}

.content>button {
  margin-bottom: 12px;
}

.mat-field-1-4 {
  @apply flex-grow;

  @screen xs {
    @apply max-w-full w-full;
  }

  @screen md {
    // @apply max-w-1/4 w-auto;
    @apply w-auto;
    // max-width: 30%;
  }
}

.mat-field-1-2 {
  @apply flex-grow;

  @screen xs {
    @apply max-w-full w-full;
  }

  @screen md {
    // @apply max-w-1/2 w-auto;
    @apply w-auto;
    // max-width: 33%;
  }
}

.mat-field-1-3 {
  @apply w-full;
}

.state {
  color: #000000;
  border: var(--border);
}

.avaiable {
  color: #000000;
}

.time {
  color: var(--text-input);
  border: none !important;
}

.header1 {
  margin-top: -2.4rem;
}

.line {
  color: #7d7d7d;
}

.img {
  width: 5px;
}

.title1 {
  font-size: 10px;
  //color: var(--button-background) !important;
}

.button12 {
  line-height: 26px !important;
  min-width: unset !important;
  font-size: 12px;
  background-color: var(--button-background) !important;
  color: #ffff;
  border: 1px solid var(--button-background) !important;
}

.button1 {
  line-height: 26px !important;
  min-width: unset !important;
  font-size: 12px;
  color: var(--button-background) !important;
  border: 1px solid var(--button-background) !important;
}

.button {
  line-height: 20px !important;
  width: 70px !important;
  min-width: unset !important;
  font-size: 12px;
  height: 30px;
}

agm-map {
  height: 300px;
}

.rdn {
  height: 10%;
  width: 10%;
}

.slots3 {
  font-size: 8px;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  opacity: 0 !important;
}

.fontColor1 {
  color: #27262c;
  font-weight: 500;
}

.icons {
  font-size: 14px;
  margin-top: 10px;
}

.header {
  height: 36px;
  background-color: #4444 !important;
  // position: sticky !important;

  .titled {
    color: #11263c;
    font-size: 10px;
    // position: sticky !important;
  }
}

::ng-deep .mat-select-panel .mat-option-text {
  font-size: 10px;
}

::ng-deep .mat-datepicker-content .mat-calendar {
  width: 200px !important;
  height: 250px !important;
  font-size: 10px !important;
}

::ng-deep .mat-calendar-body-cell-content.mat-focus-indicator {
  font-size: 8px !important;
}

::ng-deep .mat-calendar-header {
  padding: 0px 0px 0px 0px !important;
}

::ng-deep .mat-calendar-controls {
  margin: 0% calc(33% / 7 - 16px) !important;
}

.fontColor2 {
  color: #a7a8ac;
  font-size: 10px !important;
}

.fontColor {
  color: #a7a8ac;
  // font-size: 16px !important;
}

.slots2 {
  font-size: 10px;
  line-height: 20px;
  // color: #7d7d7d;
}

.link {
  color: #0076C1 !important;
  text-decoration: underline;
}

.highlight1 {
  color: #ee9400 !important;
}

.highlight {
  color: #0076C1 !important;
}

.minimise {
  width: 120px;
  white-space: nowrap;
  overflow: hidden;

}

.mat-testCenter {
  height: calc(100vh - 80vh);
  touch-action: auto !important;
  // overflow: auto;
}

.mat-testCenter1 {
  // height: calc(100vh - 82vh);
  touch-action: auto !important;
}

.mi {
  padding: 1rem;
  color: var(--button-background);
  cursor: pointer;
}

.km {
  padding: 1rem;
  cursor: pointer;
}

.buttom6 {
  padding: 8px 26px;
  // margin: 0px 7px 9px 26px;
}

.Limited_Slots {
  color: #ee9400;
  border: 1px solid #ee9400;
}

.mat-testCenter {
  height: calc(100vh - 82vh);
}

.avaiable {
  color: #000000;
  border: 1px solid #000000;
}

.sloted {
  font-size: 15px;
  color: #7d7d7d;
}

.sloted1 {
  font-size: 14px;
  color: #7d7d7d;
}

.iconSize {
  font-size: 16px;
}

.tx {
  font-size: 12px;
}

::ng-deep .mat-calendar-body-label {
  opacity: 0 !important;
}

::ng-deep .mat-calendar-arrow {
  opacity: 0 !important;
}

:host::ng-deep .mat-button .mat-button-wrapper>*,
.mat-flat-button .mat-button-wrapper>*,
.mat-stroked-button .mat-button-wrapper>*,
.mat-raised-button .mat-button-wrapper>*,
.mat-icon-button .mat-button-wrapper>*,
.mat-fab .mat-button-wrapper>*,
.mat-mini-fab .mat-button-wrapper>* {
  vertical-align: middle;
  font-size: 10px;
}

::ng-deep .mat-calendar-table {
  border-spacing: 0;
  border-collapse: collapse;
  width: 100%;
  border-bottom: 1px lightgray solid;
}

// ::ng-deep .mat-calendar-content {
//   border-bottom: 1px lightgray solid !important;
// }
// ::ng-deep .mat-calendar-content::after {
//   content: "⚫️ Available ⚪️ Not available  🟢 Today  🟡 Selected";
//   font-size: 7px;
//   &:before{
//     content: "Available"
//     }
// }
// ::ng-deep .mat-calendar-content::after {
//   width:5px;
//   height:5px;
//   border-radius:50%;
//   color: #b83b3b;
//  }
::ng-deep .mat-calendar-body-label {
  padding-top: 1% !important;
  padding-bottom: 1% !important;
}

::ng-deep .mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
  // border-color: rgba(0, 0, 0, 0.38);
  border-radius: 0px !important;
  border-color: #ccf5e2 !important;
  background-color: #ccf5e2 !important;
}

::ng-deep .mat-calendar-body-cell-content.mat-focus-indicator {
  border-radius: 0px !important;
  // border-color: var(--text-color2) !important;
  // background-color: var(--text-color2) !important;
}

.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),
.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),
.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
  // background-color: rgba(32,158,145) !important;
  background-color: var(--text-color2) !important;
}

.select {
  font-size: 10px;
}

::ng-deep .mat-select-value {
  font-size: 10px !important;
}

::ng-deep input.mat-input-element {
  font-size: 10px !important;
}

.select-slot-btn {
  color: var(--text-color2);
  border: 1px solid var(--text-color2);
}

.example-container {
  display: flex;
  flex-direction: column;
}

.example-container>* {
  width: 100%;
}

.example-container form {
  margin-bottom: 20px;
}

.example-container form>* {
  margin: 5px 0;
}

.example-container .mat-radio-button {
  margin: 0 12px;
}

.textBox {
  border-top: 0.3px solid #c9c9c9;
  border-bottom: 0.3px solid #c9c9c9;
  border-left: 0.3px solid #c9c9c9;
  border-right: 0.3px solid #c9c9c9;
}

.empty-eligible {
  color: var(--text-color1);
}

// ::ng-deep .custom__menu ~ .cdk-overlay-connected-position-bounding-box {

//     max-width: 461px;
//     .cdk-overlay-pane{
//       width:100% !important;
//       min-width: 100% !important;
//       .testCenerMenu{
//         width:100% !important;
//           min-width:100% !important;
//         .mat-menu-content{
//           width:100% !important;
//           min-width:100% !important;
//           @apply flex flex-col p-4;
//         }
//       }
//     }
//   }
@screen xs {
  ::ng-deep .testCenerMenu {
    padding: 16px !important;
    min-width: 85vw;
    border: var(--border);
  }
}

@screen md {
  ::ng-deep .testCenerMenu {
    padding: 16px !important;
    min-width: 530px;
    border: var(--border);
  }
}

.INF {
  position: absolute;
  font-size: 2rem;
  margin-top: 0.2rem;

}

.INFLABEL {
  font-size: 0.55rem;
}

.INFTEXT {
  font-size: 0.7rem;
  font-color: gray;
}


.accord1 {

  // @screen md {
  //   // width: max-content!important;
  //   max-width: fit-content !important;
  // }
  // @screen sm {
  //   // width: max-content!important;
  //   max-width: fit-content !important;
  // }

}

.slots-test {
  @screen xs {
    max-width: fit-content !important;
  }

  @screen xxs {
    max-width: fit-content !important;
  }
}

.mat-date-range-input-inner {
  margin-left: 0.25rem;
  vertical-align: text-top;
  font-size: 10px;
  // top: 3px !important;
}

::ng-deep .mat-date-range-input-separator {
  margin: -2px 4px 2px !important;
}

::ng-deep .mat-menu-content:not(:empty) {
  padding: 8px !important;
}

// .start {
//   font-size: 11px;
//   top: 3px !important;
// }
// .end {
//   font-size: 11px;
// }
::ng-deep mat-expansion-panel-header.isSticky {
  box-shadow: 0px 4px 8px rgba(13, 45, 60, 0.3);
}

::ng-deep mat-expansion-panel-header {
  padding: 40px 25px;
  position: sticky;
  top: -1px;
  background-color: #fff;
}

// side arrow
// @arrow-size: 20vw;
body {
  background-color: beige;
  animation: paint 10s ease-in-out infinite;
}

.arrow-right {
  width: 6vw;
  height: 6vw;
  background-color: black;
  position: absolute;
  top: 59%;
  left: 37%;
  transform: translate(-50%, -50%) rotate(45deg);
}

.arrow-mask {
  width: 100%;
  height: 100%;
  background-color: beige;
  position: absolute;
  left: 15%;
  top: -15%;
  right: 0%;
  bottom: 0%;
  // animation: paint 10s ease-in-out infinite,
  // flip 10s ease-in-out infinite;
}

@keyframes flip {

  /* arrow facing left */
  /* arrow facing bottom */
  // 5%,25% {
  //   left: -15%;
  //   top: -15%;
  //   right: 0%;
  // }
  /* arrow facing right */
  30%,
  50% {
    left: -15%;
    top: 15%;
    right: 0%;
  }

  /* arrow facing top */
  // 55%,75% {
  //   left: 15%;
  //   top: 15%;
  //   right: 0%;
  // }
  // /* arrow facing left (again) */
  80%,
  0% {
    left: 15%;
    top: -15%;
    right: 0%;
  }
}

@keyframes paint {

  /* arrow facing bottom */
  5%,
  25% {
    // background-color: #fd4136;
  }

  /* arrow facing right */
  30%,
  50% {
    // background-color: #0f213e;
  }

  /* arrow facing top */
  55%,
  75% {
    // background-color: #1abc9c;
  }

  /* arrow facing left (again) */
  80%,
  0% {
    background-color: beige;
  }
}

@keyframes paintArrow {

  /* arrow facing bottom */
  5%,
  25% {
    background-color: white;
  }

  /* arrow facing right */
  30%,
  50% {
    background-color: #f1c40f;
  }

  /* arrow facing top */
  55%,
  75% {
    background-color: #2c3e50;
  }

  /* arrow facing left (again) */
  80%,
  0% {
    background-color: #2ecc71;
  }
}

::ng-deep {
  mat-tab-group.button-tabs {
    .mat-tab-label {
      margin: 0 2px;
      height: 36px;
      padding: 0 10px;
      font-size: 12px;
      border: 1px solid gray;
      border-radius: 5px;
      min-width: 85px;
    }

    .mat-ink-bar {
      opacity: 0;
    }

  }
}

::ng-deep {

  mat-tab-nav-bar,
  .mat-tab-header {
    border-bottom: 0px solid rgba(82, 63, 105, 0.06) !important;
  }

  .mat-tab-labels>.mat-tab-label:first-child {
    display: none;
  }
}

.active {
  color: var(--button-background) !important;
  border: 1px solid var(--button-background) !important;
}

::ng-deep .mat-tab-label-active:not(.mat-tab-disabled),
.mat-tab-label.mat-tab-label-active.cdk-keyboard-focused:not(.mat-tab-disabled) {
  border: #0076C1 1px solid !important;
  color: #0076C1 !important;
  opacity: 1;
}

.alert {
  color: red;
  margin-top: -10px;
  font-size: 0.65rem;

}