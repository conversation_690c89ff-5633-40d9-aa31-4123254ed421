import { Component, Input, OnInit } from '@angular/core';
import { Score } from '../model';

@Component({
  selector: 'exai-question-performance',
  templateUrl: './question-performance.component.html',
  styleUrls: ['./question-performance.component.scss']
})
export class QuestionPerformanceComponent  implements OnInit{
  result :Score[];
  data = [];

  @Input() scores:any;

  ngOnInit(): void {
    this.result = this.scores.scores;
    this.splitArray(this.result,10);
  }

  splitArray(array , n){
    for (let i = 0; i < array.length; i += n) {
      const chunk = array.slice(i, i + n);
      this.data.push(chunk);
    } 
  }
}
