<div [class.collapsed]="collapsed" [class.open]="collapsed && collapsedOpen$ | async" class="sidenav cardBorder flex flex-col">

    <exai-scrollbar class="flex-auto">
        <div class="sidenav-items mt-4">
            <ng-container *ngFor="let item of items; trackBy: trackByRoute">
                <exai-sidenav-item *ngIf="!(item.route == '/registry' && (this.global.stateId == 15
                    || this.global.stateId ==14 ))" [item]="item" [level]="0" class="my-2">
                </exai-sidenav-item>
            </ng-container>
        </div>
    </exai-scrollbar>

    <div class="sidenav-toolbar flex-none flex items-center">
        <img (click)="toggleCollapse()" src="assets/img/side-toggle1.svg" class="mb-12 select-none flex-none hidden lg:block cursor-pointer">
        <span (click)="toggleCollapse()" class="mb-12 ml-4 item-label1 select-none flex-none hidden lg:block cursor-pointer">Toggle Sidebar</span>
        <img (click)="toggleCollapse()" [src]="imageUrl$ | async" class="w-4 select-none flex-none hidden lg:block cursor-pointer">
        <h2 class="">{{ title$ | async }}</h2>

        <button (click)="toggleCollapse()" *ngIf="showCollapsePin$ | async" class="w-8 h-8 -mr-2 leading-none flex-none hidden lg:block" mat-icon-button type="button">
    </button>
    </div>
</div>