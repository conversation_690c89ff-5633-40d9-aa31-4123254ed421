export interface PracticeBundleSkill {
  bundleSkillGuid: string;
  tenantGuid: string;
  practiceSkillGuid: string;
  skillTitle: string;
  programName: string;
  tutorialURL?: string;
  duration?: number;
  validity?: number;
  skillStepCount?: number;
  totalAttempts?: number;
  priceUsd?: number;
  noOfSkill?: number;
}

export interface PracticeSkillBundleResponse {
  status: string;
  data: PracticeBundleSkill[];
  totalRecords: number;
  pageSize: number;
  pageNo: number;
  message: string;
}