// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@import "~@angular/material/theming";

// SCSS Variables
@import "var";

// Styles / CSS Variables
@import "partials/styles/style-dark";
@import "partials/styles/style-light";
@import "partials/styles/style-default";
@import "partials/styles/style-purple";

// Mixins
@import "partials/mixins";

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat-core($config);

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include angular-material-theme($exai-theme);

// Partials
@import "partials/horizontal";
@import "partials/vertical";
@import "partials/print";
@import "partials/typography";
@import "partials/overrides";
@import "partials/scrollbar";
@import "partials/plugins/angular-material";
@import "partials/plugins/apexcharts";

// Layouts
@import "partials/layouts/layout-apollo";
@import "partials/layouts/layout-ares";
@import "partials/layouts/layout-hermes";
@import "partials/layouts/layout-ikaros";
@import "partials/layouts/layout-zeus";

// Plus imports for other components in your app.

/* You can add global styles to this file, and also import other style files */

html {
  box-sizing: border-box;
  font-size: 16px;
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  @include mat-typography-level-to-styles($config, body-1);
  @apply text-black leading-normal antialiased;
  font-feature-settings: "kern";
}

div {
  box-sizing: border-box;
}

// Temporary placed here as global styles
.dashboard_block{
  @apply flex flex-col;
}
  .dashboard_items{
    @apply flex flex-wrap;
  }


  .dashboard_item{
    @apply shadow-none  justify-start flex-grow ;
    @media (min-width: theme("screens.xs")){
      @apply max-w-full pr-0 pb-4 w-full;
    }
    @media (min-width: theme("screens.sm")){
      @apply max-w-1/2 pr-4 pb-0 ;
    }
    @media (min-width: theme("screens.lg")){
      @apply max-w-1/3;
    }
    @media (min-width: theme("screens.xl")){
      @apply max-w-1/4;
    }
  }
  .help_layout{
    @apply flex items-stretch;
    @screen xs{
      @apply flex-col;
    }
    @screen md{
      @apply flex-row;
    }
  }
  .help_layout-sidebar{
    @apply flex-grow;
    @screen xs{
      min-height: 0;
      @apply max-w-full  mr-0 mb-4;
    }
    @screen md{
      min-height: calc(100vh - 180px);
      @apply max-w-1/4  mr-4 mb-0;
    }
  }
  exai-select-category, exai-get-help, exai-tickets, exai-ticket-details,exai-support-ticket{
    @apply flex-grow ;
    @screen xs{
      @apply max-w-full;
    }
    @screen md{
      @apply max-w-3/4;
    }
  }
  .help_layout-category{
    @apply w-full h-full;
  }
  .exai-button{
    @apply m-4 py-4 px-2;
  }

  .exai-link{
    color: #FFFFFF;
  }
  .light-txt{
    color: var(--text-toggle);
  }
