import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root',
  })

export class ShowPracticeExamResult{
    constructor(private http:HttpClient){

    }

    getPracticeExamResults(personeventIds:number){
        const url = environment.baseUrl+`schedulemsvc/api/scheduler/showPracticeExamScore?personeventIds=${personeventIds}`
        return this.http.get<any>(url)
    }
}
