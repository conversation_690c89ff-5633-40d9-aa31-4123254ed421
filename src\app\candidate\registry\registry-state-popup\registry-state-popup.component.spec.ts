import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RegistryStatePopupComponent } from './registry-state-popup.component';

describe('RegistryStatePopupComponent', () => {
  let component: RegistryStatePopupComponent;
  let fixture: ComponentFixture<RegistryStatePopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RegistryStatePopupComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RegistryStatePopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
