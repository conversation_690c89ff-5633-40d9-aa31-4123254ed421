import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { of } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { getCartItems, getCartPracticeItems, getUserDetails, gotCartItems, gotCartPracticwItems, gotUserDetails, setErrorMessage } from "./shared.actions";
import { URL } from 'src/app/core/url';

@Injectable({
    providedIn: "root",
})
export class SharedEffects {
    personTenantRoleId;
    idToken;
    constructor(
        private httpClient: HttpClient,
        private actions$: Actions,
        private router: Router,
    ) {

    }

    effectivelyGetUserDetails$ = createEffect(() =>
        this.actions$.pipe(
            ofType(getUserDetails),
            switchMap((action) => {
                let result=encodeURIComponent(action.emailId);
                return this.httpClient
                    .get<any>(
                        URL.ACCOUNT_BASE_URL +
                        `/getParameters?EmailId=${result}`
                    )
                    .pipe(
                        map((data) =>
                            gotUserDetails({
                                userDetails: data,
                            })
                        ),
                        catchError((err) => of(setErrorMessage({ message: err })))
                    );
            })
        )
    );
    
    effectivelyGetCartItems$ = createEffect(() =>
        this.actions$.pipe(
            ofType(getCartItems),
            switchMap((action) => {
                return this.httpClient
                    .get<any>(
                        URL.BASE_URL +
                        `exam/cartitems?personTenantRoleId=${action.personTenantRoleId}`
                    )
                    .pipe(
                        map((cartItems) =>
                            gotCartItems({
                                cartItems: cartItems,
                            })
                        ),
                        catchError((err) => of(setErrorMessage({ message: err })))
                    );
            })
        )
    );

    
    effectivelyGetPracticeCartItems$ = createEffect(() =>
        this.actions$.pipe(
            ofType(getCartPracticeItems),
            switchMap((action) => {
                return this.httpClient
                    .get<any>(
                        URL.BASE_URL +
                        `Exam/practiceExamcartitems?personTenantRoleId=${action.personTenantRoleId}`
                    )
                    .pipe(
                        map((cartItem) =>
                            gotCartPracticwItems({
                                practicecartItems: cartItem,
                            })
                        ),
                        catchError((err) => of(setErrorMessage({ message: err })))
                    );
            })
        )
    );
}

