import { NgModule, Type } from "@angular/core";
import { CommonModule } from "@angular/common";

import {
  FormsModule,
  NG_VALIDATORS,
  ReactiveFormsModule,
  Validator,
} from "@angular/forms";
import { DynamicFormsMaterialUIModule } from "@ng-dynamic-forms/ui-material";
import { FormBuilderRoutingModule } from "./form-builder-routing.module";
import { FormBuilderComponent } from "./form-builder.component";
import { MatCardModule } from "@angular/material/card";
import { MatButtonModule } from "@angular/material/button";
import {
  DateAdapter,
  MatRippleModule,
  MAT_DATE_LOCALE,
  NativeDateAdapter,
} from "@angular/material/core";
import { MatExpansionModule } from "@angular/material/expansion";
import { FlexLayoutModule } from "@angular/flex-layout";
import { NgxMaskModule } from "ngx-mask";
import { MatIconModule } from "@angular/material/icon";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatDialogModule } from "@angular/material/dialog";
import { MatSelectModule } from "@angular/material/select";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatTabsModule } from "@angular/material/tabs";
import { MatSnackBarModule } from "@angular/material/snack-bar";
import {
  DynamicFormsCoreModule,
  DYNAMIC_MATCHER_PROVIDERS,
  HIDDEN_MATCHER_PROVIDER,
  DYNAMIC_FORM_CONTROL_MAP_FN,
  DynamicFormControlModel,
  DynamicFormControl,
  DYNAMIC_VALIDATORS,
  ValidatorFactory,
  REQUIRED_MATCHER_PROVIDER,
} from "@ng-dynamic-forms/core";

import { DragDropModule } from "@angular/cdk/drag-drop";

import { NgxEditorModule } from "ngx-editor";

import { MAT_CHIPS_DEFAULT_OPTIONS } from "@angular/material/chips";
import { MatNativeDateModule } from "@angular/material/core";
import { FormBuilderPopupComponent } from "./form-builder-popup/form-builder-popup.component";
import { MatTooltipModule } from "@angular/material/tooltip";
import { QuesGrpPopupComponent } from "./ques-grp-popup/ques-grp-popup.component";
import { MatMenuModule } from "@angular/material/menu";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { DynamicFormComponentComponent } from "./dynamic-form-component/dynamic-form-component.component";
import { TrueFormComponentComponent } from "./true-form-component/true-form-component.component";
import { FormJsonComponent } from "./form-json/form-json.component";
import { FormJsonOutputComponent } from "./form-json-output/form-json-output.component";

import { ClipboardModule } from "@angular/cdk/clipboard";
import { FileAutoViewComponent } from "./file-upload-control/file-auto-view/file-auto-view.component";
import { FileUploadControlComponent } from "./file-upload-control/file-upload-control.component";
import { MatToolbarModule } from "@angular/material/toolbar";
import { DynamicFileUploadControlComponent } from "./dynamic-file-upload-control/dynamic-file-upload-control.component";
import { LayoutFormComponent } from "./ques-grp-popup/layout-form/layout-form.component";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { NgxDocViewerModule } from "ngx-doc-viewer";
import { FileViewPopupComponent } from "./file-upload-control/file-view-popup/file-view-popup.component";
import { customCheckboxRequiredValidator } from "./formBuilder.validators";
import { MatRadioModule } from "@angular/material/radio";
import { FormUploadControlComponent } from "./form-upload-control/form-upload-control.component";
import { DynamicFormUploadComponent } from "./dynamic-form-upload/dynamic-form-upload.component";
import { DynamicFormLinkComponent } from "./dynamic-form-link/dynamic-form-link.component";
import { FormLinkAttachComponent } from "./form-link-attach/form-link-attach.component";
import { FormViePopupComponent } from "./form-link-attach/form-vie-popup/form-vie-popup.component";
import { MatListModule } from "@angular/material/list";
import { MatTableModule } from "@angular/material/table";
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import {
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  MomentDateAdapter,
  MomentDateModule,
} from "@angular/material-moment-adapter";
import moment from "moment";
import { CommentPopupComponent } from "./form-link-attach/comment-popup/comment-popup.component";
import { FileViewComponent } from "./file-upload-control/file-view/file-view.component";

export class AppDateAdapter extends NativeDateAdapter {
  format(date: Date, displayFormat: Object): string {
    return moment(date).utc().format("MM/DD/YYYY").toString();
  }
}

// @ts-ignore
@NgModule({
  declarations: [
    FormBuilderComponent,
    FormBuilderPopupComponent,
    QuesGrpPopupComponent,
    DynamicFormComponentComponent,
    TrueFormComponentComponent,
    FormJsonComponent,
    FormJsonOutputComponent,
    FileUploadControlComponent,
    DynamicFileUploadControlComponent,
    LayoutFormComponent,
    FileAutoViewComponent,
    FileViewPopupComponent,
    FormUploadControlComponent,
    DynamicFormUploadComponent,
    DynamicFormLinkComponent,
    FormLinkAttachComponent,
    FormViePopupComponent,
    CommentPopupComponent,
 FileViewComponent
  ],
  imports: [
    CommonModule,
    FormBuilderRoutingModule,
    ReactiveFormsModule,
    DynamicFormsMaterialUIModule,
    MatExpansionModule,
    FlexLayoutModule,
    MatCardModule,
    MatListModule,
    MatRadioModule,
    FormsModule,
    NgxMaskModule.forRoot(),
    DynamicFormsCoreModule.forRoot(),
    MatNativeDateModule,
    MatIconModule,
    MatButtonModule,
    MatRippleModule,
    MatFormFieldModule,
    MatInputModule,
    NgxEditorModule,
    MatDialogModule,
    MatTooltipModule,
    MatMenuModule,
    MatSelectModule,
    MatCheckboxModule,
    MatTabsModule,
    MatSlideToggleModule,
    ClipboardModule,
    MatToolbarModule,
    DragDropModule,
    MatDatepickerModule,
    NgxDocViewerModule,
    MatTableModule,
    MatSnackBarModule,
    MomentDateModule,
    MatAutocompleteModule
  ],

  providers: [
    [
      { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
      {
        provide: DateAdapter,
        useClass: AppDateAdapter,
        deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
      },
    ],
    REQUIRED_MATCHER_PROVIDER,
    HIDDEN_MATCHER_PROVIDER,
    ...DYNAMIC_MATCHER_PROVIDERS,
    {
      provide: NG_VALIDATORS,
      useValue: customCheckboxRequiredValidator,
      multi: true,
    },
    {
      provide: DYNAMIC_VALIDATORS,
      useValue: new Map<string, Validator | ValidatorFactory>([
        ["customCheckboxRequiredValidator", customCheckboxRequiredValidator],
      ]),
    },
    {
      provide: MAT_CHIPS_DEFAULT_OPTIONS,
      useValue: {
        separatorKeyCodes: [13, 188],
      },
    },
    {
      provide: DYNAMIC_FORM_CONTROL_MAP_FN,
      useValue: (
        model: DynamicFormControlModel
      ): Type<DynamicFormControl> | null => {
        switch (model.type) {
          case "FILE_UPLOAD":
            return DynamicFileUploadControlComponent;
          case "RATING":
            return DynamicFormUploadComponent;
          case "COLORPICKER":
            return DynamicFormLinkComponent;
        }
      },
    },
  ],
  exports: [FormBuilderComponent, DynamicFormComponentComponent,FileViewComponent],
  entryComponents: [
    DynamicFileUploadControlComponent,
    DynamicFormUploadComponent,
    DynamicFormLinkComponent,
  ],
})
export class FormBuilderModule {}
