import { Component, ElementRef, forwardRef, ViewChild, Input, EventEmitter, Output, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { BehaviorSubject, Observable } from 'rxjs';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { BreakpointObserver } from '@angular/cdk/layout';
import { FileViewPopupComponent } from './file-view-popup/file-view-popup.component';
import { SnackbarService } from '../../../snackbar.service';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { FormBuilderService } from '../form-builder.service';
@Component({
  selector: 'exai-file-upload-control',
  templateUrl: './file-upload-control.component.html',
  styleUrls: ['./file-upload-control.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FileUploadControlComponent),
      multi: true
    }
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileUploadControlComponent implements ControlValueAccessor, OnInit {

  @ViewChild('fileInput') fileInput: ElementRef;
  file: any;
  toggleFileView: boolean = true;

  @Input() url: string;
  @Input() removeUrl: string;
  @Input() label: string;
  @Input() name: string;
  @Input() accept: Array<string>;
  @Input() multiple: boolean;
  @Input() showFileList: boolean = false;
  @Input() autoUpload: boolean;
  @Input() required: boolean;
  @Input() disabled:boolean;

  @Output() blur: EventEmitter<any> = new EventEmitter();
  @Output() change: EventEmitter<any> = new EventEmitter();
  @Output() focus: EventEmitter<any> = new EventEmitter();

  viewUrl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  viewUrl$:Observable<string> = this.viewUrl.asObservable();
  displayKey:string = 'Upload';
  writeValue(value: any) {
    if (value !== undefined && value != null)
      this.file = Array.isArray(value) ? value : value.split(',');
    else
      this.file = [];
  }
  propagateChange = (_: any) => { };

  registerOnChange(fn) {
    this.propagateChange = fn;
  }

  registerOnTouched() { }

  constructor(private http: HttpClient,
    private cdr: ChangeDetectorRef,
    public dialog: MatDialog,
    private breakpointobserver: BreakpointObserver,
    private snackbar:SnackbarService,
    private global:GlobalUserService,
    private formBuilderservice:FormBuilderService
  ) {
    this.displayKey = this.global.navigate === 'manage-grievence' ? 'Attachment' : 'Upload';

   }

  ngOnInit() {
  }

  fileUpload(){
    this.fileInput.nativeElement.value = '';
    this.fileInput.nativeElement.click();
  }

  removeEvent(fileIndex: number) {
    
    this.file.splice(fileIndex, 1);
    this.formBuilderservice.remainigFiles=this.file;
    // need to make api call here; to remove file from s3
    this.propagateChange(this.file.join(','));
    this.cdr.markForCheck();
  }
  uploadFileEvt(files: FileList) {
    if (files) {
      let duplicateFile=this.file.length!=0?this.file.every(x=>x.split('|')[0]!=files[0].name):true;
      if(duplicateFile){
      if (!this.file) this.file = [];
      if (!this.multiple && files.length > 1) {
        this.snackbar.callSnackbaronWarning('Multiple files are not allowed!!');
        return;
      }
      var fileExtionsions = Array.from(files).map((x: File) => { return this.getExtension(x) });
      var fileSizes = Array.from(files).map((x: File) => { return x.size; });
      var invalidExtensions = [];
      var invalidSizes = [];
      fileExtionsions.forEach((fileExt: string) => {
        if (!this.accept.includes('.'+fileExt.toLowerCase())) invalidExtensions.push(fileExt);
      })
     
      // limiting the max upload size to 18 mb
      fileSizes.forEach((size: number,index) => {
        if (size > 18000000) invalidSizes.push(files[index].name);
      })
      if (invalidExtensions.length > 0) {
        this.snackbar.callSnackbaronWarning('File with following extensions: '+invalidExtensions.join(', ')+' are not allowed!');
        return;
      }
      if (invalidSizes.length > 0) {
        this.snackbar.callSnackbaronWarning('The following files are too large: '+invalidSizes.join(', '));
        return;
      }
      var fileArray = Array.from(files);
      fileArray.forEach((file: File) => {
        file.name.length <=50?this.uploadMyFile(file).subscribe((response: any) => {
          var result = response.fileName + "|" + response.systemFileName;
          this.file.push(result);
          this.propagateChange(this.file.join(','));
          this.cdr.markForCheck();
        }, (err: any) => {
          console.error(JSON.stringify(err));
        }):this.snackbar.callSnackbaronError(`FileName:${file.name} Max length in the Supporting Document Title must be 45 characters. Please rename the file before trying again`)
      });
    }
    } else {
      this.file = [];
      this.cdr.markForCheck();
    }
    this.propagateChange(this.file);
  }

  
  uploadMyFile(file: File) {
    const formData = new FormData();
    formData.append('File', file);
    // simply removing commas so that later while parsing 
    // the response the.split is possible
    formData.append('SubFolderName', file.name.split(',').join(''));
    var url = environment.baseUrl + `formmsvc/api/file/upload`;
    return this.http.post(url, formData);
  }

  getToggleFileView(event = null) { 
    if(event) {
      this.toggleFileView = event.value;
    } else {
      this.toggleFileView = !this.toggleFileView;
    }
  }
  
  downloadForView(fileIndex: number) {
    var url = environment.baseUrl + `formmsvc/api/file/url?systemFileName=${this.file[fileIndex].split('|')[1]}`;
    this.http.get(url).subscribe((response: any) => {
      this.viewUrl.next(response.url);
      const dialogConfig = new MatDialogConfig();
      dialogConfig.hasBackdrop = true;
      this.breakpointobserver
        .observe(['(min-width : 1024px)']).subscribe((result: { matches: any }) => {
          if (result.matches) {
            dialogConfig.minWidth = '60vw';
            dialogConfig.minHeight = '85vh';
            dialogConfig.maxWidth = '70vw';
            dialogConfig.maxHeight = "85vh";
          } else {
            dialogConfig.minWidth = '90vw';
            dialogConfig.minHeight = '90vh';
          }
        });
      dialogConfig.data = {
        viewUrl:this.viewUrl$,
        fileExtension:this.file[fileIndex].split('.').pop()
      }
      
      const dialogRef = this.dialog.open(FileViewPopupComponent, dialogConfig);
      dialogRef.afterClosed().subscribe((data) => {
        this.viewUrl.next(null);
      });
    })
  }
  getExtension(file:File) {
    return file.name.split('.').pop();
  }
}
