<mat-dialog-content>
    <div class="flex justify-end shadow-none pt-2 pr-4">
        <mat-icon class="text-sm flex justify-end cursor-pointer" mat-dialog-close>close</mat-icon>
    </div>
    <div class="px-4 flex" gdColumns="1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr" gdColumns.lt-sm="1fr" gdGap="16px">
        <div gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
            <div class="dialog-content pb-2">
                <div class="dialog-content_block card cardBorder shadow-none">
                    <h6 class="text-xs font-bold fontColor1 px-4 py-2">
                        Previous Tickets
                    </h6>
                    <div class="popUp-height">
                        <ul class="eligibility touch-auto overflow-auto">
                            <li class="px-4 eligibility-list-btn my-2"
                                *ngFor="let category of previousTickets$ | async">
                                <button [ngClass]="{ active: category.id == currentTicket }" mat-stroked-button
                                    fxFlex="auto" class="btn flex t-xs justify-between"
                                    (click)="selectCategory(category.id)">
                                    Id-{{ category.id }}
                                    &nbsp;
                                    Subject-{{ category.subject }}
                                    <mat-icon class="text-end flex icons" *ngIf="category.id === currentTicket ">
                                        arrow_forward_ios
                                    </mat-icon>
                                </button>

                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-2" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
            <div class="dialog-content_block card cardBorder shadow-none">
                <h6 class="text-xs font-bold fontColor1 px-4 pt-2">
                    Preview
                </h6>
                <div class="popUp-height mt-1">
                    <div class="card cardBorder shadow-none m-4" *ngFor="let ticket of previousTicketDetails$ | async">
                        <div class="title px-4 pb-1 mt-4">
                            <h5 class="text_size font-bold fontColor1 -mt-4">{{ ticket.subject }}</h5>
                            <h6 class="text-xs fontStyle italic text_size">{{ ticket.createdDate |
                                date:'MM/dd/yyyy' }}</h6>
                        </div>
                        <div class="flex px-4 pt-1">
                            <div class="card-body_block">
                                <span class="t-xs status">Ticket Id</span>
                                <p class="t-xs item-label1">{{ ticket.id }}</p>
                            </div>
                        </div>
                        <div class="px-2" fxLayout="column ">
                            <div fxLayout="column">
                                <div class="pt-2 pl-2 card-body_block" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr " exaiContainer>
                                    <div gdColumn="1/5 " gdColumn.lt-md="1/5 " gdColumn.lt-sm="1/3 ">
                                        <div class="h4 status t-xs ">Description</div>
                                    </div>
                                    <div gdColumn="6/-1 " gdColumn.lt-md="6/-1 " gdColumn.lt-sm="4/-1 ">
                                        <div class="h4 status t-xs ">Status</div>
                                    </div>
                                </div>
                                <div class="pt-1 pb-2 pl-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr " exaiContainer>
                                    <div gdColumn="1/5 " gdColumn.lt-md="1/5 " gdColumn.lt-sm="1/3 ">
                                        <div class="h4 status1 t-xs "> {{ticket.description }}</div>
                                    </div>
                                    <div gdColumn="6/-1 " gdColumn.lt-md="6/-1 " gdColumn.lt-sm="4/-1">
                                        <div class="h4 status1 t-xs " [style.color]="
                                        ticket.statusName == 'Drafted' || ticket.statusName == 'Pending'
                                            ? '#EE9400'
                                            : ticket.statusName == 'Approved'
                                            ? '#00AB72'
                                            : '#F7685B'
                                        ">{{ticket.statusName }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div fxLayout="column">
                            <div class="card-body_block mt-4">
                                <!-- <p class="popUp-input active" *ngFor="let attachment of ticket.attachments">{{
                                    attachment.fileName }}
                                    <button (click)="downloadForView(attachment)"
                                        class="hover:bg-gray-100 text-gray-800 font-semibold border-gray-400 rounded  m-0.5 never-disabled">
                                        <mat-icon class="cursor-pointer text-base align-text-top">visibility
                                        </mat-icon>
                                    </button>
                                </p> -->
                                <form class="ml-4" *ngIf="fileUploadFormGroup" [formGroup]="fileUploadFormGroup">
                                    <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup" [model]="fileUploadModel[0]">
                                    </dynamic-material-form-control>
                                </form>
                            </div>
                        </div>
                        <div class="flex px-4 pt-1">
                            <div class="">
                                <h6 class="t-xs fontColor1">Response</h6>
                                <div *ngFor="let response of ticket.responses ">
                                    <p class="item-label1">{{ response.response}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</mat-dialog-content>