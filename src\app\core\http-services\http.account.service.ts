import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { GlobalUserService } from "../global-user.service";
import { Observable } from "rxjs";
import { PersonDetails } from "../Dto/persondetail";
import { URL } from 'src/app/core/url';
import { TemplateStatusModel } from "../Dto/template-status.model";
import { PersonFormModel } from "../Dto/personform.model";
import { PersonFormLogModel } from "../Dto/personform-log.model";
import { UserDetails } from "src/app/candidate/candiate.types";

@Injectable({
  providedIn: "root",
})
export class HttpAccountService {  

  constructor(private http: HttpClient,
    private global: GlobalUserService) {       
    }

  public getAccountPersonDetails() : Observable<PersonDetails> {
    return this.http.post<PersonDetails>(`${URL.ACCOUNT_BASE_URL}User/getUser`, {});
  }

  public editProfile(personDetails: PersonDetails) : Observable<TemplateStatusModel> {
    return this.http.post<TemplateStatusModel>(URL.ACCOUNT_BASE_URL + "User/updateuser", personDetails);
  }
  
  public uploadProfilePic(formData: FormData): Observable<boolean> {
    var url = `${URL.ACCOUNT_BASE_URL}User/updateprofilepic`;
    return this.http.post<boolean>(url, formData);
  }
  
  public getCorrectionFormValue(personFormId : number) : Observable<PersonFormModel[]>{
    var url = `${URL.BASE_URL}form/personform/list?personFormId=${personFormId}`;
    return this.http.get<PersonFormModel[]>(url);
  }

  public getCorrectionlogs(personFormId: number): Observable<PersonFormLogModel[]> {
    var url = (`${URL.BASE_URL}Form/personformlogs?personFormId=${personFormId}`);
    return this.http.get<PersonFormLogModel[]>(url);
  }

  public getParameters(emailId: string): Observable<UserDetails>{
    let result=encodeURIComponent(emailId);
    return this.http.get<UserDetails>(URL.ACCOUNT_BASE_URL + `/getParameters?EmailId=${result}`);  
  }


}
