import { Component, Input, OnInit } from '@angular/core';
import { trackByValue } from '../../utils/track-by';

@Component({
  selector: 'exai-breadcrumbs',
  template: `
  <style>
  .active-link{   
    color: #209E91;
    }
  </style>
    <div class="flex items-center">
      <exai-breadcrumb>
        <a [routerLink]="['/']">
          Home
        </a>
      </exai-breadcrumb>
    </div>
  `
})
export class BreadcrumbsComponent implements OnInit {


  trackByValue = trackByValue;

  constructor() {
  }

  ngOnInit() {
  }
}
