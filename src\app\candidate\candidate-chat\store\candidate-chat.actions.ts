import { createAction, props } from "@ngrx/store";
import { SupportStaffInfo } from "./candidate-chat.state";

export const ClearCandidateChatState = createAction('[SUPPORT CHAT] Clearing Support Chat Module State');

export const getAvailableStaffInfo = createAction('[GET STAFF] Get info of Available  Support Staff');

export const gotAvailableStaffInfo = createAction('[GOT STAFF] Got info of Available Support Staff', props<SupportStaffInfo>());

export const staffNotAvailable = createAction('[GOT STAFF] NO Staff Available',props<{message:string}>());

export const notifyConnection = createAction('[UPDATE] Update connection to backend', props<{
  candidateId: number,
  token: string
}>());

export const connectionNotified = createAction('[UPDATE] Updated connection successfully to backend');

export const notifyConnectionClose = createAction('[UPDATE] Update connection close', props<{
  candidateId: number,
  token: string
}>());

export const connectionCloseNotified = createAction('[UPDATE] Update connection close');