import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
  selector: "exai-skill-card-type",
  templateUrl: "./skill-card-type.component.html",
  styleUrls: ["./skill-card-type.component.scss"],
})
export class SkillCardTypeComponent implements OnInit {
  @Input() skill: any;
  @Input() isMobileOrTablet: boolean = false;
  @Input() menuItems: any[] = [];

  @Output() tryNow = new EventEmitter<any>();
  @Output() viewSkill = new EventEmitter<any>();
  @Output() menuAction = new EventEmitter<{ action: string; skill: any }>();

  toggleExpanded() {
    this.skill.isExpanded = !this.skill.isExpanded;
  }

  emitViewSkill() {
    this.viewSkill.emit(this.skill);
  }

  emitTryNow() {
    this.tryNow.emit(this.skill);
  }

  handleMenuAction(event: string) {
    this.menuAction.emit({ action: event, skill: this.skill });
  }
  constructor() {}

  ngOnInit(): void {}
}
