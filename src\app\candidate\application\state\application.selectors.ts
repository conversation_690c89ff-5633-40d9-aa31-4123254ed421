import { createFeatureSelector, createSelector } from "@ngrx/store";
import { ApplicationState } from "./application.state";

export const APPLICATION_STATE_NAME = "ApplicationModuleState";

const getApplicationState = createFeatureSelector<ApplicationState>(APPLICATION_STATE_NAME);

export const selectApplicationState = createSelector(getApplicationState,(state)=>{
  return state
})

export const selectEligibilityRoutes = createSelector(
  getApplicationState,
  (state) => {
    return state.eligibilityRoutes;
  }
)

export const selectEligibilityRouteIndex = createSelector(
  getApplicationState,
  (state) => {
    return state.activeEligibilityRouteIndex;
  }
)

export const selectPersonForms= createSelector(getApplicationState, (state) =>{
  return state.personForms;
})

export const selectorShowRegisterExam$ = createSelector(getApplicationState, (state)=>{
  return state.showRegisterExamStatus
});

