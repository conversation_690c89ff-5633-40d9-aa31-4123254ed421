<ng-container *ngIf="examResult">
    <div class="flex justify-center baground-result pt-8">
          <img src="assets/img/Icons/Credentia-logo.svg" id="credLogo" alt="logo" class="logo">
      </div>
    <div class="bg-white content "> 
        <div class="justify-center w-full border-b baground-result pt-4">
            <div class="text-center p-color text-2xl font-light">NNAAP Practice Exam</div>
            <div class="text-center mb-4 p-color text-sm font-medium">EXAMINATION RESULTS</div>
        </div>
    <ng-container *ngFor="let result of examResult">
        <div class="flex justify-center" >
            <div class="w">
                <div class="mb-3">
                    <exai-exam-details 
                    [examDetails]="result.examDetails" ></exai-exam-details>
                </div>

                <div>
                    <exai-question-performance 
                    [scores]="result" ></exai-question-performance>
                </div>
                <div>
                    <exai-area-performance
                    [performances]="result.performance"></exai-area-performance>
                </div>
            </div>
        </div>
    </ng-container>
    </div>
</ng-container>
