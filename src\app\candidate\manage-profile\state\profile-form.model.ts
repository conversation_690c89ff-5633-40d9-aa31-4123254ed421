import { Data } from "@angular/router";
import { PersonDetails } from "src/app/core/Dto/persondetail";
import { PersonFormDetailsModel } from "src/app/core/Dto/personform-details.model";
import { PersonFormModel } from "src/app/core/Dto/personform.model";
import { PersonForm } from "../../application/application.types";

export interface ProfileModel {
    status: string;
    iconUrl: string;
    fields: string
}

export interface Form {
    id?: number;
    formTypeId: number;
    formJson?: string;
    dataDetail: string;
    formUrl: string;
    name: string;
    personTenantRoleId: number;
    tenantId: number;
}

export interface CorrectionData {
    comment: string;
    reviwer: string;
    actionOn: Date;
    name: string
}

export interface upcomingExam {
    id: number;
    examId: number;
    candidateId: number;
    examName: string;
    candidateEmailId: string;
    candidateName: string;
    examDateTime: Date;
    examDateTimeUtc: Date;
    timeZoneOffset?: any;
    examStatusId: number;
    examStatus: string;
    stateCode: string;
    eligibilityRouteCode: string;
    eligibilityRouteName: string;
    iconUrl: string;
    registeredDateTime: Date;
    timezone: string;
    timezoneCode: string;
    examMode: string;
    allowReschedule: string;
    allowCancel: string;
    isPrimary: boolean;
    reschedule :boolean;
    cancel:boolean
}

export interface ProfileState {
    demographicForm: Form,
    personDetails: PersonDetails,
    imageUrl: string,
    submitDemographicform: any,
    savedDemographicResponseId: number,
    personForm: Array<PersonForm>;
    getCorrectionData: PersonFormModel[];
    deletedDemographicForm: any;
    personFormLogs: any;
    updateSuccess: boolean;
    disableEditProfiles: boolean;
    upcomingExams: upcomingExam[]
}