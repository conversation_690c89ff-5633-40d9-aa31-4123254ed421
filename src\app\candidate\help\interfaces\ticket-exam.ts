export interface TicketExam {
    id: number,
    slotId: number,
    examId: number,
    candidateId: number,
    examName: string,
    mode: string,
    candidateEmailId:string,
    candidateName: string,
    examDateTime: string,
    examDateTimeUtc: string,
    timeZone: string,
    timeZoneOffset: boolean,
    examStatusId: number,
    examStatusType: string,
    personFormId: number,
    isGrievanceFormSubmitted: boolean,
    isGrievanceFilled: boolean,
    examStatus: string,
    allowReschedule: boolean,
    allowShowResult: boolean,
    allowPayment: boolean,
    eventDataDetail: any,
    eligibilityRouteName: string,
    clientPersonEventId: string
}
