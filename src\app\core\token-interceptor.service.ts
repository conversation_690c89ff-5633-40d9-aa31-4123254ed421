import { Injectable } from "@angular/core";
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpResponse,
} from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { catchError, filter, map, tap } from "rxjs/operators";
import { Action, Store } from "@ngrx/store";
import { setErrorMessage, setLoadingSpinner } from "../candidate/state/shared/shared.actions";
import { GlobalUserService } from "./global-user.service";
import { URL } from 'src/app/core/url';
import { SnackbarService } from "./snackbar.service";
import { ReplaySessionService } from "./openreplay.services";
@Injectable({
  providedIn: "root",
})
export class TokenInterceptorService {

  constructor(
    private store: Store,
    private global: GlobalUserService,
    private snackbar: SnackbarService,
    private openReplayService:ReplaySessionService) { }

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>tt<PERSON><PERSON><PERSON><PERSON>
  ): Observable<HttpEvent<unknown>> {
    this.store.dispatch<Action>(setLoadingSpinner({ status: true }));
    let idToken = this.global.getUserIdToken();
    request = request.clone({
      setHeaders: {
        Authorization: `Bearer ${idToken}`
      }
    })
    const handleResponse = (request: HttpRequest<any>, response: HttpResponse<any>, event: string) => {
      //we forward our data to the service, which will create the custom event and send it
     this.openReplayService.sendEventToReplaySession(event, { request, response })
   }
    return next.handle(request)
    //  commenting out this part as error message
      .pipe(
        catchError((err) => {
          if (err.status == 401) {
            this.snackbar.callSnackbaronError('Your login has expired! Please login again....' )
            this.global.clearSessionStorage();
            window.location.href = URL.REDIRECT_URL;
          }
          else {
            this.store.dispatch<Action>(setErrorMessage({ message: err }));
            this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
          }
        return throwError({error: err.error});
      })
    )
      .pipe(
        filter( (event: any) => event instanceof HttpResponse),
        map( (resp: HttpResponse<any>) => { //for each response, call handleResponse
          handleResponse(request, resp, `${request.url}`)
          return resp
        }),
        map<HttpEvent<any>, any>((evt: HttpEvent<any>) => {
          if (evt instanceof HttpResponse) {
            this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
          }
          return evt;
        })
      );
  }
}
