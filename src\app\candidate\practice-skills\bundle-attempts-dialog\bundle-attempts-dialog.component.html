<div class="p-2">
  <div class="flex justify-between items-center px-4 py-2 shadow">
    <h2 class="text-sm font-semibold">Select skills for {{ bundle.title }} </h2>
    <button class="close-btn" (click)="closeDialog()">✕</button>
  </div>
</div>

<div class="px-2 mb-2 space-y-4">

  <div class="dialog-body rounded-md shadow p-4">
    <h2 class="text-sm font-semibold">{{ bundle.title }}</h2>
    <p class="text-xs leading-relaxed tracking-wide text-justify mb-4">
      {{ bundle.description }}
    </p>

    <div *ngIf="bundle.includes && bundle.includes.length > 0" class="bundle-includes">
      <p class="font-medium  font-semibold text-sm">Includes:</p>
      <ul class="includes-list">
        <li *ngFor="let item of bundle.includes">{{ item }}</li>
      </ul>
    </div>

    <div class="flex flex-wrap justify-between text-center text-xs mt-2 px-3">
      <div class="w-1/5">
        <div class="meta-label">Skills</div>
        <div class="meta-value">{{ bundle.totalSkills }}</div>
      </div>
      <div class="w-1/5">
        <div class="meta-label">Attempts</div>
        <div class="meta-value">{{ bundle.attemptsPerSkill }}</div>
      </div>
      <div class="w-1/4">
        <div class="meta-label">Validity</div>
        <div class="meta-value">{{ bundle.validity }}</div>
      </div>
      <div class="w-1/4">
        <div class="meta-label">Price</div>
        <div class="meta-value">{{ bundle.price }}</div>
      </div>
    </div>

  </div>
  <div class="mt-2 px-4">
    <h2 class="text-sm font-semibold">Select a pair of Skills of your choice</h2>
    <!-- First Skill -->
    <h1 class="select_skill text-xs px-1 font-semibold">1. Select the first Skill *</h1>
    <select class="w-full border p-1 rounded mb-2 text-xs" [(ngModel)]="selectedSkill1">
      <option [ngValue]="null" disabled selected>Select a skill </option>
      <option *ngFor="let skill of availableSkills" [ngValue]="skill"> {{skill.title}} </option>
    </select>
    <div class="flex items-center gap-3 text-xs">
      <span class="mt-1 px-2">Additional Attempts</span>
      <button class="qty-btn" (click)="decreaseAttempts(selectedSkill1?.id)">-</button>
      <span class="w-6 text-center mt-1">{{ additionalAttempts[selectedSkill1?.id] || 0 }}</span>
      <button class="qty-btn px-2" (click)="increaseAttempts(selectedSkill1?.id)">+</button>
      <span class="mt-1 px-2">
        x $5 <span class="italic per_attempt">per attempt</span>
      </span>
    </div>
    <h1 class="select_skill text-xs px-1 font-semibold">2. Select the first Skill *</h1>
    <select class="w-full border p-1 rounded mb-2 text-xs" [(ngModel)]="selectedSkill2">
      <option [ngValue]="null" disabled selected>Select a skill</option>
      <option  *ngFor="let skill of availableSkills" [ngValue]="skill" [disabled]="skill === selectedSkill1">{{
        skill.title }}</option>
    </select>
    <div class="flex items-center gap-3 text-xs">
      <span class="mt-1 px-2">Additional Attempts</span>
      <button class="qty-btn" (click)="decreaseAttempts(selectedSkill2?.id)">-</button>
      <span class="w-6 text-center mt-1">{{ additionalAttempts[selectedSkill2?.id] || 0 }}</span>
      <button class="qty-btn px-2" (click)="increaseAttempts(selectedSkill2?.id)">+</button>
      <span class="mt-1 px-2">
        x $5 <span class="italic per_attempt">per attempt</span>
      </span>
    </div>
  </div>

  <div class="p-4 flex justify-between items-center shadow">
    <div class="text-sm font-semibold">
      Subtotal: <span class="subtotal-text">${{ getTotal(bundle) }}</span>
    </div>
    <div class="space-x-2">
      <button mat-button (click)="closeDialog()" class="cancel-btn">Cancel</button>
      <button mat-button (click)="proceedNow()" class="proceed-btn">Proceed</button>
    </div>
  </div>
</div>