import { state } from "@angular/animations";
import { createFeatureSelector, createSelector } from "@ngrx/store";
import { FormModel } from "./dashboard.models";
import { getupcomingExam } from "./dashboard.actions";
import { upcomingExam } from "./dashboard.models/Upcomingexam";
import {DashboardState} from './dashboard.state';

export const DASHBOARD_STATE_NAME = 'DashboardModuleState';
export const APPLICATION_STATE_NAME = "ApplicationModuleState";

const getDashboardState = createFeatureSelector<DashboardState>(DASHBOARD_STATE_NAME);

export const selectDashboard = createSelector(getDashboardState,(state)=>{
  return state
})

export const selectForm = createSelector(getDashboardState,(state):FormModel[]=>{
    return state.form
})

export const selectUpcommmingExam = createSelector(getDashboardState, (state):upcomingExam[]=>{
    return state.upcomingExam
})

export const selectPersonForms= createSelector(getDashboardState, (state) =>{
    return state.personForms;
  })

  export const get_isCancelled = createSelector(getDashboardState, (state)=>{
    return state.isCancelled
  });
  export const getCandidateLogin$ = createSelector(getDashboardState, (state)=>{
    return state.LoginResponse
  });

  export const selectorShowRegisterExam$ = createSelector(getDashboardState, (state)=>{
    return state.showRegisterExamStatus
  });