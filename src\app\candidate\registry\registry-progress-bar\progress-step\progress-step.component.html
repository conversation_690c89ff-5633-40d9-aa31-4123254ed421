<!-- <div fxLayout="column" fxFlex class="name-container">
    <p class="mat-body-4">{{ reviewer }}</p>
</div> -->
<div fxLayout="column" fxFlex>
    <div fxLayout="row" fxFlex>
        <div>
            <mat-icon *ngIf="name == 'Approved'"
                [ngClass]="{'dot': name == 'Approved', 'selected-dot': name =='Drafted', 'incomplete-dot': name == 'Change Request'}">
                check_circle</mat-icon>
            <mat-icon *ngIf="name == 'Drafted'"
                [ngClass]="{'dot': name == 'Approved', 'selected-dot': name =='Drafted', 'incomplete-dot': name == 'Change Request'}">
                arrow_circle_left</mat-icon>
            <mat-icon *ngIf="name == 'Change Request'"
                [ngClass]="{'dot': name == 'Approved', 'selected-dot': name =='Drafted', 'incomplete-dot': name == 'Change Request'}">
                cancel</mat-icon>
            <mat-icon *ngIf="name == 'Rejected'"
                [ngClass]="{'dot': name == 'Approved', 'selected-dot': name =='Drafted', 'incomplete-dot': name == 'Rejected'}">
                cancel</mat-icon>
            <mat-icon *ngIf="name == 'Pending'"
                [ngClass]="{'dot': name == 'Approved', 'selected-dot': name =='Pending', 'incomplete-dot': name == 'Rejected'}">
                cancel</mat-icon>
            <mat-icon *ngIf="name == 'Yet to Receive'"
                [ngClass]="{'dot': name == 'Approved', 'selected-dot': name =='Yet to Receive', 'incomplete-dot': name == 'Rejected'}">
                cancel</mat-icon>
                <mat-icon *ngIf="name == 'Submitted'"
                [ngClass]="{'dot': name == 'Submitted', 'selected-dot': name =='Yet to Receive', 'incomplete-dot': name == 'Rejected'}">
                check_circle</mat-icon>
            <mat-icon *ngIf="name == 'Saved'"
                [ngClass]="{'dot': name == 'Saved', 'selected-dot': name =='Yet to Receive', 'incomplete-dot': name == 'Rejected'}">
                cancel</mat-icon>
        </div>
        <div fxLayout="row" fxFlex class="name-container font-semibold item-center pl-4 text-xs">
            {{ reviewer }}
        </div>

    </div>
    <div fxLayout="row" fxFlex>
        <div class="line-box" fxLayout="row">
            <div *ngIf="!isLast" class="line">
            </div>
        </div>
        <div fxLayout="column">
            <p class="mat-body-2 pl-4 text-xs font-normal">
                {{ name }}</p>
            <div class="pl-4 text-xs">
                <!-- <p class="mat-body-1 stepper-date">{{ actionOn |date:'d MMM, y | h:mm a' }}</p> -->
                <p class="mat-body-1 stepper-date">{{actionOnN}}</p>
            </div>
            <div>
                <div class="mat-body-3 pl-4 text-xs" [ngClass]="{'limitTextHeight': isReadMore ,onhover:onHover}"
                    (click)="showText()">
                    {{ comment }}
                    <p *ngIf="isReadMore">...</p>
                </div>
            </div>
        </div>
    </div>
</div>