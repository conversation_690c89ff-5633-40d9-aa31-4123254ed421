<div fxLayout="column" fxLayout.xs="column">
    <div class="image-bg intro-head" fxLayout="column" fxLayoutAlign="center center">
        <img [src]="imageSource" alt="error" class="error" />
    </div>

    <div class="mt-4" *ngIf="errorDetails.message == 'camera'" fxLayout="column" fxLayoutAlign="start start">
        <div fxLayout="column">
            <h4>
                Our system is not connecting to your camera. Please use the instructions below to correct this
                issue.
            </h4>
        </div>
        <div fxLayout="column">
            <div class="message" [innerHTML]="errorMessage"></div>
        </div>
    </div>

    <div class="mt-1" *ngIf="errorDetails.message == 'camera-1'" fxLayout="column" fxLayoutAlign="start start">
        <div fxLayout="column">
            <h4>
                Our system is not connecting to your camera, It has been used by other application. Please use
                the
                instructions below to correct this issue.
            </h4>
        </div>
        <div fxLayout="column">
            <div class="message" [innerHTML]="errorMessage"></div>
        </div>
    </div>

    <div class="mt-1" *ngIf="errorDetails.message == 'microphone'" fxLayout="column" fxLayoutAlign="start start">
        <div fxLayout="column">
            <h4>
                Our system is not connecting to your microphone. Please use the instructions below to correct
                this
                issue.
            </h4>
        </div>
        <div fxLayout="column">
            <div class="message" [innerHTML]="errorMessage"></div>
        </div>
    </div>

    <div class="mt-1" *ngIf="errorDetails.message == 'microphone-1'" fxLayout="column" fxLayoutAlign="start start">
        <div fxLayout="column">
            <h4>
                Our system is not connecting to your microphone, It has been used by other application. Please
                use
                the instructions below to correct this issue.
            </h4>
        </div>
        <div fxLayout="column">
            <div class="message" [innerHTML]="errorMessage"></div>
        </div>
    </div>
    <div class="mt-1" *ngIf="errorDetails.message == 'cookie'" fxLayout="column" fxLayoutAlign="start start">
        <div fxLayout="column">
            <h4>
                We have detected your system is blocking browser cookies. Please use the instructions below to
                correct the issue
            </h4>
        </div>
        <div fxLayout="column">
            <div class="message" [innerHTML]="errorMessage"></div>
        </div>
    </div>
    <div fxLayout="column" fxLayoutAlign="start start">
        <p>Please do <button class="btn btn-primary primary" (click)="recheck()"> System Recheck </button> once issue is
            fixed</p>
    </div>
</div>