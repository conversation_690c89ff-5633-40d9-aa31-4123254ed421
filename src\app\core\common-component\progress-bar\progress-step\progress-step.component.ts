import { Component, Input, OnInit } from '@angular/core';
import { PersonFormLog } from '../progress-bar.types';

@Component({
  selector: 'progress-step',
  templateUrl: './progress-step.component.html',
  styleUrls: ['./progress-step.component.scss']
})
export class ProgressStepComponent implements OnInit {

  isReadMore = false;
  onHover: boolean = false;

  showText() {
    if (this.onHover) {
      this.isReadMore = !this.isReadMore
    }
  }

  // @Input() comment: string;
  // @Input() name: any;
  // @Input() status :any
  // @Input() actionOn: Date;
  // @Input() reviewer: string;
  @Input() isLast: boolean = false;
  @Input() performlog: PersonFormLog
  constructor() { }

  ngOnInit(): void {
    if (this.performlog.comment?.length > 10) {
      this.onHover = true;
      this.isReadMore = true
    }
  }

}