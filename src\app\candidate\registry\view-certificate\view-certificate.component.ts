import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { forkJoin, Observable, Observer } from 'rxjs';
import { Store} from '@ngrx/store';
import { GlobalUserService } from 'src/app/core/global-user.service';
import {
  getSelectedCertificate,
  selectorLoadCertificatePath
} from './../state/registry.selectors';
import {
  CertificateModel,
  RegistryModuleState,
  RequestModel
} from './../state/registry.model';
import {
  loadCertificatePath,
} from '../state/registry.actions';
import { FormTypes } from 'src/app/core/Dto/enum';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'exai-view-certificate',
  templateUrl: './view-certificate.component.html',
  styleUrls: ['./view-certificate.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ViewCertificateComponent implements OnInit {

  StatusBoolean:boolean
  certificateLink: string = "";


  existingForm$: Observable<any>;
  existingForm = null;

  isSubmit: boolean = false;

  certificate: CertificateModel = null;
  request: RequestModel = null;


  constructor(
    public global: GlobalUserService,
    private store: Store<RegistryModuleState>,
    private router: Router,
    private http:HttpClient
  ) { }

  ngOnInit(): void {
    
    this.store.select(getSelectedCertificate).subscribe(data => {
      if (data) {
        this.certificate = data;
        this.subscribe();
      }
    })
     this.http.get(`${environment.baseUrl}registry/api/Certificate/show-duplicate/${Number(this.certificate.Id)}`).subscribe((data:{showDuplicate:boolean})=>{
         if(data){
          this.StatusBoolean = data.showDuplicate
         }
     })

  }

  subscribe() {
    this.store.dispatch(loadCertificatePath({Id:this.certificate.Id}))
    this.store.select(selectorLoadCertificatePath).subscribe((data: any) => {
      if (data) this.transformUrlsToDataUris(data);
    })

  }
  
  download() {
    const dashboard = document.getElementById('fullDiv');
    const options = {
      background: 'white',
      scale: 1.5
    };
    html2canvas(dashboard, options).then((canvas) => {
      var img = canvas.toDataURL("image/PNG");
      var doc = new jsPDF('l', 'mm', 'a4', true);
      const bufferX = 5;
      const bufferY = 5;
      const imgProps = (<any>doc).getImageProperties(img);
      const pdfWidth = doc.internal.pageSize.getWidth() - 2 * bufferX;
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      doc.addImage(img, 'PNG', bufferX, bufferY, pdfWidth, pdfHeight, undefined, 'FAST');
      return doc;
    }).then((doc) => {
      doc.save('Certificate.pdf');
    });
}
  getDuplicateForm() {
    this.router.navigate(['registry', 'duplicate-form', FormTypes.CertificateDuplicate, this.global.candidateId, this.certificate.EligibilityRouteId, this.global.stateId, this.certificate.Id,'']);
  }

  transformUrlsToDataUris(data: string) {
    let allIndexes = [];
    let allFoundUrls = [];
    while (true) {
      let nextOccurence = (allIndexes.length > 0) ? data.indexOf("src=", allIndexes[allIndexes.length - 1]) : data.indexOf("src=");
      if (nextOccurence == -1) break;
      else {
        allIndexes.push(nextOccurence + 4);
        let url = "";
        for (let i = nextOccurence + 5; data[i] != '"' && i < data.length; i++) url += data[i];
        if (url != "") allFoundUrls.push(url);
      }
    }
    let allDataUris = allFoundUrls.map((x: string) => this.getBase64ImageFromURL(x));
    if(allDataUris.length == 0) 
      this.certificateLink = data;
    forkJoin(allDataUris).subscribe((images: Array<string>) => {
      let allBlobs = images.map((x: string) => this.dataURItoBlob(x));
      forkJoin(allBlobs).subscribe((blobs: Array<Blob>) => {
        for (let i = 0; i < blobs.length; i++) {
          data = data.replace(
            allFoundUrls[i],
            window.URL.createObjectURL(new File(
              [blobs[i]], this.generateName(),
              { type: 'image/jpeg' }
            ))
          );
        }
        this.certificateLink = data;
      })
    })
  }

  getBase64ImageFromURL(url: string): Observable<string> {
    return new Observable((observer: Observer<string>) => {
      // create an image object
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.src = url;
      if (!img.complete) {
        // This will call another method that will create image from url
        img.onload = () => {
          observer.next(this.getBase64Image(img));
          observer.complete();
        };
        img.onerror = err => {
          observer.error(err);
        };
      } else {
        observer.next(this.getBase64Image(img));
        observer.complete();
      }
    });
  }

  getBase64Image(img: HTMLImageElement): string {
    // We create a HTML canvas object that will create a 2d image
    let canvas: HTMLCanvasElement = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx: CanvasRenderingContext2D = canvas.getContext('2d');
    // This will draw image
    ctx.drawImage(img, 0, 0);
    // Convert the drawn image to Data URL
    const dataURL: string = canvas.toDataURL('image/png');
    return dataURL.replace(/^data:image\/(png|jpg);base64,/, '');
  }

  dataURItoBlob(dataURI: string): Observable<Blob> {
    return new Observable((observer: Observer<Blob>) => {
      const byteString: string = window.atob(dataURI);
      const arrayBuffer: ArrayBuffer = new ArrayBuffer(byteString.length);
      const int8Array: Uint8Array = new Uint8Array(arrayBuffer);
      for (let i = 0; i < byteString.length; i++) {
        int8Array[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([int8Array], { type: 'image/jpeg' });
      observer.next(blob);
      observer.complete();
    });
  }
  generateName(): string {
    const date: number = new Date().valueOf();
    let text = '';
    const possibleText =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 5; i++) {
      text += possibleText.charAt(
        Math.floor(Math.random() * possibleText.length)
      );
    }
    // Replace extension according to your media type like this
    return date + '.' + text + '.jpeg';
  }
}
