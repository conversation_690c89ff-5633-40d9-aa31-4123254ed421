.exai-layout-ares {
  --footer-background: var(--background-base);
  --sidenav-background: var(--background-base);
  --toolbar-background: var(--background-base);
  --sidenav-color: var(--text-color);
  --sidenav-item-background-active: var(--background-card);
  --sidenav-item-color: var(--text-color);
  --sidenav-item-color-active: var(--text-color);
  --sidenav-item-dropdown-background: var(--background-base);

  --sidenav-item-dropdown-background-hover: var(--background-hover);
  --secondary-toolbar-background: var(--background-base);

  --sidenav-toolbar-background: var(--background-base);

  --secondary-toolbar-height: 64px;

  .sidenav {
    border: none;

    exai-sidenav-item {
      .item {
        width: unset;
        @apply rounded mx-4 mr-10 my-1;

        &.active {
          @apply shadow;
        }
      }

      .item-level-0 > .item {
        @apply my-1;
      }

      .item-level-1 .item.active {
        @apply shadow-none font-medium;
        font-weight: 500;

        .item-label {
          @apply text-black;
        }
      }

      .item-level-1 .item:hover .item-label {
        @apply text-black;
      }
    }
  }

  .sidenav-collapsed .sidenav exai-sidenav-item .item {
    margin-right: 14px;
  }

  exai-secondary-toolbar {
    @apply rounded-b overflow-hidden;

    .secondary-toolbar {
      @apply shadow-none border-t-0 -mb-gutter;
    }
  }

  exai-footer {
    @apply rounded-t overflow-hidden;
  }

  .exai-page-layout-header {
    background: var(--background-base);
    color: var(--text-color);

    exai-breadcrumbs {
      .text-hint {
        color: var(--text-hint) !important;
      }
    }
  }

  &.exai-style-default {
    // --sidenav-item-background-active: theme("backgroundColor.primary.DEFAULT");
    --sidenav-item-background-active: #209e91;
    --sidenav-item-color-active: theme("textColor.primary-contrast");
    --sidenav-item-dropdown-color-hover: var(--text-color);
    // --sidenav-item-icon-color-active: theme("textColor.primary-contrast");
    --sidenav-item-icon-color-active: white;
  }
  &.exai-style-purple {
    // --sidenav-item-background-active: theme("backgroundColor.primary.DEFAULT");
    --sidenav-item-background-active: #0076C1;
    --sidenav-item-color-active: theme("textColor.primary-contrast");
    --sidenav-item-dropdown-color-hover: var(--text-color);
    // --sidenav-item-icon-color-active: theme("textColor.primary-contrast");
    --sidenav-item-icon-color-active: white;
  }
}
