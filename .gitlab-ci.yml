#image: ubuntu:18.04
image: node:14.17.3
stages:
  - Build
  #- Test
  - Deploy

variables:
    GIT_STRATEGY: clone
    GIT_SUBMODULE_STRATEGY: normal
    GIT_SUBMODULE_UPDATE_FLAGS: --init --remote 
   
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/
    - dist/

Build:
  stage: Build
#   tags: 
#    - credentia
  script:
     
    - git submodule sync 
    - git submodule init
    - git submodule update 
    - npm i
    - npm install -g @angular/cli@11.1.0
    - ng build --c dev --base-href /candidate/

  artifacts:
    expire_in: 1 week
    paths:
      - dist

  only:
    - UAT    


Deploy-S3:
  stage: Deploy
#   tags: 
#    - credentia
  before_script:
    - node --version
    - npm install -g @angular/cli@11.1.0
    - ng new ibui --routing
    - apt-get update
    - apt-get install awscli -y
    - aws configure set aws_access_key_id ********************; aws configure set aws_secret_access_key VxaSL8t7G+sZ6n6Hq9UY+BgdRrXv3/wqUNnL3ufD; aws configure set default.region us-east-2

  script:
    - aws --version
    - ls
    - cp -r dist/app/shell/* dist/app
    - rm -r dist/app/shell
    - aws s3 rm s3://credentia-uat-candidate --recursive
    - aws s3 cp ./dist/app s3://credentia-uat-candidate --recursive
    - echo "Invalidate index.html"
    - aws cloudfront create-invalidation --distribution-id E2I4GIX8I7RGK1 --paths /index.html
  # environment:
  #   name: Dev
  only:
    - UAT


