<ng-template #sidenavRef>
    <exai-sidenav [collapsed]="sidenavCollapsed$ | async"></exai-sidenav>
</ng-template>

<ng-template #toolbarRef>
    <exai-toolbar [hasShadow]="toolbarShadowEnabled$ | async" [mobileQuery]="!(isDesktop$ | async)" class="exai-toolbar">
    </exai-toolbar>
</ng-template>

<ng-template #loading>
    <ng-container *ngIf="loadingObs | async">
        <div class="card-div">
            <div class="spinner w-full">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
        </div>

    </ng-container>

</ng-template>

<ng-template #footerRef >
    <exai-footer *ngIf="isFooterVisible$ | async" class="exai-footer" [chatPanelRef]="chatPanel" [chatPanelExpanded]="chatPanelExpanded" [chatPanelExpanded$]="chatPanelExpanded$"></exai-footer>
</ng-template>

<ng-template #quickpanelRef>
    <exai-quickpanel></exai-quickpanel>
</ng-template>

<exai-layout [footerRef]="footerRef" [quickpanelRef]="quickpanelRef" [sidenavRef]="sidenavRef" [toolbarRef]="toolbarRef" [loading]="loading">
</exai-layout>

<!-- <exai-config-panel-toggle (openConfig)="configpanel.open()"></exai-config-panel-toggle> -->

<!-- CONFIGPANEL -->
<exai-sidebar #configpanel [invisibleBackdrop]="true" position="right">
    <exai-config-panel></exai-config-panel>
</exai-sidebar>

<ng-template #chatPanel>
    <exai-candidate-chat (closeChatConnection)="chatPanelExpanded.next(false)"></exai-candidate-chat>
</ng-template>
<!-- END CONFIGPANEL -->