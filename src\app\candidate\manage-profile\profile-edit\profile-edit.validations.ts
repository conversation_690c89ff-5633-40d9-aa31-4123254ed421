export class ProfileEditValidation{
  public  validations = {
        'zipCode': [
          { type: 'required', message: 'Zip Code is required' },
          { type: 'pattern', message: 'Must contain 5 digits' }
        ],
        'city': [
          { type: 'pattern', message: 'City should not contain numbers' },
          { type: 'required', message: 'City is required' },
        ],
        'phoneNumber': [
          { type: 'required', message: 'Phone number is required' },
          { type: 'minlength', message: 'Enter a valid phone number' },
          { type: 'maxlength', message: 'Enter a valid phone number' }
        ],
        'emailId': [
          { type: 'pattern', message: 'email must be valid' }
        ],
        'address': [
          { type: 'required', message: 'Address is required' }
        ],
        'gender': [
          { type: 'required', message: 'Gender is required' }
        ],
      };
}