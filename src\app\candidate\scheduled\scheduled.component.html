<div class="px-gutter pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
        <div class=" " fxLayout="column">
            <h5 class="titleFont"><strong>Exam Schedule</strong></h5>
            <!-- </div> -->
            <!-- <div class="-mt-1"> -->
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
                [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
        <div class="py-2 -mt-10" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
            <div class="flex justify-end" fxLayout="">
                <button mat-button color="var(--text-color2)" class="btn-2 t-xs mr-2" *ngIf="cart.length > 0 && !NotAllowScheduleforCheating" 
                (click)="clearCart()">
               Clear Cart
            </button>
                <button mat-button color="var(--text-color2)" class="add-new t-xs" *ngIf="timeslot" [matTooltip]="helpText"
                (click)="clickRoute()" [disabled]="disableSubmit ||  NotAllowScheduleforCheating">
                    Register For Exam
                </button>
                
               
            </div>
        
        </div>

        <div class="mb-1 content1 " *ngIf="exam" exaiContainer>
            <div class="" *ngIf="exam" fxLayoutGap="6px grid">
                <div fxLayout="row wrap" fxLayoutGap="10px grid">
                    <div [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%" *ngFor="let item of listExam">
                        <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                            <div class="bg-color px-4 py-3">
                                <div class="flex justify-between title-hed" fxLayout="row">
                                    <!-- <div class="t-xs title-hed"> -->
                                    <strong>{{ item.examName }}</strong>
                                    <!-- </div> -->
                                </div>
                                <div fxLayout="row" class="t-xs state-elig pt-1">
                                    {{ item.eligibilityRouteName }}
                                </div>
                            </div>
                            <div fxLayout="column">
                                <h4 class="px-4 status t-xs pt-2">Current Status</h4>
                                <div class="small-container -mt-2 pb-2" fxFlexFill>
                                    <img src="{{ statusIcon[item.examStatus] || 'assets/img/Icons/approved.svg' }}" class="inline iconSize ml-4" />
                                    <span class="t-xs ml-2 ml-2"
                                          [ngStyle]="item.examStatus === 'Failed' ? {'color': 'red'} : null"
                                          [ngClass]="item.examStatus !== 'Failed' ? 'active2' : ''">
                                      {{ item.examStatus }}
                                    </span>
                                    <div class="t-xs mt-0 status1 pb-3 pt-1 cardHeight ml-4">
                                        <!-- <span class="fontStyle">{{item.examDateTimeUtc | date }} / {{item.examDateTime | date:"HH:mm"}}kkjk</span> -->
                                        <span class="fontStyle pb-3 pt-1">{{ item.registeredDateTime }}</span>

                                        <div *ngIf="enableTestCenterDirections(item)" (click)="openTestCenterDirections(item)" class="pt-2 active2 onhover" gdColumns="1fr">
                                            Test Center Directions
                                        </div>

                                        <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                            gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                                            gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                                <div *ngIf="item.mode=='Online' " class="h4 status t-xs">Exam Mode</div>
                                                <div  *ngIf="item.mode=='Test Center'"class="h4 status t-xs">Test Center Name</div>
                                            </div>

                                            <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                                <div class="status t-xs pb-1">Exam Date</div>
                                            </div>
                                            <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                                <div class="status t-xs pb-1">Exam Time</div>
                                            </div>
                                        </div>
                                        <div class="pb-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                            gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr "
                                            gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                                <div *ngIf="item.mode =='Online'" class="h4 status1 t-xs">{{item.mode}}</div>
                                                <div  *ngIf="item.mode=='Test Center'" class="h4 status1 t-xs">{{item.testCenterDetails.testCenterName}}</div>
                                            </div>
                                            <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                                 <div class="status1 t-xs -mt-4"> {{ item.examDateTime | date: "MM/dd/yyyy":'+0000' }}</div>
                                            </div>
                                            <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                                <div class="h4  status1 t-xs ">
                                                    {{item.examDateTime | date:'shortTime':'+0000' }} {{item.timeZoneAbbreviation}}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- <div class="flex" gdColumn="1 / 2" gdColumn.lt-md="1"
                                        gdColumn.lt-sm="1/2">
                                        <div class="t-xs status" *ngIf="item.examStatusId == 64">
                                            Comments:
                                          </div>
                                    </div>
                                    <div fxLayout="row" fxLayoutAlign="" fxLayoutGap="8px"
                                    *ngIf="item.examStatusId == 64">
                                    <div class="status3 t-xs minimise" matTooltip="{{item.examCancelComments}}">{{ item.examCancelComments }}</div>
                                </div> -->


                                        <div class="flex justify-end" gdColumn="2 / -1" gdColumn.lt-md="1"
                                            gdColumn.lt-sm="1/2">
                                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                                                *ngIf="item.examStatus == 'Waiting for Proctor'">
                                            </div>
                                            <div  class="pr-2 pb-1" fxLayout="row" fxLayoutAlign="end center"
                                                fxLayoutGap="8px" *ngIf="item.allowReschedule && item.examStatusId == 1 && !NotAllowScheduleforCheating">
                                                <button mat-button color="var(--text-color2)"
                                                    class="btn-4 font-bold t-xs mt-2" (click)="getConfirmation(item)"
                                                    *ngIf="item.cancel">
                                                    Cancel
                                                </button>
                                                <button mat-button color="var(--text-color2)"
                                                    class="btn-4 font-bold t-xs mt-2" (click)="reschedule(item)"
                                                    *ngIf="item.reschedule">
                                                    Reschedule
                                                </button>
                                            </div>
                                            <div   class="pr-2 pb-1" fxLayout="row" fxLayoutAlign="end center"
                                            fxLayoutGap="8px" *ngIf=" item.examStatusId === 9 && item.mode ==='Test Center' && item.allowReschedule && !NotAllowScheduleforCheating">
                                            <button mat-button color="var(--text-color2)"
                                                class="btn-4 font-bold t-xs mt-2" (click)="reschedule(item)"
                                                >
                                                Reschedule
                                            </button>
                                        </div>
                                        <div  class="pr-2 pb-1" fxLayout="row" fxLayoutAlign="end center"
                                        fxLayoutGap="8px" *ngIf="item.examStatusId === 9 && RescheduleButton && item.mode==='Online' && item.allowReschedule && !NotAllowScheduleforCheating">
                                        <button mat-button color="var(--text-color2)"
                                            class="btn-4 font-bold t-xs mt-2" (click)="reschedule(item)"
                                            >
                                            Reschedule
                                        </button>
                                    </div>
                                    <div  class="pr-2 pb-1" fxLayout="row" fxLayoutAlign="end center"
                                    fxLayoutGap="8px" *ngIf="rescheduleallow.includes(item.examStatusId) && item.allowReschedule && !NotAllowScheduleforCheating">
                                    <button mat-button color="var(--text-color2)"
                                        class="btn-4 font-bold t-xs mt-2" (click)="reschedule(item)"
                                        >
                                        Reschedule
                                    </button>
                                </div>
                                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" *ngIf="item.allowPayment && !NotAllowScheduleforCheating">
                                                <button mat-button color="var(--text-color2)"
                                                    class="btn-4 font-bold t-xs mt-2 mr-8" (click)="payNow()">
                                                    Pay Now
                                                </button>
                                            </div>

                                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" *ngIf="item.examStatusId === this.global.scheduling_error && item.allowScheduleAgain && !NotAllowScheduleforCheating">
                                                <button mat-button color="var(--text-color2)"
                                                    class="btn-4 font-bold t-xs mt-2 mr-8" (click)="reschedule(item)">
                                                    Schedule Again
                                                </button>
                                            </div>
                                        </div>
                                        <!-- added button to report -->

                                        <div class="flex justify-end" gdColumn="2 / -1" gdColumn.lt-md="1"
                                            gdColumn.lt-sm="1/2">
                                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                                                class="-mt-1" *ngIf="item.examStatusId == this.global.Exam_Completed && !NotAllowScheduleforCheating">
                                                <button *ngIf="item.isGrievanceFormSubmitted == false && item.allowGrievance"
                                                    (click)="viewGrievance(item, item.personFormId)" mat-button
                                                    color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2" >
                                                    Report Grievance
                                                </button>
                                                <button *ngIf="item.allowShowResult && !NotAllowScheduleforCheating"
                                                    (click)="getConfirmation1(item)" mat-button
                                                    color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2">
                                                    Show Result
                                                </button>
                                                <button mat-button color="var(--text-color2)"
                                                    class="btn-4 font-bold t-xs mt-2 mr-2"
                                                    *ngIf="item.isGrievanceFormSubmitted == true && item.allowGrievance && !NotAllowScheduleforCheating"
                                                    (click)="viewGrievance(item, item.personFormId)">
                                                    View Grievance
                                                </button>
                                            </div>
                                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                                            class="-mt-1 " *ngIf="item.examStatusId ==  this.global.no_Show">
                                                <button  *ngIf="item.allowExcuseAbsence && item.excuseabsencechangerequest != true &&  item.excuseabsencedrafted != true && !NotAllowScheduleforCheating"
                                                (click)="absence(item,item.personFormId)" mat-button
                                                color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2" >
                                                Submit Excused Absence
                                            </button>
                                            <button  *ngIf="item.allowExcuseAbsence && (item.excuseabsencechangerequest == true || item.excuseabsencedrafted == true) && !NotAllowScheduleforCheating"
                                            (click)="absence(item,item.personFormId)" mat-button
                                            color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2" >
                                         Edit Excused Absence
                                        </button>
                                          
                                        <button mat-button color="var(--text-color2)"
                                                    class="btn-4 font-bold t-xs mt-2 mr-2"
                                                    *ngIf="item.isExcuseAbsenceSubmitted && !NotAllowScheduleforCheating"
                                                    (click)="absence(item, item.personFormId)">
                                                    View Absence
                                                </button>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
        <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
            <div class="card shadow-none cardBorder h-full" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                fxFlex="auto" *ngIf="details">
                <div class="flex justify-center pt-8">
                    <img class="justify-center w-3/12" src="assets/img/register-exam1.svg" /><br />
                </div>
                <div class="content1 pb-6">
                    <section class="mr-5 ml-5">
                        <div class="bg-color">
                            <span class="welc-note flex justify-center text-center text-xs mx-4 mt-4">
                                <span class="pt-3 pb-2">
                                    {{ errors }}
                                </span>
                            </span>
                            <br />
                        </div>
                    </section>
                    <div class="flex justify-center -mt-5 mb-4 t-xs">
                        <button *ngIf="!NotAllowScheduleforCheating" mat-button color="primary" class="add-new text-xs" (click)="clickRoute()"
                            [matTooltip]="helpText" [disabled]='bothApproved'>
                            Register For Exam
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- <img src="{{ statusIcon[item.examStatus] }}" class="inline iconSize ml-4" />
                                    <span class="t-xs ml-2 active2 ml-2"> {{ item.examStatus }}</span>
                                     -->