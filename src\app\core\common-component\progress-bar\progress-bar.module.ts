import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FlexLayoutModule } from "@angular/flex-layout";
import { PageLayoutModule } from "src/@exai/components/page-layout/page-layout.module";
import { IconModule } from "@visurel/iconify-angular";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatInputModule } from "@angular/material/input";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatRippleModule } from "@angular/material/core";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { MatSelectModule } from "@angular/material/select";
import { MatCheckboxModule } from "@angular/material/checkbox";

import { ProgressBarComponent } from "./progress-bar.component";
import { ProgressStepComponent } from "./progress-step/progress-step.component";
import { MatStepperModule } from "@angular/material/stepper";

import { PrgBarTemplateComponent } from './prg-bar-template/prg-bar-template.component';
import {MatExpansionModule} from '@angular/material/expansion';
@NgModule({
  declarations: [
    ProgressBarComponent,
    ProgressStepComponent,
    PrgBarTemplateComponent,
  ],
  imports: [
    CommonModule,
    FlexLayoutModule,
    PageLayoutModule,
    IconModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRippleModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatStepperModule,
    MatIconModule,
  ],
  exports:[
    ProgressBarComponent,
    ProgressStepComponent,
    PrgBarTemplateComponent]
})
export class ProgressBarModule {}
