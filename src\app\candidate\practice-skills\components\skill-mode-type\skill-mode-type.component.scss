.dialog-body {
  border-radius: 4px;
  border-width: 0.5px;
  opacity: 1;
  border-color: #e5e5e5;
  background-color: #fafafa;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
}

/* Add this for selected state */
.dialog-body.selected {
  background-color: #e5f1f9; /* light blue background */
  border-color: #2563eb; /* blue border */
}

.select_text {
  color: #1b75bb;
}

.link-text {
  color: #1b75bb;
}
