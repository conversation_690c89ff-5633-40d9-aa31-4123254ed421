import { Form } from "@angular/forms";
import { createAction, props } from "@ngrx/store";
import { PersonDetails } from "src/app/core/Dto/persondetail";
import { PersonFormDetailsModel } from "src/app/core/Dto/personform-details.model";
import { PersonFormModel } from "src/app/core/Dto/personform.model";
import { PersonForm } from "../../application/application.types";
import { upcomingExam } from "../../grievance-form/state/grievance.model";


export const getFormJSON = createAction('[Froms] GET form JSON');

export const gotFormJSON = createAction('success', props<{ value: Form }>());

export const getPersonDetails = createAction('Got Details');

export const gotPersonDetails = createAction('success of personForm', props<{ value: any }>());

export const getImageUrl = createAction('success of Image Url', props<{ value: any }>());

export const passPersonDetail = createAction('get person data', props<{ personDetails: PersonDetails }>());

export const successMessage = createAction('Pass person Detail', props<{ value: any }>());

export const getSubmitDemographicData = createAction('get submit demographic data');

export const submitDemographicData = createAction('submit demographic data', props<{ SubmitDemographic: any }>());

export const submitedDemographicData = createAction('submited demographic data', props<{ savedResId: number }>());

export const getPersonForm = createAction('get PersonForm');

export const fetchPersonData = createAction('success of correctionForm', props<{ value: Array<PersonForm> }>());

export const getCorrectionFormData = createAction('get Correction for edit correction', props<{ value: number }>());

export const passCorrectionFormData = createAction('passing id to correctionForm', props<{ value: PersonFormModel[] }>());

export const deletedCorrectionForm = createAction('deleting the correction form', props<{ correctionForm: any }>());

export const deletedDemographicForm = createAction('passing the data to delete the form', props<{ correctionForm: any }>());

export const getPersonFormLogs = createAction('getting Correction Logs', props<{ personFormLogs: any }>());

export const isPersonFormLogs = createAction('passing the ID to get Correction logs', props<{ personFormLogs: any }>());

// export const disableProfile = createAction('Disable manage profile', props<{ roleId: any, formTypeId: number }>());

// export const disableProfileResponse = createAction('Disable manage profile response', props<{ disableEditProfile: boolean }>());

// export const disableProfile =createAction('[Profile] GET Profile',
//   props<{roleId: any, formTypeId: number}>())
//   export const disableProfileResponse = createAction('[editProfile] GOT editProfile',
//   props<{disableEditProfile:boolean}>())

export const getupcomingExam = createAction('[Froms] GET upcomingExam',
    props<{ candidateId: number }>())

export const gotupcomingExam = createAction('[Froms] GOT upcomingExam',
    props<{ upcomingExam: upcomingExam[] }>())

export const clearDemographicData = createAction('[CLEAR Demographic response form] Cleared Demographic response form State');

export const clearPersonFormlogsState = createAction('[CLEAR PERSON FORM LOGS]');

export const resetUpdateProfile = createAction('[RESET UPDATE PROFILE]');

