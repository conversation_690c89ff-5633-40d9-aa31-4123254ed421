.text {
  color: var(--text-color1);
}
.text1 {
  color: var(--text-edit);
}
.error {
}

.cardBorder {
  border: var(--border);
  border-radius: var(--border-radius);
  // max-width: 47vh;
  // @screen xl{
  //   max-width: 47vh;
  // }
  // @screen lg{
  //   max-width: 47vh;
  // }
  // @screen md{
  //   max-width: 47vh;
  // }
  // max-height: fit-content;
  // overflow: hidden;
  // overflow: auto;
  // height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 80px);

  // @screen sm {
  //   max-width: 51vh!important;
  // }
  // @screen xs {
  //   max-width: 51vh!important;
  // }
  // @screen xxs {
  //   max-width: 43vh!important;
  // }
}

.state {
  color: var(--text-input);
}


.mat-form-field {
  font-size: 10px !important;
}

::ng-deep {
  .eligible.mat-form-field-prefix .mat-icon,
  .mat-form-field-suffix .mat-icon {
    font-size: 138%;
    // line-height: -1.875;
    margin-right: -11px;
    // padding-bottom: 2px;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline {
    display: flex;
    position: absolute;
    top: 0.25em;
    left: 0;
    right: 0;
    bottom: -5px !important;
    pointer-events: none;
  }
  .mat-select-arrow {
    margin: 5px 0 0 0 !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: var(--text-color1);
  }
  .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: var(--text-color2);
  }
  .mat-form-field-appearance-outline
    .mat-form-field-outline-thick
    .mat-form-field-outline-start {
    border-width: 1px;
  }
  .mat-form-field-appearance-outline
    .mat-form-field-outline-thick
    .mat-form-field-outline-gap {
    border-width: 1px;
  }

  .mat-form-field-appearance-outline.mat-form-field-disabled
    .mat-form-field-outline {
    color: var(--text-input);
    max-height: 32px;
    bottom: 1px;
  }
  .edit
    .mat-form-field-appearance-outline
    .mat-form-field-outline-thick
    .mat-form-field-outline-start,
  .mat-form-field-appearance-outline
    .mat-form-field-outline-thick
    .mat-form-field-outline-end,
  .mat-form-field-appearance-outline
    .mat-form-field-outline-thick
    .mat-form-field-outline-gap {
    border-width: 1px !important;
  }
}

.mat-input-element {
}
::ng-deep .mat-form-field-outline-start {
  border-radius: 3px 0 0 3px !important;
  // min-width: 28px !important;
}

::ng-deep .mat-form-field-outline-end {
  border-radius: 0 3px 3px 0 !important;
}

.border-button {
  border: 1px solid var(--text-color2);
}
.mat-button {
  min-width: 77px !important;
}

::ng-deep {
  .country-selector {
    width: 0px !important;
    text-align: start;
    // padding-left:2px ;
    opacity: 1 !important;
  }
  .mat-button {
    min-width: 68px !important;
  }
  .phNumberInput.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-outline-gap {
    border-top-color: currentColor;
  }

  .phNumberInput.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label,
  .phNumberInput.mat-form-field-appearance-outline.mat-form-field-can-float
    .mat-input-server:focus
    + .mat-form-field-label-wrapper
    .mat-form-field-label {
    transform: none;
    width: 0;
  }
}

.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: 0.1rem 0.5rem;
  @screen sm {
    margin-right: 1rem;
  }
  @screen xs {
    margin-right: 1rem;
  }
}

::ng-deep {
  .mat-menu-panel {
    padding: 8px 8px 8px 8px;
    max-width: 416px !important;
    max-height: calc(100vh - 55vh) !important;
    // overflow-x: hidden !important;
  }
}
.titleFont {
  font-size: 1.1em;
  // font-weight: bolder;
  font-family: "Roboto", sans-serif;
}