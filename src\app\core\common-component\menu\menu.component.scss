.menu-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 20px;
  line-height: 24px;
  cursor: pointer;
  color: #555;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.menu-toggle:hover {
  background-color: #d1e4f0;
  padding: 1px 2px 1px 1px;
}

.menu_pop {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.menu_pop_item {
  color: #4b5563;
  cursor: pointer;
  border-bottom: 1px solid #c1c3c7;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
    color: var(--text-color2);
  }
}
