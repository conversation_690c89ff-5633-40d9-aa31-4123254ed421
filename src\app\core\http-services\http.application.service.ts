import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { eligibilityRoute, eligibilityRouteDetails, Form, PersonForm, PersonFormLog } from "src/app/candidate/application/application.types";
import { GlobalUserService } from "../global-user.service";
import { URL } from 'src/app/core/url';
import { SaveFormModel } from "../Dto/save-form.model";

@Injectable({
    providedIn: "root",
})
export class HttpApplicationService {
  
    constructor(private httpClient: HttpClient,
        private global: GlobalUserService) {
    }
  
    public getEligibilityRoutesByState(stateId: number): Observable<eligibilityRoute[]> {
        return this.httpClient.get<Array<eligibilityRoute>>(URL.BASE_URL + `eligibilityroute/listByState?stateId=${stateId}`);        
    }

    public getEligibilityRouteDetails(eligibilityRouteId: number): Observable<eligibilityRouteDetails> {
       return this.httpClient.get<eligibilityRouteDetails>(URL.BASE_URL + `eligibilityroute/detail/${eligibilityRouteId}`);
    }

    public getFormDetails(formTypeID: number,eligibilityID: number): Observable<Form> {
      return  this.httpClient.get<Form>(`${URL.BASE_URL}form/formsbyformtypeid?formTypeId=${formTypeID}&eligibilityId=${eligibilityID}`);        
    }

    public saveApplicationForm(saveFormModel : SaveFormModel) {
      return  this.httpClient.post<any>(`${URL.BASE_URL}form/savepersonform`,saveFormModel)
    }

    public getApplicationFormLogs(code: string): Observable<PersonFormLog[]> {
        return this.httpClient.get<PersonFormLog[]>(`${URL.BASE_URL}form/personformlogs?code=${code}`);
    }
    
    public getPersonForms(candidateId: string, formTypeIds: string[]): Observable<PersonForm[]> {
        let params = {'candidateId': candidateId, 'formTypeIds': formTypeIds};
        return this.httpClient.get<PersonForm[]>(URL.BASE_URL + `form/personform`, { params });
    }

    public getPersonFormsByCode(code: string) {
        return this.httpClient.get<any>(URL.BASE_URL + `form/personform/list?code=${code}`);        
    }
}