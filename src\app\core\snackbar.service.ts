import { Injectable } from "@angular/core";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { SnackbarComponent } from "./common-component/snackbar/snackbar.component";
import { Observable, throwError } from "rxjs";
import { environment } from "src/environments/environment";
import { catchError } from "rxjs/operators";
import { HttpClient } from "@angular/common/http";

@Injectable({
  providedIn: "root",
})
export class SnackbarService {
  constructor(private _snackbar: MatSnackBar,private http:HttpClient) {}

  callSnackbaronSuccess(msg) {
    this.openSnackBar(msg, "success-snackbar");
  }
  callSnackbarsonSuccess(msg) {
    this.openSnackBar(msg, "success-snackbars");
  }
  callSnackbaronError(msg) {
    this.openSnackBar(msg, "error-snackbar");
  }
  callSnackbaronWarning(msg) {
    this.openSnackBar(msg, "warning-snackbar");
  }

  getStateAgeLimits(clientStateCode: string): Observable<any[]> {
    var url = `${environment.baseUrl}login/StateExamAgeLimit?clientStateCode=${clientStateCode}`;
    return this.http.get<any[]>(url).pipe(
      catchError((error: any) => throwError(error))
    );
  }
  openSnackBar(message: string, panelClass: string) {
    this._snackbar.openFromComponent(SnackbarComponent, {
      data: message,
      panelClass: panelClass,
       duration: 60000,
    });
  }
}
