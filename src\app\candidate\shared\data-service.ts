import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { take } from 'rxjs/operators';

@Injectable()
export class DataService {

  private personFormSource = new BehaviorSubject('0');
  currentpersonForm = this.personFormSource.asObservable();

  constructor() { }

  public setPersonForm(id: string): void {
    this.personFormSource.next(id);
  }

  public getPersonForm() : Observable<string> {
   return this.personFormSource.pipe(take(1));
  }
}