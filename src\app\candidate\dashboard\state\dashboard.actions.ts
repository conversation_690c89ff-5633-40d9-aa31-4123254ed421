import { createAction, props } from "@ngrx/store";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { PersonForm } from "../../application/application.types";
import { FormModel } from "./dashboard.models";
import { upcomingExam } from "./dashboard.models/Upcomingexam";
import { candidateLoginResponse, v1CandidateLogin } from "./dashboard.models/v1candidate";
import { DashboardData } from "./dashboard.state";

export const getDashboardData = createAction('[DASHBOARD] GET DASHBOARD DATA');

export const gotDashboardData = createAction('[DASHBOARD] GOT DASHBOARD DATA',
  props<{ dashboardData: DashboardData }>())

export const getForm = createAction('[Forms] GET form',
  props<{ candidateId: number, formTypeId1:number,formTypeId2:number }>())

export const gotForm = createAction('[Forms] GOT form',
  props<{ form: FormModel[] }>())


export const getupcomingExam = createAction('[Froms] GET upcomingExam',
  props<{ candidateId: number }>())

export const gotupcomingExam = createAction('[Froms] GOT upcomingExam',
  props<{ upcomingExam: upcomingExam[] }>())

export const getPersonForms = createAction('[personforms] GET personforms dashboard',
  props<{ candidateId: number, formTypeId1: number, formTypeId2: number }>())

export const gotPersonForms = createAction('[personforms] GOT personforms dashboard',
  props<{ personForms: PersonForm[] }>())

export const cancelExam = createAction('[Cancel] cancelExam',
  props<{ examScheduleId: number, candidateId: number }>())


export const Examcancelled = createAction('[Cancel] examCancelled',
  props<{ isCancelled: boolean }>())

  //v1 api integration for start exam
export const getCandidateLogin = createAction(' GET Candidate Login',
// props<{ APIKey: string, SecretKey: string, email: string, password:string }>())
props<{ candidateLoginInputs: v1CandidateLogin }>())

export const gotCandidateLogin = createAction('GOT Candidate Login',  props<{ Result: candidateLoginResponse }>());

export const getShowRegisterExam = createAction('[GET SHOW REGISTERED EXAM]', props<{ personTenantRoleId: number }>())

export const gotShowRegisterExam = createAction('[GOT SHOW REGISTERED EXAM]', props<{ data: ShowRegisterExamModel }>())