import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { switchMap, map, take } from "rxjs/operators";
import * as GrievanceActions from './grievance.actions';
import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { cancelExam, Examcancelled, getupcomingExam } from "./grievance.actions";
import { FormTypes } from "src/app/core/Dto/enum";
import { URL } from 'src/app/core/url';
import { HttpService } from "src/app/core/http-services/http.service";
import { ReportGrievance } from "./grievance.model";
@Injectable({
  providedIn: 'root'
})
export class GrievanceEffects {
  constructor(private actions$: Actions, private http: HttpClient,
    private global: GlobalUserService,
    private snackbar: SnackbarService,
    private httpService: HttpService) { }

  loadAll$ = createEffect(() => this.actions$.pipe(
    ofType(GrievanceActions.loadAll),
    switchMap(
      (action) => {
        return this.http
          .get<any>(`${URL.BASE_URL}form/personform?candidateId=${this.global.candidateId}&formTypeId=${FormTypes.Grievance}`)
          .pipe(
            map(data => GrievanceActions.loadAllSuccess({ data })),
            take(1)
          );
      })
  ));

  loadReportGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(GrievanceActions.loadReportGrievance),
    switchMap(
      (action) => {
        return this.httpService.loadReportGrievanceForms().pipe(
          map((data: ReportGrievance) => {
            return GrievanceActions.loadReportGrievanceSuccess({ data });
          }),
          take(1)
        );
      })
  ));

  saveReportGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(GrievanceActions.requestReportGrievance),
    switchMap(
      (action) => {
        return this.http.post(`${URL.BASE_URL}form/savepersonform`, action.params)
          .pipe(
            map(data => {
              if (data) {
                this.snackbar.callSnackbaronSuccess("Submitted successfully")
              }
              return GrievanceActions.postReportGrievanceSuccess({ data })
            }),
            take(1)
          )
          ;
      })
  ));

  saveDraftReportGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(GrievanceActions.requestSaveDraftReportGrievance),
    switchMap(
      (action) => {
        return this.http.post(`${URL.BASE_URL}form/savepersonform`, action.params)
          .pipe(
            map(data => {
              if (data) {
                this.snackbar.callSnackbaronSuccess("Saved successfully")
              }
              return GrievanceActions.postSaveDraftReportGrievanceSuccess({ data })
            }),
            take(1)
          );
      })
  ));

  viewGrievance$ = createEffect(() => this.actions$.pipe(
    ofType(GrievanceActions.requestViewGrievance),
    switchMap(
      (action) => {
        return this.http
          .get<any>(`${URL.BASE_URL}form/personform/list?personFormId=${action.params.personFormId}`)
          .pipe(
            map(data => GrievanceActions.postViewGrievanceSuccess({ data })),
            take(1)
          );
      })
  ));

  viewGrievanceProgress$ = createEffect(() => this.actions$.pipe(
    ofType(GrievanceActions.requestPersonProgress),
    switchMap(
      (action) => {
        return this.http
          .get<any>(`${URL.BASE_URL}form/personformlogs?personFormId=${action.personFormId}`)
          .pipe(
            map(data => GrievanceActions.getPersonProgressSuccess({ data })),
            take(1)
          );
      })
  ));


  effectivelyCancelExam$ = createEffect(() => this.actions$.pipe(
    ofType(cancelExam),
    switchMap(
      (action) => {
        return this.http
          .delete<any>(`${URL.BASE_URL}form/personform?candidateId=${action.candidateId}&personFormId=${action.personFormId}`)
          .pipe(
            map((data) => {
              if (data) {
                this.snackbar.callSnackbaronSuccess("Cancelled successfully")
              }
              return Examcancelled({ isCancelled: data })
            }),
            take(1)
          );
      })
  ));


  effectivelyGetupcomingExam$ = createEffect(() => this.actions$.pipe(
    ofType(getupcomingExam),
    switchMap(
      (action) => {
        return this.http.get<any>(`${URL.BASE_URL}exam/upcomingexam?candidateId=${this.global.candidateId}`)
          .pipe(
            map(upcomingExam => GrievanceActions.gotupcomingExam({ upcomingExam: upcomingExam })),
            take(1)
          );
      }
    )
  ))
}