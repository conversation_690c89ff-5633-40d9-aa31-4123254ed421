import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HelpService } from '../../help.service';

@Component({
  selector: 'exai-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.scss']
})
export class CardComponent implements OnInit {

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private helpService: HelpService
  ) { }

  categoryName: string

  @Input() selectedCategoryName
  @Input() name
  @Input() eligibilityRouteName
  @Input() id
  @Input() applicationId
  @Input() status
  @Input() createdDate
  @Input() changedDate
  @Input() getHelp
  @Input() ticketRaised


  ngOnInit(): void {
    if (this.categoryName !== this.route.snapshot.params.category) {
      this.categoryName = this.route.snapshot.params.category      
    }
  }

  navigateToGetHelp() {
    if(this.id==undefined) {
      this.id="1";
    }

    this.router.navigate(['/help', this.id, 'get-help'])
  }

  navigateToRaiseTicket() {
    if(this.id==undefined) {
      this.id="1";
    }
    this.helpService.selectedTicketId.next(this.id)
    this.router.navigate(['/help', 'raise-ticket'])
  }
}
