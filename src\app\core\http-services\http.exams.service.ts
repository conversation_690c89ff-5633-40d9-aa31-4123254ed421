import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { GlobalUserService } from "../global-user.service";
import { Observable } from "rxjs";
import { URL } from 'src/app/core/url';
import { ExamModel } from "../Dto/exam.model";
import { SlotModel } from "../Dto/slot.model";
import { DayWiseSlotModel } from "../Dto/daywiseslot.model";
import { CartItemModel } from "../Dto/cartitem.model";
import { TimeZoneModel } from "../Dto/timezone.model";
import { ShowRegisterExamModel } from "../Dto/show-register-exam.model";

@Injectable({
    providedIn: "root",
})
export class HttpExamService {

    constructor(private http: HttpClient,
        private global: GlobalUserService) {
    }

    public getExamsByERId(id: number,personTenantRoleId:number): Observable<ExamModel[]> {
        var url = `${URL.BASE_URL}exam/exams?eligibilityRouteId=${id}&personTenantRoleId=${personTenantRoleId}`;
        return this.http.get<ExamModel[]>(url);
    }

    public getSlots(time: string, date: Date): Observable<SlotModel[]> {
        var url = `${URL.EXAM_BASE_URL}slots?timezoneId=${time}&startDate=${new Date(date).toLocaleDateString()}`;
        return this.http.get<SlotModel[]>(url);
    }

    public getMonthlySlots(date: number, year: number, timezone: string): Observable<DayWiseSlotModel[]> {
        var url = `${URL.EXAM_BASE_URL}monthlySlots?month=${date}&year=${year}&timeZone=${timezone}`;
        return this.http.get<DayWiseSlotModel[]>(url);
    }

    public getCartItems(): Observable<CartItemModel[]> {
        var url = `${URL.EXAM_BASE_URL}cartitems?personTenantRoleId=${this.global.candidateId}`;
        return this.http.get<CartItemModel[]>(url);
    }

    public getExamList(): Observable<TimeZoneModel[]> {
        var url = `${URL.BASE_URL}Exam/timezones`;
        return this.http.get<TimeZoneModel[]>(url);
    }

    public getshowregisterExam(personTenantRoleId: number): Observable<ShowRegisterExamModel> {
        return this.http.get<ShowRegisterExamModel>(URL.BASE_URL + `Exam/showregisterexam?personTenantRoleId=${personTenantRoleId}`);
    }
}