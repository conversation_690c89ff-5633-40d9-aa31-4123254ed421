import { DatePipe } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { Subject } from 'rxjs';
import { SlotModel } from 'src/app/core/Dto/slot.model';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { getCartItems, getCartPracticeItems } from '../../state/shared/shared.actions';
import { get_PracticecartItems, get_cartItems } from '../../state/shared/shared.selectors';
import {  Details } from '../state/models/cart';
import { Slot } from '../state/models/slot';
import { Timezone } from '../state/models/timezone.model';
import { getCart, getPracticeCart, getRegisteredExam, getTimeSlots, getTimezones, removeCartItem } from '../state/scheduled.actions';
import { selectorGetCart, selectorGetCartDeleteStatus, selectorGetPracticeCart, selectorGetTimeslots, selectorGetTimezones } from '../state/scheduled.selectors';
import { ScheduledState } from '../state/scheduled.state';

@Component({
  selector: 'exai-reschudel-payment',
  templateUrl: './reschudel-payment.component.html',
  styleUrls: ['./reschudel-payment.component.scss']
})
export class ReschudelPaymentComponent implements OnInit {
  SelectedItems:Details
  Validators: FormGroup;
  selectedExam:any;
  minDate = new Date();
  slotsAvaiable: SlotModel[] = [];
  scheduleEvent:Subject<any> = new Subject<any>();
  step: any = 0;
  slots = [];
  timeZoneId: Timezone
  PracticeNumberExam:number
  cartItemList:any;
  bookedslot: any;
  timezones: Timezone[];
  selectedDate: Date = null;
  examDateTime: any;

  disableDateRange:boolean = true;
  disableTimeSlot:boolean = false;
  cartPayment:boolean = false;
  timeSlot: Array<any>;
  constructor(public dialogRef: MatDialogRef<ReschudelPaymentComponent>,
    private router:Router,
    private store: Store<ScheduledState>,private global:GlobalUserService,
    @Inject(MAT_DIALOG_DATA) public data: any) {
      this.PracticeNumberExam = data.Id
     }
  
  ngOnInit(): void {
   
    this.store.select(get_cartItems).subscribe((cartItems) => {
      if(cartItems!=null){
        this.cartItemList=cartItems
      }
    })


    this.scheduleEvent.next(true)
    this.store.dispatch<Action>(getTimezones());
    this.store.select(selectorGetTimezones).subscribe((timezones: Timezone[]) => {
      if (timezones.length > 0) {
        this.timezones = timezones
      }
    });
    this.store.select(selectorGetTimeslots).subscribe((data: Slot[]) => {
      if (data) {
        this.slotsAvaiable = [];
        let availableSlots = data;
        if (availableSlots.length > 0) {
          availableSlots.forEach((ele: Slot) => {
            let slotstring = `${ele.strSlotDate} ${ele.strSlotTime}`;
            let slotDate = new Date(Date.parse(slotstring));
            let slotmodel = new SlotModel({
              slotId: ele.slotId,
              availableSlots: ele.availableSlots,
              bookedSlots: ele.bookedSlots,
              slotDate: ele.slotDate,
              strSlotDate: ele.strSlotDate,
              strSlotTime: ele.strSlotTime,
              totalSlots: ele.totalSlots,
              slotDateTime: slotDate,
              slotDateUtc: ele.slotDateUtc
            })
            this.slotsAvaiable.push(slotmodel);
          });
          this.slotsAvaiable = this.slotsAvaiable.sort((a, b) => new Date(a.slotDateTime).getTime() - new Date(b.slotDateTime).getTime());
          this.slotsAvaiable.forEach((ele: SlotModel) => {
            let slotDate = ele.slotDateTime;
            if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 00:00 AM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 04:00 AM`))) {
              this.timeSlot ?this.timeSlot[0].data.push(ele) : []
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 04:00 AM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 08:00 AM`))) {
              this.timeSlot ?this.timeSlot[1].data.push(ele) : this.timeSlot
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 08:00 AM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 12:00 PM`))) {
              this.timeSlot ?this.timeSlot[2].data.push(ele) : this.timeSlot
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 12:00 PM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 04:00 PM`))) {
              this.timeSlot ?this.timeSlot[3].data.push(ele) : this.timeSlot
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 04:00 PM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 08:00 PM`))) {
              this.timeSlot ?this.timeSlot[4].data.push(ele) : this.timeSlot
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 08:00 PM`)) && slotDate <= new Date(Date.parse(`${ele.strSlotDate} 11:59 PM`))) {
              this.timeSlot ?this.timeSlot[5].data.push(ele) : this.timeSlot
            }
          });
        } else if (this.slotsAvaiable.length == 0) {
          
        }
      }
    });
    
  }

  public examEvent(tz: Timezone,item): void {
    this.setSlot();
    this.timeZoneId = tz;
      this.store.dispatch<Action>(
        getTimeSlots({ timezone: item.timeZoneCode, startDate: item.eventDateUtc, examId: item.examId, offset: item.timeZoneOffset,candidateId:this.global.userDetails.getValue().personId })
      );
    let nowDate = moment();
    let currentMonth = nowDate.month() != 0 ? nowDate.month() : 12;
    let CurrentYear = nowDate.month() != 0 ? nowDate.year() : nowDate.year() - 1;
    // this.store.dispatch<Action>(getMonthlySlots({ month: currentMonth + 1, year: CurrentYear, timezone: this.timeZoneId.id }));
  }

  selectrangeactive(event) {
    this.slots = event.data;
    this.step = event.id;
    this.disableTimeSlot=true;
  }

  onNoClick(item){
    if(item==0){
    this.dialogRef.close({confirmed:true});
         this.PracticeNumberExam == 1?this.router.navigateByUrl('/exam-scheduled') :this.router.navigateByUrl('/practice_exam')

    this.store.dispatch(getRegisteredExam({candidateId:this.global.candidateId}))

    }
    else if(item==1){
      this.dialogRef.close({confirmed:true});
    this.PracticeNumberExam == 1?this.router.navigateByUrl('/exam-scheduled/payment/1'):this.router.navigateByUrl('/exam-scheduled/payment/2')
    }
  }

  bookslot(event) {
    this.bookedslot = event.slotId;
    this.examDateTime = event.slotDateUtc
    this.cartPayment=true;
  }

  setSlot() {
    this.timeSlot = [
      { title: "12 AM - 04 AM", id: 1, key: "phase1", data: [], },
      { title: "04 AM - 08 AM", id: 2, key: "phase2", data: [], },
      { title: "08 AM - 12 PM", id: 3, key: "phase3", data: [], },
      { title: "12 PM - 04 PM", id: 4, key: "phase4", data: [], },
      { title: "04 PM - 08 PM", id: 5, key: "phase5", data: [], },
      { title: "08 PM - 11:45 PM", id: 6, key: "phase6", data: [] },
    ];
  }

  addToCart(selectedExam){
    // this.store.dispatch<Action>(
    //   getCartItems({ personTenantRoleId: this.global.candidateId })
    // )

    // let cartCall=this.store.select(get_cartItems).subscribe((cartItems) => {
    //   if (cartItems != null) {
    //     var cartList=cartItems.filter(item=>item.examId==selectedExam.examId)
    //     if(cartItems.find(item=>item.examId==selectedExam.examId)){
    //       this.store.dispatch<Action>(
    //         removeCartItem({ tetantId: this.global.candidateId, cartItemsId: Number(cartList[0].personEventCartId) })
    //       );
    //     }
    //   }
    //   cartCall.unsubscribe();
    //   });

    //   this.store.select(selectorGetCartDeleteStatus).subscribe((data: any) => {
    //     if (data) {
    //       setTimeout(()=>{
        this.PracticeNumberExam == 1?
        this.store.dispatch<Action>(
              getCart({
                details: {
                  personTenantRoleId: this.global.candidateId,
                  amount: this.selectedExam.price,
                  cartItemTypeId: 1,
                  currencyId: 1,
                  examDetail: {
                    candidateId: this.global.candidateId,
                    examId: this.selectedExam.id,
                    slotId: this.bookedslot,
                    timeZone: this.timeZoneId!=undefined ? this.timeZoneId.id:selectedExam.timeZoneCode,
                    offSet: this.timeZoneId!=undefined ?this.timeZoneId.offset:selectedExam.timeZoneOffset,
                    examModeId: 1,
                    personTenantRoleId: this.global.candidateId,
                    examDateTime: this.examDateTime,
                    
                  },
                   personEventId: this.SelectedItems.personEventId?this.SelectedItems.personEventId:null,
                    voucherCode: this.SelectedItems.voucherCode?this.SelectedItems.voucherCode:null
        
                },
                isPayment: false,
              })
      ):   this.store.dispatch<Action>(
        getPracticeCart({
          details: {
            personTenantRoleId: this.global.candidateId,
            amount: this.selectedExam.price,
            cartItemTypeId: 1,
            currencyId: 1,
            attempts:1,
            examDetail: {
              candidateId: this.global.candidateId,
              examId: this.selectedExam.id,
              slotId: this.bookedslot,
              timeZone: this.timeZoneId!=undefined ? this.timeZoneId.id:selectedExam.timeZoneCode,
              offSet: this.timeZoneId!=undefined ?this.timeZoneId.offset:selectedExam.timeZoneOffset,
              examModeId: 1,
              personTenantRoleId: this.global.candidateId,
              examDateTime: this.examDateTime,
              
            },
             personEventId: this.SelectedItems.personEventId?this.SelectedItems.personEventId:null,
              voucherCode: this.SelectedItems.voucherCode?this.SelectedItems.voucherCode:null
  
          },
          isPayment: false,
        })
);
  
      this.store.select(selectorGetPracticeCart).subscribe(data=>{
          if(data){
            this.store.dispatch<Action>(
              getCartPracticeItems({ personTenantRoleId: this.global.candidateId })
            )
          }
      })
      //     },1000)
      //   }
      // });
   
      this.store.select(get_cartItems).subscribe((cartItems) => {
        if(cartItems!=null){
          this.cartItemList=cartItems
        }
      })
  }

  getData(item,date){
    this.SelectedItems = item
    let Online = this.data.List.filter(x=>x.examMode == "Online")
    this.setSlot();


    
    if(date!=null && Online.length > 0){
      this.selectedDate = date.value;
    this.store.dispatch<Action>(
      getTimeSlots({ timezone: item.timeZoneCode, startDate: this.selectedDate.toDateString(), examId: item.examId, offset: item.timeZoneOffset, candidateId:this.global.userDetails.getValue().personId })
    );
    this.disableDateRange=true;
    this.disableTimeSlot=false;
    this.cartPayment=false;
    }
    if(date==null && Online.length > 0){
    this.store.dispatch<Action>(
      getTimeSlots({ timezone: item.timeZoneCode, startDate: item.eventDateUtc, examId: item.examId, offset: item.timeZoneOffset, candidateId:this.global.userDetails.getValue().personId })
    );
    this.disableDateRange=true
    }
    this.selectedExam={
      id:item.examId,
      price:item.amount,
      title:item.examName,
      page:"PopPage",
      personEventId: this.SelectedItems.personEventId,
      voucherCode: this.SelectedItems.voucherCode

    }
  }

  payNow(){
    this.store.dispatch<Action>(
      getCartItems({ personTenantRoleId: this.global.candidateId })
    )
   this.PracticeNumberExam == 1? this.store.select(get_cartItems).subscribe(data=>{
      if(data.length>0){
        this.onNoClick(1);
       }
    }) :this.store.select(get_PracticecartItems).subscribe(data=>{
      if(data.length>0){
        this.onNoClick(1);
       }
    })
   
  }

}
