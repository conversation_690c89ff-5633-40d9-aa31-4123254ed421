import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { environment } from 'src/environments/environment';
import { chatConnectionAtSuppCen } from 'src/app/core/common-component/chat/chat.types';
import { ClearCandidateChatState, getAvailableStaffInfo, notifyConnection, notifyConnectionClose } from './store/candidate-chat.actions';
import { Store } from '@ngrx/store';
import { CandidateChatState, SupportStaffInfo } from './store/candidate-chat.state';
import { selectErrorMessage, selectSupportStaffInfo } from './store/candidate-chat.selectors';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { DecodedIdentityToken } from '../candiate.types';
import { get_decodeInfo } from '../state/shared/shared.selectors';
import Peer from 'peerjs';
import { closeChatConn } from '../../core/common-component/chat/chat.types';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { setLoadingSpinner } from '../state/shared/shared.actions';
@Component({
  selector: 'exai-candidate-chat',
  templateUrl: './candidate-chat.component.html',
  styleUrls: ['./candidate-chat.component.scss']
})
export class CandidateChatComponent implements OnInit {

  // PLAN FOR CHAT CHANNEL
  // candidate does not need to listen to incoming connections
  // When candidate/client clicks on "Get Help" that is the point we have to 
  // connect them with a conversational AI that can ask questions related 
  // to the candidate/client's ID on the platform(if needed), the exam the candidate is
  // facing issues with, then using all this information a summary will be generated 
  // with relevant information and using this summary a ticket will be generated that 
  // will become a part of the thread for the same candidate/client and this will
  // be assigned to an available agent.
  // For agents some max threshold of how many chat tickets can be assigned to them 
  // conncurrently (is yet to be decided), so that until the agent does have 
  // this many chat tickets concurrently assigned to them they will keep getting new chat tickets,
  // also when a agent a assigned a chat ticket they will be automatically connected 
  // to candidate/client whose ticket thread it is,

  peer: Peer;
  conversation: chatConnectionAtSuppCen = null;

  userData: DecodedIdentityToken = null;
  name: string = "";
  supportStaffInfo: SupportStaffInfo = null;
  candidateORclientID: string;
  initialStatusMessage: string = "Connecting....";
  @Output() closeChatConnection :EventEmitter<any> = new EventEmitter<any>();

  constructor(private store: Store<CandidateChatState>,
    public global: GlobalUserService,
    public snackbar:SnackbarService ) {
    // Create own peer object with connection to custom stun/turn servers
    this.peer = new Peer(null, {
      debug: 2,
      config: environment.config,
    }); 
  }

  ngOnInit(): void {
    this.store.dispatch(ClearCandidateChatState());
    this.store.select(selectErrorMessage).subscribe((msg: string) => {
      if (msg) this.initialStatusMessage = msg;
      // this.store.dispatch(setLoadingSpinner({ status: false }));
    })
    this.store.select(get_decodeInfo).subscribe((data: DecodedIdentityToken) => {
      if (data) {
        this.userData = data;
        this.name += this.userData.given_name ? this.userData.given_name : '';
        this.name += this.userData.middle_name ? this.userData.middle_name : ' ';
        this.name += this.userData.family_name ? this.userData.family_name : '';
      }
    });
    this.store.select(selectSupportStaffInfo).subscribe((info: SupportStaffInfo) => {
      if (info) {
        this.supportStaffInfo = info;
        this.joinChat();
      }
    })
    this.setUpPeer();
  }

  private setUpPeer() {
    this.peer.on('open', (id) => {
      this.candidateORclientID = id;
      this.store.dispatch(getAvailableStaffInfo());
    });
    let self = this;
    this.peer.on('disconnected', function () {
      self.initialStatusMessage = "Connection lost. Attempting to reconnect....";
      // Workaround for peer.reconnect deleting previous id
      // self.candidateORclientID = self.agentID;
      // self.peer._lastServerId = self.agentID;
      self.peer.reconnect();
    });
    this.peer.on('close', function () {
      // self.chatConnection = null;
      this.store.dispatch(notifyConnectionClose({
        candidateId: this.global.candidateId,
        token: this.supportStaffInfo.token
      }));
    });
    this.peer.on('error', function (err) {
      self.initialStatusMessage = "Something went wrong, unable to connect to Chat Support Agent";
      console.error(err);
    });
  }

  joinChat() {
    if (!this.conversation) {
      let c = this.peer.connect(this.supportStaffInfo.token,
        {
          reliable: true,
          metadata: {
            clientORcandidateName: this.name
          }
        })
      c.on('open', () => {
        this.conversation = new chatConnectionAtSuppCen({
          chatConnectionStatus: 'Connected',
          screenShareConnectionStatus: null,
          clientORcandidatePeerjsID: this.candidateORclientID,
          agentPeerjsID: c.peer,
          clientORcandidateName: this.name,
          agentName: this.supportStaffInfo.name,
          chatConnection: c,
          screenShareConnection: null,
          ChatPeerConnection: c.peerConnection,
          clientORcandidateRemoteStream: null,
          conversation: []
        })
      })
      this.store.dispatch(notifyConnection({
        candidateId: this.global.candidateId,
        token:this.supportStaffInfo.token
      }));
    }
  }
  closeChat($event:closeChatConn) {
    // this.store.dispatch(notifyConnectionClose({ candidateId: this.global.userDetails.value.roles[0].personTenantRoleId, token: this.supportStaffInfo.token }));
    this.snackbar.callSnackbaronSuccess('Chat Connection Closed' + ($event.closerId != this.candidateORclientID ? ' By Support Staff' : ''));
    this.closeChatConnection.emit();
  }
}


