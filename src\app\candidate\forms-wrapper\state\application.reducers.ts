import { createReducer, on } from "@ngrx/store";
import { clearApplicationState, setBasicDetails, gotForm<PERSON>son, savedUserResponse, gotUserResponse, gotPersonFormLogs, RenewelCartResponse, getRegistryResponse } from './application.actions';
import { ApplicationState, intialApplicationState } from "./application.state";

const _applicationReducer = createReducer<ApplicationState>(
  intialApplicationState,
  on(gotFormJson, (state, action) => {
    return {
      ...state,
      form: action
    }
  }),
  on(setBasicDetails, (state, action) => {
    return {
      ...state,
      form: {
        formTypeID: [action.formTypeId],
        stateID: action.stateId,
        eligibilityID: action.eligibilityRouteId,
        isSubmitAllowed: [true]
      },
      candidateId: action.candidateId,
      personEventId:action.personEventId
      // process personFormId and code at a later stage when implementing edit
    }
  }),
  on(savedUserResponse, (state, action) => {
    return {
      ...state,
      latestPersonFormId:action.personFormId
    }
  }),
  on(gotUserResponse, (state, action) => {
    
    return {
      ...state,
      userResponse:action.userResponse
    }
  }),
  on(gotPersonFormLogs, (state, action) => {
    return {
      ...state,
      personFormLogs: action.personFormLogs
    }
  }),
  on(clearApplicationState, (state, action) => {
    return {
      ...state,
      form: null,
      userResponse: null,
      candidateId: null,
      latestPersonFormId: null, 
      personEventId: null,
      personFormLogs:[]
    }
  }),
  on(RenewelCartResponse, (state, action) => {
    return {
      ...state,
      renewelFee: action.renewelAddedDetails
    }
  }),
  on(getRegistryResponse, (state, action) => {
    
    return {
      ...state,
      registrySave:action.registryRespone
    }
  }),
)



export function applicationReducer(state, action) {
  return _applicationReducer(state, action);
}

