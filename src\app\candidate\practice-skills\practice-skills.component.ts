import { Component, OnInit } from "@angular/core";
import { NgDynamicBreadcrumbService } from "ng-dynamic-breadcrumb";
import { LanguageService } from "src/app/core/language.service";
import { Observable } from "rxjs";
import { PracticeSkill } from "./state/models/practice-skill.model";
import { PracticeSkillsState } from "./state/practice-skills.state";
import * as PracticeSkillActions from "./state/practice-skills.actions";
import { Store } from "@ngrx/store";
import * as PracticeSkillSelectors from "./state/practice-skills.selectors";
import { PracticeFacade } from "./state/practice.facade";
import {
  PracticeSkillMode,
  PracticeSkillModeDetail,
} from "./state/models/practice-skill-mode.model";
import { HttpExamService } from "src/app/core/http-services/http.exams.service";
import { PurchasedSkill } from "./state/models/purchased-skill.model";

@Component({
  selector: "exai-practice-skills",
  templateUrl: "./practice-skills.component.html",
  styleUrls: ["./practice-skills.component.scss"],
})
export class PracticeSkillsComponent implements OnInit {
  selectedTab: number = 0;
  selectedSubTab: number = 0;

  skills$: Observable<PracticeSkill[]>;
  skillsMode$: Observable<PracticeSkillMode>;
  loading$: Observable<boolean>;
  error$: Observable<any>;

  //move purchased to store
  purchasedSkills: PurchasedSkill[] = [];

  constructor(
    public lngSrvc: LanguageService,
    private _ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private _practiceFacade: PracticeFacade,
    private _httpExamService: HttpExamService
  ) {
    this.skills$ = this._practiceFacade.skill$;
    this.skillsMode$ = this._practiceFacade.skillModes$;
    this.loading$ = this._practiceFacade.skillsLoading$;
  }
  ngOnInit(): void {
    this._ngDynamicBreadcrumbService.updateBreadcrumb([
      { label: "Home", url: "/" },
      { label: this.lngSrvc.curLangObj.value.practice_skills, url: "" },
    ]);

    //    this._practiceFacade.loadPracticeSkills(1, 10,"20403475-448A-4F5D-AE72-BFD2127F9C0B","7D8ED605-EC2C-4449-A41F-6F2ED50F792E");

    this._practiceFacade.loadPracticeSkills(1, 10);
    this._practiceFacade.loadPracticeSkillBundles(1, 5);
    this._practiceFacade.loadPracticeSkillsMode();
    this.getPurchasedPracticeSkills();
  }
  handleAddToCart(skill: PracticeSkill) {
    console.log("Skill added to cart from parent:", skill);
  }

  getPurchasedPracticeSkills() {
    this._httpExamService.getPurchasedPracticeSKill().subscribe(
      (skills) => {
        this.purchasedSkills = skills;
      },
      (error) => {
        console.error("Error fetching purchased skills:", error);
      }
    );
  }
}
