import { Component, Input, OnInit } from '@angular/core';
import { trackByRoute } from '../../utils/track-by';
import { NavigationService } from '../../services/navigation.service';
import icRadioButtonChecked from '@iconify/icons-ic/twotone-radio-button-checked';
import icRadioButtonUnchecked from '@iconify/icons-ic/twotone-radio-button-unchecked';
import { LayoutService } from '../../services/layout.service';
import { ConfigService } from '../../services/config.service';
import { map } from 'rxjs/operators';
import { GlobalUserService } from 'src/app/core/global-user.service';

@Component({
  selector: 'exai-sidenav',
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss']
})
export class SidenavComponent implements OnInit {

  @Input() collapsed: boolean;

  removeTab : boolean;

  collapsedOpen$ = this.layoutService.sidenavCollapsedOpen$;
  title$ = this.configService.config$.pipe(map(config => config.sidenav.title));

  imageUrl$ = this.configService.config$.pipe(map(config => config.sidenav.imageUrl));

  showCollapsePin$ = this.configService.config$.pipe(map(config => config.sidenav.showCollapsePin));

  items = this.navigationService.items;
  trackByRoute = trackByRoute;
  icRadioButtonChecked = icRadioButtonChecked;
  icRadioButtonUnchecked = icRadioButtonUnchecked;

  constructor(private navigationService: NavigationService,
    private layoutService: LayoutService,
    private configService: ConfigService,
    public global: GlobalUserService
    ) {
  }

  ngOnInit() {
    let params = this.global.getUserInformation();
    //Hide and Show button Registry in nav bar(show only for 4 states)
    switch (this.global.stateId) {
  
      case 8:{
              let removeRegistry = this.items.filter( el => el.label != "Registry" );
              this.items = removeRegistry;
            }
            break;
      case 9:{
              let removeRegistry = this.items.filter( el => el.label != "Registry" );
              this.items = removeRegistry;
            }
            break;
      case 10: {
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
      case 11:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
      case 12:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
      case 13:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
      case 15:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
      case 16:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
      case 18:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
       case 19:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
              case 23557: {
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;  
              }
              break;
              case 23564: {
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;  
              }
                break;
              case 23975:{
                let removeRegistry = this.items.filter( el => el.label != "Registry" );
                this.items = removeRegistry;
              }
              break;
          default:
            this.items;
            break;
    }
  }

  onMouseEnter() {
    this.layoutService.collapseOpenSidenav();
  }

  onMouseLeave() {
    this.layoutService.collapseCloseSidenav();
  }

  toggleCollapse() {
    let menu = document.getElementsByClassName('menu-icon') as HTMLCollectionOf<HTMLElement>
    this.collapsed ? this.layoutService.expandSidenav() : this.layoutService.collapseSidenav();
    // if(this.collapsed){
    //   menu[0].style.transform = "rotate(" + 360 + "deg)";
    // }
    // else{
    //   menu[0].style.transform = "rotate(" + 180 + "deg)";
    // }
  }
}
