import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { PracticeSkillService } from "./service/practice-skill.service";
import * as PracticeSkillActions from "./practice-skills.actions";
import { catchError, map, mergeMap, switchMap, tap } from "rxjs/operators";
import { error } from "@angular/compiler/src/util";
import { of } from "rxjs";

@Injectable()
export class PracticeSkillsEffects {
  constructor(
    private _action$: Actions,
    private _practiceSkillsService: PracticeSkillService
  ) {}

  /**
   * loadSkillsMode
   * This effect listens for the loadPracticeSkillsMode action and fetches the practice skill modes from the service.
   * On success, it dispatches loadPracticeSkillsModeSuccess with the response.
   * On failure, it dispatches loadPracticeSkillsModeFailure with the error.
   */
  loadSkillsMode$ = createEffect(() =>
    this._action$.pipe(
      ofType(PracticeSkillActions.loadPracticeSkillsMode),
      switchMap(({}) =>
        this._practiceSkillsService.getPracticeSkillModes().pipe(
          map((response) =>
            PracticeSkillActions.loadPracticeSkillsModeSuccess({ response })
          ),
          catchError((error) =>
            of(PracticeSkillActions.loadPracticeSkillsModeFailure({ error }))
          )
        )
      )
    )
  );

  /**
   * loadSkills
   */
  loadSkills$ = createEffect(() =>
    this._action$.pipe(
      ofType(PracticeSkillActions.loadPracticeSkills),
      switchMap(({ pageNo, pageSize }) =>
        this._practiceSkillsService.getPracticeSkills(pageNo, pageSize).pipe(
          map((response) =>
            PracticeSkillActions.loadPracticeSkillsSuccess({ response })
          ),
          catchError((error) =>
            of(PracticeSkillActions.loadPracticeSkillsFailure({ error }))
          )
        )
      )
    )
  );

  /**
   * loadbundles
   */
  loadPracticeSkillBundles$ = createEffect(() =>
    this._action$.pipe(
      ofType(PracticeSkillActions.loadPracticeSkillBundles),
      mergeMap(({ pageNo, pageSize }) =>
        this._practiceSkillsService
          .getPracticeSkillBundles(pageNo, pageSize)
          .pipe(
            map((response) =>
              PracticeSkillActions.loadPracticeSkillBundlesSuccess({ response })
            ),
            catchError((error) =>
              of(
                PracticeSkillActions.loadPracticeSkillBundlesFailure({ error })
              )
            )
          )
      )
    )
  );

  /**
   * loadSkillByGuid
   */

  loadSkillByGuid$ = createEffect(() =>
    this._action$.pipe(
      ofType(PracticeSkillActions.loadPracticeSkillByGuid),
      switchMap(({ practiceSkillGuid }) =>
        this._practiceSkillsService
          .getPracticeSkillByGuid(practiceSkillGuid)
          .pipe(
            map((skillResponse) =>
              PracticeSkillActions.loadPracticeSkillByGuidSuccess({
                skillResponse,
              })
            ),
            catchError((error) =>
              of(PracticeSkillActions.loadPracticeSkillByGuidFailure({ error }))
            )
          )
      )
    )
  );
}
