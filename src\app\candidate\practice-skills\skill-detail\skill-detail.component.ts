import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgDynamicBreadcrumbComponent, NgDynamicBreadcrumbService } from 'ng-dynamic-breadcrumb';
import { Observable, Subscription } from 'rxjs';
import { LanguageService } from 'src/app/core/language.service';
import { PracticeSkill } from '../state/models/practice-skill.model';
import { filter, map, switchMap, take, tap } from 'rxjs/operators';
import { PracticeSkillsState } from '../state/practice-skills.state';
import { Store } from '@ngrx/store';
import * as PracticeSkillActions from '../state/practice-skills.actions';
import * as PracticeSkillSelectors from '../state/practice-skills.selectors'
import { PracticeFacade } from '../state/practice.facade';
import { Breadcrumb } from 'ng-dynamic-breadcrumb/lib/breadcrumb.model';


@Component({
  selector: 'exai-skill-detail',
  templateUrl: './skill-detail.component.html',
  styleUrls: ['./skill-detail.component.scss']
})
export class SkillDetailComponent implements OnInit {

  skill$ = this._practiceFacade.skillByGuidResponse$.pipe(map(res => res?.data?.[0] ?? null));
  loading$ = this._practiceFacade.skillByGuidLoading$;
  error$ = this._practiceFacade.skillByGuidError$;

  constructor(
    private route: ActivatedRoute,
    public lngSrvc: LanguageService,
    private _ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private _practiceFacade: PracticeFacade
  ) {
  }

  ngOnInit(): void {
    // Check if skill data was passed in router state (fast load)
    const stateData = history.state?.skillData;

    if (stateData?.practiceSkillGuid) {
      this._practiceFacade.loadPracticeSkillByGuid(stateData.practiceSkillGuid);
    } else {
      this.route.paramMap.subscribe(params => {
        const id = params.get('id');
        if (id) {
          this._practiceFacade.loadPracticeSkillByGuid(id);
        }
      });
    }


    // Dynamically update the breadcrumb once the skill is loaded
    this._practiceFacade.skillByGuidResponse$.subscribe(res => {
      const skill = res?.data?.[0];

      if (skill) {
        const breadcrumb: Breadcrumb = {
          label: 'Practice Skill View',
          url: ''
        };

        this._ngDynamicBreadcrumbService.updateBreadcrumb([
          { label: 'Home', url: '/' },
          { label: 'Practice Skills', url: '/practice-skills' },
          breadcrumb
        ]);
      }
    });
  }
}