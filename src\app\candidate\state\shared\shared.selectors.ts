import { SharedState } from './shared.state';
import { createFeatureSelector, createSelector } from '@ngrx/store';
export const SHARED_STATE_NAME = 'shared';

const getSharedState = createFeatureSelector<SharedState>(SHARED_STATE_NAME);

export const getLoading = createSelector(getSharedState, (state) => {
  return state.showLoading;
});

export const getErrorMessage = createSelector(getSharedState, (state) => {
  return state.errorMessage;
});


export const get_cartItems = createSelector(getSharedState, (state) => {
  return state.cartItems
})

export const get_userDetails = createSelector(getSharedState, (state) => {
  return state.userdetails
})

export const get_PracticecartItems = createSelector(getSharedState, (state) => {
  return state.PracticecartItems
})

export const get_decodeInfo = createSelector(getSharedState, (state) => {
  return state.decodeInfo
})




