import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { catchError, take } from "rxjs/operators";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { GlobalUserService } from "src/app/core/global-user.service";
import { HttpDashboardService } from "src/app/core/http-services/http.dashboard.service";
import { PersonForm } from "../application/application.types";
import { FormModel } from "./state/dashboard.models";
import { upcomingExam } from "./state/dashboard.models/Upcomingexam";


@Injectable({
    providedIn: "root",
})
export class DashboardRepository {

    constructor(private _dashboardService: HttpDashboardService,
        private global: GlobalUserService) {
    }

    public getUserDetails(): Observable<any> {
      return this.global.userDetails.pipe(catchError(() => {
          return of();
      }));
    }

    public getactiveForms(candidateId: number, formTypeIds: number[]): Observable<FormModel[]>{
        return this._dashboardService.getactiveForms(candidateId, formTypeIds).pipe(take(1),
        catchError(() => {
            return of(new Array<FormModel>());
        }));
    }

    public getUpcomingExam(candidateId: number): Observable<upcomingExam[]> {
        return this._dashboardService.getUpcomingExam(candidateId).pipe(take(1),
        catchError(() => {
            return of(new Array<upcomingExam>());
        }));
    }

    public getPersonForms(candidateId: number, formTypeIds: number[]): Observable<PersonForm[]> {
        return this._dashboardService.getPersonForms(candidateId, formTypeIds).pipe(take(1),
        catchError(() => {
            return of(new Array<PersonForm>());
        }));
    }

    public getshowregisterExam(personTenantRoleId: number): Observable<ShowRegisterExamModel> {
        return this._dashboardService.getshowregisterExam(personTenantRoleId).pipe(take(1),
        catchError(() => {
            return of(null);
        }));
    }


    
}