import { Injectable } from "@angular/core";
import { forkJoin, Observable, of } from "rxjs";
import { catchError, map, switchMap, take } from "rxjs/operators";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { GlobalUserService } from "src/app/core/global-user.service";
import { PersonForm } from "../application/application.types";
import { DashboardRepository } from "./dashboard.repository";
import { DashboardData } from "./state/dashboard.state";

@Injectable({
    providedIn: "root",
})
export class DashboardViewModel {

    constructor(private _repository: DashboardRepository,
        private global: GlobalUserService     ) {
    }

    public fetchDashboardData(): Observable<DashboardData> {
        return this.global.userDetails.pipe(switchMap((data) => {
            if (data) {
                let formTypeids: number[] = [1, 3];
                let formModels$ = this._repository.getactiveForms(this.global.candidateId, formTypeids).pipe(take(1));
                let upcomingExams$ = this._repository.getUpcomingExam(this.global.candidateId).pipe(take(1));
                let personForms$ = this._repository.getPersonForms(this.global.candidateId, formTypeids).pipe(take(1));
                return forkJoin([formModels$, upcomingExams$, personForms$]).pipe(
                    map(([formModels, upcomingExams, personForms]) => {
                        let test = new DashboardData();
                        test.form = formModels == null? []: formModels;
                        test.upcomingExam = upcomingExams == null? []: upcomingExams;
                        test.personForms = personForms == null? []: personForms;
                        return test;
                    })
                )
            }
        }),
        catchError(() => {
            return of(new DashboardData());
        })
        )
    }

    public getPersonForms(candidateId: number, formTypeIds: number[]): Observable<PersonForm[]> {
        return this._repository.getPersonForms(candidateId, formTypeIds).pipe(take(1),
        catchError(() => {
            return of(new Array<PersonForm>());
        }));
    }

    public getshowregisterExam(personTenantRoleId: number): Observable<ShowRegisterExamModel> {
        return this._repository.getshowregisterExam(personTenantRoleId).pipe(take(1),
        catchError(() => {
            return of(null);
        }));
    }

}