export class VoucherCartDetailsModel {
    public constructor(init?: Partial<VoucherCartDetailsModel>) {
        Object.assign(this, init);
    }

    cartId: number = 0;
    voucherCode: string = '';
    userId: number = 0;
    voucherItems: VoucherItemModel[] = [];
}

export class VoucherItemModel {
    public constructor(init?: Partial<VoucherItemModel>) {
        Object.assign(this, init);
    }

    personEventCartId: any = 0;
    voucherAmount: any = 0;
}