{"name": "examroom.ai", "version": "11.1.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "NODE_ENV=production node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build --configuration production", "build:prod": "ng build --prod --base-href /candidate/", "build:uat": "ng build --c uat --base-href /candidate/", "build:qa": "ng build --c qa --base-href /candidate/", "build:dev": "ng build --c dev --base-href /candidate/", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "ngcc --properties es2015 browser module main --first-only --create-ivy-entry-points --tsconfig './tsconfig.app.json'", "bundle-report": "webpack-bundle-analyzer dist/exai/stats.json"}, "private": true, "dependencies": {"@agm/core": "^1.1.0", "@angular/animations": "~12.1.0", "@angular/cdk": "~11.2.4", "@angular/common": "~12.1.0", "@angular/compiler": "~12.1.0", "@angular/core": "~12.1.0", "@angular/flex-layout": "11.0.0-beta.33", "@angular/forms": "~12.1.0", "@angular/material": "~11.2.4", "@angular/material-moment-adapter": "^11.2.13", "@angular/platform-browser": "~12.1.0", "@angular/platform-browser-dynamic": "~12.1.0", "@angular/router": "~12.1.0", "@iconify/icons-emojione": "~1.1.0", "@iconify/icons-fa-brands": "~1.1.0", "@iconify/icons-fa-solid": "~1.1.0", "@iconify/icons-ic": "~1.1.3", "@iconify/icons-logos": "~1.1.9", "@material/radio": "^12.0.0", "@ng-dynamic-forms/core": "^14.0.1", "@ng-dynamic-forms/ui-material": "^14.0.1", "@ngneat/until-destroy": "~8.0.4", "@ngrx/effects": "^12.4.0", "@ngrx/router-store": "^12.4.0", "@ngrx/store": "^12.4.0", "@ngx-loading-bar/core": "~5.1.1", "@ngx-loading-bar/router": "~5.1.1", "@openreplay/tracker": "^9.0.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/googlemaps": "^3.43.3", "@visurel/iconify-angular": "~11.0.0", "angular-imask": "3.4.0", "apexcharts": "^3.27.2", "date-fns": "~2.19.0", "dom-to-image": "^2.6.0", "file-saver": "^2.0.5", "highlight.js": "~10.6.0", "html-to-pdfmake": "^2.3.7", "html2canvas": "^1.4.1", "imask": "^6.2.2", "jspdf": "^2.5.0", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.9.39", "luxon": "~1.26.0", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "ng-apexcharts": "^1.5.12", "ng-dynamic-breadcrumb": "^6.0.0", "ng2-charts": "^2.4.2", "ngx-doc-viewer": "^2.0.5", "ngx-editor": "^11.1.0", "ngx-mask": "^12.0.0", "ngx-mat-intl-tel-input": "^4.0.0", "pdfmake": "^0.2.4", "peerjs": "^1.3.2", "product-fruits": "^1.0.15", "rxjs": "~6.6.0", "simplebar": "~5.3.0", "tailwindcss": "~2.0.3", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.0", "@angular/cli": "~12.1.0", "@angular/compiler-cli": "~12.1.0", "@angular/language-service": "~12.1.0", "@ngrx/store-devtools": "^12.4.0", "@paypal/paypal-js": "^7.0.3", "@types/faker": "^5.1.5", "@types/luxon": "~1.25.0", "@types/node": "~14.14.7", "@types/showdown": "~1.9.3", "@types/simplebar": "~5.1.1", "codelyzer": "~6.0.1", "postcss": "^8.1.0", "postcss-scss": "^3.0.5", "typescript": "~4.3.4", "webpack-bundle-analyzer": "^4.4.2"}}