.my-12 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.fb-container {
    padding-left: 1rem!important;
    padding-right: 1rem!important;
}
.fb-ht-fixed{
    height: 93vh;
    overflow: auto;
}
.fb-ht-fixed-sm{
    height: 25vh;
    overflow: auto;
}
#wMsg {
	display: none;
}
.add-icon{
    position:absolute;
    bottom:8px;
    right:10px;
    font-size: 1.7rem!important;

    cursor:pointer;
}
.editor{
    width:92%!important;
    margin:0.5rem;
}

.basic-card{
    // margin:1rem 1rem;
    max-height:100%;
    max-width:100%;
    min-height:fit-content;
    min-width: fit-content;
    border:1px solid rgba(49, 48, 48, 0.829);
    border-radius:8px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    position:relative;
    padding:0.7rem;
}
.basic-card-children{
    margin:0.5rem 0.5rem;
    max-height:100%;
    max-width:100%;
    min-height:fit-content;
    min-width: fit-content;
    border:1px solid rgba(49, 48, 48, 0.829);
    border-radius:12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.genesis-form-feild{
    width:27%;
    margin:0.7rem;
}