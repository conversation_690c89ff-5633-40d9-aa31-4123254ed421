import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { switchMap, tap } from 'rxjs/operators';
import { HelpService } from '../../help.service';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { Roles } from '../../help.component'
@Injectable({
  providedIn: 'root'
})
export class FileUploadService {

  constructor(
    private http: HttpClient,
    private helpService: HelpService,
    private globaService: GlobalUserService
  ) { }

  createTicketUrl: string =  environment.baseUrl + 'customerservice/api/tickets'
  createAttachment: string =  environment.baseUrl + 'customerservice/api/tickets/create-ticket-attachment'


  raiseTicket(event): any {
    let formatedUploadData = event.b.split('|')
    
    return this.helpService.selectedCategory.pipe(switchMap(selectedCategory => {
      let referenceIdSet 

      return this.helpService.selectedTicket.pipe(switchMap(selectedTicket => {     
        // --------check which ticket category ------
        if (selectedTicket.length > 0 && selectedTicket[0].id) {
          referenceIdSet = selectedTicket[0].id
        } else if (selectedTicket.length > 0 &&  selectedTicket[0].Id) {
          referenceIdSet = selectedTicket[0].Id
        } else if (selectedTicket.length > 0 &&  selectedTicket[0].personFormId) {
          referenceIdSet = selectedTicket[0].personFormId
        } else {
          return this.http.post(this.createTicketUrl, {
            ticketCategoryId: selectedCategory.id,
            subject: event.a.subject,
            description: event.a.description,
            createdBy: this.helpService.personTenantRoleId,
            assignedToRoleId: Roles.SupportingStaff,
            personTenantRoleId: this.helpService.personTenantRoleId, 
          })
        }

        return this.http.post(this.createTicketUrl, {
          ticketCategoryId: selectedCategory.id,
          referenceId: referenceIdSet,
          subject: event.a.subject,
          description: event.a.description,
          createdBy: this.helpService.personTenantRoleId,
          assignedToRoleId: Roles.SupportingStaff,
          personTenantRoleId: this.helpService.personTenantRoleId, 
        })
      })).pipe(
        switchMap(data => {
          if(data && data["id"]){
            return this.http.post(this.createAttachment, {
              ticketId: data["id"],
              fileName: formatedUploadData[0],
              systemFileName: formatedUploadData[1],
              createdBy: this.globaService.userDetails.value.personId
            })
          }
        })
      )
    }))
  }
}
