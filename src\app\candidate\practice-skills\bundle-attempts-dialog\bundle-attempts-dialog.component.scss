.dialog-body {
  background-color: var(--background-base1);
}

.close-btn {
  color: var(--text-color2);
  background-color: transparent;
  border: none;
  cursor: pointer;
  width: 2rem;
  height: 2rem;
  transition: background-color 0.2s, color 0.2s, transform 0.1s;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #d32f2f;
  transform: scale(1.05);
  border-radius: 50%;
}

.close-btn:focus {
  outline: none;
}

.description_text {
  color: var(--text-color4);
  margin-bottom: 1rem;
}

.meta-label {
  color: var(--text-edit);
}

.meta-value {
  color: var(--text-color2);
}

.qty-btn {
  width: 2rem;
  height: 2rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: #d1e4f0;
  cursor: pointer;
}

.qty-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.subtotal-text {
  color: var(--text-color2);
  font-weight: 600;
}

.per_attempt {
  color: var(--text-edit);
}

.cancel-btn {
  border: 1px solid #ef4444;
  color: #ef4444;
  border-radius: 0.375rem;
  background-color: transparent;
  cursor: pointer;
  transition: background-color 0.3s ease;
  padding: 0.25rem 1rem;
}

.cancel-btn:hover {
  background-color: #fef2f2;
}

.proceed-btn {
  background-color: var(--text-color2);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: background-color 0.3s ease;
  padding: 0.25rem 1rem;
}

.proceed-btn:hover {
  background-color: #2563eb;
}



.bundle-includes {
  .includes-list {
    list-style-type: disc;
    padding-left: 1.25rem; 
    font-size: 0.75rem; 
    color: #4b5563; 

    li {
      margin-bottom: 0.25rem;
    }
  }
}
.select_skill{
   color: var(--text-edit);
}