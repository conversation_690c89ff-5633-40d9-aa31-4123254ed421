{"v": "5.5.2", "fr": 24, "ip": 0, "op": 109, "w": 700, "h": 700, "nm": "コンボ１", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "フラッシュ", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [131, 249, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[269, 71], [0, 228.5], [-0.5, 496.5], [5, 495], [268.5, 345]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 1"}], "w": 262, "h": 498, "ip": 21, "op": 261, "st": 21, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "シェイプレイヤー 16", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [63, 355.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.263, -3.927], [-2.631, 1.519], [0.461, 3.549], [3.552, -1.656]], "o": [[0.237, 3.539], [2.631, -1.519], [-0.461, -3.55], [-3.552, 1.656]], "v": [[-5.92, 3.943], [1.25, 6.25], [5.723, -2.516], [-0.921, -6.113]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.956862804936, 0.384313755409, 0.423529441684, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "シェイプレイヤー 15", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "シェイプレイヤー 14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "シェイプレイヤー 13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [146, 308, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.122, -3.826], [-2.131, 1.23], [0.365, 5.086], [2.618, -1.268]], "o": [[0.127, 3.98], [2.131, -1.23], [-0.365, -5.086], [-2.618, 1.268]], "v": [[-6.484, 3.421], [0.517, 6.624], [6.241, -2.768], [-1.857, -6.252]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223529426724, 0.211764720842, 0.227450995352, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "シェイプレイヤー 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -8.823], [7.344, -4.24], [0, 8.823], [-7.344, 4.24]], "o": [[0, 8.823], [-7.344, 4.24], [0, -8.823], [7.344, -4.24]], "v": [[14.244, -7.699], [2.592, 13.9], [-14.244, 7.813], [-3.565, -13.9]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223529426724, 0.211764720842, 0.227450995352, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-28.801, 87.918], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "シェイプレイヤー 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.951, 0.55], [0, 0], [-5.16, 5.655], [-3.249, 3.214], [0, 0], [0, 0], [0, 0], [5.925, -6.096], [6.296, -6.497], [0.238, -0.567]], "o": [[0, 0], [0, 0], [5.16, -5.655], [3.249, -3.214], [0, 0], [0, 0], [0, 0], [-5.925, 6.096], [-6.296, 6.497], [-0.239, 0.567]], "v": [[-45.056, 38.846], [-3.01, 12.469], [6.164, 4.879], [27.951, -19.549], [45.916, -37.948], [46.107, -39.396], [7.12, -14.402], [-13.33, 3.711], [-31.607, 21.78], [-45.869, 37.836]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686334348, 0.490196108351, 0.313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1.782, -28.027], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "シェイプレイヤー 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.585, -3.806], [-0.573, -1.196], [0, 0], [0, 0], [0, 0], [0, 0], [5.15, -3.617]], "o": [[0, 0], [1.255, 3.013], [1.288, 2.69], [0, 0], [0, 0], [0, 0], [0, 0], [-5.15, 3.617]], "v": [[-48.908, 32.267], [-45.96, 39.331], [-42.296, 46.06], [-35.644, 54.452], [15.645, 24.196], [48.908, -54.452], [12.426, -8.281], [1.911, 1.009]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223529426724, 0.211764720842, 0.227450995352, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.726, -1.841], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "シェイプレイヤー 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.36, -0.332], [0, 0], [5.584, -3.584], [0, 0], [-1.261, 4.15], [0, 0]], "o": [[0, 0], [0.36, 0.332], [0, 0], [-5.584, 3.584], [0, 0], [1.261, -4.15], [0, 0]], "v": [[37.465, -40.031], [60.521, -53.162], [44.13, -9.656], [37.105, -3.618], [-59.62, 52.406], [-59.62, 49.344], [-41.248, 12.619]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686334348, 0.490196108351, 0.313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1.777, 18.609], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "シェイプレイヤー 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.738, 2.641], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.738, -2.641], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-100.997, 78.814], [-100.93, 94.006], [-100.93, 96.221], [10.555, 39.238], [13.508, 35.318], [13.877, 17.016], [36.765, 0.849], [100.998, -43.619], [100.259, -46.884], [60.391, -96.221], [39.718, -46.631], [36.765, -48.618], [37.503, -43.138], [35.288, -40.382], [38.979, -9.29], [24.213, 6.619], [-82.103, 63.571], [-85.057, 66.014], [-85.795, 67.917]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223529426724, 0.211764720842, 0.227450995352, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.543, 68.016], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "シェイプレイヤー 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.623, -2.229], [0, 0], [0, 0], [0, 0], [-6.437, 3.509], [0, 0], [0, 0], [0.415, 9.104], [0, 0], [0, 0], [0, 0], [5.191, -2.582], [0, 0], [0, 0], [0, 0], [0, 0], [2.907, -1.679], [0, 0], [1.661, -3.451], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.038], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [6.437, -3.509], [0, 0], [0, 0], [-0.415, -9.104], [0, 0], [0, 0], [0, 0], [-5.191, 2.582], [0, 0], [0, 0], [0, 0], [0, 0], [-2.907, 1.678], [0, 0], [-1.661, 3.451], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.038], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-101.852, 94.272], [-101.852, 110.676], [-105.382, 113.753], [-105.382, 118.736], [-99.36, 119.828], [10.279, 65.457], [99.36, -5.078], [105.382, -17.069], [104.967, -36.348], [101.644, -35.676], [59.907, -88.409], [55.546, -87.137], [29.175, -71.704], [44.541, -111.515], [50.563, -119.976], [48.486, -122.93], [44.126, -121.658], [-1.973, -92.759], [-57.623, -35.712], [-58.453, -28.172], [-57.415, -22.75], [-54.093, -24.253], [-51.186, -17.21], [-73.819, 26.798], [-86.901, 39.749], [-88.978, 44.894], [-88.147, 47.737], [-86.694, 46.274], [-82.125, 77.484], [-83.994, 80.224], [-84.617, 81.83], [-100.191, 91.859]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.84313731474, 0.839215746113, 0.85882358925, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [5.027, 52.66], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "シェイプレイヤー 6", "sr": 1, "ks": {"o": {"a": 0, "k": 80, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [211.5, 354.375, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, 0], [0, 0], [0, 3.826]], "v": [[124.708, 67.981], [-124.708, 211.981], [-129.904, 208.052], [-129.904, -63.638], [129.904, -213.638], [129.904, 58.052]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.974984681373, 0.974984681373, 0.974984681373, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-80.506, -70.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "シェイプレイヤー 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.072, 313.312, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-129.904, 61.65], [-129.904, 88.35], [129.904, -61.65], [129.904, -88.35]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.078, -180.336], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "シェイプレイヤー 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [29.5, 195.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607858097, 0.776470648074, 0.270588235294, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.478, -48.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "シェイプレイヤー 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.5, 156.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.964705942191, 0.764705942191, 0.188235309077, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "シェイプレイヤー 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20, 164.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.372549019608, 0.235294132607, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "シェイプレイヤー 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144, 311, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0], [2.87, -1.657], [0, 0], [0, 3.826]], "v": [[124.708, 101.205], [-124.708, 245.205], [-129.904, 241.277], [-129.904, -91.277], [-124.708, -101.205], [124.708, -245.205], [129.904, -241.277], [129.904, 91.277]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.87450986376, 0.882353001015, 0.898039275525, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.212, -61.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "シェイプレイヤー 17", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [100]}, {"t": 10, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100.065, 340.32, 0], "ix": 2}, "a": {"a": 0, "k": [-27.195, 89.334, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 3, "s": [2621.037, 2621.037, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [33.5, 28.148], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "楕円形パス 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-27.195, 89.334], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "楕円形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "シェイプレイヤー 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [117, 241, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.243, 0.093], [-0.111, -0.045], [1.531, -1.709], [0.871, -0.12], [-0.728, 0.522], [1.891, -0.178], [0.078, -0.721], [-0.624, 0.35], [0.44, -0.734], [0.597, -1.117], [1.793, -2.981], [0.863, -1.621], [0.787, 0.17], [-0.177, -0.763], [-0.169, -1.089], [-0.213, -0.201], [0.471, -0.394], [-0.016, -0.477], [0.239, -0.212], [-0.062, -0.223], [-0.507, -0.103], [-0.014, -0.965], [0.015, -0.061], [0.028, -2.178], [-0.245, 0.223], [-0.016, 0.129], [0.056, 0.342], [-1.371, 0.798], [0.176, -1.313], [-0.025, -0.203], [-0.581, 0.406], [0.011, 0.31], [0.299, 1.853], [-0.464, 0.863], [-1.48, -0.562], [-0.603, 0.995], [-3.363, 5.502], [-0.574, -0.043], [-0.745, 0.722], [-0.653, -0.152], [-0.764, -0.184], [-0.276, -0.239], [-1.169, 2.642], [0.385, 0.22], [0.419, -0.276], [0.713, -0.669], [0.395, 0.127], [-0.022, 0.209], [-0.09, 0.078], [-0.114, 0.152], [-0.519, 0.832], [-0.65, -0.154], [-0.635, 1.374], [-0.212, 0.341], [0.013, 0.461], [1.168, -1.792], [0.711, 0.345], [0.591, -1.32], [0.327, -1.086], [0.701, 0.115], [1.467, 0.01], [-1.208, 1.061], [-0.072, 0.667], [-0.194, 0.099], [-1.747, 1.193], [-1.17, 1.879], [-1.225, 2.057], [-3.348, 0.193], [-0.349, 0.036], [-0.508, 0.051], [-1.453, -0.137], [-0.697, 1.021], [0.272, 3.215], [0.793, -0.18], [0.422, -0.503], [0.678, -1.275], [0.226, 0.191], [0.557, 0.944], [-0.242, 1.035], [-0.137, -0.8], [0.787, -0.145], [1.06, -1.038], [0.42, -0.792], [0.573, -1.5], [0.311, -0.547], [1.966, -3.057], [1.052, -1.104], [2.666, -2.273], [0.953, -0.733], [-0.131, -1.327], [0.493, -1.565], [0.004, -0.074], [-0.152, -0.309], [0.043, -0.225], [0.925, -0.674], [0.113, -0.202], [-0.147, -0.093], [0.309, -0.888], [0.646, -0.409], [0.484, -0.847], [-0.28, -0.312], [0.736, 0.934], [-1.193, 1.542], [-0.022, 0.943], [-0.635, 0.885], [-2.666, 1.643], [-0.053, 0.185], [0.073, 0.403], [0.07, -0.066], [0.026, -0.317], [1.017, -0.816], [0.108, 0.048], [0.361, -0.237], [0.003, -0.04]], "o": [[0.111, 0.045], [-0.555, 2.032], [-0.453, -1.024], [0.648, 1.147], [-0.631, 1.577], [-0.337, 0.032], [-0.097, 0.892], [-0.44, 0.734], [-0.833, -0.918], [-2.16, 2.796], [-0.926, 1.54], [-0.474, 0.89], [-0.512, -0.111], [0.24, 1.034], [0.213, 0.201], [-0.036, 0.636], [-0.547, 0.458], [-0.295, 0.064], [-0.281, 0.25], [0.149, 0.534], [0.561, 0.114], [-0.105, 0.087], [-0.622, 2.498], [-0.003, 0.207], [0.132, -0.12], [0.05, -0.408], [-0.187, -1.146], [1.405, -0.818], [-0.031, 0.234], [0.041, 0.333], [0.429, -0.3], [-0.072, -1.99], [0.564, -0.843], [1.067, -1.986], [0.534, 0.203], [3.339, -5.51], [0.721, -0.671], [0.525, 0.039], [0.919, -0.891], [0.767, 0.178], [0.2, 0.334], [1.352, 1.171], [0.336, -0.759], [-0.39, -0.223], [-0.059, -0.354], [-0.712, 0.668], [0.023, -0.208], [0.09, -0.079], [0.114, -0.152], [0.664, -0.355], [0.507, -0.813], [1.105, 0.262], [0.212, -0.341], [0.41, -0.641], [-0.234, -0.906], [-0.854, 1.311], [-1.356, -0.658], [-0.704, 0.978], [-0.649, 1.155], [-1.37, -0.224], [-1.159, -0.008], [-0.264, -0.364], [0.03, -0.279], [1.87, -0.951], [1.366, 0.19], [1.27, -2.041], [2.499, -4.196], [0.342, -0.02], [0.309, 0.347], [1.452, 0.138], [0.658, 0.062], [2.56, -3.749], [0.139, -0.845], [-0.922, 0.21], [-1.003, 1.194], [-0.156, 0.293], [-0.743, -0.63], [0.599, -0.39], [-1.693, 0.792], [-0.968, 0.601], [-0.782, 0.144], [-0.688, 0.3], [-1.1, -0.014], [-0.205, 0.537], [-1.966, 3.057], [-0.121, -1.37], [-2.46, 2.58], [-0.177, -0.313], [-0.645, 0.496], [-0.458, 0.309], [-0.242, 0.199], [-0.026, 0.516], [-0.045, 0.226], [-0.927, 0.663], [-0.204, 0.149], [-0.147, 0.264], [0.237, 0.15], [-0.272, 0.782], [-0.583, 0.369], [0.231, 0.53], [-1.329, 2.172], [1.012, -1.619], [0.668, -0.864], [0.887, -0.611], [1.709, -2.384], [0.059, -0.036], [0.337, -0.245], [-0.017, -0.093], [-0.409, 0.381], [-0.907, 0.498], [-0.109, -0.048], [-0.046, -0.218], [-0.039, 0.025], [-0.03, 0.378]], "v": [[-11.568, 7.831], [-11.236, 7.965], [-14.544, 13.518], [-16.754, 14.021], [-14.541, 13.514], [-17.549, 16.956], [-18.76, 18.063], [-17.512, 18.224], [-18.831, 20.425], [-20.85, 21.81], [-26.563, 30.553], [-29.499, 35.181], [-31.54, 36.489], [-32.275, 37.468], [-31.749, 40.722], [-31.11, 41.326], [-32.092, 42.855], [-32.761, 44.202], [-33.634, 44.425], [-33.691, 45.198], [-32.639, 46.003], [-31.144, 46.719], [-31.447, 46.976], [-31.663, 53.58], [-31.181, 53.603], [-30.861, 53.106], [-30.826, 51.935], [-29.248, 49.32], [-27.691, 50.147], [-27.693, 50.807], [-27.149, 51.286], [-26.772, 50.273], [-26.919, 44.262], [-25.245, 41.728], [-21.617, 39.09], [-19.908, 38.003], [-9.83, 21.494], [-8.017, 20.938], [-6.212, 20.452], [-3.973, 19.827], [-1.68, 20.377], [-1.068, 21.366], [3.451, 18.707], [3.481, 16.994], [2.39, 18.031], [1.785, 17.563], [0.387, 17.842], [0.455, 17.217], [0.724, 16.982], [1.066, 16.526], [2.87, 14.939], [4.608, 13.787], [7.12, 11.696], [7.757, 10.672], [7.965, 9.184], [6.199, 9.373], [3.77, 10.84], [1.347, 12.921], [-0.405, 15.957], [-2.39, 17.139], [-6.573, 16.613], [-6.435, 15.132], [-7.435, 14.943], [-6.834, 13.904], [-1.916, 9.457], [2.002, 6.882], [5.743, 0.733], [11.581, -3.657], [12.627, -3.764], [13.991, -3.6], [18.349, -3.186], [20.226, -4.397], [23.594, -14.791], [22.856, -16.175], [21.759, -14.463], [19.512, -10.706], [18.867, -10.105], [15.982, -10.743], [17.554, -12.335], [15.998, -10.742], [13.695, -10.513], [11.34, -10.065], [9.682, -8.422], [7.213, -6.239], [6.296, -4.601], [0.399, 4.57], [-1.533, 4.799], [-8.876, 12.594], [-9.507, 11.813], [-11.221, 13.944], [-12.59, 14.696], [-13.283, 15.275], [-12.512, 15.692], [-12.645, 16.369], [-15.425, 18.365], [-16.005, 18.908], [-15.839, 19.373], [-15.084, 19.73], [-16.604, 21.358], [-18.365, 22.675], [-17.084, 22.89], [-20.87, 25.118], [-17.771, 20.289], [-16.447, 17.598], [-14.404, 15.144], [-9.059, 8.138], [-8.899, 7.602], [-8.303, 6.684], [-8.615, 6.615], [-8.9, 7.602], [-11.212, 7.928], [-11.538, 7.784], [-11.956, 7.533], [-12.06, 7.696]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 1"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.187, 0.281], [0, 0], [-0.739, 1.17], [-0.19, 0.913], [0.076, 0.375], [-0.045, 0.036], [0.21, -0.178], [1.013, -1.069], [-0.086, -0.195], [0.098, -1.094], [-0.115, 0.398]], "o": [[0, 0], [0.375, 1.128], [1.601, -0.914], [0.09, -0.429], [0, -0.002], [-0.657, 0.456], [-1.082, 0.915], [-0.206, 0.218], [-0.093, 1.036], [0.743, -0.569], [0.187, -0.281]], "v": [[54.195, -45.603], [54.259, -45.637], [55.833, -45.696], [57.065, -48.512], [56.853, -49.637], [56.982, -49.741], [56.01, -50.005], [52.861, -46.988], [53.224, -46.568], [52.941, -43.403], [53.635, -44.76]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 2"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.002, 0.273], [2.129, -1.807], [-0.799, 0.879], [-0.718, 2.167], [0.06, 0.401], [0.616, -0.755], [0.006, -0.112], [0.064, -2.087], [-3.026, 1.969], [-0.088, 0.697], [0.401, -0.058], [0, 0.134], [-0.122, 0.283]], "o": [[0.248, -0.294], [-0.178, -1.65], [0.004, -0.682], [0.851, 0.031], [0.348, -0.415], [-0.095, -0.636], [-0.054, 0.066], [-0.891, 2.509], [-0.093, 3.05], [0.687, -0.447], [0.143, -1.137], [0, -0.134], [0.122, -0.283], [0, 0]], "v": [[62.803, 59.498], [63.127, 58.652], [59.826, 58.877], [60.433, 56.683], [62.887, 55.124], [63.42, 53.882], [59.551, 54.508], [59.526, 54.838], [59.094, 61.236], [62.479, 62.379], [63.539, 60.805], [62.45, 60.729], [62.45, 60.327], [62.816, 59.478]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 3"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.463, 0.197], [-0.132, 0.503], [0.314, -0.954]], "o": [[1.285, -0.382], [-0.609, -0.083], [-0.293, 0.889]], "v": [[-30.843, 73.422], [-30.121, 71.947], [-31.661, 73.109]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 4"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.737, 0.435], [-0.299, 0.262], [-0.147, 1.098], [0, 0], [0.368, 0.537], [2.494, -1.828], [0.102, -0.902], [-0.038, -1.233]], "o": [[0.299, -0.262], [0.925, -0.766], [0, 0], [0.26, -0.903], [-0.589, -1.694], [-1.664, 1.219], [-0.149, 1.318], [0.583, 0.607]], "v": [[51.556, 5.845], [52.454, 5.058], [54.195, 2.318], [54.19, 2.218], [54.178, -0.029], [50.139, 0.063], [49.339, 3.018], [49.405, 6.739]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 5"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.157, 0.278], [-0.435, 0.272], [-0.563, 0.172], [0.066, 0.587], [1.037, -0.374], [0.864, -0.723], [1.608, -1.381], [0.137, -0.612], [-0.061, -0.047]], "o": [[0.435, -0.272], [0.596, -0.314], [0.691, -0.211], [-0.051, -0.451], [-1.573, 0.567], [0.028, -0.896], [-0.781, 0.671], [-0.029, 0.166], [0.181, 0.139]], "v": [[-36.117, 76.785], [-34.812, 75.969], [-33.03, 75.057], [-32.135, 73.871], [-32.859, 73.023], [-34.819, 75.734], [-36.056, 74.845], [-36.577, 76.691], [-36.651, 77.17]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 6"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.983, -0.598], [0.726, -0.707], [0.217, -0.223], [0.549, -0.567], [-0.174, -0.359], [-0.661, 0.299], [-0.345, 0.556], [-0.332, 0.182], [-1.335, 1.163]], "o": [[-0.492, -0.8], [-0.217, 0.223], [-0.334, -0.765], [-0.607, 0.626], [0.434, 0.9], [0.32, 0.224], [0.332, -0.182], [0.938, -0.569], [-1.148, 0.23]], "v": [[-4.928, 49.474], [-6.902, 50.275], [-7.552, 50.943], [-9.17, 51.488], [-9.523, 53.162], [-7.572, 52.303], [-6.573, 51.759], [-5.578, 51.212], [-2.354, 49.286]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 7"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.974, -1.354], [0.085, -1.51], [0.649, -0.305], [0.767, -0.303], [-0.114, -0.905], [-0.059, -0.552], [-0.589, 0.29], [-0.823, 0.44], [-0.417, 3.609], [0.595, 1.028]], "o": [[-0.923, 0.501], [-0.649, 0.305], [0.145, -0.493], [-0.553, 0.219], [-0.875, 0.715], [0.058, 0.545], [0.823, -0.405], [3.339, -1.786], [0.506, -1.422], [-0.525, 1.537]], "v": [[-26.129, 7.29], [-28.193, 9.641], [-30.142, 10.555], [-30.223, 9.46], [-31.475, 11.273], [-32.079, 13.088], [-31.091, 13.427], [-28.628, 12.287], [-24.871, 6.879], [-24.898, 3.269]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 8"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.312, 0.624], [-0.262, -0.123], [-0.771, 1.612], [-0.712, 0.438], [0.022, 0.503], [0.927, -0.904], [1.215, -0.945], [-0.385, -0.617]], "o": [[0.333, -0.213], [0.997, 0.467], [-0.049, 0.997], [0.856, -0.526], [-0.074, -1.667], [-1.21, 0.251], [-0.418, 0.544], [0.209, 0.335]], "v": [[-25.419, 65.96], [-24.52, 65.713], [-21.865, 63.898], [-20.57, 64.118], [-19.845, 62.523], [-21.854, 62.879], [-25.495, 64.954], [-26.437, 66.63]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 9"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.044, 0.689], [-0.075, 0.129], [-0.416, 0.698], [0.228, 0.842], [-0.467, 1.452], [0.254, -1.178], [-0.444, -1.27]], "o": [[0.074, -0.129], [0.469, -0.368], [-0.444, -0.431], [-0.361, -1.336], [-1.024, 0.932], [-0.358, 1.662], [0.565, -0.93]], "v": [[39.721, -47.312], [39.945, -47.7], [41.401, -48.806], [39.716, -48.678], [41.624, -51.614], [39.123, -49.586], [39.305, -45.098]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 10"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.327, -0.715], [-0.741, 1.27], [0.677, 0.478], [0.921, -0.985], [-0.177, -0.096]], "o": [[0.893, 0.423], [0.739, -1.267], [-0.677, -0.478], [-0.395, 0.423], [-1.395, 1.685]], "v": [[-3.669, 65.671], [-1.124, 64.084], [-1.118, 61.361], [-3.632, 62.418], [-3.595, 63.016]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 11"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.379, 0.223], [-0.164, 0.101], [0.014, 1.137], [0.822, -0.556], [0.483, -0.029], [0.321, -0.726], [-0.126, -0.358], [-0.525, 0.283]], "o": [[0.164, -0.093], [0.6, -0.371], [-0.012, -1.013], [-0.485, 0.328], [-0.502, 0.03], [-0.217, 0.491], [0.206, 0.583], [0.379, -0.223]], "v": [[-34.049, 63.894], [-33.556, 63.615], [-32.188, 62.005], [-33.586, 61.721], [-35.026, 62.41], [-36.502, 63.448], [-36.6, 64.905], [-35.187, 64.562]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 12"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.597, 0.137], [-0.089, -0.054], [0.265, -0.419], [-0.802, -0.271], [-0.361, 0.702], [0.185, 0.071], [-1.715, 1.927], [-0.199, 0.931], [0.972, -0.355], [0.538, -0.689], [-0.081, -0.011]], "o": [[0.089, 0.054], [-0.132, 0.475], [-1.165, 1.844], [0.156, 0.053], [0.47, -0.914], [-1.384, -0.526], [0.468, -0.526], [-1.031, 0.063], [-0.565, 0.206], [-0.06, 0.078], [0.593, 0.079]], "v": [[25.133, -41.137], [25.399, -40.975], [24.982, -39.559], [25.481, -37.163], [26.45, -37.584], [25.934, -37.998], [26.47, -41.746], [27.912, -43.519], [25.081, -41.575], [23.352, -40.434], [23.396, -40.036]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 13"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.003, 1.156], [0.288, -0.162], [-0.082, -0.361], [1.086, -0.931], [0.365, 0.979], [-0.346, -1.603], [0.044, 0.316], [-0.909, 0.538], [0.053, -0.855], [-0.598, 0.344], [-0.014, 0.439]], "o": [[0.218, -0.455], [-0.348, 0.196], [-0.185, 0.634], [-1.82, 1.56], [-0.282, 1.884], [0.67, -0.845], [-0.123, -0.883], [0.921, -0.546], [-0.025, 0.398], [0.733, -0.421], [0.036, -1.174]], "v": [[-10.726, 65.4], [-10.99, 65.028], [-11.492, 65.928], [-12.02, 67.837], [-14.217, 67.08], [-14.305, 72.284], [-13.832, 70.786], [-12.646, 68.91], [-11.549, 69.429], [-11.198, 70.296], [-10.748, 68.873]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 14"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.623, 0.82], [-1.07, 1.144], [1.531, -1.192], [0.893, -1.313], [-0.373, -0.029], [0.442, -0.76], [0.092, -0.187], [-0.244, 0.265], [-0.488, 1.352], [-0.888, 0.922]], "o": [[-0.097, -0.45], [-1.575, 0.388], [0.878, 0.195], [-0.326, 0.408], [-0.031, 0.376], [-0.109, 0.186], [-0.145, 0.296], [0.584, -0.633], [0.777, -0.821], [1.489, -1.545]], "v": [[49.458, -44.357], [49.509, -45.863], [45.012, -43.297], [44.997, -41.789], [44.973, -41.083], [45.305, -40.355], [44.97, -39.799], [45.123, -39.464], [46.829, -40.545], [48.702, -41.071]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 15"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.055, -0.033], [0.583, -1.023], [-0.212, 0.143], [-0.408, -0.466], [0.199, -0.334], [0.483, -0.241], [0.172, -0.712], [-0.474, 0.129], [-0.439, 1.197], [1.058, -0.357], [-0.714, 1.23], [-0.169, 0.183], [0.116, -0.142]], "o": [[-0.044, 0.878], [0.385, -0.222], [0.666, -0.45], [0.154, 0.177], [-0.629, 1.057], [-0.621, 0.31], [-0.181, 0.747], [1.326, -0.361], [0.48, -1.309], [-0.794, 0.268], [0.051, -0.353], [-0.109, 0.149], [-0.039, 0.048]], "v": [[62.459, -63.135], [61.121, -60.373], [61.935, -60.846], [63.679, -61.292], [63.556, -60.467], [62.176, -59.983], [60.866, -58.513], [61.709, -58.01], [64.329, -60.927], [62.995, -62.149], [62.441, -63.136], [62.95, -63.681], [62.62, -63.236]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 16"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [0.166, 0.547], [0.965, -0.564], [0.012, -0.817], [-0.007, -1.874], [-0.249, 0.093], [0.041, 0.147], [-1.548, 1.37], [-0.002, 0.057]], "o": [[1.224, -1.063], [-0.323, -1.066], [-0.434, 0.254], [-0.027, 1.893], [0.001, 0.235], [0.254, -0.095], [-0.438, -1.576], [0.017, -0.016], [0, 0]], "v": [[-12.198, 39.064], [-11.557, 36.639], [-14.045, 37.078], [-15.165, 38.276], [-15.167, 43.911], [-14.805, 44.183], [-14.322, 43.592], [-12.215, 39.242], [-12.207, 39.077]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 17"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.098, -0.994], [0.107, -0.661], [-0.999, 0.158], [-0.954, -0.016], [1.147, -2.374], [-0.503, 0.533], [-0.345, 1.001], [-0.683, 0.412], [-1.164, 0.271], [-0.13, 0.535], [0.241, -0.008], [1.102, -0.039], [0.39, 0.211], [0.451, 0.227], [0.477, -0.553], [0.729, 0.114], [0.752, -0.417], [-0.021, 0.748], [0.221, -0.134]], "o": [[-0.314, 0.115], [-0.116, 0.712], [0.471, 0.691], [0.71, 0.012], [-0.27, 0.558], [0.736, -0.78], [0.273, -0.792], [1.069, -0.062], [0.297, -0.069], [0.09, -0.37], [-1.102, 0.038], [-0.294, -0.418], [-0.172, -0.505], [-0.586, -0.295], [-0.895, 1.037], [-0.511, -0.08], [0.267, -0.626], [0.007, -0.259], [-0.554, 0.337]], "v": [[-7.572, -2.089], [-8.469, -1.545], [-7.968, 0.099], [-5.826, 1.012], [-4.488, 2.684], [-3.85, 2.785], [-1.986, 0.211], [-0.626, -1.568], [2.424, -1.415], [3.232, -2.077], [2.772, -2.4], [-0.534, -2.284], [-1.961, -2.362], [-1.441, -4.013], [-2.925, -2.693], [-5.321, -1.711], [-6.898, -1.859], [-5.736, -3.493], [-6.205, -3.561]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 18"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.65, 0.335], [-0.854, 1.344], [0.612, 0.873], [1.025, -0.794], [0.093, -1.32], [-0.031, -0.421]], "o": [[1.023, 0.368], [1.017, -1.6], [-0.458, -0.653], [-1.324, 1.025], [-0.032, 0.452], [0.001, 0.606]], "v": [[-29.227, 80.426], [-26.31, 78.593], [-25.923, 74.852], [-28.24, 74.995], [-30.212, 78.646], [-30.13, 79.913]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 19"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.44, -0.041], [0.576, 0.074], [0.386, -0.458], [-0.106, -0.413], [-0.017, -0.099], [0.77, -0.501], [0.683, 1.796], [0.256, 1.504], [-0.388, -1.417], [-0.046, -0.466], [-1.997, 1.514], [-1.427, 0.796], [0.034, 0.915]], "o": [[-0.19, -0.397], [-0.435, -0.056], [-0.693, 0.823], [0.024, 0.093], [-0.772, 0.48], [-2.628, 1.708], [0.197, -1.804], [-0.694, 2.078], [0.012, 0.489], [0.194, 1.96], [1.427, -1.082], [0.828, -0.461], [-0.038, -1.008]], "v": [[39.566, 64.645], [39.882, 63.235], [38.19, 63.95], [38.485, 65.14], [38.537, 65.436], [36.222, 66.878], [32.911, 66.795], [32.383, 62.133], [32.582, 66.974], [32.623, 68.437], [35.168, 69.003], [39.446, 66.339], [40.913, 64.379]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 20"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.514, -0.459], [-0.755, 1.205], [-0.2, 0.172], [0.827, 0.509], [-0.825, 0.655], [-0.072, 0.479], [0.269, -0.058], [0.697, -0.851], [0.623, -0.331], [-0.728, -0.909]], "o": [[0.484, 0.295], [0.193, -0.196], [1.068, -0.918], [-0.789, -0.486], [0.319, -0.253], [0.052, -0.344], [-0.583, 0.125], [-0.774, 0.572], [-0.877, 1.369], [-1.209, 1.354]], "v": [[53.481, -53.645], [55.211, -54.296], [55.79, -54.88], [56.119, -56.426], [56.644, -58.085], [57.412, -58.967], [56.985, -59.349], [55.171, -58.611], [54.169, -59.049], [53.175, -55.457]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 21"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.487, -0.283], [0.276, -2.486], [0.02, -0.78], [-0.534, 0.5], [-0.467, 1.899], [-0.398, 0.078], [-0.766, 0.128], [0, 0.343], [0.048, 1.18]], "o": [[-2.715, 0.77], [-0.032, 0.786], [-0.008, 0.327], [0.877, -0.82], [0.056, -0.23], [0.399, 0.181], [0.279, -0.047], [0.001, -1.21], [-0.029, -0.712]], "v": [[63.454, -54.466], [59.899, -50.597], [59.807, -48.241], [60.303, -47.98], [62.974, -50.45], [63.956, -51.328], [64.081, -50.14], [64.591, -50.891], [64.578, -54.51]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 22"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.769, 0.315], [0.503, -1.372], [1.018, 0.079], [1.22, -0.114], [-0.205, 1.442], [-0.49, -2.4], [0.109, -0.056], [0.518, -1.151], [-0.584, 0.041], [-1.656, 0.073], [-0.395, 0.974], [-0.799, 2.037], [0.812, -0.484], [0.809, -0.239], [-0.057, -0.044]], "o": [[-0.557, 1.379], [-0.413, 1.128], [-1.125, -0.087], [0.347, -0.738], [-0.583, 1.326], [-0.108, 0.039], [-0.463, 0.237], [-0.459, 1.019], [1.673, -0.116], [0.706, -0.031], [0.826, -2.039], [0.204, -0.52], [-0.765, -0.122], [-0.037, 0.167], [0.612, 0.476]], "v": [[29.745, 51.022], [28.086, 55.156], [26.294, 56.788], [22.655, 57.085], [24.2, 54.656], [22.303, 57.017], [21.979, 57.136], [20.616, 57.748], [21.319, 58.703], [26.316, 58.411], [27.987, 57.043], [30.43, 50.928], [30.04, 50.281], [27.565, 51.385], [27.465, 51.871]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 23"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.077, -0.373], [0.318, 0.844], [-0.398, 0.625], [0.074, -0.081], [0.545, -1.751], [0.474, -0.866], [-0.705, -0.25], [-1.263, -0.509], [-0.401, 0.501], [-1.164, 2.702], [0.298, 0.19], [0.484, -0.432], [0.549, -0.009], [0.132, -0.37], [-0.289, -0.035], [-0.199, -0.28], [0.593, -0.927], [0.489, 0.479]], "o": [[-1.065, 0.355], [0.48, -0.312], [0.248, -0.39], [-0.806, 0.877], [-0.409, 0.097], [-0.423, 0.774], [1.293, 0.46], [0.358, 0.144], [1.872, -2.337], [0.506, -0.527], [-0.293, -0.187], [-0.443, 0.395], [-0.252, 0.004], [-0.154, 0.435], [0.304, 0.036], [-0.593, 0.927], [0.303, -1.888], [-0.174, -0.17]], "v": [[47.533, 18.186], [45.503, 17.381], [46.903, 16.28], [47.047, 15.385], [44.517, 17.383], [43.338, 17.644], [43.403, 19.54], [47.207, 21.04], [48.414, 20.566], [53.342, 13.189], [53.67, 11.935], [52.356, 12.268], [51.017, 13.318], [50.32, 13.892], [50.649, 14.528], [51.609, 14.525], [49.829, 17.307], [47.931, 17.15]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 24"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.448, -1.69], [1.05, 0.078], [0.355, -0.535], [-0.267, -0.279], [-0.779, -0.061], [-2.432, 3.402], [-1.105, 2.15], [0.788, 0.062], [0.407, -0.549], [0.036, -0.807], [-0.009, -0.049], [0.206, -0.255], [0.569, -0.655], [-0.056, -0.525], [-0.302, 0.076], [-0.18, -0.011], [0.45, -0.707]], "o": [[-1.04, -0.099], [-0.298, -0.022], [-0.385, 0.58], [0.505, 0.526], [2.784, 0.217], [1.42, -1.986], [0.527, -1.025], [-0.684, -0.135], [-0.992, 1.338], [-0.003, 0.055], [-0.206, 0.255], [-0.474, -0.054], [-0.306, 0.353], [0.043, 0.41], [0.196, -0.049], [-0.45, 0.707], [-0.938, 0.203]], "v": [[-0.265, -7.634], [-3.389, -7.923], [-4.361, -7.738], [-4.373, -6.349], [-2.263, -5.758], [3.016, -8.913], [6.895, -15.109], [6.7, -16.781], [4.441, -16.207], [5.046, -14.532], [5.071, -14.384], [4.452, -13.62], [2.922, -12.946], [2.138, -11.829], [2.894, -11.786], [3.436, -11.765], [2.087, -9.643]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 25"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.062, -1.672], [0.736, -0.512], [-0.39, -0.218], [-1.303, -0.669], [-0.425, 0.593], [-2.986, 4.596], [0.765, 0.159], [-0.082, 0.4], [0.061, -0.03], [0.104, -0.382], [0.111, -0.079], [0.876, -0.767], [0.079, -0.556], [-0.459, 0.161], [-0.133, -0.012], [0.762, -1.012], [0.475, -0.634], [0.196, -0.923], [-0.485, 0.085], [-0.126, -0.026], [0.889, -0.685], [0.495, -0.239], [0.006, -0.683], [-0.367, 0.198], [-0.271, 0.174], [0.79, -0.92], [0.56, -0.149], [-0.014, -0.798], [0.551, 0.679], [-0.247, 1.19]], "o": [[-0.602, 0.238], [-0.289, 0.72], [1.275, 0.711], [0.348, 0.179], [3.196, -4.464], [0.39, -0.6], [0.241, -0.227], [0.014, -0.069], [-0.419, 0.204], [-0.111, 0.079], [-0.119, -0.328], [-0.388, 0.339], [-0.108, 0.76], [0.145, -0.051], [-0.762, 1.012], [-0.161, -0.286], [-0.651, 0.871], [-0.156, 0.735], [0.143, -0.025], [-0.721, 0.829], [0.192, -1.943], [-0.325, 0.157], [-0.004, 0.533], [0.27, -0.146], [-0.369, 1.141], [-0.503, -0.107], [-0.829, 0.22], [-1.014, 0.042], [0.686, -0.485], [-0.531, 0.73]], "v": [[16.975, 36.175], [15.489, 36.136], [15.593, 37.63], [19.485, 39.665], [20.647, 39.103], [30.244, 25.72], [30.44, 24.122], [31.102, 23.379], [30.948, 23.249], [30.299, 24.216], [29.967, 24.454], [29.273, 23.944], [28.142, 25.035], [29.172, 25.165], [29.572, 25.191], [27.286, 28.227], [26.877, 27.415], [25.079, 28.957], [26.232, 29.182], [26.612, 29.251], [24.574, 31.847], [23.229, 32.106], [22.393, 32.88], [23.262, 32.86], [24.074, 32.344], [22.081, 35.303], [20.381, 35.777], [19.275, 37.196], [16.97, 36.172], [18.785, 34.327]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 26"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.002, -2.437], [-0.937, -0.238], [0.003, -1.769], [-0.006, -0.014], [-0.042, -0.055], [-1.269, 0.753], [-4.356, 2.489], [-0.604, -0.148], [-0.084, 0.368], [-0.122, 0.166], [-0.003, 0.581], [0.04, 3.881], [0.836, 0.509], [0.139, 0.07], [0.218, -0.389], [0.451, -0.13], [0.434, -0.593], [0.229, -0.134], [3.974, -2.623], [0.238, -0.147], [0.079, -0.1], [-0.231, -0.026]], "o": [[-0.927, 1.317], [-0.003, 1.769], [-0.039, 0.055], [0.027, 0.068], [-0.074, 1.19], [4.355, -2.583], [0.448, 0.4], [0.379, 0.093], [0.122, -0.166], [0.515, -0.797], [0.019, -3.915], [-0.011, -1.045], [-0.139, -0.07], [-0.21, -0.154], [-0.449, 0.409], [-0.435, -0.088], [-0.229, 0.134], [-3.972, 1.865], [-0.238, 0.147], [-0.143, 0.044], [-0.233, 0.294], [0.002, 2.437]], "v": [[51.779, 85.096], [51.782, 87.435], [51.773, 92.743], [51.662, 92.902], [51.782, 93.071], [53.386, 93.71], [66.453, 86.207], [68.359, 85.894], [68.773, 84.874], [69.141, 84.375], [69.438, 82.482], [69.432, 70.773], [69.187, 67.756], [68.769, 67.545], [68.127, 67.906], [66.776, 68.697], [65.472, 69.452], [64.785, 69.853], [52.866, 76.703], [52.15, 77.143], [51.732, 77.284], [51.773, 77.786]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 27"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 1.197], [-0.158, 0.137], [-0.796, -0.889], [-0.456, 0.339], [-0.002, 0.381], [0.001, 1.71], [0.544, -0.354], [0.002, -0.371], [0, -1.781], [0.9, 0.884], [0.432, -0.371], [-0.002, -0.458], [0.032, -1.727], [-0.461, 0.246], [-0.007, 0.35]], "o": [[0.158, -0.137], [0.802, 0.882], [0.209, 0.233], [0.535, -0.398], [0.01, -1.717], [0, -0.381], [-0.522, 0.339], [-0.006, 1.293], [-1.178, -1.133], [-0.17, -0.167], [-0.398, 0.341], [0.009, 1.703], [-0.006, 0.322], [0.603, -0.321], [0.026, -1.211]], "v": [[34.042, 11.574], [34.516, 11.164], [36.92, 13.813], [37.826, 13.859], [38.306, 12.675], [38.322, 7.534], [37.81, 6.982], [37.386, 8.144], [37.383, 12.505], [34.392, 9.621], [33.741, 9.413], [33.228, 10.596], [33.133, 15.776], [33.534, 16.306], [34.032, 15.171]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 28"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.496], [0.002, -0.001], [-0.021, -0.814], [-0.548, 0.287], [-0.006, 0.367], [0.009, 0.436], [-0.516, 0.349], [-0.207, -0.271], [-0.186, -0.16], [-0.728, 0.784], [0.081, 0.688], [-0.823, 1.186], [0.634, 0.428], [1.017, -0.816], [0.102, -0.742]], "o": [[-0.002, 0.001], [0, 0.827], [0.009, 0.348], [0.555, -0.291], [0.007, -0.445], [-0.008, -0.394], [0.389, -0.263], [0.154, 0.203], [0.411, 0.351], [0.847, -0.911], [-0.063, -0.535], [0.805, -1.16], [-0.755, -0.509], [-1.112, 0.893], [-0.073, 0.527]], "v": [[-3.674, 34.323], [-3.679, 34.326], [-3.673, 36.802], [-3.269, 37.489], [-2.81, 36.369], [-2.823, 35.054], [-2.313, 33.888], [-1.337, 33.791], [-0.9, 34.427], [0.293, 35.383], [-0.135, 33.577], [-0.375, 32.007], [-0.153, 29.535], [-3.057, 30.528], [-3.66, 32.829]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 29"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.497], [-0.002, 0.001], [-0.01, -0.933], [-0.476, 0.33], [-0.009, 0.295], [-0.049, 0.354], [-0.777, 0.663], [-0.249, -0.498], [-0.187, -0.155], [-0.712, 0.756], [0.139, 0.375], [0.242, 0.289], [-0.484, 0.7], [0.693, 0.426], [1.013, -0.755], [0.125, -0.735]], "o": [[0.002, -0.001], [0, 0.939], [0.003, 0.334], [0.334, -0.232], [0.01, -0.337], [0.075, -0.535], [0.837, -0.715], [0.12, 0.239], [0.287, 0.237], [0.751, -0.798], [-0.143, -0.388], [-0.232, -0.278], [0.77, -1.114], [-0.792, -0.487], [-1.182, 0.882], [-0.09, 0.532]], "v": [[62.426, -3.822], [62.432, -3.825], [62.436, -1.01], [62.948, -0.652], [63.352, -1.51], [63.386, -2.522], [63.762, -4.164], [65.146, -3.877], [65.542, -3.2], [66.536, -2.742], [66.31, -3.973], [65.716, -4.989], [65.784, -6.151], [65.991, -8.606], [63.066, -7.585], [62.444, -5.32]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 30"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.387], [0.003, -0.001], [-0.043, -0.636], [-0.921, 0.422], [-0.829, 0.487], [-0.016, 0.274], [0.272, -0.133], [0.273, -0.188], [0.022, 0.968], [-0.877, 0.439], [-0.004, 0.619], [0.434, -0.216], [-0.452, 1.127], [-0.555, 0.407], [-0.035, 0.407], [0.375, -0.18], [-0.026, -2.467]], "o": [[-0.003, 0.001], [0, 0.663], [0.036, 0.529], [0.818, -0.375], [0.235, -0.138], [0.022, -0.396], [-0.274, 0.134], [-0.897, 0.619], [-0.023, -1.019], [0.386, -0.193], [0.005, -0.757], [-1.045, 0.521], [0.379, -0.943], [0.321, -0.235], [0.051, -0.583], [-2.471, 1.186], [0.004, 0.385]], "v": [[-9.446, 37.669], [-9.453, 37.674], [-9.443, 39.658], [-8.823, 40.762], [-6.337, 39.347], [-5.881, 38.707], [-6.435, 38.576], [-7.264, 39.058], [-8.564, 38.622], [-7.272, 36.721], [-6.194, 35.709], [-7.354, 35.907], [-8.469, 34.836], [-6.839, 33.484], [-6.01, 32.656], [-6.853, 32.636], [-9.446, 36.508]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 31"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.29, 0.874], [0.883, -0.438], [-0.007, -0.582], [-0.005, -1.711], [-0.512, 0.273], [-0.829, 0.469], [-0.024, 0.252], [0.317, -0.174], [0.275, -0.183], [0.019, 1.007], [-0.929, 0.467], [0.046, 0.755], [0.394, -0.265], [-0.006, 0.969], [-0.667, 0.396]], "o": [[-0.824, -0.188], [-0.641, 0.319], [0.02, 1.702], [0.001, 0.414], [0.828, -0.442], [0.25, -0.141], [0.034, -0.353], [-0.276, 0.152], [-0.841, 0.559], [-0.018, -0.953], [0.41, -0.206], [-0.034, -0.551], [-0.924, 0.622], [0.006, -1.066], [0.465, -0.276]], "v": [[60.178, -5.655], [57.508, -4.428], [56.689, -2.984], [56.711, 2.146], [57.292, 2.646], [59.779, 1.243], [60.256, 0.617], [59.746, 0.41], [58.918, 0.907], [57.554, 0.55], [58.801, -1.374], [59.929, -2.504], [58.811, -2.211], [57.546, -2.671], [58.87, -4.383]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 32"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.331], [-0.003, 0.002], [0.004, -0.444], [-2.463, 1.517], [-0.034, 0.652], [0.395, -0.194], [0.03, 1.383], [-0.905, 0.462], [0.012, 0.553], [0.34, -0.259], [-0.234, 1.275], [-0.566, 0.461], [0.048, 0.577], [0.322, -0.152], [-0.037, -2.53]], "o": [[0.003, -0.002], [0, 0.441], [-0.023, 2.424], [0.394, -0.243], [0.036, -0.684], [-0.766, 0.376], [-0.028, -1.296], [0.343, -0.175], [-0.012, -0.539], [-1.359, 1.037], [0.21, -1.143], [0.335, -0.273], [-0.033, -0.398], [-2.502, 1.184], [0.005, 0.328]], "v": [[-24.43, 46.144], [-24.422, 46.14], [-24.422, 47.464], [-21.983, 48.38], [-20.899, 47.372], [-21.921, 47.506], [-23.595, 47.344], [-22.059, 45.237], [-21.17, 44.294], [-22.08, 44.406], [-23.524, 43.669], [-21.89, 42.146], [-21.062, 41.203], [-21.866, 41.289], [-24.431, 45.152]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 33"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.754, -0.358], [0.309, -0.803], [-0.19, -0.025], [-0.585, 1.133], [0.95, 0.189], [0.509, 0.035], [-0.146, 0.603], [-0.429, 0.324], [-0.321, 0.127], [-0.05, 0.396], [0.277, -0.088], [0.67, -1.348], [-0.809, -0.153], [-0.363, -0.038], [0.349, -1.037]], "o": [[-0.33, 0.23], [-0.113, 0.294], [0.716, 0.093], [0.788, -1.526], [-0.476, -0.094], [-0.388, -0.026], [0.139, -0.574], [0.311, -0.235], [0.287, -0.114], [0.041, -0.325], [-1.118, 0.354], [-0.699, 1.407], [0.347, 0.066], [0.54, 0.056], [-0.308, 0.917]], "v": [[41.535, 10.982], [40.436, 11.737], [40.683, 12.135], [43.868, 9.647], [43.349, 7.232], [41.814, 7.147], [41.253, 6.39], [42.26, 5.071], [43.234, 4.585], [43.875, 3.959], [43.38, 3.669], [40.587, 6.186], [41.271, 8.184], [42.399, 8.219], [43.272, 9.261]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 34"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.001, -0.331], [-0.031, -0.755], [-0.679, 0.387], [-0.034, 0.482], [-1.781, 0.784], [0.053, 0.645], [0.409, -0.213], [-0.551, 1.069], [-0.578, 0.39], [0.053, 0.432], [0.274, -0.118], [-0.026, -2.427]], "o": [[0, 0.774], [0.018, 0.446], [0.677, -0.386], [0.066, -0.928], [0.365, -0.161], [-0.047, -0.575], [-1.049, 0.545], [0.438, -0.849], [0.347, -0.234], [-0.041, -0.334], [-2.386, 1.026], [0.004, 0.329]], "v": [[2.413, 30.701], [2.422, 33.017], [2.891, 33.957], [3.381, 32.472], [4.686, 29.803], [5.69, 28.766], [4.595, 29.011], [3.431, 27.88], [5.136, 26.601], [5.781, 25.707], [5.142, 25.715], [2.412, 29.707]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 35"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.959, 1.864], [0.425, 1.415], [-0.81, 1.258], [-1.384, 1.021], [0.701, -2.068], [-1.615, 0.061]], "o": [[-1.862, 1.615], [-0.283, -0.943], [0.816, -1.267], [-2.918, 1.08], [-0.74, 2.181], [0.976, -0.037]], "v": [[18.061, 24.55], [14.131, 24.789], [14.962, 21.435], [18.307, 18.627], [13.527, 22.855], [14.971, 26.801]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 36"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -1.808], [-1.102, 0.735], [-0.48, -0.411], [-0.106, 0.041], [-0.865, 1.75], [0.864, -0.414], [0.081, -1.185]], "o": [[1.196, -1.526], [0.871, -0.582], [0.029, 0.025], [-0.105, -1.058], [0.482, -0.975], [-0.623, 0.298], [-0.103, 1.508]], "v": [[-24.142, 78.143], [-22.977, 75.062], [-21.376, 75.976], [-21.135, 75.888], [-21.203, 72.577], [-22.431, 71.765], [-24.117, 73.411]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 37"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.042, 1.615], [-0.887, -1.474], [0.582, 0.898], [-0.637, 0.442], [-0.374, 0.882]], "o": [[-2.533, 3.146], [0.491, -1.276], [-0.257, -0.396], [0.392, -0.272], [-0.923, 0.444]], "v": [[11.021, -34.305], [10.42, -28.435], [11.163, -31.82], [11.888, -33.224], [13.148, -34.372]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 38"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.551], [-0.003, 0.002], [-0.007, -0.768], [-0.334, 0.167], [-0.002, 0.315], [0.046, 1.295], [-1.26, 0.495], [-0.012, 0.343], [0.331, -0.188], [1.102, -0.637], [0.035, -0.364], [-0.313, 0.222], [0.072, -0.986]], "o": [[0.003, -0.002], [0, 0.772], [0.002, 0.276], [0.48, -0.24], [0.009, -1.328], [-0.032, -0.907], [0.271, -0.106], [0.015, -0.454], [-1.101, 0.626], [-0.278, 0.161], [-0.048, 0.495], [1.227, -0.873], [-0.042, 0.572]], "v": [[22.16, 19.889], [22.168, 19.884], [22.171, 22.198], [22.533, 22.63], [22.993, 21.667], [22.976, 17.708], [24.248, 15.523], [24.881, 14.807], [24.17, 14.754], [20.865, 16.65], [20.219, 17.38], [20.888, 17.478], [22.168, 18.232]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 39"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.442, -0.405], [-0.159, 0.098], [0.066, 0.815], [0.8, -0.639], [-0.074, 0.79], [0.372, -0.182], [0.001, -0.103], [0, -1.68], [-0.173, 0.101], [-0.178, 0.814], [-0.578, 0.334], [-0.053, -0.481]], "o": [[0.159, -0.098], [0, -0.858], [-0.064, -0.786], [-0.95, 0.759], [0.026, -0.28], [-0.097, 0.047], [-0.019, 1.69], [0.173, -0.101], [0.392, -0.904], [0.129, -0.588], [0.651, -0.377], [0.075, 0.673]], "v": [[6.594, 60.121], [7.072, 59.827], [7.057, 57.262], [5.644, 56.818], [4.679, 56.492], [4.323, 56.073], [4.018, 56.536], [4.004, 61.582], [4.522, 61.28], [4.708, 58.993], [5.631, 57.455], [6.382, 58.092]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 40"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.553], [0.005, -0.003], [-0.02, -0.707], [-0.562, 0.36], [-0.003, 0.366], [0.055, 1.183], [-1.229, 0.541], [-0.008, 0.327], [0.333, -0.19], [1.05, -0.612], [0.024, -0.467], [-0.3, 0.213], [0.064, -0.914]], "o": [[-0.005, 0.003], [0, 0.719], [0.01, 0.352], [0.516, -0.33], [0.01, -1.223], [-0.043, -0.925], [0.285, -0.125], [0.011, -0.45], [-1.05, 0.598], [-0.319, 0.186], [-0.026, 0.504], [1.165, -0.826], [-0.04, 0.574]], "v": [[-36.186, 53.435], [-36.202, 53.444], [-36.196, 55.598], [-35.747, 56.216], [-35.347, 55.045], [-35.365, 51.405], [-34.083, 49.158], [-33.444, 48.436], [-34.171, 48.396], [-37.323, 50.211], [-38.089, 51.049], [-37.364, 51.045], [-36.179, 51.772]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 41"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.203, 0.558], [0.101, 1.873], [-0.632, 1.328], [-0.035, -3.137], [0.017, -0.948], [-0.648, 0.38], [-0.718, 0.415], [0.028, 0.349], [0.284, -0.159], [0.274, -0.187], [-0.076, 1.281], [-0.906, 0.251]], "o": [[-0.649, -0.377], [-0.113, -2.101], [-3.264, 1.62], [0.01, 0.932], [-0.011, 0.601], [0.717, -0.421], [0.294, -0.17], [-0.027, -0.339], [-0.276, 0.155], [-0.96, 0.656], [0.073, -1.232], [0.366, -0.101]], "v": [[10.825, 25.955], [8.371, 25.986], [10.978, 22.723], [7.754, 27.477], [7.737, 30.303], [8.592, 30.745], [10.745, 29.519], [11.287, 28.758], [10.682, 28.701], [9.854, 29.206], [8.369, 28.776], [9.873, 26.956]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 42"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.035, 1.196], [0.508, 0.462], [-0.021, 0.921], [0.452, -0.183], [0.018, -0.237], [-0.457, -1.311], [-0.022, 0.751], [-0.744, 0.297], [0.158, -0.67], [-0.648, 0.286]], "o": [[-0.593, 1.722], [-0.485, -0.44], [0.008, -0.35], [-0.241, 0.097], [-0.131, 1.679], [0.292, -0.917], [0.012, -0.407], [0.477, -0.191], [-0.252, 1.067], [0.69, -0.304]], "v": [[-33.493, 80.941], [-34.918, 82.062], [-35.791, 80.273], [-36.468, 79.835], [-36.964, 80.446], [-36.693, 85.069], [-36.394, 82.671], [-35.788, 81.467], [-35.2, 82.034], [-34.349, 83.664]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 43"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.884], [-0.005, 0.003], [-0.012, -0.933], [-0.344, 0.177], [-0.003, 0.276], [0.006, 1.876], [0.456, -0.332], [-0.003, -0.343]], "o": [[0.005, -0.003], [0, 0.939], [0.004, 0.29], [0.372, -0.192], [0.017, -1.889], [-0.001, -0.34], [-0.423, 0.308], [0.008, 0.88]], "v": [[46.165, 5.631], [46.179, 5.623], [46.184, 8.439], [46.641, 8.754], [47.108, 7.935], [47.121, 2.29], [46.581, 1.965], [46.162, 2.98]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 44"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.013, 0.163], [0.157, 0.051], [1.101, 0.405], [0.104, -0.531], [-0.412, -0.077], [-1.299, 0.12], [-0.023, 0.474]], "o": [[-0.026, -0.158], [-1.027, -0.331], [-0.222, -0.082], [-0.104, 0.533], [1.165, 0.219], [0.453, -0.042], [0.003, -0.057]], "v": [[-9.86, 56.681], [-9.985, 56.056], [-13.391, 57.718], [-14.225, 58.762], [-14.362, 60.242], [-10.527, 58.254], [-9.882, 56.959]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 45"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.441, -2.075], [-1.1, 0.544], [-0.001, 0.841], [0.691, -0.365], [0.507, -0.869]], "o": [[1.142, -0.66], [1.021, -0.505], [0.001, -1.007], [-1.31, 0.691], [-0.839, -0.303]], "v": [[-36.623, 73.304], [-33.268, 71.388], [-32.086, 69.453], [-33.415, 69.257], [-34.491, 71.604]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 46"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.452, 0.663], [1.913, -1.92], [-0.221, 0.852], [-0.182, 0.129]], "o": [[-2.508, 1.794], [0.649, -0.379], [0.109, -0.419], [2.306, -1.641]], "v": [[34.489, -46.697], [32.065, -41.229], [33.52, -42.779], [33.935, -44.02]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 47"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-3.374, 1.948], [0.002, 0.089], [3.374, -1.948], [-0.002, -0.089]], "o": [[-0.002, -0.089], [-3.374, 1.948], [0.002, 0.089], [3.374, -1.948]], "v": [[62.549, 89.998], [62.544, 89.731], [52.422, 95.574], [52.429, 95.841]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 48"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0.88], [0.002, -0.001], [0.005, 0.931], [0.437, -0.257], [0, -0.301], [-0.009, -1.864], [-0.328, 0.159], [-0.006, 0.324]], "o": [[-0.002, 0.001], [0, -0.934], [-0.002, -0.336], [-0.303, 0.178], [0.002, 1.867], [0.001, 0.279], [0.475, -0.23], [0.016, -0.888]], "v": [[30.991, 14.437], [30.985, 14.44], [30.983, 11.638], [30.516, 11.19], [30.097, 12.036], [30.108, 17.635], [30.499, 18.038], [30.986, 17.078]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 49"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.768, 0.037], [-0.127, 1.492], [0.378, -0.175], [0.209, -0.61], [-0.629, 0.365], [-0.244, -0.413]], "o": [[0.403, -1.713], [0.029, -0.343], [-0.872, 0.404], [-0.098, 0.284], [1.669, -0.968], [0.179, 0.303]], "v": [[-31.83, 82.376], [-31.68, 77.935], [-32.018, 77.31], [-33.038, 79.148], [-33.181, 80.066], [-32.349, 81.272]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 50"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.838, -3.437], [-1.221, -0.666], [-0.224, -0.018], [-0.575, 0.927], [0.401, 0.144]], "o": [[1.127, -2.304], [0.183, 0.1], [0.493, 0.04], [0.464, -0.749], [-2.299, -0.824]], "v": [[15.369, -33.325], [18.423, -35.468], [18.991, -35.198], [20.47, -35.247], [19.516, -35.956]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 51"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.123, 1.501], [0.291, -0.754], [0.134, -0.087], [0.184, -0.841], [-0.357, 0.057], [-0.113, 0.276]], "o": [[0.247, 0.736], [-0.03, 0.079], [1.276, 0.191], [-0.069, 0.316], [0.249, -0.04], [0.74, -1.804]], "v": [[32.159, -45.773], [31.093, -43.451], [30.704, -43.191], [30.399, -40.763], [30.519, -40.168], [31.02, -40.738]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 52"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.005, -0.049], [-0.05, -0.179], [-0.521, 0.201], [-0.128, 0.226], [0.097, 0.242], [0.078, 0.32], [0.911, -0.346], [0.203, -0.762]], "o": [[0, 0.338], [0.082, 0.296], [0.246, -0.095], [0.411, -0.725], [-0.113, -0.283], [-0.138, -0.562], [-0.633, 0.24], [-0.048, 0.183]], "v": [[44.676, -50.942], [44.685, -50.172], [45.132, -49.486], [45.896, -50.045], [45.508, -50.685], [45.397, -51.739], [45.347, -53.511], [44.724, -51.342]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 53"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.02, -0.725], [0.367, -0.003], [-1.348, 1.154], [0.34, -1.148], [-0.485, -0.147]], "o": [[-0.13, -0.189], [-1.474, 0.011], [-1.009, 0.272], [-0.297, 1], [0.376, 0.114]], "v": [[2.635, 61.566], [2.034, 60.768], [2.533, 58.897], [0.392, 60.641], [1.418, 61.523]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 54"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.019, 0.64], [0.036, 0.83], [0.988, -0.561], [0.026, -0.683]], "o": [[0.436, -0.237], [-0.033, -0.755], [-0.676, 0.384], [-0.029, 0.774]], "v": [[-35.51, 68.702], [-34.564, 67.369], [-35.681, 66.592], [-36.695, 68.145]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 55"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.06, 0.372], [0.724, -0.328], [-0.01, -0.634], [-0.816, 0.348]], "o": [[0.403, -1.37], [-0.629, 0.285], [0.014, 0.952], [0.914, -0.389]], "v": [[-12.599, 46.82], [-13.712, 46.054], [-14.749, 47.628], [-13.23, 48.069]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 56"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.029, 0.659], [0.791, -0.449], [-0.002, -0.826], [-0.81, 0.466]], "o": [[0.051, -0.598], [-0.809, 0.459], [0.002, 0.727], [0.878, -0.506]], "v": [[-22.531, 52.163], [-23.319, 51.532], [-24.497, 53.388], [-23.468, 53.866]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 57"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.071, -0.658], [-0.825, 0.494], [0.053, 0.734], [0.937, -0.504]], "o": [[-0.098, 0.783], [0.636, -0.381], [-0.049, -0.667], [-0.862, 0.464]], "v": [[-29.146, 67.939], [-28.191, 68.544], [-27.179, 67.014], [-28.203, 66.232]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 58"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.014, 0.683], [0.749, -0.522], [0.038, -0.888], [-0.851, 0.478]], "o": [[-0.076, -0.331], [-0.576, 0.401], [-0.03, 0.683], [0.767, -0.431]], "v": [[-9.899, 48.846], [-10.504, 48.177], [-12.041, 49.959], [-10.809, 50.488]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 59"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.043, -0.627], [-0.846, 0.488], [-0.051, 0.903], [0.707, -0.297]], "o": [[-0.039, 0.598], [0.651, -0.375], [0.046, -0.812], [-0.804, 0.337]], "v": [[-21.815, 55.717], [-21.064, 56.434], [-19.818, 54.792], [-20.84, 54.194]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 60"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.771, -0.485], [-0.162, -0.634], [-0.894, 0.582], [-0.021, 0.603]], "o": [[-0.599, 0.391], [0.119, 0.466], [0.721, -0.469], [0.023, -0.645]], "v": [[-12.115, 60.972], [-13.056, 62.496], [-12.067, 63.148], [-11.221, 61.502]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 61"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.042, 0.53], [0.77, -0.395], [0.043, -0.659], [-0.502, 0.115]], "o": [[-0.036, -0.566], [-0.725, 0.373], [-0.066, 1.02], [0.733, -0.168]], "v": [[-34.787, 59.647], [-35.784, 59.02], [-36.718, 60.627], [-35.487, 60.855]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 62"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.421, -0.861], [-1.105, 0.643], [-0.162, 0.624], [0.404, -0.221]], "o": [[-0.001, 0.36], [1.124, -0.655], [0.276, -1.062], [-0.506, 0.277]], "v": [[-21.834, 51.924], [-21.115, 52.523], [-20.139, 50.643], [-21.021, 50.457]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 63"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.004, 0.517], [0.697, -0.442], [-0.207, -0.608], [-0.985, 0.575]], "o": [[0.137, -0.817], [-0.497, 0.315], [0.148, 0.435], [0.874, -0.51]], "v": [[-2.665, 44.732], [-3.528, 44.129], [-4.368, 45.521], [-3.457, 46.268]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 64"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.071, -0.934], [-0.887, 0.478], [-0.195, 0.625], [0.536, -0.279]], "o": [[-0.026, 0.457], [0.976, -0.526], [0.219, -0.703], [-0.653, 0.34]], "v": [[-34.039, 66.837], [-33.293, 67.428], [-32.327, 65.658], [-32.882, 65.07]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 65"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.477, -0.911], [0.381, 0.959]], "o": [[0.458, -1.473], [-0.524, 1.5]], "v": [[-8.814, 68.965], [-8.783, 65.37]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 66"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.196, -0.726], [-0.143, 0.07], [-0.07, 0.928], [0.195, -0.069], [0.013, -0.097]], "o": [[0.143, -0.07], [0.078, -0.933], [0.025, -0.33], [-0.451, 0.159], [-0.124, 0.922]], "v": [[-6.893, 67.833], [-6.463, 67.623], [-6.23, 64.826], [-6.68, 64.737], [-6.92, 65.301]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 67"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.758, 1.01], [-0.729, 0.402], [-0.824, 1.483]], "o": [[-0.189, 1.172], [0.705, -0.389], [-1.417, 0.773]], "v": [[46.845, -51.318], [47.853, -50.377], [50.238, -52.521]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 68"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.802, 0.546], [-0.013, 0.537], [0.677, -0.403], [-0.105, -0.592]], "o": [[0.751, -0.402], [0.017, -0.704], [-0.57, 0.339], [0.099, 0.56]], "v": [[12.875, 48.751], [13.613, 47.342], [12.654, 46.898], [11.776, 48.275]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 69"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.794, -0.555], [0.048, -0.541], [-0.616, 0.343], [0.012, 0.603]], "o": [[-0.899, 0.464], [-0.056, 0.621], [0.739, -0.412], [-0.012, -0.599]], "v": [[-23.405, 67.508], [-24.151, 69.075], [-23.354, 69.615], [-22.482, 67.995]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 70"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.626, 0.351], [-0.084, 0.657], [0.597, -0.314], [0.113, -0.649]], "o": [[0.527, -0.161], [0.087, -0.679], [-0.773, 0.406], [-0.122, 0.7]], "v": [[-30.69, 69.655], [-29.623, 68.222], [-30.611, 67.693], [-31.717, 69.565]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 71"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.484, -0.283], [0.03, -0.698], [-0.664, 0.405], [-0.056, 0.634]], "o": [[-0.619, 0.361], [-0.028, 0.665], [0.778, -0.475], [0.055, -0.619]], "v": [[1.512, 45.416], [0.424, 46.966], [1.384, 47.441], [2.37, 45.619]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 72"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.1, 0.349], [0.897, -0.526], [0.044, -0.564], [-0.596, 0.245]], "o": [[0.062, -0.762], [-0.708, 0.415], [-0.07, 0.893], [0.783, -0.322]], "v": [[-24.833, 61.595], [-25.721, 60.822], [-26.517, 62.277], [-25.384, 62.749]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 73"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 1.474], [0.234, -0.127], [-0.021, -1.52], [-0.122, 0.058], [0, 0.236]], "o": [[-0.234, 0.127], [0, 1.532], [0.001, 0.097], [0.275, -0.132], [0.003, -1.475]], "v": [[-18.792, 69.83], [-19.493, 70.212], [-19.478, 74.801], [-19.15, 74.887], [-18.793, 74.252]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 74"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.202, 0.637], [0.568, -0.237], [0.07, -0.508], [-0.573, 0.321]], "o": [[-0.129, -0.522], [-0.555, 0.232], [-0.093, 0.668], [0.445, -0.25]], "v": [[-27.177, 59.172], [-28.235, 58.666], [-29.295, 60.099], [-28.315, 60.596]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 75"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.156, 0.712], [0.546, -0.37], [-0.04, -0.613], [-0.587, 0.437]], "o": [[-0.133, -0.511], [-0.658, 0.447], [0.03, 0.465], [0.639, -0.476]], "v": [[-14.904, 51.711], [-15.97, 51.536], [-16.828, 53.172], [-16.091, 53.555]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 76"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.489, 0.918], [-0.001, -1.12], [-0.504, 0.384]], "o": [[-0.803, 0.629], [0.001, 0.647], [1.231, -0.939]], "v": [[-0.158, 46.253], [-1.894, 48.438], [-0.926, 48.742]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 77"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.547, -0.108], [0.08, -0.498], [-0.519, 0.221], [0.024, 0.514]], "o": [[-0.641, 0.464], [-0.079, 0.49], [0.645, -0.275], [-0.021, -0.455]], "v": [[-15.822, 59.224], [-16.664, 60.807], [-15.99, 61.402], [-15.073, 59.972]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 78"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.157, 0.562], [0.49, -0.169], [0.08, -0.576], [-0.602, 0.269]], "o": [[0.066, -0.727], [-0.698, 0.241], [-0.099, 0.708], [0.699, -0.313]], "v": [[11.183, 48.613], [10.266, 48.168], [9.353, 49.559], [10.184, 50.017]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 79"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.283, 0.519], [0.554, -0.318], [0.041, -0.537], [-0.493, 0.199]], "o": [[-0.066, -0.505], [-0.559, 0.321], [-0.056, 0.735], [0.76, -0.307]], "v": [[-14.97, 55.722], [-15.872, 55.287], [-16.784, 56.779], [-15.725, 57.192]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 80"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.109, 0.483], [0.765, -0.556], [0.016, -0.705], [-0.549, 0.317]], "o": [[-0.082, -0.327], [-0.556, 0.404], [-0.017, 0.734], [0.777, -0.449]], "v": [[-7.598, 55.42], [-8.146, 54.837], [-9.228, 56.5], [-8.156, 56.872]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 81"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.052, 0.598], [0.665, -0.374], [-0.011, -0.577], [-0.595, 0.312]], "o": [[0.058, -0.576], [-0.679, 0.382], [0.011, 0.583], [0.677, -0.356]], "v": [[-27.48, 63.061], [-28.296, 62.645], [-29.136, 64.064], [-28.257, 64.542]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 82"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.186, 0.864], [0.427, -0.243], [0.003, -0.592], [-0.509, 0.388]], "o": [[-0.229, -0.192], [-0.726, 0.414], [-0.002, 0.437], [0.638, -0.487]], "v": [[-22.251, 59.743], [-23.292, 59.797], [-24.154, 61.425], [-23.493, 61.741]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 83"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.512, 0.238], [0.049, 0.567], [0.54, -0.355], [0.013, -0.614]], "o": [[0.629, -0.439], [-0.035, -0.408], [-0.567, 0.373], [-0.012, 0.548]], "v": [[-7.068, 60.196], [-6.302, 58.709], [-6.933, 58.32], [-7.878, 59.809]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 84"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.793, 1.233], [1.294, -1.419]], "o": [[-0.86, 0.419], [1.364, -0.56]], "v": [[49.58, -55.687], [46.558, -53.535]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 85"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.07, 0.675], [0.929, -0.482], [0.029, -0.469], [-0.697, 0.394]], "o": [[-0.365, -0.11], [-0.6, 0.311], [-0.034, 0.551], [0.361, -0.204]], "v": [[-12.729, 54.965], [-13.512, 54.108], [-14.178, 55.397], [-13.473, 56.004]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 86"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.002, 0.728], [0.569, -0.262], [-0.03, -0.529], [-0.7, 0.554]], "o": [[-0.034, -0.529], [-0.676, 0.312], [0.022, 0.387], [0.417, -0.33]], "v": [[-17.549, 61.424], [-18.405, 60.918], [-19.203, 62.294], [-18.592, 62.779]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 87"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.186, 0.621], [0.237, -0.4], [0.101, -1.276], [-0.519, 0.276]], "o": [[-0.012, -0.533], [-0.462, 0.778], [-0.041, 0.513], [0.619, -0.33]], "v": [[49.787, 11.862], [49.153, 11.85], [47.637, 13.142], [48.647, 13.451]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 88"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.047, 0.537], [0.614, -0.317], [-0.01, -0.427], [-0.616, 0.365]], "o": [[-0.45, -0.033], [-0.474, 0.244], [0.013, 0.537], [0.437, -0.259]], "v": [[3.668, 53.207], [2.602, 52.631], [2.104, 53.962], [2.958, 54.435]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 89"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.419, 0.217], [-0.726, 0.236], [0.138, 0.1], [0.523, -0.374], [0.188, -0.668]], "o": [[0.495, -0.864], [0.141, -0.046], [-0.309, -0.224], [-0.575, 0.411], [-0.099, 0.354]], "v": [[6.891, -9.936], [8.765, -11.409], [8.894, -12.022], [7.632, -11.87], [6.437, -10.22]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 90"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.153, 0.499], [0.174, -0.002], [0.454, -0.793], [-0.243, -0.019], [-0.498, 0.411]], "o": [[-0.032, -0.304], [-0.87, 0.007], [-0.182, 0.318], [0.637, 0.05], [0.315, -0.26]], "v": [[10.823, -15.367], [10.352, -15.477], [8.612, -13.788], [8.53, -13.016], [9.891, -14.354]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 91"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.904, 0.188], [0.898, -0.234], [0.035, -0.475]], "o": [[-0.471, -0.625], [-0.505, 0.132], [-0.085, 1.15]], "v": [[-19.819, 67.685], [-20.815, 66.114], [-21.487, 67.408]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 92"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.01, 0.545], [0.501, -0.343], [0.122, -0.54], [-0.343, 0.265]], "o": [[-0.047, -0.385], [-0.809, 0.553], [-0.105, 0.466], [0.561, -0.433]], "v": [[-25.157, 57.62], [-25.833, 57.273], [-26.525, 58.963], [-25.919, 59.158]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 93"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.038, 0.685], [0.576, -0.297], [0.023, -0.46], [-0.591, 0.303]], "o": [[-0.008, -0.52], [-0.488, 0.252], [-0.026, 0.517], [0.508, -0.261]], "v": [[-22.549, 56.224], [-23.459, 55.868], [-24.15, 57], [-23.462, 57.51]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 94"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.942, 0.666], [0.294, -0.218], [0.015, -0.603], [-0.505, 0.394]], "o": [[0.017, -0.469], [-0.591, 0.438], [-0.013, 0.521], [0.788, -0.614]], "v": [[-24.902, 68.979], [-25.391, 68.646], [-26.435, 70.332], [-25.552, 70.798]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 95"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.524, -0.268], [-0.041, -0.441], [-0.483, 0.314], [-0.027, 0.499]], "o": [[-0.588, 0.435], [0.038, 0.412], [0.655, -0.426], [0.032, -0.592]], "v": [[0.199, 54.132], [-0.471, 55.594], [0.401, 55.887], [1.061, 54.4]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 96"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.381, 0.758], [-0.034, -0.996], [-0.397, 0.248]], "o": [[-0.798, 0.457], [0.013, 0.377], [1.121, -0.702]], "v": [[-7.736, 47.027], [-9.239, 48.932], [-8.653, 49.223]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 97"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.428, 0.089], [-0.001, 0.415], [0.569, -0.327], [-0.02, -0.582]], "o": [[0.573, -0.305], [0.002, -0.514], [-0.494, 0.283], [0.021, 0.637]], "v": [[-18.164, 66.644], [-17.555, 65.543], [-18.299, 65.145], [-19.147, 66.34]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 98"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.629, -0.31], [-0.008, -0.415], [-0.559, 0.383], [0.084, 0.355]], "o": [[-0.606, 0.506], [0.01, 0.482], [0.46, -0.315], [-0.096, -0.406]], "v": [[-10.998, 52.652], [-11.561, 54.05], [-10.744, 54.397], [-10.154, 53.218]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 99"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.26, 0.36], [1.121, -0.786], [0.02, -0.499], [-0.479, 0.302]], "o": [[-0.526, 0.01], [-0.553, 0.388], [-0.024, 0.62], [0.349, -0.22]], "v": [[-17.786, 57.966], [-18.497, 57.077], [-19.157, 58.66], [-18.24, 58.8]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 100"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.432, -0.226], [-0.221, -0.463], [-0.646, 0.313], [-0.175, 0.542]], "o": [[-0.352, 0.259], [0.16, 0.335], [0.769, -0.373], [0.2, -0.618]], "v": [[-11.044, 44.718], [-11.712, 45.713], [-11.144, 46.626], [-10.333, 44.944]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 101"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.31, 0.276], [0.012, -0.344], [-0.711, 0.33], [0.122, 0.274]], "o": [[-0.529, 0.349], [-0.021, 0.613], [0.351, -0.163], [-0.186, -0.418]], "v": [[-8.918, 43.541], [-9.505, 44.583], [-8.678, 45.299], [-7.995, 44.428]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 102"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.099, 0.414], [0.521, -0.294], [0.001, -0.464], [-0.527, 0.301]], "o": [[-0.007, -0.397], [-0.563, 0.317], [-0.002, 0.471], [0.676, -0.386]], "v": [[-17.864, 53.547], [-18.394, 53.016], [-19.09, 54.305], [-18.391, 54.809]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 103"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.358, -0.088], [0.094, -0.792], [-0.525, 0.238], [0.003, 0.511]], "o": [[-0.359, 0.343], [-0.089, 0.744], [0.475, -0.215], [-0.006, -1.001]], "v": [[18.836, -15.439], [17.774, -14.647], [18.94, -14.484], [19.891, -15.698]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 104"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.315, -0.558], [-0.509, 0.274], [-0.084, 0.56], [0.488, -0.262]], "o": [[-0.079, 0.381], [0.73, -0.392], [0.057, -0.379], [-1.014, 0.544]], "v": [[-0.689, 40.147], [-0.492, 40.831], [0.361, 39.176], [0.011, 38.437]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 105"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.554, -0.132], [0.101, -0.452], [-0.581, 0.324], [-0.001, 0.425]], "o": [[-0.54, 0.395], [-0.129, 0.579], [0.531, -0.296], [0.001, -0.451]], "v": [[-30.729, 64.143], [-31.423, 65.509], [-30.772, 66.01], [-30.181, 64.898]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 106"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.333, 0.128], [0.055, 0.513], [0.362, -0.109], [0.091, -0.49]], "o": [[0.35, -0.226], [-0.041, -0.387], [-0.608, 0.182], [-0.133, 0.714]], "v": [[-2.294, 57.223], [-1.407, 56.348], [-2.222, 55.708], [-3.069, 57.169]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 107"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.353, -0.627], [-0.121, -0.091], [-0.494, 0.6], [0.17, 0.224], [0.404, -0.254]], "o": [[0.079, 0.109], [0.447, 0.337], [0.29, -0.351], [-0.425, -0.559], [-0.307, 0.193]], "v": [[-8.807, 9.531], [-8.537, 9.889], [-7.09, 9.124], [-6.656, 8.076], [-7.829, 8.885]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 108"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.148, 0.677], [-0.776, 0.076], [-0.094, 0.364]], "o": [[-0.232, 0.833], [0.297, -0.029], [0.292, -1.135]], "v": [[-13.955, 50.535], [-13.618, 52.077], [-12.745, 51.404]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 109"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.06, -1.043], [-0.998, 0.791], [0.183, 0.304]], "o": [[0.935, -0.382], [0.34, -0.269], [-0.351, -0.584]], "v": [[-19.743, 50.715], [-18.236, 50.95], [-17.677, 49.745]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 110"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.016, 0.389], [0.454, -0.237], [-0.013, -0.438], [-0.445, 0.252]], "o": [[0.034, -0.488], [-0.643, 0.335], [0.014, 0.452], [0.579, -0.329]], "v": [[18.293, 44.508], [17.648, 44.095], [17, 45.375], [17.755, 45.638]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 111"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.672, 0.131], [-0.824, 0.781], [0.21, 0.211]], "o": [[0.164, 0.722], [0.346, -0.327], [-0.482, -0.487]], "v": [[19.828, 31.325], [21.17, 31.59], [21.799, 30.394]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 112"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.363, 0.532], [0.303, -0.167], [-0.05, -0.507], [-0.58, 0.451]], "o": [[0.099, -0.468], [-0.575, 0.317], [0.041, 0.418], [0.574, -0.446]], "v": [[-5.38, 45.978], [-5.816, 45.677], [-6.538, 46.95], [-5.901, 47.514]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 113"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.416, -0.14], [-0.026, -0.709], [-0.446, 0.284], [0.065, 0.513]], "o": [[-0.241, 0.349], [0.018, 0.484], [0.436, -0.277], [-0.063, -0.496]], "v": [[38.666, 59.823], [37.755, 60.787], [38.784, 60.736], [39.692, 59.551]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 114"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.719, -2.287], [-1.094, 0.282]], "o": [[0.831, -0.821], [-1.682, -0.497]], "v": [[4.507, -6.475], [7.14, -8.451]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 115"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.899, 0.706], [0.914, -2.139]], "o": [[-1.365, -0.353], [0.782, -0.613]], "v": [[4.704, -5.074], [2.306, -3.192]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 116"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.509, 0.118], [-0.15, 0.455], [0.402, -0.261], [-0.106, -0.527]], "o": [[0.622, -0.446], [0.17, -0.519], [-0.345, 0.225], [0.071, 0.35]], "v": [[-28.324, 72.209], [-27.683, 70.826], [-28.139, 70.486], [-28.914, 71.478]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 117"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.063, 0.446], [0.64, -0.473], [0.105, -0.483], [-0.676, 0.445]], "o": [[-0.121, -0.281], [-0.727, 0.537], [-0.085, 0.392], [0.58, -0.382]], "v": [[22.629, 42.026], [22.162, 41.334], [21.883, 42.731], [22.164, 43.365]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 118"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.398, 0.199], [-0.082, 0.553], [0.364, -0.41], [0.073, -0.715]], "o": [[0.471, -0.29], [0.07, -0.47], [-0.393, 0.443], [-0.048, 0.466]], "v": [[23.622, 27.991], [24.561, 26.754], [23.992, 26.647], [22.809, 27.879]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 119"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.846, -0.836], [-0.799, 0.763], [0.133, 0.181]], "o": [[0.536, 0.21], [0.273, -0.26], [-0.384, -0.52]], "v": [[-1.926, 44.317], [-0.425, 44.409], [-0.075, 43.561]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 120"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.232, 0.703], [0.352, -0.26], [0.31, -0.479], [-0.337, 0.118]], "o": [[0.048, -0.328], [-0.468, 0.345], [-0.245, 0.379], [0.529, -0.185]], "v": [[0.989, -13.007], [0.618, -13.347], [-0.717, -12.111], [-0.224, -11.742]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 121"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.092, 1.086], [1.089, -1.087]], "o": [[-1.09, 0.188], [1.087, -0.169]], "v": [[-27.344, 55.092], [-30.269, 56.789]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 122"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.587, -0.184], [-0.601, 0.644], [0.028, 0.01]], "o": [[0.293, 0.605], [0.155, -0.166], [-0.516, -0.187]], "v": [[24.113, 51.854], [25.514, 51.641], [25.827, 50.98]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 123"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.475, 1.232], [0.38, -0.957], [-0.154, 0.021]], "o": [[-0.773, 0.777], [-0.044, 0.112], [0.613, -0.082]], "v": [[-0.968, 10.841], [-2.757, 12.397], [-2.44, 12.687]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 124"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.054, 0.34], [0.227, -0.23], [-0.004, -0.337], [-0.225, 0.176]], "o": [[-0.031, -0.301], [-0.239, 0.243], [0.003, 0.291], [0.287, -0.224]], "v": [[11.229, -13.581], [10.765, -13.553], [10.196, -12.721], [10.682, -12.73]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 125"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.309, -0.149], [0.004, -0.253], [-0.062, 0.039], [-0.005, 0.312]], "o": [[-0.358, 0.27], [-0.002, 0.134], [0.315, -0.197], [0.003, -0.224]], "v": [[-10.249, 60.597], [-10.574, 61.414], [-10.382, 61.685], [-9.904, 60.864]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 126"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.13, 0.136], [0.02, -0.017], [0.091, -0.154], [-0.131, 0.087]], "o": [[-0.059, -0.074], [-0.146, 0.121], [-0.186, 0.314], [0.131, -0.087]], "v": [[4.607, -3.053], [4.471, -3.218], [4.055, -2.811], [4.221, -2.662]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 127"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.04, 0.116], [0.093, -0.014], [-0.027, -0.05], [-0.068, 0.065]], "o": [[-0.093, 0.014], [-0.04, 0.116], [0.027, 0.051], [0.065, -0.062]], "v": [[-12.688, 7.376], [-12.967, 7.416], [-13.071, 7.749], [-12.813, 7.711]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 128"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0.221], [-0.039, -0.21], [0.286, -0.163]], "o": [[0.228, -0.158], [0.061, 0.332], [0, -0.221]], "v": [[-43.423, 61.395], [-42.91, 61.35], [-43.423, 62.058]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 129"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.064, -0.323], [0.111, 0.047]], "o": [[-0.111, -0.047], [0.286, -0.229]], "v": [[74.423, -79.012], [74.091, -79.152]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 130"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.286, 0.1], [0.111, -0.174]], "o": [[-0.111, 0.174], [-0.065, -0.249]], "v": [[-43.091, -11.497], [-43.423, -10.973]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 131"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.065, 0.323], [-0.111, -0.047]], "o": [[0.111, 0.047], [-0.286, 0.229]], "v": [[-43.423, 154.012], [-43.091, 154.152]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 132"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.286, -0.1], [-0.206, 0.135]], "o": [[0.016, -0.215], [0.064, 0.249]], "v": [[74.091, 86.497], [74.423, 85.973]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 133"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.126, 0.214], [-0.238, 0.147], [-3.974, 2.437], [-0.229, 0.134], [-0.435, 0.414], [-0.449, 0.409], [-0.214, 0.12], [-0.139, -0.07], [0.016, -5.54], [0.122, -0.166], [0.774, -0.652], [4.355, -2.583], [-0.074, 1.19], [0.003, 0.109], [-0.003, 1.769], [0.171, 0.681], [0.002, 2.437]], "o": [[0.238, -0.147], [3.973, -2.097], [0.229, -0.134], [0.435, -0.093], [0.451, -0.13], [0.214, -0.12], [0.139, 0.07], [-0.015, 5.54], [-0.122, 0.166], [-0.773, 0.324], [-4.356, 2.489], [-1.269, 0.753], [-0.003, -0.109], [0.003, -1.769], [0.16, -0.873], [-0.002, -2.437], [0.126, -0.214]], "v": [[52.15, 77.143], [52.866, 76.703], [64.785, 69.853], [65.472, 69.452], [66.776, 68.697], [68.127, 67.906], [68.769, 67.545], [69.187, 67.756], [69.141, 84.375], [68.773, 84.874], [66.453, 86.207], [53.386, 93.71], [51.782, 93.071], [51.773, 92.743], [51.782, 87.435], [51.779, 85.096], [51.773, 77.786]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 134"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [0.561, 0.114], [0.149, 0.534], [-0.281, 0.25], [-0.294, 0.066], [0, 0], [-1.11, 0.184], [-0.622, 0.675], [0.536, 0.179], [1.253, -0.297], [0.213, 0.201], [-0.826, 0.164], [-0.322, 1.44], [-0.239, 0.945], [-0.512, 0.578], [-0.464, 0.135], [-0.841, 0.077], [-0.313, 0.687], [-3.344, 4.53], [-0.579, -0.668], [3.339, -5.51], [0.534, 0.203], [1.067, -1.986], [0.564, -0.843], [0.033, -0.608], [1.348, -0.935]], "o": [[-0.014, -0.965], [-0.507, -0.103], [-0.062, -0.223], [0.239, -0.212], [-0.001, -0.002], [0.954, 0.122], [1.013, 0.68], [0.832, -0.903], [-0.976, -0.325], [-0.213, -0.201], [1.171, -1.448], [0.534, -0.106], [0.581, -0.248], [0.512, -0.578], [0.111, 1.174], [-0.158, 1.39], [0.818, -0.075], [2.204, -4.832], [1.516, -0.953], [-3.363, 5.502], [-0.603, 0.995], [-1.48, -0.562], [-0.464, 0.863], [-0.127, 0.598], [-1.397, 1.096], [0, 0]], "v": [[-31.144, 46.719], [-32.639, 46.003], [-33.691, 45.198], [-33.634, 44.425], [-32.762, 44.2], [-32.647, 44.22], [-29.809, 44.632], [-27.62, 42.884], [-27.788, 41.413], [-31.11, 41.326], [-31.749, 40.722], [-29.222, 40.074], [-27.818, 39.081], [-26.385, 37.706], [-24.85, 35.972], [-23.471, 35.616], [-21.74, 36.738], [-20.293, 34.9], [-12.31, 20.775], [-9.83, 21.494], [-19.908, 38.003], [-21.617, 39.09], [-25.245, 41.728], [-26.919, 44.262], [-27.774, 46.005], [-31.164, 46.693]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 135"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.721, -0.671], [1.515, -0.953], [-0.147, 0.385], [-0.212, 0.71], [0.018, 0.091], [0.249, 0.16], [0.326, 0.038], [0.141, 0.153], [-0.044, 0.226], [0.09, 0.29], [-0.458, 0.309], [-0.26, 0.136], [-0.434, 1.053], [0, 0], [-2.46, 2.58], [-0.121, -1.37], [0, 0], [0.047, -0.451], [1.854, -1.882], [-0.062, -0.111], [-0.555, 0.411], [-0.6, 0.354], [0, 0], [-0.115, -0.125], [1.87, -0.951], [0.03, -0.279], [-0.264, -0.364], [-1.159, -0.008], [-1.37, -0.224], [-0.65, 1.156], [0.476, -1.19], [-0.415, -0.03], [0.023, -0.208], [0.574, -0.605], [0.455, -0.26], [0.767, 0.178], [0.919, -0.891], [0.525, 0.039]], "o": [[-0.579, -0.668], [0.148, -0.385], [0.628, -0.456], [0.036, -0.118], [0.572, -1.176], [-0.326, -0.038], [-0.141, -0.153], [0.045, -0.226], [1.171, -1.113], [0.493, -1.565], [0.131, 0.171], [0.704, 0.114], [0, 0], [2.665, -2.273], [1.052, -1.104], [0, 0], [-0.518, 0.231], [-1.854, 1.882], [0.062, 0.111], [0.54, -0.493], [0.586, -0.433], [0, 0], [0.115, 0.125], [-1.747, 1.195], [-0.194, 0.099], [-0.072, 0.667], [-1.208, 1.061], [1.467, 0.01], [0.701, 0.115], [0.484, -0.228], [-0.186, 0.466], [-0.023, 0.208], [-0.389, 0.594], [-0.455, 0.26], [-0.764, -0.184], [-0.653, -0.152], [-0.745, 0.722], [-0.574, -0.043]], "v": [[-9.83, 21.494], [-12.31, 20.775], [-11.868, 19.621], [-10.626, 17.86], [-10.6, 17.547], [-11.245, 16.943], [-12.224, 16.828], [-12.646, 16.37], [-12.512, 15.692], [-12.59, 14.696], [-11.221, 13.944], [-10.608, 13.932], [-8.886, 12.587], [-8.876, 12.595], [-1.533, 4.799], [0.399, 4.57], [0.407, 4.556], [-0.303, 5.644], [-5.866, 11.289], [-5.679, 11.623], [-4.055, 10.161], [-2.259, 9.081], [-2.261, 9.08], [-1.916, 9.455], [-6.834, 13.904], [-7.435, 14.943], [-6.435, 15.132], [-6.573, 16.613], [-2.39, 17.139], [-0.404, 15.957], [0.333, 16.336], [0.455, 17.218], [0.387, 17.842], [-0.315, 19.598], [-1.68, 20.377], [-3.973, 19.827], [-6.212, 20.452], [-8.017, 20.938]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 136"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.073, 0.542], [-0.782, 0.144], [-0.968, 0.601], [0, 0], [-0.743, -0.63], [-0.156, 0.293], [-1.003, 1.194], [-0.922, 0.21], [0.139, -0.844], [-0.141, -3.36], [1.613, -1.118], [0.211, 0.315], [-0.024, 0.062], [0.026, -0.061], [0.01, -0.447], [0.309, 0.347], [1.519, -0.919], [0.065, -0.004], [1.964, -2.585], [0.723, 0.906], [-0.205, 0.537], [-1.1, -0.014], [-0.561, 0.565]], "o": [[1.06, -1.038], [0.787, -0.145], [0, 0], [0.557, 0.944], [0.226, 0.191], [0.678, -1.275], [0.422, -0.503], [0.793, -0.18], [-3.122, 3.302], [-0.9, -1.048], [-0.519, 0.36], [0.024, -0.062], [-0.026, 0.061], [-0.46, 0.265], [-0.507, 0.051], [0.674, -1.502], [-0.687, 0.416], [-0.756, 0.046], [-0.814, -0.402], [0.311, -0.547], [0.573, -1.5], [0.442, 0.084], [0.53, -0.534]], "v": [[11.34, -10.065], [13.695, -10.513], [15.998, -10.742], [15.982, -10.743], [18.867, -10.105], [19.512, -10.706], [21.759, -14.463], [22.856, -16.175], [23.595, -14.791], [19.442, -4.832], [15.715, -4.674], [14.673, -4.677], [14.745, -4.863], [14.668, -4.68], [13.991, -3.599], [12.627, -3.765], [11.702, -5.108], [11.208, -5.429], [9.146, -5.676], [6.296, -4.601], [7.213, -6.239], [9.682, -8.422], [11.104, -8.476]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 137"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.966, 3.057], [-0.814, -0.402], [-0.756, 0.046], [-0.687, 0.416], [0.674, -1.502], [0.342, -0.02], [2.499, -4.196], [1.27, -2.041], [1.366, 0.191], [0.115, 0.125], [-0.145, 0.217], [0.065, 0.004], [0.078, -0.221], [0.586, -0.433], [0.54, -0.493], [0.062, 0.111], [-1.855, 1.882], [-0.11, 0.422], [0, 0]], "o": [[0.723, 0.906], [1.964, -2.585], [0.065, -0.004], [1.519, -0.919], [-0.349, 0.037], [-3.348, 0.193], [-1.225, 2.057], [-1.17, 1.879], [-0.115, -0.126], [0.145, -0.217], [-0.065, -0.004], [-0.079, 0.221], [-0.6, 0.354], [-0.555, 0.411], [-0.062, -0.111], [1.854, -1.882], [0.332, -0.318], [0, 0], [1.966, -3.057]], "v": [[6.296, -4.601], [9.146, -5.676], [11.208, -5.429], [11.702, -5.108], [12.627, -3.765], [11.581, -3.657], [5.743, 0.733], [2.002, 6.882], [-1.916, 9.456], [-2.261, 9.08], [-1.827, 8.429], [-2.023, 8.417], [-2.259, 9.081], [-4.055, 10.161], [-5.679, 11.623], [-5.866, 11.289], [-0.302, 5.644], [0.407, 4.556], [0.399, 4.57]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 138"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.001, -0.098], [0.534, -0.106], [1.171, -1.448], [0.24, 1.034], [-0.512, -0.111], [-0.474, 0.89], [-0.926, 1.54], [-2.16, 2.796], [-0.592, 0.304], [-0.19, 0.873], [-0.44, 0.734], [-0.354, 0.341], [0.668, -0.864], [1.012, -1.619], [-0.021, -0.871], [1.036, -2.097], [0.372, -0.866], [0.24, -0.456], [-0.456, -0.357]], "o": [[-0.322, 1.44], [-0.826, 0.164], [-0.169, -1.089], [-0.177, -0.763], [0.787, 0.17], [0.863, -1.621], [1.793, -2.981], [-0.001, 0.435], [0.817, -0.42], [0.44, -0.734], [0.357, 0.041], [-0.022, 0.943], [-1.193, 1.542], [-0.532, 0.662], [-1.247, 2.022], [-0.812, 0.598], [-0.24, 0.456], [-2.165, 2.3], [0.019, 0.086]], "v": [[-27.818, 39.081], [-29.222, 40.074], [-31.749, 40.722], [-32.275, 37.468], [-31.54, 36.489], [-29.499, 35.181], [-26.563, 30.553], [-20.85, 21.81], [-20.293, 22.464], [-18.831, 20.425], [-17.512, 18.224], [-16.447, 17.598], [-17.771, 20.289], [-20.87, 25.118], [-22.163, 27.22], [-25.845, 33.307], [-27.46, 35.601], [-28.18, 36.969], [-27.847, 38.807]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 139"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.519, 0.36], [-0.9, -1.048], [-3.122, 3.302], [2.56, -3.749], [0.658, 0.062], [1.453, 0.138], [-0.46, 0.265]], "o": [[0.211, 0.315], [1.613, -1.118], [-0.141, -3.36], [0.272, 3.215], [-0.697, 1.021], [-1.453, -0.137], [0.01, -0.447], [0, 0]], "v": [[14.673, -4.677], [15.715, -4.674], [19.442, -4.832], [23.595, -14.791], [20.226, -4.397], [18.349, -3.186], [13.991, -3.6], [14.668, -4.68]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 140"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.134, 0.385], [-0.763, 1.012], [-0.131, 0.246], [-0.11, 0.08], [0, 0], [0.39, -0.6], [3.196, -4.464], [0.348, 0.179], [1.275, 0.711], [-0.289, 0.72], [-0.602, 0.238], [0, 0], [-1.014, 0.042], [0, 0], [-0.002, -0.11], [-1.267, 2.581], [-0.368, 1.14], [-0.168, 0.167], [-0.721, 0.829]], "o": [[0.762, -1.012], [0.131, -0.246], [0.111, -0.079], [0, -0.001], [0.765, 0.159], [-2.986, 4.596], [-0.425, 0.593], [-1.303, -0.669], [-0.39, -0.218], [0.736, -0.512], [0, 0], [0.551, 0.679], [0, 0], [0.002, 0.11], [1.155, 0.687], [0.79, -0.92], [0.166, -0.165], [0.889, -0.685], [0.32, -0.295]], "v": [[27.286, 28.227], [29.573, 25.191], [29.967, 24.454], [30.298, 24.216], [30.44, 24.122], [30.244, 25.72], [20.647, 39.103], [19.485, 39.665], [15.593, 37.63], [15.489, 36.136], [16.975, 36.175], [16.97, 36.172], [19.275, 37.195], [19.292, 37.147], [19.299, 37.478], [22.081, 35.303], [24.073, 32.345], [24.574, 31.847], [26.613, 29.251]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 141"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.158, -0.137], [0.026, -1.211], [0.603, -0.321], [-0.006, 0.322], [0.009, 1.703], [-0.398, 0.341], [-0.17, -0.167], [-1.178, -1.133], [-0.006, 1.293], [-0.522, 0.339], [0, -0.381], [0.01, -1.717], [0.535, -0.398], [0.209, 0.233], [0.802, 0.882]], "o": [[0, 1.197], [-0.007, 0.35], [-0.461, 0.246], [0.032, -1.727], [-0.002, -0.458], [0.432, -0.371], [0.9, 0.884], [0, -1.781], [0.002, -0.371], [0.544, -0.354], [0.001, 1.71], [-0.002, 0.381], [-0.456, 0.339], [-0.796, -0.889], [-0.158, 0.137]], "v": [[34.042, 11.574], [34.032, 15.171], [33.534, 16.306], [33.133, 15.776], [33.228, 10.596], [33.741, 9.413], [34.392, 9.621], [37.383, 12.505], [37.386, 8.144], [37.81, 6.982], [38.322, 7.534], [38.306, 12.675], [37.826, 13.859], [36.92, 13.813], [34.516, 11.164]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 142"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.606, 1.212], [-0.449, 0.707], [-0.047, 0.684], [-0.206, 0.255], [-0.557, 0.792], [0.527, -1.025], [1.42, -1.986], [2.784, 0.217], [0.505, 0.526], [-0.385, 0.58], [-0.298, -0.022], [-1.04, -0.099]], "o": [[0.45, -0.707], [0.744, -0.527], [0.206, -0.255], [0.759, -0.682], [0.788, 0.062], [-1.105, 2.15], [-2.432, 3.402], [-0.779, -0.061], [-0.267, -0.279], [0.355, -0.535], [1.05, 0.078], [1.195, 0.578]], "v": [[2.087, -9.643], [3.435, -11.765], [4.452, -13.62], [5.07, -14.384], [6.7, -16.781], [6.895, -15.109], [3.016, -8.913], [-2.263, -5.758], [-4.373, -6.349], [-4.361, -7.738], [-3.389, -7.923], [-0.265, -7.634]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 143"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-1.065, 0.355], [0, 0], [-0.278, 0.014], [0, 0], [-0.595, 0.823], [-0.211, 0.302], [-0.593, 0.927], [-0.578, 0.446], [1.872, -2.337], [0.358, 0.144], [1.293, 0.46], [-0.423, 0.774], [-0.409, 0.099], [-0.087, -0.027], [-0.344, 0.287]], "o": [[0.318, 0.844], [0, 0], [-0.071, 0.362], [0, 0], [0.313, 0.482], [0.211, -0.302], [0.593, -0.927], [0.577, -0.445], [-1.164, 2.702], [-0.401, 0.501], [-1.263, -0.509], [-0.705, -0.25], [0.474, -0.866], [0.087, 0.026], [0.189, 0.151], [0, 0]], "v": [[45.503, 17.381], [47.533, 18.186], [47.523, 18.178], [47.867, 18.664], [47.849, 18.658], [49.198, 18.212], [49.829, 17.307], [51.609, 14.525], [53.342, 13.189], [48.414, 20.566], [47.207, 21.04], [43.403, 19.54], [43.338, 17.644], [44.517, 17.381], [44.777, 17.46], [45.54, 17.361]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 144"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.002, 0.001], [-0.073, 0.527], [-1.112, 0.893], [-0.755, -0.509], [0.805, -1.16], [-0.063, -0.535], [0.847, -0.911], [0.411, 0.351], [0.154, 0.203], [0.389, -0.263], [-0.008, -0.394], [0.007, -0.445], [0.555, -0.291], [0.009, 0.348], [0, 0.827]], "o": [[0, -0.496], [0.102, -0.742], [1.017, -0.816], [0.634, 0.428], [-0.823, 1.186], [0.081, 0.688], [-0.728, 0.784], [-0.186, -0.16], [-0.207, -0.271], [-0.516, 0.349], [0.009, 0.436], [-0.006, 0.367], [-0.548, 0.287], [-0.021, -0.814], [0.002, -0.001]], "v": [[-3.674, 34.323], [-3.66, 32.829], [-3.057, 30.528], [-0.153, 29.535], [-0.375, 32.007], [-0.135, 33.577], [0.293, 35.383], [-0.9, 34.427], [-1.337, 33.791], [-2.313, 33.888], [-2.823, 35.054], [-2.81, 36.369], [-3.269, 37.489], [-3.673, 36.802], [-3.679, 34.326]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 145"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.094, -0.59], [-0.935, 0.635], [-0.009, 0.789], [0.657, -0.325]], "o": [[-0.045, 0.655], [0.733, -0.498], [0.01, -0.879], [-0.756, 0.374]], "v": [[-2.847, 32.212], [-1.966, 32.835], [-0.644, 30.912], [-2.035, 30.668]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 146"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.002, -0.001], [-0.09, 0.532], [-1.182, 0.882], [-0.792, -0.487], [0.77, -1.114], [-0.232, -0.278], [-0.143, -0.388], [0.751, -0.798], [0.287, 0.237], [0.12, 0.239], [0.837, -0.715], [0.075, -0.535], [0.01, -0.337], [0.334, -0.232], [0.003, 0.334], [0, 0.939]], "o": [[0, -0.497], [0.125, -0.735], [1.013, -0.755], [0.693, 0.426], [-0.484, 0.7], [0.242, 0.289], [0.139, 0.375], [-0.712, 0.756], [-0.187, -0.155], [-0.249, -0.498], [-0.777, 0.663], [-0.049, 0.354], [-0.009, 0.295], [-0.476, 0.33], [-0.01, -0.933], [-0.002, 0.001]], "v": [[62.426, -3.822], [62.444, -5.32], [63.066, -7.585], [65.991, -8.606], [65.784, -6.151], [65.716, -4.989], [66.31, -3.973], [66.536, -2.742], [65.542, -3.2], [65.146, -3.877], [63.762, -4.164], [63.386, -2.522], [63.352, -1.51], [62.948, -0.652], [62.436, -1.01], [62.432, -3.825]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 147"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.055, -0.696], [-0.834, 0.529], [-0.081, 0.877], [0.637, -0.275]], "o": [[-0.003, 0.574], [0.706, -0.448], [0.081, -0.883], [-0.826, 0.356]], "v": [[63.308, -5.924], [64.096, -5.245], [65.489, -7.146], [64.338, -7.542]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 148"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.003, 0.001], [0.004, 0.385], [-2.471, 1.186], [0.051, -0.583], [0.321, -0.235], [0.379, -0.943], [-1.045, 0.521], [0.005, -0.757], [0.386, -0.193], [-0.023, -1.019], [-0.897, 0.619], [-0.274, 0.134], [0.022, -0.396], [0.235, -0.138], [0.818, -0.375], [0.036, 0.529], [0, 0.663]], "o": [[0, -0.387], [-0.026, -2.467], [0.375, -0.18], [-0.035, 0.407], [-0.555, 0.407], [-0.452, 1.127], [0.434, -0.216], [-0.004, 0.619], [-0.877, 0.439], [0.022, 0.968], [0.273, -0.188], [0.272, -0.133], [-0.016, 0.274], [-0.829, 0.487], [-0.921, 0.422], [-0.043, -0.636], [0.003, -0.001]], "v": [[-9.446, 37.669], [-9.446, 36.508], [-6.853, 32.636], [-6.01, 32.656], [-6.839, 33.484], [-8.469, 34.836], [-7.354, 35.907], [-6.194, 35.709], [-7.272, 36.721], [-8.564, 38.622], [-7.264, 39.058], [-6.435, 38.576], [-5.881, 38.707], [-6.337, 39.347], [-8.823, 40.762], [-9.443, 39.658], [-9.453, 37.674]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 149"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.824, -0.188], [0.465, -0.276], [0.006, -1.066], [-0.924, 0.622], [-0.034, -0.551], [0.41, -0.206], [-0.018, -0.953], [-0.841, 0.559], [-0.276, 0.152], [0.034, -0.353], [0.25, -0.141], [0.828, -0.442], [0.001, 0.414], [0.02, 1.702], [-0.641, 0.319]], "o": [[-0.29, 0.874], [-0.667, 0.396], [-0.006, 0.969], [0.394, -0.265], [0.046, 0.755], [-0.929, 0.467], [0.019, 1.007], [0.275, -0.183], [0.317, -0.174], [-0.024, 0.252], [-0.829, 0.469], [-0.512, 0.273], [-0.005, -1.711], [-0.007, -0.582], [0.883, -0.438]], "v": [[60.178, -5.655], [58.87, -4.383], [57.546, -2.671], [58.811, -2.211], [59.929, -2.504], [58.801, -1.374], [57.554, 0.55], [58.918, 0.907], [59.746, 0.41], [60.256, 0.617], [59.779, 1.243], [57.292, 2.646], [56.711, 2.146], [56.689, -2.984], [57.508, -4.428]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 150"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.462, 0.691], [-0.65, 0.304], [-0.569, 0.29], [-0.023, 0.488], [-0.306, 0.171], [3.339, -1.786], [0.823, -0.405], [0.058, 0.545], [-0.876, 0.715]], "o": [[0.649, -0.305], [0.569, -0.289], [0.714, -0.396], [0.306, -0.172], [-0.417, 3.609], [-0.823, 0.44], [-0.589, 0.29], [-0.059, -0.552], [0.426, 0.253]], "v": [[-30.142, 10.555], [-28.193, 9.642], [-26.485, 8.774], [-25.79, 7.393], [-24.871, 6.879], [-28.628, 12.287], [-31.091, 13.427], [-32.079, 13.088], [-31.475, 11.273]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 151"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.32, 1.056], [-0.127, 0.598], [-0.072, -1.99], [0.429, -0.3], [0.041, 0.333], [-0.031, 0.234], [1.405, -0.818], [-0.187, -1.146], [0.05, -0.408], [0.132, -0.12], [-0.003, 0.207], [-0.622, 2.498], [-0.105, 0.087], [0, 0], [0.103, -0.562], [-1.218, 0.749]], "o": [[0.033, -0.608], [0.299, 1.853], [0.011, 0.31], [-0.581, 0.406], [-0.025, -0.203], [0.176, -1.313], [-1.371, 0.798], [0.056, 0.342], [-0.016, 0.129], [-0.245, 0.223], [0.028, -2.178], [0.015, -0.061], [0, 0], [0.31, 0.238], [-0.282, 1.542], [1.233, -0.758]], "v": [[-27.774, 46.005], [-26.919, 44.262], [-26.772, 50.273], [-27.149, 51.286], [-27.693, 50.807], [-27.691, 50.147], [-29.248, 49.32], [-30.826, 51.935], [-30.861, 53.106], [-31.181, 53.603], [-31.663, 53.58], [-31.447, 46.976], [-31.144, 46.719], [-31.164, 46.693], [-30.822, 47.89], [-29.209, 48.563]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 152"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.117, -0.023], [-1.125, -0.087], [-0.413, 1.128], [-0.556, 1.378], [-0.099, 0.248], [0.204, -0.52], [0.826, -2.039], [0.706, -0.031], [1.673, -0.116], [-0.459, 1.019], [-0.463, 0.237], [-0.108, 0.039]], "o": [[1.22, -0.114], [1.018, 0.079], [0.503, -1.372], [0.098, -0.247], [0.812, -0.484], [-0.799, 2.037], [-0.395, 0.974], [-1.656, 0.073], [-0.584, 0.041], [0.518, -1.151], [0.109, -0.056], [0.117, 0.023]], "v": [[22.655, 57.085], [26.294, 56.788], [28.086, 55.156], [29.744, 51.023], [30.04, 50.281], [30.43, 50.928], [27.987, 57.043], [26.316, 58.411], [21.319, 58.703], [20.616, 57.748], [21.979, 57.136], [22.303, 57.017]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 153"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.003, -0.002], [0.005, 0.328], [-2.502, 1.184], [-0.033, -0.398], [0.335, -0.273], [0.21, -1.143], [-1.359, 1.037], [-0.012, -0.539], [0.343, -0.175], [-0.028, -1.296], [-0.766, 0.376], [0.036, -0.684], [0.394, -0.243], [-0.023, 2.424], [0, 0.441]], "o": [[0, -0.331], [-0.037, -2.53], [0.322, -0.152], [0.048, 0.577], [-0.566, 0.461], [-0.234, 1.275], [0.34, -0.259], [0.012, 0.553], [-0.905, 0.462], [0.03, 1.383], [0.395, -0.194], [-0.034, 0.652], [-2.463, 1.517], [0.004, -0.444], [-0.003, 0.002]], "v": [[-24.43, 46.144], [-24.431, 45.152], [-21.866, 41.289], [-21.062, 41.203], [-21.89, 42.146], [-23.524, 43.669], [-22.08, 44.406], [-21.17, 44.294], [-22.059, 45.237], [-23.595, 47.344], [-21.921, 47.506], [-20.899, 47.372], [-21.983, 48.38], [-24.422, 47.464], [-24.422, 46.14]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 154"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.027, 0.508], [-0.029, -0.712], [0.001, -1.21], [0.279, -0.047], [0.399, 0.181], [0.056, -0.23], [0.877, -0.82], [-0.008, 0.327], [-0.032, 0.786], [-0.95, 0.248], [0.32, 1.491]], "o": [[0.487, -0.283], [0.048, 1.18], [0, 0.343], [-0.766, 0.128], [-0.398, 0.078], [-0.467, 1.899], [-0.534, 0.5], [0.02, -0.78], [0.976, -0.71], [0.57, -0.233], [0.3, -0.84]], "v": [[63.454, -54.466], [64.578, -54.51], [64.591, -50.891], [64.081, -50.14], [63.956, -51.328], [62.974, -50.45], [60.303, -47.98], [59.807, -48.241], [59.899, -50.597], [62.793, -52.104], [64.042, -53.453]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 155"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.33, 0.23], [-0.308, 0.917], [0.54, 0.056], [0.347, 0.066], [-0.699, 1.407], [-1.118, 0.354], [0.041, -0.325], [0.287, -0.114], [0.311, -0.235], [0.139, -0.574], [-0.388, -0.026], [-0.476, -0.094], [0.788, -1.526], [0.716, 0.093], [-0.113, 0.294]], "o": [[0.754, -0.358], [0.349, -1.037], [-0.363, -0.038], [-0.809, -0.153], [0.67, -1.348], [0.277, -0.088], [-0.05, 0.396], [-0.321, 0.127], [-0.429, 0.324], [-0.146, 0.603], [0.509, 0.035], [0.95, 0.189], [-0.585, 1.133], [-0.19, -0.025], [0.309, -0.803]], "v": [[41.535, 10.982], [43.272, 9.261], [42.399, 8.219], [41.271, 8.184], [40.587, 6.186], [43.38, 3.669], [43.875, 3.959], [43.234, 4.585], [42.26, 5.071], [41.253, 6.39], [41.814, 7.147], [43.349, 7.232], [43.868, 9.647], [40.683, 12.135], [40.436, 11.737]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 156"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.327, -0.173], [-0.532, 0.662], [-1.329, 2.172], [0.231, 0.53], [-0.583, 0.369], [-0.272, 0.782], [0.237, 0.15], [-0.147, 0.264], [-0.204, 0.149], [-0.926, 0.663], [-0.141, -0.152], [1.242, -1.891], [0.984, -0.983], [0.858, -0.094]], "o": [[-0.021, -0.871], [0.736, 0.934], [-0.28, -0.312], [0.484, -0.847], [0.646, -0.409], [0.309, -0.888], [-0.147, -0.093], [0.113, -0.202], [0.925, -0.674], [0.14, 0.153], [-1.755, 1.594], [-0.835, 1.272], [-0.738, 0.736], [-0.275, 0.03]], "v": [[-22.163, 27.22], [-20.87, 25.118], [-17.084, 22.89], [-18.365, 22.675], [-16.604, 21.358], [-15.084, 19.73], [-15.839, 19.373], [-16.005, 18.908], [-15.425, 18.365], [-12.645, 16.37], [-12.224, 16.828], [-16.099, 22.417], [-18.6, 26.181], [-21.188, 26.724]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 157"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.452, 0.152], [-1.356, -0.658], [-0.854, 1.311], [-0.234, -0.905], [-0.478, -0.264], [0.213, -0.341], [1.118, 0.31], [0.081, -0.101]], "o": [[0.591, -1.32], [0.711, 0.345], [1.168, -1.792], [-0.978, 0.881], [-0.212, 0.341], [-1.579, 1.28], [-0.056, -0.016], [-1, 1.246]], "v": [[1.347, 12.921], [3.77, 10.84], [6.199, 9.373], [7.966, 9.183], [7.757, 10.672], [7.119, 11.696], [3.245, 12.581], [2.93, 12.799]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 158"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.386, 1.847], [-0.293, -0.419], [0.031, -0.238], [0.273, -0.792], [0.736, -0.78], [-0.27, 0.558], [0.71, 0.012], [0.471, 0.691], [-0.263, -0.309]], "o": [[0.39, 0.211], [-0.031, 0.239], [-0.683, 0.412], [-0.345, 1.001], [-0.503, 0.533], [1.147, -2.374], [-0.954, -0.016], [0.808, -0.316], [1.099, 1.291]], "v": [[-1.961, -2.362], [-0.534, -2.283], [-0.626, -1.568], [-1.986, 0.211], [-3.85, 2.785], [-4.488, 2.684], [-5.826, 1.012], [-7.967, 0.099], [-5.626, -0.756]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 159"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.91, 0.949], [-0.583, 0.125], [0.052, -0.344], [0.319, -0.253], [-0.789, -0.486], [1.068, -0.918], [0.193, -0.196], [0.629, -0.506], [-1.209, 1.354]], "o": [[0.697, -0.851], [0.269, -0.058], [-0.072, 0.479], [-0.825, 0.655], [0.827, 0.509], [-0.2, 0.172], [-0.533, -0.024], [-1.514, -0.459], [0.673, -1.048]], "v": [[55.171, -58.611], [56.985, -59.349], [57.412, -58.967], [56.644, -58.085], [56.119, -56.426], [55.79, -54.88], [55.211, -54.296], [53.481, -53.645], [53.175, -55.457]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 160"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.887, -0.611], [0.357, 0.041], [-0.097, 0.892], [-0.337, 0.032], [-0.631, 1.577], [0, 0], [-0.555, 2.032], [0, 0], [-0.907, 0.498], [0, 0], [0.059, -0.036], [1.709, -2.384]], "o": [[-0.354, 0.341], [-0.624, 0.35], [0.078, -0.721], [1.891, -0.178], [0, 0], [1.531, -1.709], [0, 0], [1.017, -0.815], [0, 0], [-0.053, 0.185], [-2.666, 1.643], [-0.635, 0.885]], "v": [[-16.447, 17.598], [-17.512, 18.224], [-18.76, 18.063], [-17.549, 16.956], [-14.541, 13.514], [-14.544, 13.518], [-11.236, 7.965], [-11.212, 7.928], [-8.9, 7.602], [-8.899, 7.602], [-9.059, 8.138], [-14.404, 15.144]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 161"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.11, 0.066], [-2.628, 1.708], [-0.772, 0.479], [-0.289, 0.51], [-0.038, -1.008], [0.828, -0.461], [1.427, -1.082], [0.194, 1.96], [0.012, 0.489]], "o": [[0.683, 1.796], [0.77, -0.501], [0.377, -0.11], [0.44, -0.041], [0.034, 0.915], [-1.427, 0.796], [-1.997, 1.514], [-0.046, -0.466], [0.11, -0.054]], "v": [[32.911, 66.795], [36.222, 66.878], [38.537, 65.436], [39.566, 64.645], [40.913, 64.379], [39.446, 66.339], [35.168, 69.003], [32.623, 68.437], [32.582, 66.974]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 162"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0.774], [0.004, 0.329], [-2.386, 1.026], [-0.041, -0.334], [0.347, -0.234], [0.438, -0.849], [-1.049, 0.545], [-0.047, -0.575], [0.365, -0.161], [0.066, -0.928], [0.677, -0.386], [0.018, 0.446]], "o": [[0, -0.331], [-0.026, -2.427], [0.274, -0.118], [0.053, 0.432], [-0.578, 0.39], [-0.551, 1.069], [0.409, -0.213], [0.053, 0.645], [-1.781, 0.784], [-0.034, 0.482], [-0.679, 0.387], [-0.031, -0.755]], "v": [[2.413, 30.701], [2.412, 29.707], [5.142, 25.715], [5.781, 25.707], [5.136, 26.601], [3.431, 27.88], [4.595, 29.011], [5.69, 28.766], [4.686, 29.803], [3.381, 32.472], [2.891, 33.957], [2.422, 33.017]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 163"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-2.751, 1.556], [0.143, -1.137], [0.687, -0.447], [-0.093, 3.05], [-0.891, 2.509], [-1.307, 0.348], [0, 0], [0.004, -0.682]], "o": [[0.401, -0.058], [-0.088, 0.697], [-3.026, 1.969], [0.064, -2.087], [0.741, 0.195], [0, 0], [-0.799, 0.879], [-0.127, 3.407]], "v": [[62.45, 60.729], [63.539, 60.805], [62.479, 62.379], [59.094, 61.236], [59.526, 54.837], [60.435, 56.686], [60.433, 56.683], [59.825, 58.878]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 164"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.704, 0.978], [-1, 1.246], [-0.056, -0.016], [-1.579, 1.28], [1.105, 0.262], [0.507, -0.813], [0.664, -0.354], [0.114, -0.152], [0.089, -0.079], [-0.186, 0.466], [0.484, -0.228]], "o": [[0.452, 0.152], [0.081, -0.101], [1.118, 0.31], [-0.634, 1.374], [-0.65, -0.154], [-0.519, 0.832], [-0.114, 0.152], [-0.09, 0.078], [-0.415, -0.03], [0.476, -1.19], [0.326, -1.085]], "v": [[1.347, 12.921], [2.93, 12.799], [3.245, 12.581], [7.119, 11.696], [4.608, 13.787], [2.87, 14.939], [1.066, 16.526], [0.724, 16.982], [0.455, 17.218], [0.333, 16.336], [-0.404, 15.957]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 165"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.862, 1.615], [0.976, -0.037], [-0.74, 2.181], [-2.918, 1.08], [0.816, -1.267], [-0.283, -0.943]], "o": [[-0.959, 1.864], [-1.615, 0.061], [0.701, -2.068], [-1.384, 1.021], [-0.81, 1.258], [0.425, 1.415]], "v": [[18.061, 24.55], [14.971, 26.801], [13.527, 22.855], [18.307, 18.627], [14.962, 21.435], [14.131, 24.789]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 166"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.196, -1.526], [-0.103, 1.508], [-0.623, 0.298], [0.482, -0.975], [-0.105, -1.058], [0.029, 0.025], [0.871, -0.582]], "o": [[0, -1.808], [0.081, -1.185], [0.864, -0.414], [-0.865, 1.75], [-0.106, 0.041], [-0.48, -0.411], [-1.102, 0.735]], "v": [[-24.142, 78.143], [-24.117, 73.411], [-22.431, 71.765], [-21.203, 72.577], [-21.135, 75.888], [-21.376, 75.976], [-22.977, 75.062]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 167"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.035, 0.539], [0.453, -0.237], [-0.001, -0.457], [-0.448, 0.296]], "o": [[-0.066, -0.541], [-0.643, 0.337], [0.001, 0.502], [0.495, -0.327]], "v": [[-21.871, 72.766], [-22.813, 72.59], [-23.464, 73.857], [-22.719, 74.057]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 168"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-2.533, 3.146], [-0.923, 0.444], [0.392, -0.272], [-0.257, -0.396], [0.491, -1.276]], "o": [[-0.042, 1.615], [-0.374, 0.882], [-0.637, 0.442], [0.582, 0.898], [-0.887, -1.474]], "v": [[11.021, -34.305], [13.148, -34.372], [11.888, -33.224], [11.163, -31.82], [10.42, -28.435]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 169"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.184, -0.347], [0.001, 0.606], [-0.162, 0.581], [-1.085, 0.587], [-0.117, -0.871], [1.52, -0.723]], "o": [[-0.65, 0.335], [1.312, -0.765], [0.294, -1.056], [1.052, -0.57], [0.18, 1.344], [-0.309, 0.147]], "v": [[-29.227, 80.426], [-30.13, 79.913], [-29.534, 78.111], [-27.857, 75.421], [-26.258, 76.301], [-28.512, 79.759]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 170"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.103, -0.094], [-0.206, 0.218], [-1.082, 0.915], [-0.657, 0.456], [0, -0.002], [0.09, -0.429], [1.601, -0.914], [1.121, -0.635], [0.277, -0.142], [0.539, -0.234]], "o": [[-0.086, -0.195], [1.013, -1.069], [0.21, -0.178], [-0.045, 0.036], [0.076, 0.375], [-0.19, 0.913], [-0.22, -0.322], [-0.277, 0.143], [-0.489, 0.16], [-0.087, -0.015]], "v": [[53.224, -46.568], [52.861, -46.988], [56.01, -50.005], [56.982, -49.741], [56.853, -49.637], [57.065, -48.512], [55.833, -45.696], [55.178, -46.667], [54.348, -46.24], [53.509, -46.687]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 171"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.023, 0.368], [-0.309, 0.147], [0.18, 1.344], [1.052, -0.57], [0.294, -1.056], [1.312, -0.765], [-0.032, 0.452], [-1.324, 1.025], [-0.458, -0.653], [1.017, -1.6]], "o": [[0.184, -0.347], [1.52, -0.723], [-0.117, -0.871], [-1.085, 0.587], [-0.162, 0.581], [-0.031, -0.421], [0.093, -1.32], [1.025, -0.794], [0.612, 0.873], [-0.854, 1.344]], "v": [[-29.227, 80.426], [-28.512, 79.759], [-26.258, 76.301], [-27.857, 75.421], [-29.534, 78.111], [-30.13, 79.913], [-30.212, 78.646], [-28.24, 74.995], [-25.923, 74.852], [-26.31, 78.593]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 172"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.172, -0.505], [1.099, 1.291], [0.808, -0.316], [-0.116, 0.712], [-0.314, 0.116], [-0.324, 0.089], [-0.511, -0.08], [-0.895, 1.037], [-0.586, -0.295]], "o": [[-1.386, 1.847], [-0.263, -0.309], [-0.999, 0.159], [0.107, -0.661], [0.137, 0.223], [0.752, -0.417], [0.729, 0.114], [0.477, -0.553], [0.451, 0.227]], "v": [[-1.961, -2.362], [-5.626, -0.756], [-7.967, 0.099], [-8.469, -1.545], [-7.572, -2.089], [-6.898, -1.859], [-5.321, -1.711], [-2.925, -2.693], [-1.441, -4.013]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 173"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.003, -0.002], [-0.042, 0.572], [1.227, -0.873], [-0.048, 0.495], [-0.278, 0.161], [-1.101, 0.626], [0.015, -0.454], [0.271, -0.106], [-0.032, -0.907], [0.009, -1.328], [0.48, -0.24], [0.002, 0.276], [0, 0.772]], "o": [[0, -0.551], [0.072, -0.986], [-0.313, 0.222], [0.035, -0.364], [1.102, -0.637], [0.331, -0.188], [-0.012, 0.343], [-1.26, 0.495], [0.046, 1.295], [-0.002, 0.315], [-0.334, 0.167], [-0.007, -0.768], [-0.003, 0.002]], "v": [[22.16, 19.889], [22.168, 18.232], [20.888, 17.478], [20.219, 17.38], [20.865, 16.65], [24.17, 14.754], [24.881, 14.807], [24.248, 15.523], [22.976, 17.708], [22.993, 21.667], [22.533, 22.63], [22.171, 22.198], [22.168, 19.884]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 174"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.159, -0.098], [0.075, 0.673], [0.651, -0.377], [0.129, -0.588], [0.392, -0.904], [0.173, -0.101], [-0.019, 1.69], [-0.097, 0.047], [0.026, -0.28], [-0.95, 0.759], [-0.064, -0.786], [0, -0.858]], "o": [[-0.442, -0.405], [-0.053, -0.481], [-0.578, 0.334], [-0.178, 0.814], [-0.173, 0.101], [0, -1.68], [0.001, -0.103], [0.372, -0.182], [-0.074, 0.79], [0.8, -0.639], [0.066, 0.815], [-0.159, 0.098]], "v": [[6.594, 60.121], [6.382, 58.092], [5.631, 57.455], [4.708, 58.993], [4.522, 61.28], [4.004, 61.582], [4.018, 56.536], [4.323, 56.073], [4.679, 56.492], [5.644, 56.818], [7.057, 57.262], [7.072, 59.827]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 175"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.371, -0.16], [-0.149, 1.318], [-1.664, 1.219], [-0.589, -1.695], [-0.004, -0.748], [0, -0.001], [0.238, -0.135], [0.214, 0.641], [1.223, -0.845], [0.043, -0.599], [-0.017, -1.146]], "o": [[-0.038, -1.233], [0.102, -0.902], [2.494, -1.828], [0.004, 0.749], [0, 0], [-0.238, 0.135], [0.315, -0.991], [-0.351, -1.052], [-1.241, 0.858], [-0.085, 1.197], [-0.133, 0.49]], "v": [[49.405, 6.739], [49.339, 3.018], [50.139, 0.063], [54.178, -0.028], [54.19, 2.218], [54.195, 2.319], [53.48, 2.722], [53.442, 0.326], [50.856, 0.346], [50.202, 2.397], [50.223, 5.85]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 176"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.005, 0.003], [-0.04, 0.574], [1.165, -0.826], [-0.026, 0.504], [-0.319, 0.186], [-1.05, 0.598], [0.011, -0.45], [0.285, -0.125], [-0.043, -0.925], [0.01, -1.223], [0.516, -0.33], [0.01, 0.352], [0, 0.719]], "o": [[0, -0.553], [0.064, -0.914], [-0.3, 0.213], [0.024, -0.467], [1.05, -0.612], [0.333, -0.19], [-0.008, 0.327], [-1.229, 0.541], [0.055, 1.183], [-0.003, 0.366], [-0.562, 0.36], [-0.02, -0.707], [0.005, -0.003]], "v": [[-36.186, 53.435], [-36.179, 51.772], [-37.364, 51.045], [-38.089, 51.049], [-37.323, 50.211], [-34.171, 48.396], [-33.444, 48.436], [-34.083, 49.158], [-35.365, 51.405], [-35.347, 55.045], [-35.747, 56.216], [-36.196, 55.598], [-36.202, 53.444]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 177"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.649, -0.377], [0.366, -0.101], [0.073, -1.232], [-0.96, 0.656], [-0.276, 0.155], [-0.027, -0.339], [0.294, -0.17], [0.717, -0.421], [-0.011, 0.601], [0.01, 0.932], [-3.264, 1.62], [-0.113, -2.101]], "o": [[-0.203, 0.558], [-0.906, 0.251], [-0.076, 1.281], [0.274, -0.187], [0.284, -0.159], [0.028, 0.349], [-0.718, 0.415], [-0.648, 0.38], [0.017, -0.948], [-0.035, -3.137], [-0.632, 1.328], [0.101, 1.873]], "v": [[10.825, 25.955], [9.873, 26.956], [8.369, 28.776], [9.854, 29.206], [10.682, 28.701], [11.287, 28.758], [10.745, 29.519], [8.592, 30.745], [7.737, 30.303], [7.754, 27.477], [10.978, 22.723], [8.371, 25.986]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 178"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.593, 1.722], [0.69, -0.304], [-0.252, 1.067], [0.477, -0.191], [0.012, -0.407], [0.292, -0.917], [-0.131, 1.679], [-0.241, 0.097], [0.008, -0.35], [-0.485, -0.44]], "o": [[-0.035, 1.196], [-0.648, 0.286], [0.158, -0.67], [-0.744, 0.297], [-0.022, 0.751], [-0.457, -1.311], [0.018, -0.237], [0.452, -0.183], [-0.021, 0.921], [0.508, 0.462]], "v": [[-33.493, 80.941], [-34.349, 83.664], [-35.2, 82.034], [-35.788, 81.467], [-36.394, 82.671], [-36.693, 85.069], [-36.964, 80.446], [-36.468, 79.835], [-35.791, 80.273], [-34.918, 82.062]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 179"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.2, 0.334], [-0.456, 0.26], [-0.836, 2.17], [-0.39, -0.223], [0.336, -0.759], [1.352, 1.171]], "o": [[0.455, -0.26], [1.487, 0.825], [0.419, -0.276], [0.385, 0.22], [-1.169, 2.642], [-0.276, -0.239]], "v": [[-1.68, 20.377], [-0.314, 19.597], [2.39, 18.031], [3.481, 16.994], [3.451, 18.707], [-1.068, 21.366]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 180"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.036, 0.636], [-0.976, -0.325], [0.832, -0.903], [1.013, 0.68], [-0.008, 0.346], [0.068, 0.832], [0.44, -0.176], [0.108, -0.057], [0.849, -0.276], [-0.001, -0.002], [-0.547, 0.458]], "o": [[1.253, -0.297], [0.536, 0.179], [-0.622, 0.675], [0.008, -0.347], [0.457, -0.375], [-0.052, -0.629], [-0.105, 0.042], [-0.856, -0.143], [0, 0], [-0.015, -0.475], [0.471, -0.394]], "v": [[-31.11, 41.326], [-27.788, 41.413], [-27.62, 42.884], [-29.809, 44.632], [-29.785, 43.593], [-28.582, 42.295], [-29.79, 42.543], [-30.113, 42.713], [-32.647, 44.22], [-32.762, 44.2], [-32.092, 42.855]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 181"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.131, -0.349], [0.017, -0.016], [-0.438, -1.576], [0.254, -0.095], [0.001, 0.235], [-0.027, 1.893], [-0.434, 0.254], [-0.323, -1.066], [1.224, -1.063], [-0.047, 0.259], [1.314, -0.692], [0.055, -0.653], [-0.746, 0.473]], "o": [[-0.002, 0.057], [-1.548, 1.37], [0.041, 0.147], [-0.249, 0.093], [-0.007, -1.874], [0.012, -0.817], [0.965, -0.564], [0.166, 0.547], [-0.421, 0.08], [0.222, -1.216], [-1.277, 0.673], [-0.132, 1.567], [0.241, -0.153]], "v": [[-12.207, 39.077], [-12.215, 39.242], [-14.322, 43.592], [-14.805, 44.183], [-15.167, 43.911], [-15.165, 38.276], [-14.045, 37.078], [-11.557, 36.639], [-12.198, 39.065], [-12.294, 38.462], [-13.609, 37.336], [-14.51, 39.318], [-12.855, 39.299]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 182"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.794, 0.268], [0.48, -1.309], [1.326, -0.361], [-0.181, 0.747], [-0.621, 0.31], [-0.629, 1.057], [0.154, 0.177], [0.666, -0.45], [0.385, -0.222], [-0.044, 0.878]], "o": [[-0.714, 1.23], [1.058, -0.357], [-0.439, 1.197], [-0.474, 0.129], [0.172, -0.712], [0.483, -0.241], [0.199, -0.334], [-0.408, -0.466], [-0.212, 0.143], [0.583, -1.023], [0, 0]], "v": [[62.44, -63.135], [62.995, -62.149], [64.329, -60.927], [61.709, -58.01], [60.866, -58.513], [62.176, -59.983], [63.556, -60.467], [63.679, -61.292], [61.935, -60.846], [61.121, -60.373], [62.459, -63.135]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 183"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.005, -0.003], [0.008, 0.88], [-0.423, 0.308], [-0.001, -0.34], [0.017, -1.889], [0.372, -0.192], [0.004, 0.29], [0, 0.939]], "o": [[0, -0.884], [-0.003, -0.343], [0.456, -0.332], [0.006, 1.876], [-0.003, 0.276], [-0.344, 0.177], [-0.012, -0.933], [-0.005, 0.003]], "v": [[46.165, 5.631], [46.162, 2.98], [46.581, 1.965], [47.121, 2.29], [47.108, 7.935], [46.641, 8.754], [46.184, 8.439], [46.179, 5.623]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 184"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.026, -0.158], [0.003, -0.057], [0.453, -0.042], [1.165, 0.219], [-0.104, 0.533], [-0.222, -0.082], [-1.027, -0.331]], "o": [[-0.013, 0.163], [-0.023, 0.474], [-1.299, 0.12], [-0.412, -0.077], [0.104, -0.531], [1.101, 0.405], [0.157, 0.051]], "v": [[-9.86, 56.681], [-9.882, 56.959], [-10.527, 58.254], [-14.362, 60.242], [-14.225, 58.762], [-13.391, 57.718], [-9.985, 56.056]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 185"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.211, -0.845], [-0.326, 0.408], [-1.486, 0.073], [1.489, -1.545], [0.777, -0.821]], "o": [[-0.373, -0.029], [1.488, -1.297], [0.623, 0.82], [-0.888, 0.922], [-0.323, -0.69]], "v": [[44.973, -41.083], [44.997, -41.789], [49.458, -44.357], [48.702, -41.071], [46.829, -40.545]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 186"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.537, 0.289], [-0.056, 0.545], [0.35, -0.186], [-0.021, -0.539]], "o": [[0.509, -0.319], [0.053, -0.511], [-0.437, 0.232], [0.015, 0.384]], "v": [[48.124, -41.585], [48.928, -42.852], [48.343, -43.312], [47.56, -42.053]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 187"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.134], [-0.127, 3.407], [-0.178, -1.65], [0.108, -0.282], [0, 0], [0.943, -0.65], [-0.803, 0.508]], "o": [[-2.751, 1.556], [2.129, -1.807], [-0.108, 0.282], [0, 0], [-0.792, 0.766], [0.535, 0.558], [0, 0.134]], "v": [[62.45, 60.729], [59.825, 58.878], [63.127, 58.652], [62.803, 59.498], [62.816, 59.478], [60.469, 60.395], [62.45, 60.327]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 188"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.255, 0.176], [0.036, -1.174], [0.733, -0.421], [-0.025, 0.398], [0.921, -0.546], [-0.123, -0.883], [0.67, -0.845], [-0.282, 1.884], [-1.82, 1.56], [-0.185, 0.634]], "o": [[-0.003, 1.156], [-0.014, 0.439], [-0.598, 0.344], [0.053, -0.855], [-0.909, 0.538], [0.044, 0.316], [-0.346, -1.603], [0.365, 0.979], [1.086, -0.931], [0.255, -0.176]], "v": [[-10.726, 65.4], [-10.748, 68.873], [-11.198, 70.296], [-11.549, 69.429], [-12.646, 68.91], [-13.832, 70.786], [-14.305, 72.284], [-14.217, 67.08], [-12.02, 67.837], [-11.492, 65.928]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 189"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.137, -0.079], [0.593, 0.079], [-0.06, 0.078], [-0.565, 0.206], [-1.031, 0.063], [0.468, -0.526], [-1.384, -0.526], [0.47, -0.914], [0.156, 0.053], [-1.165, 1.844], [-0.132, 0.475], [-0.048, 0.133]], "o": [[-0.597, 0.137], [-0.081, -0.011], [0.538, -0.689], [0.972, -0.355], [-0.199, 0.931], [-1.715, 1.927], [0.185, 0.071], [-0.361, 0.702], [-0.802, -0.271], [0.265, -0.419], [0.048, -0.133], [-0.137, 0.079]], "v": [[25.133, -41.137], [23.396, -40.036], [23.352, -40.434], [25.081, -41.575], [27.912, -43.519], [26.47, -41.746], [25.934, -37.998], [26.45, -37.584], [25.481, -37.163], [24.982, -39.559], [25.399, -40.975], [25.543, -41.373]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 190"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.249, -0.624], [0.206, 0.583], [-0.217, 0.491], [-0.502, 0.03], [-0.485, 0.328], [-0.012, -1.013], [0.6, -0.371], [0.165, -0.093], [0.049, 0.299], [0.083, 0.103]], "o": [[-0.524, 0.283], [-0.126, -0.358], [0.321, -0.726], [0.483, -0.029], [0.822, -0.556], [0.014, 1.137], [-0.164, 0.101], [-0.049, -0.298], [-0.084, -0.102], [-0.999, 0.671]], "v": [[-35.189, 64.562], [-36.6, 64.905], [-36.502, 63.448], [-35.026, 62.41], [-33.586, 61.721], [-32.188, 62.005], [-33.556, 63.615], [-34.05, 63.894], [-34.196, 62.998], [-34.446, 62.691]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 191"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.142, -0.66], [-0.839, -0.303], [-1.31, 0.691], [0.001, -1.007], [1.021, -0.505]], "o": [[0.441, -2.075], [0.507, -0.869], [0.691, -0.365], [-0.001, 0.841], [-1.1, 0.544]], "v": [[-36.623, 73.304], [-34.491, 71.604], [-33.415, 69.257], [-32.086, 69.453], [-33.268, 71.388]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 192"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.128, -0.162], [-1.395, 1.685], [-0.574, -0.501], [0.301, -0.788], [0.738, -0.151]], "o": [[-1.327, -0.715], [0.647, -0.448], [0.366, 0.319], [-0.415, 1.085], [-0.084, 0.017]], "v": [[-3.669, 65.671], [-3.595, 63.016], [-1.685, 62.07], [-1.508, 63.651], [-3.291, 65.206]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 193"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-2.508, 1.794], [2.306, -1.641], [0.109, -0.419], [0.649, -0.379]], "o": [[0.452, 0.663], [-0.182, 0.129], [-0.221, 0.852], [1.913, -1.92]], "v": [[34.489, -46.697], [33.935, -44.02], [33.52, -42.779], [32.065, -41.229]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 194"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.129, -0.856], [0.976, -0.71], [-2.715, 0.77], [0.3, -0.839]], "o": [[-0.95, 0.248], [0.276, -2.486], [0.027, 0.508], [-0.493, 0.341]], "v": [[62.793, -52.104], [59.899, -50.597], [63.454, -54.466], [64.042, -53.453]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 195"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.18, -0.409], [0.741, 0.195], [-0.054, 0.066], [-0.095, -0.636], [0.349, -0.416], [0.499, 0.172]], "o": [[-1.307, 0.348], [0.006, -0.111], [0.616, -0.755], [0.06, 0.401], [-0.583, 0.325], [-0.977, -0.338]], "v": [[60.435, 56.686], [59.526, 54.837], [59.551, 54.508], [63.42, 53.882], [62.886, 55.124], [61.222, 55.69]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 196"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.002, -0.089], [3.374, -1.948], [0.002, 0.089], [-3.374, 1.948]], "o": [[-3.374, 1.948], [-0.002, -0.089], [3.374, -1.948], [0.002, 0.089]], "v": [[62.549, 89.998], [52.429, 95.841], [52.422, 95.574], [62.544, 89.731]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 197"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.097, -0.45], [1.488, -1.297], [0.878, 0.195], [-1.575, 0.388]], "o": [[-1.486, 0.073], [0.893, -1.313], [1.531, -1.192], [-1.07, 1.144]], "v": [[49.458, -44.357], [44.997, -41.789], [45.012, -43.297], [49.509, -45.863]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 198"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.002, 0.001], [0.016, -0.888], [0.475, -0.23], [0.001, 0.279], [0.002, 1.867], [-0.303, 0.178], [-0.002, -0.336], [0, -0.934]], "o": [[0, 0.88], [-0.006, 0.324], [-0.328, 0.159], [-0.009, -1.864], [0, -0.301], [0.437, -0.257], [0.005, 0.931], [0.002, -0.001]], "v": [[30.991, 14.437], [30.986, 17.078], [30.499, 18.038], [30.108, 17.635], [30.097, 12.036], [30.516, 11.19], [30.983, 11.638], [30.985, 14.44]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 199"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.403, -1.713], [0.179, 0.303], [1.669, -0.968], [-0.098, 0.284], [-0.872, 0.404], [0.029, -0.343]], "o": [[-0.768, 0.037], [-0.244, -0.413], [-0.629, 0.365], [0.209, -0.61], [0.378, -0.175], [-0.127, 1.492]], "v": [[-31.83, 82.376], [-32.349, 81.272], [-33.181, 80.066], [-33.038, 79.148], [-32.018, 77.31], [-31.68, 77.935]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 200"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.097, -0.179], [0.565, -0.93], [-0.358, 1.662], [-1.024, 0.932], [-0.361, -1.336], [-0.444, -0.431], [0.469, -0.368]], "o": [[-0.044, 0.689], [-0.444, -1.27], [0.254, -1.178], [-0.467, 1.452], [0.228, 0.842], [-0.416, 0.698], [-0.229, 0.085]], "v": [[39.721, -47.312], [39.305, -45.098], [39.123, -49.586], [41.624, -51.614], [39.716, -48.678], [41.401, -48.806], [39.945, -47.7]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 201"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.127, -2.304], [-2.299, -0.824], [0.464, -0.749], [0.493, 0.04], [0.183, 0.1]], "o": [[1.838, -3.437], [0.401, 0.144], [-0.575, 0.927], [-0.224, -0.018], [-1.221, -0.666]], "v": [[15.369, -33.325], [19.516, -35.956], [20.47, -35.247], [18.991, -35.198], [18.423, -35.468]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 202"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.025, 0.335], [-1.21, 0.251], [0.004, -0.34], [0.997, 0.467], [0.333, -0.213]], "o": [[1.215, -0.945], [-0.004, 0.34], [-0.771, 1.612], [-0.262, -0.123], [-0.025, -0.335]], "v": [[-25.495, 64.954], [-21.854, 62.879], [-21.865, 63.898], [-24.52, 65.713], [-25.419, 65.96]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 203"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.893, 0.423], [-0.084, 0.017], [-0.415, 1.085], [0.366, 0.319], [0.647, -0.448], [-0.395, 0.423], [-0.677, -0.478], [0.739, -1.267]], "o": [[0.128, -0.162], [0.738, -0.151], [0.301, -0.788], [-0.574, -0.501], [-0.177, -0.096], [0.921, -0.985], [0.677, 0.478], [-0.741, 1.27]], "v": [[-3.669, 65.671], [-3.291, 65.206], [-1.508, 63.651], [-1.685, 62.07], [-3.595, 63.016], [-3.632, 62.418], [-1.118, 61.361], [-1.124, 64.084]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 204"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.247, 0.736], [0.74, -1.804], [0.249, -0.04], [-0.069, 0.316], [1.276, 0.191], [-0.03, 0.079]], "o": [[0.123, 1.501], [-0.113, 0.276], [-0.357, 0.057], [0.184, -0.841], [0.134, -0.087], [0.291, -0.754]], "v": [[32.159, -45.773], [31.02, -40.738], [30.519, -40.168], [30.399, -40.763], [30.704, -43.191], [31.093, -43.451]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 205"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.515, -0.797], [-0.015, 5.54], [-0.011, -1.045], [0.019, -3.915]], "o": [[0.016, -5.54], [0.836, 0.509], [0.04, 3.881], [-0.003, 0.581]], "v": [[69.141, 84.375], [69.187, 67.756], [69.432, 70.773], [69.438, 82.482]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 206"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0.338], [-0.048, 0.183], [-0.633, 0.24], [-0.138, -0.562], [-0.113, -0.283], [0.411, -0.725], [0.246, -0.095], [0.082, 0.296]], "o": [[0.005, -0.049], [0.203, -0.762], [0.911, -0.346], [0.078, 0.32], [0.097, 0.242], [-0.128, 0.226], [-0.521, 0.201], [-0.05, -0.179]], "v": [[44.676, -50.942], [44.724, -51.342], [45.347, -53.511], [45.397, -51.739], [45.508, -50.685], [45.896, -50.045], [45.132, -49.486], [44.685, -50.172]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 207"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.13, -0.189], [0.376, 0.114], [-0.297, 1], [-1.009, 0.272], [-1.474, 0.011]], "o": [[-0.02, -0.725], [-0.485, -0.147], [0.34, -1.148], [-1.348, 1.154], [0.367, -0.003]], "v": [[2.635, 61.566], [1.418, 61.523], [0.392, 60.641], [2.533, 58.897], [2.034, 60.768]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 208"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.506, -0.527], [0.577, -0.445], [0.304, 0.036], [-0.154, 0.435], [-0.252, 0.004], [-0.443, 0.395], [-0.293, -0.187]], "o": [[-0.578, 0.446], [-0.199, -0.281], [-0.289, -0.035], [0.132, -0.37], [0.549, -0.009], [0.484, -0.432], [0.298, 0.19]], "v": [[53.342, 13.189], [51.609, 14.525], [50.649, 14.528], [50.32, 13.892], [51.017, 13.318], [52.356, 12.268], [53.67, 11.935]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 209"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.436, -0.237], [-0.029, 0.774], [-0.676, 0.384], [-0.033, -0.755]], "o": [[-1.019, 0.64], [0.026, -0.683], [0.988, -0.561], [0.036, 0.83]], "v": [[-35.51, 68.702], [-36.695, 68.145], [-35.681, 66.592], [-34.564, 67.369]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 210"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.403, -1.37], [0.914, -0.389], [0.014, 0.952], [-0.629, 0.285]], "o": [[-0.06, 0.372], [-0.816, 0.348], [-0.01, -0.634], [0.724, -0.328]], "v": [[-12.599, 46.82], [-13.23, 48.069], [-14.749, 47.628], [-13.712, 46.054]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 211"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.049, 0.997], [-0.004, 0.34], [-0.074, -1.667], [0.856, -0.526]], "o": [[0.004, -0.34], [0.926, -0.904], [0.022, 0.503], [-0.712, 0.438]], "v": [[-21.865, 63.898], [-21.854, 62.879], [-19.845, 62.523], [-20.57, 64.118]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 212"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.059, -0.354], [1.487, 0.825], [-0.389, 0.594], [-0.712, 0.668]], "o": [[-0.836, 2.17], [0.573, -0.604], [0.395, 0.127], [0.713, -0.669]], "v": [[2.39, 18.031], [-0.314, 19.597], [0.387, 17.842], [1.785, 17.563]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 213"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.051, -0.598], [0.878, -0.506], [0.002, 0.727], [-0.809, 0.459]], "o": [[0.029, 0.659], [-0.81, 0.466], [-0.002, -0.826], [0.791, -0.449]], "v": [[-22.531, 52.163], [-23.468, 53.866], [-24.497, 53.388], [-23.319, 51.532]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 214"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.098, 0.783], [-0.862, 0.464], [-0.049, -0.667], [0.636, -0.381]], "o": [[-0.071, -0.658], [0.937, -0.504], [0.053, 0.734], [-0.825, 0.494]], "v": [[-29.146, 67.939], [-28.203, 66.232], [-27.179, 67.014], [-28.191, 68.544]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 215"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.076, -0.331], [0.767, -0.431], [-0.03, 0.683], [-0.576, 0.401]], "o": [[0.014, 0.683], [-0.851, 0.478], [0.038, -0.888], [0.749, -0.522]], "v": [[-9.899, 48.846], [-10.809, 50.488], [-12.041, 49.959], [-10.504, 48.177]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 216"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0.08], [-1.573, 0.567], [-0.051, -0.451], [0.691, -0.211], [0.596, -0.314]], "o": [[0.864, -0.723], [1.037, -0.374], [0.066, 0.587], [-0.563, 0.172], [-0.005, -0.076]], "v": [[-34.819, 75.734], [-32.859, 73.023], [-32.135, 73.871], [-33.03, 75.057], [-34.812, 75.969]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 217"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.503, -0.107], [1.155, 0.688], [0.268, -0.049], [0, 0], [-0.829, 0.22]], "o": [[-1.267, 2.581], [0.217, -0.242], [0, 0], [-0.014, -0.797], [0.56, -0.149]], "v": [[22.081, 35.303], [19.299, 37.478], [19.292, 37.147], [19.275, 37.195], [20.381, 35.777]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 218"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.007, -0.453], [0.434, 0.9], [-0.607, 0.626], [-0.334, -0.765]], "o": [[-0.661, 0.299], [-0.174, -0.359], [0.549, -0.567], [-0.007, 0.453]], "v": [[-7.572, 52.303], [-9.523, 53.162], [-9.17, 51.488], [-7.552, 50.943]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 219"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.039, 0.598], [-0.804, 0.337], [0.046, -0.812], [0.651, -0.375]], "o": [[0.043, -0.627], [0.707, -0.297], [-0.051, 0.903], [-0.846, 0.488]], "v": [[-21.815, 55.717], [-20.84, 54.194], [-19.818, 54.792], [-21.064, 56.434]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 220"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.599, 0.391], [0.023, -0.645], [0.721, -0.469], [0.119, 0.466]], "o": [[0.771, -0.485], [-0.021, 0.603], [-0.894, 0.582], [-0.162, -0.634]], "v": [[-12.115, 60.972], [-11.221, 61.502], [-12.067, 63.148], [-13.056, 62.496]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 221"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-3.972, 1.865], [3.973, -2.097]], "o": [[-3.974, 2.437], [3.974, -2.623]], "v": [[64.785, 69.853], [52.866, 76.703]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 222"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.036, -0.566], [0.733, -0.168], [-0.066, 1.02], [-0.725, 0.373]], "o": [[-0.042, 0.53], [-0.502, 0.115], [0.043, -0.659], [0.77, -0.395]], "v": [[-34.787, 59.647], [-35.487, 60.855], [-36.718, 60.627], [-35.784, 59.02]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 223"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.001, 0.36], [-0.506, 0.277], [0.276, -1.062], [1.124, -0.655]], "o": [[-0.421, -0.861], [0.404, -0.221], [-0.162, 0.624], [-1.105, 0.643]], "v": [[-21.834, 51.924], [-21.021, 50.457], [-20.139, 50.643], [-21.115, 52.523]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 224"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.137, -0.817], [0.874, -0.51], [0.148, 0.435], [-0.497, 0.315]], "o": [[0.004, 0.517], [-0.985, 0.575], [-0.207, -0.608], [0.697, -0.442]], "v": [[-2.665, 44.732], [-3.457, 46.268], [-4.368, 45.521], [-3.528, 44.129]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 225"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.026, 0.457], [-0.653, 0.34], [0.219, -0.703], [0.976, -0.526]], "o": [[-0.071, -0.934], [0.536, -0.279], [-0.195, 0.625], [-0.887, 0.478]], "v": [[-34.039, 66.837], [-32.882, 65.07], [-32.327, 65.658], [-33.293, 67.428]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 226"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.458, -1.473], [-0.524, 1.5]], "o": [[-0.477, -0.911], [0.381, 0.959]], "v": [[-8.814, 68.965], [-8.783, 65.37]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 227"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.143, -0.07], [-0.124, 0.922], [-0.451, 0.159], [0.025, -0.33], [0.078, -0.933]], "o": [[-0.196, -0.726], [0.013, -0.097], [0.195, -0.069], [-0.07, 0.928], [-0.143, 0.07]], "v": [[-6.893, 67.833], [-6.92, 65.301], [-6.68, 64.737], [-6.23, 64.826], [-6.463, 67.623]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 228"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.189, 1.172], [-1.417, 0.773], [0.705, -0.389]], "o": [[0.758, 1.01], [-0.824, 1.483], [-0.729, 0.402]], "v": [[46.845, -51.318], [50.238, -52.521], [47.853, -50.377]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 229"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.751, -0.402], [0.099, 0.56], [-0.57, 0.339], [0.017, -0.704]], "o": [[-0.802, 0.546], [-0.105, -0.592], [0.677, -0.403], [-0.013, 0.537]], "v": [[12.875, 48.751], [11.776, 48.275], [12.654, 46.898], [13.613, 47.342]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 230"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.899, 0.464], [-0.012, -0.599], [0.739, -0.412], [-0.056, 0.621]], "o": [[0.794, -0.555], [0.012, 0.603], [-0.616, 0.343], [0.048, -0.541]], "v": [[-23.405, 67.508], [-22.482, 67.995], [-23.354, 69.615], [-24.151, 69.075]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 231"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.527, -0.161], [-0.122, 0.7], [-0.773, 0.406], [0.087, -0.679]], "o": [[-0.626, 0.351], [0.113, -0.649], [0.597, -0.314], [-0.084, 0.657]], "v": [[-30.69, 69.655], [-31.717, 69.565], [-30.611, 67.693], [-29.623, 68.222]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 232"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.62, 0.359], [0.055, -0.619], [0.778, -0.475], [-0.028, 0.665]], "o": [[0.483, -0.285], [-0.056, 0.634], [-0.664, 0.405], [0.03, -0.698]], "v": [[1.513, 45.417], [2.37, 45.619], [1.384, 47.441], [0.424, 46.966]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 233"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.774, 0.572], [0.673, -1.048], [-0.877, 1.369]], "o": [[-0.91, 0.949], [-0.728, -0.909], [0.623, -0.331]], "v": [[55.171, -58.611], [53.175, -55.457], [54.169, -59.049]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 234"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.062, -0.762], [0.783, -0.322], [-0.07, 0.893], [-0.708, 0.415]], "o": [[-0.1, 0.349], [-0.596, 0.245], [0.044, -0.564], [0.897, -0.526]], "v": [[-24.833, 61.595], [-25.384, 62.749], [-26.517, 62.277], [-25.721, 60.822]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 235"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.234, 0.127], [0.003, -1.475], [0.275, -0.132], [0.001, 0.097], [0, 1.532]], "o": [[0, 1.474], [0, 0.236], [-0.122, 0.058], [-0.021, -1.52], [0.234, -0.127]], "v": [[-18.792, 69.83], [-18.793, 74.252], [-19.15, 74.887], [-19.478, 74.801], [-19.493, 70.212]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 236"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.031, 0.376], [-0.323, -0.69], [0.584, -0.633], [-0.145, 0.296], [-0.109, 0.186]], "o": [[1.211, -0.845], [-0.488, 1.352], [-0.244, 0.265], [0.092, -0.187], [0.442, -0.76]], "v": [[44.973, -41.083], [46.829, -40.545], [45.123, -39.464], [44.97, -39.799], [45.305, -40.355]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 237"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.197, -1.804], [0.11, -0.054], [-0.694, 2.078]], "o": [[-0.11, 0.066], [-0.388, -1.417], [0.256, 1.504]], "v": [[32.911, 66.795], [32.582, 66.974], [32.383, 62.133]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 238"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.069, -0.062], [-0.031, 0.239], [-1.102, 0.038], [0.09, -0.37], [0.297, -0.069]], "o": [[0.031, -0.238], [1.102, -0.04], [0.241, -0.008], [-0.13, 0.535], [-1.164, 0.271]], "v": [[-0.626, -1.568], [-0.534, -2.283], [2.772, -2.4], [3.232, -2.077], [2.424, -1.415]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 239"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.129, -0.522], [0.445, -0.25], [-0.093, 0.668], [-0.555, 0.232]], "o": [[-0.202, 0.637], [-0.573, 0.321], [0.07, -0.508], [0.568, -0.237]], "v": [[-27.177, 59.172], [-28.315, 60.596], [-29.295, 60.099], [-28.235, 58.666]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 240"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.133, -0.511], [0.639, -0.476], [0.03, 0.465], [-0.658, 0.447]], "o": [[-0.156, 0.712], [-0.587, 0.437], [-0.04, -0.613], [0.546, -0.37]], "v": [[-14.904, 51.711], [-16.091, 53.555], [-16.828, 53.172], [-15.97, 51.536]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 241"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.803, 0.629], [1.231, -0.939], [0.001, 0.647]], "o": [[-0.489, 0.918], [-0.504, 0.384], [-0.001, -1.12]], "v": [[-0.158, 46.253], [-0.926, 48.742], [-1.894, 48.438]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 242"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.833, -0.918], [0.817, -0.42], [-0.001, 0.435]], "o": [[-0.19, 0.873], [-0.592, 0.304], [0.597, -1.117]], "v": [[-18.831, 20.425], [-20.293, 22.464], [-20.85, 21.81]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 243"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.641, 0.464], [-0.021, -0.455], [0.645, -0.275], [-0.079, 0.49]], "o": [[0.547, -0.108], [0.024, 0.514], [-0.519, 0.221], [0.08, -0.498]], "v": [[-15.822, 59.224], [-15.073, 59.972], [-15.99, 61.402], [-16.664, 60.807]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 244"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.506, -1.422], [0.306, -0.172], [0.119, -0.051], [-0.001, 0.083], [-0.525, 1.537]], "o": [[-0.306, 0.171], [-0.119, 0.051], [0.011, -0.088], [0.974, -1.354], [0.595, 1.028]], "v": [[-24.871, 6.879], [-25.79, 7.393], [-26.147, 7.546], [-26.129, 7.29], [-24.898, 3.269]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 245"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.034, 0.606], [-0.492, -0.8], [-0.04, -0.554], [0.332, -0.182]], "o": [[0.726, -0.707], [-0.229, 0.581], [-0.332, 0.182], [-0.271, -0.37]], "v": [[-6.902, 50.275], [-4.928, 49.474], [-5.578, 51.212], [-6.573, 51.759]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 246"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.684, -0.135], [0.759, -0.682], [-0.003, 0.055], [-0.992, 1.338]], "o": [[-0.557, 0.792], [-0.008, -0.049], [0.036, -0.807], [0.407, -0.549]], "v": [[6.7, -16.781], [5.07, -14.384], [5.046, -14.532], [4.441, -16.207]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 247"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.066, -0.727], [0.699, -0.313], [-0.099, 0.708], [-0.698, 0.241]], "o": [[-0.157, 0.562], [-0.602, 0.269], [0.08, -0.576], [0.49, -0.169]], "v": [[11.183, 48.613], [10.184, 50.017], [9.353, 49.559], [10.266, 48.168]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 248"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.028, -0.896], [-0.005, -0.076], [0.435, -0.272], [0.153, 0.031], [-0.781, 0.671]], "o": [[0, 0.08], [-0.435, 0.272], [-0.153, -0.031], [0.137, -0.612], [1.608, -1.381]], "v": [[-34.819, 75.734], [-34.812, 75.969], [-36.117, 76.785], [-36.577, 76.691], [-36.056, 74.845]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 249"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.066, -0.505], [0.76, -0.307], [-0.056, 0.735], [-0.559, 0.321]], "o": [[-0.283, 0.519], [-0.493, 0.199], [0.041, -0.537], [0.554, -0.318]], "v": [[-14.97, 55.722], [-15.725, 57.192], [-16.784, 56.779], [-15.872, 55.287]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 250"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.938, -0.569], [-0.229, 0.581], [-1.148, 0.23]], "o": [[-0.04, -0.554], [0.983, -0.598], [-1.335, 1.163]], "v": [[-5.578, 51.212], [-4.928, 49.474], [-2.354, 49.286]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 251"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.082, -0.327], [0.777, -0.449], [-0.017, 0.734], [-0.556, 0.404]], "o": [[-0.109, 0.483], [-0.549, 0.317], [0.016, -0.705], [0.765, -0.556]], "v": [[-7.598, 55.42], [-8.156, 56.872], [-9.228, 56.5], [-8.146, 54.837]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 252"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.058, -0.576], [0.677, -0.356], [0.011, 0.583], [-0.679, 0.382]], "o": [[0.052, 0.598], [-0.595, 0.312], [-0.011, -0.577], [0.665, -0.374]], "v": [[-27.48, 63.061], [-28.257, 64.542], [-29.136, 64.064], [-28.296, 62.645]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 253"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.229, -0.192], [0.638, -0.487], [-0.002, 0.437], [-0.726, 0.414]], "o": [[-0.186, 0.864], [-0.509, 0.388], [0.003, -0.592], [0.427, -0.243]], "v": [[-22.251, 59.743], [-23.493, 61.741], [-24.154, 61.425], [-23.292, 59.797]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 254"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.629, -0.439], [-0.012, 0.548], [-0.567, 0.373], [-0.035, -0.408]], "o": [[-0.512, 0.238], [0.013, -0.614], [0.54, -0.355], [0.049, 0.567]], "v": [[-7.068, 60.196], [-7.878, 59.809], [-6.933, 58.32], [-6.302, 58.709]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 255"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.86, 0.419], [1.364, -0.56]], "o": [[-0.793, 1.233], [1.294, -1.419]], "v": [[49.58, -55.687], [46.558, -53.535]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 256"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.303, -1.888], [0.211, -0.302], [0.477, -0.275], [0, 0], [0.307, -0.045], [0, 0], [-0.174, -0.17]], "o": [[-0.211, 0.302], [-0.345, -0.337], [0, 0], [0.019, -0.307], [0, 0], [0.077, -0.373], [0.489, 0.479]], "v": [[49.829, 17.307], [49.198, 18.212], [47.849, 18.658], [47.867, 18.664], [47.523, 18.178], [47.533, 18.186], [47.931, 17.15]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 257"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.365, -0.11], [0.361, -0.204], [-0.034, 0.551], [-0.6, 0.311]], "o": [[0.07, 0.675], [-0.697, 0.394], [0.029, -0.469], [0.929, -0.482]], "v": [[-12.729, 54.965], [-13.473, 56.004], [-14.178, 55.397], [-13.512, 54.108]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 258"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.034, -0.53], [0.417, -0.33], [0.022, 0.387], [-0.676, 0.312]], "o": [[0.002, 0.727], [-0.7, 0.554], [-0.03, -0.529], [0.569, -0.262]], "v": [[-17.549, 61.426], [-18.592, 62.779], [-19.203, 62.294], [-18.405, 60.918]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 259"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.012, -0.533], [0.619, -0.33], [-0.041, 0.513], [-0.462, 0.778]], "o": [[-0.186, 0.621], [-0.519, 0.276], [0.101, -1.276], [0.237, -0.4]], "v": [[49.787, 11.862], [48.647, 13.451], [47.637, 13.142], [49.153, 11.85]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 260"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.161, -0.286], [0.32, -0.295], [0.143, -0.025], [-0.156, 0.735], [-0.651, 0.871]], "o": [[-0.134, 0.385], [-0.126, -0.026], [-0.485, 0.085], [0.196, -0.923], [0.475, -0.634]], "v": [[27.286, 28.227], [26.613, 29.251], [26.232, 29.182], [25.079, 28.957], [26.877, 27.415]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 261"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.45, -0.033], [0.437, -0.259], [0.013, 0.537], [-0.474, 0.244]], "o": [[-0.047, 0.537], [-0.616, 0.365], [-0.01, -0.427], [0.614, -0.317]], "v": [[3.668, 53.207], [2.958, 54.435], [2.104, 53.962], [2.602, 52.631]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 262"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.474, -0.054], [0.744, -0.527], [0.196, -0.049], [0.043, 0.41], [-0.306, 0.353]], "o": [[-0.047, 0.684], [-0.179, -0.011], [-0.302, 0.076], [-0.056, -0.525], [0.569, -0.655]], "v": [[4.452, -13.62], [3.435, -11.765], [2.894, -11.786], [2.138, -11.829], [2.922, -12.946]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 263"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.495, -0.864], [-0.099, 0.354], [-0.575, 0.411], [-0.309, -0.224], [0.141, -0.046]], "o": [[-0.419, 0.217], [0.188, -0.668], [0.523, -0.374], [0.138, 0.1], [-0.726, 0.236]], "v": [[6.891, -9.936], [6.437, -10.22], [7.632, -11.87], [8.894, -12.022], [8.765, -11.409]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 264"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.19, -0.397], [0.377, -0.11], [0.024, 0.093], [-0.693, 0.823], [-0.435, -0.056]], "o": [[-0.289, 0.51], [-0.017, -0.099], [-0.106, -0.413], [0.386, -0.458], [0.576, 0.074]], "v": [[39.566, 64.645], [38.537, 65.436], [38.485, 65.14], [38.19, 63.95], [39.882, 63.235]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 265"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.032, -0.304], [0.315, -0.26], [0.637, 0.05], [-0.182, 0.318], [-0.87, 0.007]], "o": [[-0.153, 0.499], [-0.498, 0.411], [-0.243, -0.019], [0.454, -0.793], [0.174, -0.002]], "v": [[10.823, -15.367], [9.891, -14.354], [8.53, -13.016], [8.612, -13.788], [10.352, -15.477]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 266"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.471, -0.625], [-0.085, 1.15], [-0.505, 0.132]], "o": [[-0.904, 0.188], [0.035, -0.475], [0.898, -0.234]], "v": [[-19.819, 67.685], [-21.487, 67.408], [-20.815, 66.114]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 267"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.938, 0.203], [1.195, 0.578]], "o": [[-0.606, 1.212], [0.448, -1.69]], "v": [[2.087, -9.643], [-0.265, -7.634]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 268"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.484, -0.834], [0.131, 0.171], [-0.645, 0.496], [-0.178, -0.313], [0, 0]], "o": [[-0.26, 0.136], [-0.131, -1.327], [0.953, -0.733], [0, 0], [-0.588, 0.386]], "v": [[-10.608, 13.932], [-11.221, 13.944], [-9.507, 11.813], [-8.876, 12.595], [-8.886, 12.587]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 269"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.048, -0.385], [0.561, -0.433], [-0.105, 0.466], [-0.809, 0.553]], "o": [[0.008, 0.545], [-0.343, 0.265], [0.122, -0.54], [0.501, -0.343]], "v": [[-25.155, 57.621], [-25.919, 59.158], [-26.525, 58.963], [-25.833, 57.273]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 270"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.008, -0.52], [0.508, -0.261], [-0.026, 0.517], [-0.488, 0.252]], "o": [[-0.038, 0.685], [-0.591, 0.303], [0.023, -0.46], [0.576, -0.297]], "v": [[-22.549, 56.224], [-23.462, 57.51], [-24.15, 57], [-23.459, 55.868]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 271"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.017, -0.469], [0.788, -0.614], [-0.013, 0.521], [-0.591, 0.438]], "o": [[-0.942, 0.666], [-0.505, 0.394], [0.015, -0.603], [0.294, -0.218]], "v": [[-24.902, 68.979], [-25.552, 70.798], [-26.435, 70.332], [-25.391, 68.646]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 272"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.588, 0.435], [0.032, -0.592], [0.655, -0.426], [0.038, 0.412]], "o": [[0.524, -0.268], [-0.027, 0.499], [-0.483, 0.314], [-0.041, -0.441]], "v": [[0.199, 54.132], [1.061, 54.4], [0.401, 55.887], [-0.471, 55.594]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 273"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.308, -0.184], [0.087, 0.026], [-0.806, 0.877], [0.248, -0.39], [0.481, -0.312], [0, 0]], "o": [[-0.087, -0.027], [0.545, -1.75], [0.074, -0.081], [-0.398, 0.625], [0, 0], [-0.167, -0.214]], "v": [[44.777, 17.46], [44.517, 17.381], [47.047, 15.385], [46.903, 16.28], [45.503, 17.381], [45.54, 17.361]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 274"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.798, 0.457], [1.121, -0.702], [0.013, 0.377]], "o": [[-0.381, 0.758], [-0.397, 0.248], [-0.034, -0.996]], "v": [[-7.736, 47.027], [-8.653, 49.223], [-9.239, 48.932]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 275"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.573, -0.305], [0.021, 0.637], [-0.494, 0.283], [0.002, -0.514]], "o": [[-0.428, 0.089], [-0.02, -0.582], [0.569, -0.327], [-0.001, 0.415]], "v": [[-18.164, 66.644], [-19.147, 66.34], [-18.299, 65.145], [-17.555, 65.543]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 276"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.606, 0.506], [-0.096, -0.406], [0.46, -0.315], [0.01, 0.482]], "o": [[0.629, -0.31], [0.084, 0.355], [-0.559, 0.383], [-0.008, -0.415]], "v": [[-10.998, 52.652], [-10.154, 53.218], [-10.744, 54.397], [-11.561, 54.05]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 277"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.526, 0.01], [0.349, -0.22], [-0.024, 0.62], [-0.553, 0.388]], "o": [[0.26, 0.36], [-0.479, 0.302], [0.02, -0.499], [1.121, -0.786]], "v": [[-17.786, 57.966], [-18.24, 58.8], [-19.157, 58.66], [-18.497, 57.077]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 278"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.352, 0.259], [0.2, -0.618], [0.769, -0.373], [0.16, 0.335]], "o": [[0.432, -0.226], [-0.175, 0.542], [-0.646, 0.313], [-0.221, -0.463]], "v": [[-11.044, 44.718], [-10.333, 44.944], [-11.144, 46.626], [-11.712, 45.713]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 279"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.529, 0.349], [-0.186, -0.418], [0.351, -0.163], [-0.021, 0.613]], "o": [[0.31, 0.276], [0.122, 0.274], [-0.711, 0.33], [0.012, -0.344]], "v": [[-8.918, 43.541], [-7.995, 44.428], [-8.678, 45.299], [-9.505, 44.583]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 280"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.007, -0.397], [0.676, -0.386], [-0.002, 0.471], [-0.563, 0.317]], "o": [[-0.099, 0.414], [-0.527, 0.301], [0.001, -0.464], [0.521, -0.294]], "v": [[-17.864, 53.547], [-18.391, 54.809], [-19.09, 54.305], [-18.394, 53.016]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 281"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.359, 0.343], [-0.006, -1.001], [0.475, -0.215], [-0.089, 0.744]], "o": [[0.358, -0.088], [0.003, 0.511], [-0.525, 0.238], [0.094, -0.792]], "v": [[18.836, -15.439], [19.891, -15.698], [18.94, -14.484], [17.774, -14.647]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 282"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.977, -0.338], [-0.583, 0.325], [0.851, 0.031]], "o": [[0.18, -0.409], [0.499, 0.172], [-0.717, 2.166], [0, 0]], "v": [[60.435, 56.686], [61.222, 55.69], [62.886, 55.124], [60.433, 56.683]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 283"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.079, 0.381], [-1.014, 0.544], [0.057, -0.379], [0.73, -0.392]], "o": [[0.315, -0.558], [0.488, -0.262], [-0.084, 0.56], [-0.509, 0.274]], "v": [[-0.689, 40.147], [0.011, 38.437], [0.361, 39.176], [-0.492, 40.831]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 284"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.54, 0.395], [0.001, -0.451], [0.531, -0.296], [-0.129, 0.579]], "o": [[0.554, -0.132], [-0.001, 0.425], [-0.581, 0.324], [0.101, -0.452]], "v": [[-30.729, 64.143], [-30.181, 64.898], [-30.772, 66.01], [-31.423, 65.509]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 285"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.352, -0.229], [-0.133, 0.714], [-0.608, 0.182], [-0.041, -0.387]], "o": [[-0.331, 0.125], [0.091, -0.49], [0.362, -0.109], [0.055, 0.513]], "v": [[-2.297, 57.226], [-3.069, 57.169], [-2.222, 55.708], [-1.407, 56.348]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 286"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.079, 0.109], [-0.307, 0.193], [-0.425, -0.559], [0.29, -0.351], [0.447, 0.337]], "o": [[0.353, -0.627], [0.404, -0.254], [0.17, 0.224], [-0.494, 0.6], [-0.121, -0.091]], "v": [[-8.807, 9.531], [-7.829, 8.885], [-6.656, 8.076], [-7.09, 9.124], [-8.537, 9.889]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 287"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.923, 0.501], [0.011, -0.089], [0.113, -0.409], [0.569, -0.289]], "o": [[-0.001, 0.083], [-0.113, 0.41], [-0.569, 0.29], [0.085, -1.511]], "v": [[-26.129, 7.29], [-26.147, 7.546], [-26.485, 8.774], [-28.193, 9.642]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 288"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.232, 0.833], [0.292, -1.135], [0.297, -0.029]], "o": [[0.148, 0.677], [-0.094, 0.364], [-0.776, 0.076]], "v": [[-13.955, 50.535], [-12.745, 51.404], [-13.618, 52.077]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 289"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.935, -0.382], [-0.351, -0.584], [0.34, -0.269]], "o": [[1.06, -1.043], [0.183, 0.304], [-0.998, 0.791]], "v": [[-19.743, 50.715], [-17.677, 49.745], [-18.236, 50.95]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 290"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.033, -0.487], [0.579, -0.329], [0.014, 0.452], [-0.643, 0.335]], "o": [[-0.018, 0.39], [-0.445, 0.252], [-0.013, -0.438], [0.454, -0.237]], "v": [[18.294, 44.507], [17.755, 45.638], [17, 45.375], [17.648, 44.095]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 291"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.306, 0.343], [-0.22, -0.322], [0.376, 1.129]], "o": [[1.121, -0.635], [-0.739, 1.17], [0.306, -0.343]], "v": [[55.178, -46.667], [55.833, -45.696], [54.259, -45.637]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 292"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.347, -0.738], [0.117, 0.023], [-0.583, 1.326]], "o": [[-0.117, -0.023], [-0.49, -2.4], [-0.205, 1.442]], "v": [[22.655, 57.085], [22.303, 57.017], [24.2, 54.656]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 293"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.119, -0.328], [0.131, -0.246], [0.145, -0.051], [-0.108, 0.76], [-0.388, 0.339]], "o": [[-0.131, 0.246], [-0.133, -0.012], [-0.459, 0.161], [0.079, -0.556], [0.876, -0.767]], "v": [[29.967, 24.454], [29.573, 25.191], [29.172, 25.165], [28.142, 25.035], [29.273, 23.944]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 294"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.145, -0.493], [0.426, 0.253], [-0.553, 0.219]], "o": [[-0.462, 0.691], [-0.114, -0.906], [0.767, -0.303]], "v": [[-30.142, 10.555], [-31.475, 11.273], [-30.223, 9.46]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 295"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.164, 0.722], [-0.482, -0.487], [0.346, -0.327]], "o": [[0.672, 0.131], [0.21, 0.211], [-0.824, 0.781]], "v": [[19.828, 31.325], [21.799, 30.394], [21.17, 31.59]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 296"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.765, -0.122], [0.098, -0.247], [0.612, 0.476], [-0.037, 0.167]], "o": [[-0.099, 0.248], [-0.768, 0.314], [-0.057, -0.044], [0.809, -0.239]], "v": [[30.04, 50.281], [29.744, 51.023], [27.465, 51.871], [27.565, 51.385]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 297"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.192, -1.943], [0.166, -0.165], [0.27, -0.146], [-0.004, 0.533], [-0.325, 0.157]], "o": [[-0.168, 0.167], [-0.269, 0.172], [-0.367, 0.198], [0.006, -0.683], [0.495, -0.239]], "v": [[24.574, 31.847], [24.073, 32.345], [23.262, 32.86], [22.393, 32.88], [23.229, 32.106]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 298"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.099, -0.468], [0.574, -0.446], [0.041, 0.418], [-0.575, 0.317]], "o": [[-0.363, 0.532], [-0.58, 0.451], [-0.05, -0.507], [0.303, -0.167]], "v": [[-5.38, 45.978], [-5.901, 47.514], [-6.538, 46.95], [-5.816, 45.677]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 299"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.241, 0.349], [-0.063, -0.496], [0.436, -0.277], [0.018, 0.484]], "o": [[0.416, -0.14], [0.065, 0.513], [-0.446, 0.284], [-0.026, -0.709]], "v": [[38.666, 59.823], [39.692, 59.551], [38.784, 60.736], [37.755, 60.787]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 300"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.831, -0.821], [-1.682, -0.497]], "o": [[0.719, -2.287], [-1.094, 0.282]], "v": [[4.507, -6.475], [7.14, -8.451]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 301"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.365, -0.353], [0.782, -0.613]], "o": [[-0.899, 0.706], [0.914, -2.139]], "v": [[4.704, -5.074], [2.306, -3.192]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 302"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.622, -0.446], [0.071, 0.35], [-0.345, 0.225], [0.17, -0.519]], "o": [[-0.509, 0.118], [-0.106, -0.527], [0.402, -0.261], [-0.15, 0.455]], "v": [[-28.324, 72.209], [-28.914, 71.478], [-28.139, 70.486], [-27.683, 70.826]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 303"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.267, -0.626], [0.137, 0.223], [-0.554, 0.337], [0.007, -0.259]], "o": [[-0.324, 0.089], [0.098, -0.994], [0.221, -0.134], [-0.021, 0.748]], "v": [[-6.898, -1.859], [-7.572, -2.089], [-6.205, -3.561], [-5.736, -3.493]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 304"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.121, -0.281], [0.58, -0.382], [-0.085, 0.392], [-0.727, 0.537]], "o": [[-0.063, 0.446], [-0.676, 0.445], [0.105, -0.483], [0.64, -0.473]], "v": [[22.629, 42.026], [22.164, 43.365], [21.883, 42.731], [22.162, 41.334]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 305"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.473, -0.291], [-0.048, 0.466], [-0.393, 0.443], [0.07, -0.47]], "o": [[-0.395, 0.198], [0.073, -0.715], [0.364, -0.41], [-0.082, 0.553]], "v": [[23.62, 27.992], [22.809, 27.879], [23.992, 26.647], [24.561, 26.754]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 306"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.536, 0.21], [-0.384, -0.52], [0.273, -0.26]], "o": [[0.846, -0.836], [0.133, 0.181], [-0.799, 0.763]], "v": [[-1.926, 44.317], [-0.075, 43.561], [-0.425, 44.409]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 307"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.048, -0.328], [0.529, -0.185], [-0.245, 0.379], [-0.468, 0.345]], "o": [[-0.232, 0.703], [-0.337, 0.118], [0.31, -0.479], [0.352, -0.26]], "v": [[0.989, -13.007], [-0.224, -11.742], [-0.717, -12.111], [0.618, -13.347]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 308"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.688, 0.3], [0.53, -0.534], [0.442, 0.084]], "o": [[-0.073, 0.542], [-0.561, 0.565], [0.42, -0.792]], "v": [[11.34, -10.065], [11.104, -8.476], [9.682, -8.422]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 309"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.38, 0.584], [-0.238, 0.135], [0.925, -0.766], [0.039, 0.195]], "o": [[0.238, -0.134], [-0.147, 1.097], [-0.038, -0.196], [0.38, -0.584]], "v": [[53.48, 2.721], [54.195, 2.319], [52.455, 5.057], [52.339, 4.472]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 310"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.583, 0.607], [-0.133, 0.49], [-0.341, 0.152], [-0.103, -0.151]], "o": [[0.371, -0.16], [0.341, -0.152], [0.103, 0.151], [-0.737, 0.435]], "v": [[49.405, 6.739], [50.223, 5.85], [51.246, 5.393], [51.556, 5.845]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 311"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [0.648, 1.147], [-0.453, -1.024]], "o": [[-0.728, 0.522], [0.871, -0.12], [0, 0]], "v": [[-14.541, 13.514], [-16.754, 14.021], [-14.544, 13.518]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 312"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.09, 0.188], [1.087, -0.169]], "o": [[-1.092, 1.086], [1.089, -1.087]], "v": [[-27.344, 55.092], [-30.269, 56.789]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 313"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.24, -0.491], [-0.293, 0.889], [-0.609, -0.083]], "o": [[-0.463, 0.197], [0.314, -0.954], [-0.241, 0.492]], "v": [[-30.843, 73.422], [-31.661, 73.109], [-30.122, 71.947]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 314"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.531, 0.73], [0.686, -0.485]], "o": [[-0.062, -1.672], [-0.247, 1.19], [0, 0]], "v": [[16.975, 36.175], [18.785, 34.327], [16.97, 36.172]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 315"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.293, 0.605], [-0.516, -0.187], [0.155, -0.166]], "o": [[0.587, -0.184], [0.028, 0.01], [-0.601, 0.644]], "v": [[24.113, 51.854], [25.827, 50.98], [25.514, 51.641]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 316"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-1.693, 0.792], [0.599, -0.39]], "o": [[-0.137, -0.8], [-0.242, 1.035], [0, 0]], "v": [[15.998, -10.742], [17.554, -12.335], [15.982, -10.743]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 317"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.773, 0.777], [0.613, -0.082], [-0.044, 0.112]], "o": [[-0.475, 1.232], [-0.154, 0.021], [0.38, -0.957]], "v": [[-0.968, 10.841], [-2.44, 12.687], [-2.757, 12.397]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 318"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.122, -0.283], [0.535, 0.558], [-0.792, 0.766]], "o": [[-0.803, 0.508], [0.943, -0.65], [-0.122, 0.283]], "v": [[62.45, 60.327], [60.469, 60.395], [62.816, 59.478]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 319"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.927, 1.317], [0.16, -0.873]], "o": [[0.171, 0.681], [-0.937, -0.238]], "v": [[51.779, 85.096], [51.782, 87.435]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 320"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.217, 0.223], [-0.271, -0.37], [0.32, 0.224], [-0.007, 0.453]], "o": [[-0.034, 0.606], [-0.345, 0.556], [0.007, -0.453], [0.217, -0.223]], "v": [[-6.902, 50.275], [-6.573, 51.759], [-7.572, 52.303], [-7.552, 50.943]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 321"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.26, -0.903], [0.004, 0.749]], "o": [[-0.004, -0.748], [0.368, 0.536]], "v": [[54.19, 2.218], [54.178, -0.028]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 322"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.083, -0.102], [-0.049, -0.298], [0.38, -0.223], [-0.999, 0.67]], "o": [[0.049, 0.299], [-0.379, 0.222], [0.248, -0.624], [0.084, 0.103]], "v": [[-34.196, 62.998], [-34.05, 63.894], [-35.188, 64.562], [-34.446, 62.692]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 323"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.093, 1.036], [-0.087, -0.015], [-0.042, -0.642], [0.743, -0.569]], "o": [[0.103, -0.094], [0.042, 0.642], [-0.115, 0.398], [0.098, -1.094]], "v": [[53.224, -46.568], [53.509, -46.687], [53.635, -44.76], [52.941, -43.403]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 324"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.448, 0.4], [-0.773, 0.324], [0.379, 0.093]], "o": [[0.774, -0.652], [-0.084, 0.368], [-0.604, -0.148]], "v": [[66.453, 86.207], [68.773, 84.874], [68.359, 85.894]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 325"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.418, 0.544], [-0.025, -0.335], [0.209, 0.335]], "o": [[0.025, 0.335], [-0.312, 0.624], [-0.385, -0.617]], "v": [[-25.495, 64.954], [-25.419, 65.96], [-26.437, 66.63]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 326"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.484, 0.295], [-0.533, -0.024]], "o": [[0.629, -0.506], [-0.755, 1.205]], "v": [[53.481, -53.645], [55.211, -54.296]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 327"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.031, -0.301], [0.287, -0.224], [0.003, 0.291], [-0.239, 0.243]], "o": [[-0.054, 0.34], [-0.225, 0.176], [-0.004, -0.337], [0.227, -0.23]], "v": [[11.23, -13.581], [10.682, -12.73], [10.196, -12.721], [10.765, -13.553]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 328"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.242, 0.199], [1.171, -1.113], [-0.026, 0.516]], "o": [[0.09, 0.29], [-0.151, -0.309], [0.004, -0.074]], "v": [[-12.59, 14.696], [-12.512, 15.692], [-13.283, 15.275]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 329"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.187, -0.281], [0.042, 0.642], [-0.489, 0.16], [-0.275, -0.114]], "o": [[-0.042, -0.642], [0.539, -0.234], [-0.287, 0.283], [-0.187, 0.281]], "v": [[53.635, -44.76], [53.509, -46.687], [54.347, -46.24], [54.195, -45.603]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 330"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.41, -0.641], [-0.978, 0.881]], "o": [[-0.478, -0.264], [0.013, 0.462]], "v": [[7.757, 10.672], [7.966, 9.183]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 331"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.358, 0.27], [0.003, -0.224], [0.315, -0.197], [-0.002, 0.134]], "o": [[0.309, -0.149], [-0.005, 0.312], [-0.062, 0.039], [0.004, -0.253]], "v": [[-10.249, 60.597], [-9.904, 60.864], [-10.382, 61.685], [-10.574, 61.414]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 332"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.285, -0.382], [-0.241, 0.492]], "o": [[0.24, -0.491], [-0.132, 0.503]], "v": [[-30.843, 73.422], [-30.122, 71.947]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 333"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.039, 0.048], [-0.109, 0.149], [0.052, -0.353]], "o": [[0.055, -0.033], [0.116, -0.142], [-0.169, 0.183], [0, 0]], "v": [[62.459, -63.135], [62.62, -63.236], [62.95, -63.681], [62.44, -63.135]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 334"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.409, 0.381], [-0.017, -0.093], [0.337, -0.245]], "o": [[0.026, -0.317], [0.07, -0.066], [0.073, 0.403], [0, 0]], "v": [[-8.9, 7.602], [-8.615, 6.615], [-8.303, 6.684], [-8.899, 7.602]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 335"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.299, -0.262], [0.104, 0.15], [-0.365, 0.307], [-0.038, -0.196]], "o": [[-0.103, -0.151], [0.364, -0.307], [0.039, 0.195], [-0.3, 0.263]], "v": [[51.556, 5.845], [51.246, 5.394], [52.339, 4.472], [52.455, 5.057]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 336"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.241, -0.227], [0, -0.001], [-0.419, 0.204], [0.014, -0.069]], "o": [[0, 0], [0.104, -0.382], [0.061, -0.03], [-0.082, 0.4]], "v": [[30.44, 24.122], [30.298, 24.216], [30.948, 23.249], [31.102, 23.379]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 337"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.218, -0.455], [0.255, -0.176], [-0.348, 0.196]], "o": [[-0.255, 0.176], [-0.082, -0.361], [0.288, -0.162]], "v": [[-10.726, 65.4], [-11.492, 65.928], [-10.99, 65.028]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 338"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.029, 0.166], [-0.153, -0.031], [0.181, 0.139]], "o": [[0.153, 0.031], [-0.157, 0.279], [-0.061, -0.047]], "v": [[-36.577, 76.691], [-36.117, 76.785], [-36.651, 77.17]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 339"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.059, -0.074], [0.131, -0.087], [-0.186, 0.314], [-0.146, 0.121]], "o": [[-0.13, 0.136], [-0.131, 0.087], [0.091, -0.154], [0.02, -0.017]], "v": [[4.607, -3.053], [4.221, -2.662], [4.055, -2.811], [4.471, -3.218]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 340"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.093, 0.013], [0.065, -0.062], [0.027, 0.051], [-0.04, 0.116]], "o": [[-0.04, 0.116], [-0.068, 0.065], [-0.027, -0.05], [0.093, -0.014]], "v": [[-12.688, 7.376], [-12.813, 7.711], [-13.071, 7.749], [-12.967, 7.416]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 341"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.435, -0.088], [0.435, -0.093]], "o": [[-0.435, 0.414], [0.434, -0.593]], "v": [[66.776, 68.697], [65.472, 69.452]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 342"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.03, 0.378], [-0.039, 0.025], [-0.046, -0.218]], "o": [[-0.243, 0.092], [0.003, -0.04], [0.361, -0.237], [0, 0]], "v": [[-11.568, 7.831], [-12.06, 7.696], [-11.956, 7.533], [-11.538, 7.784]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 343"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.287, 0.283], [-0.277, 0.143], [0.306, -0.343]], "o": [[-0.275, -0.114], [0.277, -0.143], [-0.306, 0.343], [0, 0]], "v": [[54.195, -45.603], [54.347, -46.24], [55.178, -46.667], [54.259, -45.637]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 344"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.143, 0.044], [0.126, -0.214], [-0.233, 0.294]], "o": [[-0.126, 0.214], [-0.231, -0.026], [0.079, -0.1]], "v": [[52.15, 77.143], [51.773, 77.786], [51.732, 77.284]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 345"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.248, -0.294], [-0.108, 0.282]], "o": [[0.108, -0.282], [-0.002, 0.273]], "v": [[62.803, 59.498], [63.127, 58.652]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 346"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.21, -0.154], [0.214, -0.12]], "o": [[-0.214, 0.12], [0.218, -0.389]], "v": [[68.769, 67.545], [68.127, 67.906]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 347"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.074, -0.129], [-0.229, 0.085]], "o": [[-0.097, -0.179], [-0.075, 0.129]], "v": [[39.721, -47.312], [39.945, -47.7]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 348"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.114, -0.152], [-0.114, 0.152]], "o": [[0.114, -0.152], [-0.114, 0.152]], "v": [[0.724, 16.982], [1.066, 16.526]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 349"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.109, -0.048], [0, 0], [0.111, 0.045]], "o": [[0.108, 0.048], [0, 0], [-0.111, -0.045], [0, 0]], "v": [[-11.538, 7.784], [-11.212, 7.928], [-11.236, 7.965], [-11.568, 7.831]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 350"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.039, 0.055], [-0.003, -0.109], [0.027, 0.068]], "o": [[0.003, 0.109], [-0.042, -0.055], [-0.006, -0.014]], "v": [[51.773, 92.743], [51.782, 93.071], [51.662, 92.902]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 351"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.247, 2.022], [-0.275, 0.03], [-0.738, 0.736], [-0.835, 1.272], [-1.755, 1.594], [-0.326, -0.038], [0.742, -0.367], [-0.027, -0.322], [-0.163, -0.021], [-0.274, 0.01], [0.148, -0.385], [2.204, -4.832], [0.818, -0.075], [-0.158, 1.39], [0, 0], [-0.135, 0.195], [-1.052, 2.379], [1.282, -1.497], [0.919, -1.156], [0.46, 0.507]], "o": [[0.327, -0.173], [0.858, -0.094], [0.984, -0.983], [1.242, -1.891], [0.326, 0.038], [-0.233, 0.708], [-0.275, 0.136], [0.013, 0.16], [0.252, 0.033], [-0.147, 0.385], [-3.345, 4.53], [-0.313, 0.687], [-0.841, 0.077], [0, 0], [0.135, -0.195], [1.62, -2.335], [-1.307, 1.54], [-0.06, -0.505], [-0.72, 0.906], [1.036, -2.096]], "v": [[-22.163, 27.22], [-21.188, 26.724], [-18.6, 26.181], [-16.099, 22.417], [-12.224, 16.828], [-11.245, 16.943], [-12.515, 18.667], [-13.067, 19.392], [-12.684, 19.64], [-11.868, 19.621], [-12.31, 20.775], [-20.293, 34.9], [-21.74, 36.738], [-23.471, 35.617], [-23.504, 35.642], [-23.098, 35.056], [-19.808, 27.967], [-23.165, 32.786], [-24.053, 32.652], [-25.846, 33.307]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 352"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-1.307, 1.54], [1.621, -2.335], [0.135, -0.195], [0, 0], [0.111, 1.174], [-0.328, 0.911], [0, 0], [-0.116, 0.183]], "o": [[1.282, -1.497], [-1.052, 2.379], [-0.135, 0.196], [0, 0], [-0.464, 0.135], [0.116, -0.94], [0, 0], [0.116, -0.183], [0, 0]], "v": [[-23.165, 32.786], [-19.808, 27.967], [-23.099, 35.056], [-23.504, 35.642], [-23.471, 35.617], [-24.85, 35.972], [-23.487, 33.295], [-23.505, 33.312], [-23.158, 32.764]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 353"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.323, 1.227], [0.116, -0.94], [0.513, -0.578], [0, 0], [0, 0.332], [-0.025, 0.061], [0.026, -0.061], [0.373, 0.478], [-0.24, 0.456]], "o": [[-0.328, 0.911], [-0.512, 0.578], [-0.001, 0], [0, -0.332], [0.025, -0.061], [-0.026, 0.06], [-0.892, 1.098], [0.24, -0.456], [1.323, -1.167]], "v": [[-23.487, 33.295], [-24.85, 35.972], [-26.386, 37.706], [-26.491, 37.679], [-26.492, 36.682], [-26.416, 36.499], [-26.494, 36.681], [-28.18, 36.969], [-27.461, 35.601]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 354"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.397, 1.096], [1.233, -0.758], [-0.282, 1.542], [0.31, 0.238]], "o": [[0.32, 1.056], [-1.218, 0.749], [0.103, -0.562], [1.348, -0.935]], "v": [[-27.774, 46.005], [-29.209, 48.563], [-30.822, 47.89], [-31.164, 46.693]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 355"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.954, 0.122], [-0.856, -0.143], [-0.109, -0.293], [0.008, -0.347]], "o": [[0.849, -0.276], [0.109, 0.294], [-0.008, 0.346], [-1.11, 0.185]], "v": [[-32.647, 44.22], [-30.113, 42.714], [-29.785, 43.593], [-29.809, 44.632]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 356"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.581, -0.248], [0.018, 0.086], [-0.47, 0.314], [-0.001, 0]], "o": [[-0.001, -0.098], [0.358, -0.693], [0, 0], [-0.238, 0.945]], "v": [[-27.818, 39.081], [-27.847, 38.806], [-26.491, 37.679], [-26.386, 37.706]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 357"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.628, -0.456], [0.252, 0.033], [0.013, 0.16], [-0.275, 0.136], [-0.233, 0.708], [0.572, -1.176], [0.128, -0.123], [-0.119, 0.018]], "o": [[-0.274, 0.01], [-0.163, -0.021], [-0.027, -0.322], [0.742, -0.367], [0.249, 0.16], [-0.128, 0.123], [0.119, -0.018], [-0.212, 0.71]], "v": [[-11.868, 19.621], [-12.684, 19.64], [-13.067, 19.392], [-12.515, 18.667], [-11.245, 16.943], [-10.6, 17.547], [-10.983, 17.915], [-10.626, 17.86]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 358"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.704, 0.114], [-0.588, 0.386]], "o": [[0.484, -0.834], [-0.434, 1.053]], "v": [[-10.608, 13.932], [-8.886, 12.587]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 359"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.518, 0.231], [0.332, -0.318]], "o": [[-0.11, 0.422], [0.047, -0.451]], "v": [[0.407, 4.556], [-0.302, 5.644]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 360"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.036, -0.118], [0.119, -0.018], [-0.128, 0.123]], "o": [[-0.119, 0.018], [0.128, -0.123], [0.018, 0.091]], "v": [[-10.626, 17.86], [-10.983, 17.915], [-10.6, 17.547]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 361"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.026, 0.061], [0.024, -0.062]], "o": [[0.026, -0.061], [-0.024, 0.062], [0, 0]], "v": [[14.668, -4.68], [14.745, -4.863], [14.673, -4.677]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 362"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.079, 0.221], [-0.065, -0.004], [0.145, -0.217]], "o": [[0.078, -0.221], [0.065, 0.004], [-0.145, 0.217], [0, 0]], "v": [[-2.259, 9.081], [-2.023, 8.417], [-1.827, 8.429], [-2.261, 9.08]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 363"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [1.323, -1.167], [-0.812, 0.598], [-0.72, 0.906], [-0.06, -0.505], [0, 0], [0.116, -0.182]], "o": [[-1.323, 1.227], [0.372, -0.866], [0.46, 0.507], [0.919, -1.156], [0, 0], [-0.116, 0.183], [0, 0]], "v": [[-23.487, 33.295], [-27.461, 35.601], [-25.845, 33.307], [-24.053, 32.652], [-23.165, 32.786], [-23.158, 32.765], [-23.505, 33.312]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 364"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, -0.332], [0.358, -0.693], [-2.165, 2.3], [-0.892, 1.098], [0, 0]], "o": [[-0.47, 0.314], [-0.456, -0.356], [0.373, 0.478], [0, 0], [0, 0.332]], "v": [[-26.491, 37.679], [-27.847, 38.806], [-28.18, 36.969], [-26.494, 36.681], [-26.492, 36.682]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 365"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.002, 0.11], [0.217, -0.242]], "o": [[0.268, -0.049], [-0.003, -0.11]], "v": [[19.292, 37.147], [19.299, 37.478]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 366"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.313, 0.482], [-0.345, -0.337]], "o": [[0.477, -0.275], [-0.595, 0.823]], "v": [[47.849, 18.658], [49.198, 18.212]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 367"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.071, 0.362], [0.019, -0.307]], "o": [[0.307, -0.045], [-0.278, 0.014]], "v": [[47.523, 18.178], [47.867, 18.664]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 368"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.189, 0.151], [-0.167, -0.214]], "o": [[0.308, -0.184], [-0.344, 0.287]], "v": [[44.777, 17.46], [45.54, 17.361]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 369"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.045, 0.655], [-0.756, 0.374], [0.01, -0.879], [0.733, -0.498]], "o": [[-0.094, -0.59], [0.657, -0.325], [-0.009, 0.789], [-0.935, 0.635]], "v": [[-2.847, 32.212], [-2.035, 30.668], [-0.644, 30.912], [-1.966, 32.835]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 370"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.003, 0.574], [-0.826, 0.356], [0.081, -0.883], [0.706, -0.448]], "o": [[-0.055, -0.696], [0.637, -0.275], [-0.081, 0.877], [-0.834, 0.529]], "v": [[63.308, -5.924], [64.338, -7.542], [65.489, -7.146], [64.096, -5.245]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 371"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.714, -0.396], [-0.113, 0.41], [-0.119, 0.051]], "o": [[0.113, -0.409], [0.119, -0.052], [-0.023, 0.488]], "v": [[-26.485, 8.774], [-26.147, 7.546], [-25.79, 7.393]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 372"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.57, -0.233], [-0.493, 0.341]], "o": [[0.129, -0.856], [0.32, 1.491]], "v": [[62.793, -52.104], [64.042, -53.453]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 373"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.066, -0.541], [0.495, -0.327], [0.001, 0.502], [-0.643, 0.337]], "o": [[-0.035, 0.539], [-0.448, 0.296], [-0.001, -0.457], [0.453, -0.237]], "v": [[-21.871, 72.766], [-22.719, 74.057], [-23.464, 73.857], [-22.813, 72.59]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 374"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.38, -0.584], [0.364, -0.307], [0.341, -0.152], [-0.085, 1.197], [-1.241, 0.858], [-0.351, -1.052], [0.315, -0.991]], "o": [[-0.365, 0.307], [-0.34, 0.152], [-0.017, -1.146], [0.043, -0.599], [1.223, -0.845], [0.214, 0.641], [-0.38, 0.583]], "v": [[52.339, 4.472], [51.246, 5.394], [50.223, 5.85], [50.202, 2.397], [50.856, 0.346], [53.442, 0.326], [53.48, 2.722]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 375"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.457, -0.375], [0.109, 0.294], [-0.105, 0.042], [-0.052, -0.629]], "o": [[-0.109, -0.293], [0.108, -0.058], [0.44, -0.176], [0.068, 0.832]], "v": [[-29.785, 43.593], [-30.113, 42.714], [-29.79, 42.543], [-28.582, 42.295]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 376"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [0.241, -0.153], [-0.132, 1.567], [-1.277, 0.673], [0.222, -1.216], [-0.421, 0.08]], "o": [[-0.131, -0.349], [-0.746, 0.473], [0.055, -0.653], [1.314, -0.692], [-0.047, 0.259], [0, 0]], "v": [[-12.207, 39.077], [-12.855, 39.299], [-14.51, 39.318], [-13.609, 37.336], [-12.294, 38.462], [-12.198, 39.065]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 377"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.51, -0.318], [0.015, 0.384], [-0.437, 0.232], [0.053, -0.511]], "o": [[-0.536, 0.29], [-0.021, -0.539], [0.35, -0.186], [-0.056, 0.545]], "v": [[48.123, -41.586], [47.56, -42.053], [48.343, -43.312], [48.928, -42.852]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 378"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.089, 0.054], [-0.137, 0.079], [0.048, -0.133]], "o": [[0.137, -0.079], [-0.048, 0.133], [-0.089, -0.054]], "v": [[25.133, -41.137], [25.543, -41.373], [25.399, -40.975]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 379"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.084, -0.102], [0.084, 0.103]], "o": [[-0.083, -0.102], [0.083, 0.102]], "v": [[-34.196, 62.998], [-34.446, 62.692]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 380"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.135, -0.195], [-0.135, 0.196]], "o": [[0.135, -0.195], [-0.134, 0.195]], "v": [[-23.504, 35.642], [-23.099, 35.056]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 381"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.116, -0.183], [-0.116, 0.183]], "o": [[0.116, -0.182], [-0.116, 0.183]], "v": [[-23.505, 33.312], [-23.158, 32.765]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 382"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0, 0], [-0.026, 0.06], [0.025, -0.061]], "o": [[0.026, -0.061], [-0.025, 0.061], [0, 0]], "v": [[-26.494, 36.681], [-26.416, 36.499], [-26.492, 36.682]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 383"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.228, -0.158], [0, 24.123], [-0.111, 0.174], [-39.061, 22.552], [-0.111, -0.047], [0, -54.995], [0.016, -0.215], [39.061, -22.552], [0.111, 0.047], [0, 30.651], [0.061, 0.332]], "o": [[0, -24.123], [0.111, -0.174], [39.061, -22.552], [0.111, 0.047], [0, 54.995], [-0.206, 0.135], [-39.061, 22.552], [-0.111, -0.047], [0, -30.651], [0.286, -0.163], [-0.039, -0.21]], "v": [[-43.923, 60.895], [-43.923, -11.473], [-43.591, -11.997], [73.591, -79.652], [73.923, -79.512], [73.923, 85.473], [73.591, 85.997], [-43.591, 153.652], [-43.923, 153.512], [-43.923, 61.558], [-43.41, 60.85]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "シェイプレイヤー 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [117, 241, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[71.5, 65.5], [48.375, 74.625], [50.5, 96.125], [72.25, 84.125]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.218419452742, 0.665517769608, 0.076247039496, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "シェイプレイヤー 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [117, 241, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[67, -10.75], [-3.375, 29.5], [-39.875, 50.5], [-38.125, 87.125], [10.75, 60.375], [9.625, 57.125], [5.75, 47.5], [3, 36.375], [68.25, -0.5], [70, -3.75]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.320726102941, 0.320726102941, 0.320726102941, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "シェイプレイヤー 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [117, 241, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70, -66.5], [6.25, -36.5], [5.25, -27.75], [33.25, -34.5], [70.25, -51.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.320726102941, 0.320726102941, 0.320726102941, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "シェイプレイヤー 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [117, 241, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[54.5, -38], [-38, 6.75], [-34.25, 43.75], [36, 78.5], [68.75, 63.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.795695465686, 0.795695465686, 0.795695465686, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "シェイプレイヤー 6", "sr": 1, "ks": {"o": {"a": 0, "k": 80, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [211.5, 354.375, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, 0], [0, 0], [0, 3.826]], "v": [[124.708, 67.981], [-124.708, 211.981], [-129.904, 208.052], [-129.904, -63.638], [129.904, -213.638], [129.904, 58.052]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.974984681373, 0.974984681373, 0.974984681373, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-80.506, -70.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "シェイプレイヤー 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.072, 313.312, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-129.904, 61.65], [-129.904, 88.35], [129.904, -61.65], [129.904, -88.35]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.078, -180.336], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "シェイプレイヤー 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [29.5, 195.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607858097, 0.776470648074, 0.270588235294, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.478, -48.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "シェイプレイヤー 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.5, 156.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.964705942191, 0.764705942191, 0.188235309077, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "シェイプレイヤー 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20, 164.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.372549019608, 0.235294132607, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "シェイプレイヤー 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144, 311, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0], [2.87, -1.657], [0, 0], [0, 3.826]], "v": [[124.708, 101.205], [-124.708, 245.205], [-129.904, 241.277], [-129.904, -91.277], [-124.708, -101.205], [124.708, -245.205], [129.904, -241.277], [129.904, 91.277]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.87450986376, 0.882353001015, 0.898039275525, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.212, -61.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "シェイプレイヤー 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [138, 280, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.244, 2.031], [-0.326, 1.318], [0.598, 0.134], [0.915, -0.528], [-0.084, -1.375], [0.64, -1.253]], "o": [[0.669, -0.711], [-0.77, 0.45], [-0.756, 0.436], [0.68, 0.386], [0.091, 1.484], [1.007, -0.998]], "v": [[3.831, -83.739], [5.729, -86.299], [3.673, -85.141], [1.247, -83.74], [3.313, -83.785], [1.26, -81.207]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 1"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.847, 0.629], [-0.33, -0.038], [-0.388, 0.77], [0.529, 0.158], [0.443, -0.808], [0.436, -0.349], [0.745, -0.465], [1.248, -1.269]], "o": [[0.484, -0.399], [0.587, 0.068], [0.374, -0.742], [-0.554, -0.165], [-0.256, 0.468], [-0.059, -1.296], [-1.467, 0.914], [1.442, -1.071]], "v": [[-40.582, 26.906], [-39.515, 26.774], [-37.979, 25.62], [-38.135, 24.131], [-39.641, 25.191], [-40.579, 26.484], [-42.398, 26.555], [-43.719, 29.235]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 2"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.752, -0.748], [0.36, -0.194], [1.253, -0.862], [0.001, -0.722], [-0.089, -0.34], [0.027, -0.135], [-1.655, 1.405], [0.157, 0.518], [-0.786, 0.173], [-1.135, -0.559], [1.559, 0.899]], "o": [[-0.36, 0.194], [0.157, -1.247], [-1.32, 0.908], [-0.001, 0.392], [-0.027, 0.135], [-0.542, 2.057], [1.095, -0.934], [-0.251, -0.83], [0.23, 0.725], [-0.107, -2.141], [-0.196, 0.648]], "v": [[-34.56, -47.45], [-35.641, -46.866], [-37.153, -47.646], [-37.98, -45.309], [-37.729, -44.278], [-37.811, -43.873], [-36.005, -43.107], [-35.587, -44.989], [-34.623, -46.359], [-33.791, -43.758], [-34.065, -49.315]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 3"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.922, -0.915], [0.268, -0.213], [0.798, -1.465], [-0.43, -0.598], [0.011, -0.48], [-0.029, -0.087], [-0.242, 0.277], [-0.338, 1.939], [-0.17, 0.146], [-0.942, 0.666], [-0.009, 0.638], [-0.131, 0.88]], "o": [[-0.268, 0.213], [-1.026, -0.18], [-0.334, 1.016], [-0.011, 0.48], [-0.082, 0.182], [0.078, 0.234], [0.856, -1.01], [0.17, -0.146], [0.732, -0.254], [0.854, -0.604], [0.011, -0.832], [0.235, -1.578]], "v": [[-27.394, -52.877], [-28.198, -52.239], [-30.934, -50.31], [-31.011, -47.768], [-31.044, -46.328], [-31.272, -45.797], [-30.721, -45.953], [-27.988, -48.682], [-27.479, -49.121], [-26.753, -48.259], [-26.056, -50.251], [-26.021, -52.743]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 4"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.141, 0.671], [0.353, -0.175], [0.402, -0.986], [0.324, 0.375], [0.977, -1.167], [-0.611, -0.535], [-0.157, 0.068], [-0.711, 0.043], [-0.563, -0.627], [-0.661, 0.62]], "o": [[-0.198, -0.436], [-0.449, 0.135], [-0.235, 0.576], [-0.448, -0.519], [-1.048, 1.252], [0.202, -0.381], [0.799, -0.346], [0.359, -0.795], [0.704, 0.784], [1.164, -1.091]], "v": [[-7.348, -78.123], [-8.343, -77.845], [-9.68, -77.08], [-10.238, -76.727], [-12.353, -76.401], [-12.566, -73.808], [-11.975, -74.893], [-11.157, -73.927], [-9.569, -75.368], [-7.503, -76.266]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 5"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.049, -0.084], [0.203, -1.563], [-0.028, 0.752], [-0.571, 0.299], [0.043, -0.509], [-0.321, -0.667], [-1.546, 0.159], [-0.027, 0.125], [0.878, 0.069], [1.094, -1.633]], "o": [[-1.824, 2.453], [0.802, -1.088], [0.018, -0.477], [0.733, -0.384], [-0.058, 0.69], [1.011, -0.983], [0.006, 0], [0.255, -1.176], [-0.946, -0.075], [-0.041, 0.086]], "v": [[-50.936, -38.695], [-51.226, -34.184], [-50.667, -36.666], [-49.996, -37.844], [-49.438, -37.151], [-49.91, -34.868], [-47.27, -37.942], [-47.114, -38.326], [-47.359, -41.129], [-50.801, -38.949]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 6"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.366, -0.498], [0.099, 1.077], [-0.002, -0.722], [-0.473, 0.606], [-1.502, 1.228], [-0.258, 0.713], [0.239, 0.676], [0.666, -0.352], [0.021, -0.655], [0.54, -0.624], [-0.031, 0.199], [1.023, -0.543], [0.039, -0.735]], "o": [[-0.074, -0.809], [-0.803, 1.437], [0.002, 0.556], [1.59, -0.375], [0.771, -0.63], [0.366, -1.01], [-0.203, -0.575], [-1.013, 0.534], [-0.012, 0.389], [-0.089, -0.165], [0.269, -1.721], [-1.225, 0.65], [-0.023, 0.43]], "v": [[-45.468, 109.419], [-45.715, 106.723], [-46.265, 109.521], [-45.446, 109.408], [-40.713, 106.541], [-39.108, 105.853], [-38.8, 102.979], [-40.697, 103.802], [-41.604, 105.629], [-41.989, 106.994], [-42.228, 106.518], [-44.113, 105.863], [-45.054, 108.068]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 7"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[2.576, -1.442], [0.051, -2.178], [-0.392, -0.429], [-0.871, 0.412], [-0.042, 1.937]], "o": [[-2.434, 1.362], [-0.022, 0.928], [0.523, 0.572], [1.956, -0.926], [0.063, -2.896]], "v": [[-42.515, -44.272], [-44.994, -40.735], [-45.026, -38.024], [-42.461, -39.364], [-40.002, -42.819]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 8"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.054, -0.819], [-0.862, -0.14], [-0.423, 1.695], [0.7, 0.091], [0.763, -0.927], [0.773, -0.131], [0.353, -1.31], [-0.542, 0.511]], "o": [[0.9, -1.272], [0.529, 0.086], [0.191, -0.766], [-0.649, -0.084], [-0.685, 0.833], [-0.78, 0.133], [0.641, -0.24], [0.034, 0.526]], "v": [[-11.473, 91.979], [-9.138, 90.401], [-6.912, 88.567], [-6.953, 86.667], [-9.277, 87.685], [-11.127, 88.839], [-13.241, 91.359], [-11.593, 90.147]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 9"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.304, 1.377], [0.186, 1.167], [0.689, -0.794], [-0.304, -1.299]], "o": [[1.018, -0.292], [-0.087, -0.543], [-0.63, 0.727], [0.093, 0.399]], "v": [[-4.738, -77.997], [-3.253, -80.063], [-4.019, -80.951], [-5.92, -78.37]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 10"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.397, 1.107], [0.905, -0.436], [-0.546, -1.322], [-0.386, 0.449], [-0.612, 0.325], [-0.669, 0.437], [-0.18, -0.714]], "o": [[-1.146, 0.566], [-1.47, 0.708], [0.671, -0.397], [0.404, -0.064], [0.529, -0.524], [0.848, 0.01], [1.102, -1.231]], "v": [[-32.854, 21.317], [-35.813, 22.776], [-36.946, 25.26], [-36.181, 23.829], [-35.568, 24.391], [-34.929, 22.855], [-34.237, 24.623]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 11"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.406, 0.129], [-0.192, 0.171], [0.165, 0.459], [0.493, -0.652], [-0.109, -0.996], [-0.529, -0.076]], "o": [[0.177, -0.158], [-1.247, 0.492], [-0.234, -0.652], [-0.661, 0.874], [0.115, 1.055], [0.304, 0.044]], "v": [[0.312, 85.083], [0.872, 84.583], [0.425, 83.16], [-0.856, 82.951], [-2.518, 85.9], [-0.857, 85.43]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 12"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.184, 1.303], [1.591, -2.225], [-0.212, 0.059], [0.835, -2.107], [-0.231, -0.011], [0.015, 0.044]], "o": [[-1.97, 0.695], [0.338, -0.031], [0.573, -0.159], [-0.137, 0.345], [0.046, 0.002], [-0.466, -1.362]], "v": [[-33.032, -57.174], [-38.07, -54.134], [-37.327, -54.209], [-36.218, -53.726], [-35.595, -53.591], [-35.259, -54.039]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 13"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.659, 0.234], [-0.023, 0.522], [0.847, 0.154], [0.094, -0.526]], "o": [[2.269, -0.942], [0.061, -1.362], [-0.754, -0.137], [-0.15, 0.839]], "v": [[-15.587, -71.435], [-13.944, -74.715], [-15.999, -73.945], [-16.621, -72.263]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 14"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.216, -0.721], [-0.382, 1.073], [0.776, -0.358], [0.354, -0.748], [-0.47, -0.01]], "o": [[1.186, -0.809], [0.227, -0.637], [-0.802, 0.37], [-0.48, 1.016], [0.021, 0.592]], "v": [[15.962, -89.456], [17.998, -92.417], [17.802, -93.752], [15.386, -91.682], [16.582, -91.396]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 15"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.652, 0.434], [0.816, -0.587], [-0.366, 0.192], [-0.261, -0.269]], "o": [[-0.856, -0.682], [-0.534, 0.384], [1.28, -0.671], [1.047, -0.775]], "v": [[11.182, 77.695], [8.687, 78.095], [8.17, 79.274], [8.898, 80.353]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 16"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.814, -0.329], [0.066, -1.171], [-0.593, 0.403], [-0.03, 1.22]], "o": [[-0.311, 0.102], [-0.05, 0.888], [0.66, -0.448], [0.032, -1.286]], "v": [[-15.485, 91.357], [-16.446, 92.663], [-15.488, 93.146], [-13.854, 91.138]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 17"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.148, 1.399], [0.588, -0.277], [-0.123, -1.031], [-0.665, 0.598]], "o": [[-0.043, -1.048], [-0.876, 0.413], [0.05, 0.418], [0.784, -0.706]], "v": [[-49.091, 109.536], [-50.506, 109.51], [-51.914, 111.498], [-51.247, 112.04]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 18"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.182, -0.763], [-0.364, 0.103], [-0.353, 0.746], [-0.381, 0.885], [0.545, -0.388], [-0.302, -0.262]], "o": [[0.528, -0.241], [0.498, -0.141], [0.346, -0.731], [0.05, -0.117], [-0.347, 0.247], [0.662, 0.573]], "v": [[-2.615, -79.434], [-1.327, -80.009], [0.086, -80.895], [0.103, -82.762], [-1.304, -82.303], [-1.865, -81.406]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 19"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-1.394, 1.324], [1.368, -1.34]], "o": [[-1.35, 0.321], [1.357, -0.272]], "v": [[-40.552, -52.465], [-44.654, -50.081]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 20"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[1.396, -1.345], [-1.396, 1.304]], "o": [[1.396, -0.253], [-1.396, 0.279]], "v": [[-24.024, -62.01], [-19.836, -64.43]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 21"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.051, 0.662], [0.714, -0.452], [0.003, -0.619], [-0.681, 0.423]], "o": [[-0.076, -0.593], [-0.61, 0.386], [-0.003, 0.716], [0.595, -0.369]], "v": [[-45.905, 35.394], [-47.014, 34.969], [-47.952, 36.576], [-46.869, 37.049]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 22"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.578, -0.386], [0.955, 0.161], [0.378, -0.594], [-0.242, 0.116]], "o": [[1.272, -1.809], [-0.464, -0.079], [-0.224, 0.352], [1.858, -0.895]], "v": [[-30.761, 22.087], [-30.255, 18.75], [-31.517, 19.566], [-31.561, 20.351]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 23"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.308, -0.755], [0.802, 0.889], [0.34, -0.465]], "o": [[0.231, -1.031], [-0.217, -0.24], [-0.901, 1.229]], "v": [[-51.96, 39.852], [-50.726, 36.814], [-51.811, 37.202]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 24"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.021, 1.111], [0.153, -0.03], [0.46, -1.297], [-0.11, 0.001], [-0.166, 0.347]], "o": [[-0.153, 0.03], [-0.484, 1.301], [-0.049, 0.138], [0.318, -0.004], [0.619, -1.291]], "v": [[-50.036, -48.81], [-50.494, -48.721], [-51.932, -44.821], [-51.769, -44.466], [-51.052, -45.17]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 25"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.208, -0.518], [-0.804, 0.508], [0.021, 0.57], [0.686, -0.415]], "o": [[-0.054, 0.593], [0.588, -0.372], [-0.025, -0.662], [-0.594, 0.36]], "v": [[-32.046, 27.346], [-31.218, 28.017], [-30.332, 26.399], [-31.451, 25.979]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 26"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.138, 0.677], [0.678, -0.402], [0.12, -0.634], [-0.562, 0.432]], "o": [[-0.085, -0.296], [-0.918, 0.544], [-0.095, 0.499], [0.59, -0.453]], "v": [[-46.223, 29.74], [-46.852, 29.141], [-47.794, 31.107], [-47.06, 31.35]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 27"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.008, -0.12], [1.139, -0.643], [-0.139, -0.338], [-0.871, 0.536]], "o": [[-0.094, -0.961], [-0.494, 0.279], [0.186, 0.452], [0.658, -0.405]], "v": [[-33.181, 28.588], [-33.81, 27.057], [-34.308, 28.277], [-33.384, 29.198]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 28"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.392, 0.227], [0.018, 0.749], [0.62, -0.297], [0.229, -0.147], [0.02, -0.438], [-0.349, 0.139]], "o": [[0.391, -0.456], [-0.021, -0.902], [-0.23, 0.11], [-0.335, 0.214], [-0.03, 0.652], [0.224, -0.089]], "v": [[-28.605, -60.43], [-27.135, -61.672], [-28.839, -61.144], [-29.531, -60.774], [-30.368, -59.884], [-29.459, -59.945]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 29"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.558, 0.535], [0.134, -0.925], [-0.367, 0.118], [-0.029, 0.38]], "o": [[-0.126, 0.922], [-0.045, 0.308], [0.444, -0.143], [0.072, -0.942]], "v": [[-45, 33.332], [-45.381, 36.099], [-45.18, 36.706], [-44.53, 35.72]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 30"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.276, -0.468], [-1.031, 0.688], [1.232, -1.139]], "o": [[0.997, -0.963], [-0.869, 0.336], [2.258, -1.637]], "v": [[8.857, -85.264], [10.614, -87.995], [7.728, -86.155]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 31"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.08, -0.007], [-0.003, 0.098], [0.182, -0.238], [-0.014, -0.094]], "o": [[0.072, -0.149], [0.006, -0.223], [-0.072, 0.093], [0.008, 0.055]], "v": [[4.736, -82.902], [4.925, -83.307], [4.603, -83.295], [4.517, -82.931]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "マスク 32"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.231, -0.128], [0, 58.719], [-0.118, 0.186], [-39.225, 22.646], [-0.235, 0.134], [0, -58.719], [0.009, -0.232], [39.225, -22.646]], "o": [[0, -58.719], [0.118, -0.186], [39.225, -22.647], [0.004, 0.23], [0, 58.719], [-0.233, 0.138], [-39.225, 22.646], [-0.005, -0.228]], "v": [[-59.192, 122.253], [-59.192, -53.904], [-58.837, -54.463], [58.837, -122.403], [59.192, -122.253], [59.192, 53.904], [58.837, 54.464], [-58.837, 122.403]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078431373, 0.996078431373, 0.996078431373, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "シェイプレイヤー 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [138, 280, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [96, 96, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.231, -0.128], [0, 58.719], [-0.118, 0.186], [-39.225, 22.646], [-0.235, 0.134], [0, -58.719], [0.009, -0.232], [39.225, -22.646]], "o": [[0, -58.719], [0.118, -0.186], [39.225, -22.647], [0.004, 0.23], [0, 58.719], [-0.233, 0.138], [-39.225, 22.646], [-0.005, -0.228]], "v": [[-59.192, 122.253], [-59.192, -53.904], [-58.837, -54.463], [58.837, -122.403], [59.192, -122.253], [59.192, 53.904], [58.837, 54.464], [-58.837, 122.403]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "シェイプレイヤー 6", "sr": 1, "ks": {"o": {"a": 0, "k": 80, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [211.5, 354.375, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, 0], [0, 0], [0, 3.826]], "v": [[124.708, 67.981], [-124.708, 211.981], [-129.904, 208.052], [-129.904, -63.638], [129.904, -213.638], [129.904, 58.052]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.974984681373, 0.974984681373, 0.974984681373, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-80.506, -70.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "シェイプレイヤー 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.072, 313.312, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-129.904, 61.65], [-129.904, 88.35], [129.904, -61.65], [129.904, -88.35]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.078, -180.336], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "シェイプレイヤー 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [29.5, 195.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607858097, 0.776470648074, 0.270588235294, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.478, -48.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "シェイプレイヤー 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.5, 156.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.964705942191, 0.764705942191, 0.188235309077, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "シェイプレイヤー 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20, 164.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.372549019608, 0.235294132607, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "シェイプレイヤー 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144, 311, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0], [2.87, -1.657], [0, 0], [0, 3.826]], "v": [[124.708, 101.205], [-124.708, 245.205], [-129.904, 241.277], [-129.904, -91.277], [-124.708, -101.205], [124.708, -245.205], [129.904, -241.277], [129.904, 91.277]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.87450986376, 0.882353001015, 0.898039275525, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.212, -61.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "シェイプレイヤー 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [69.5, 295.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.687, 0.452], [7.189, 2.854], [-10.876, 18.999], [0, 0], [0, -2.581], [0, 0], [2.028, 0.488], [4.424, 2.792], [5.162, -8.879], [-12.72, -0.399], [0, 0]], "o": [[0, 0], [-3.687, -0.452], [-7.19, -2.854], [10.876, -18.999], [0, 0], [0, 2.581], [0, 0], [-2.028, -0.488], [-4.424, -2.792], [-5.162, 8.879], [12.72, 0.399], [0, 0]], "v": [[24.518, 24.579], [19.909, 25.765], [-7.742, 18.871], [-13.642, -7.218], [14.563, -18.156], [16.591, -9.557], [16.96, -2.58], [13.088, -0.529], [8.48, -10.773], [-10.692, -2.653], [6.452, 12.888], [23.227, 13.157]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.937254961799, 0.937254961799, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "シェイプレイヤー 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [184.5, 196, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-30.97, 17.143], [0, -0.553], [24.702, -14.262]], "o": [[0, 0], [30.97, -17.143], [0, 0.553], [-24.702, 14.262]], "v": [[-41.938, 25.625], [-1.383, -8.482], [41.938, -23.354], [0.092, 9.654]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427451010311, 0.298039215686, 0.180392156863, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-48.189, 53.176], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "シェイプレイヤー 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.687, 0.101], [-7.927, 4.761], [-4.056, 6.397], [-2.396, 12.813], [0, 0], [41.109, -23.734], [3.871, -9.609], [0, 0]], "o": [[0, 0], [3.687, -0.101], [7.927, -4.761], [4.056, -6.397], [2.397, -12.813], [0, 0], [-41.109, 23.734], [-3.871, 9.609], [0, 0]], "v": [[-35.025, 53.866], [-17.697, 65.983], [2.95, 56.09], [32.076, 29.688], [49.404, -17.554], [51.801, -57.097], [2.95, -42.35], [-47.93, 3.616], [-41.478, 44.871]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.937254961799, 0.937254961799, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.799, 22.369], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "シェイプレイヤー 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.7, -8.958], [9.773, -11.975], [-9.911, -10.383], [-36.202, 21.865], [-6.057, 26.071], [11.15, -3.409], [14.178, -8.048]], "o": [[0, 0], [-9.773, 11.974], [9.911, 10.383], [36.202, -21.865], [6.057, -26.072], [-11.15, 3.409], [-14.178, 8.048]], "v": [[-48.384, -2.132], [-75.914, 23.948], [-89.679, 63.83], [1.445, 38.337], [93.533, -48.142], [45.493, -57.709], [-0.482, -35.57]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.870588295133, 0.866666726505, 0.866666726505, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.434, 57.152], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "シェイプレイヤー 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131, 249, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-32.445, 17.257], [-10.876, 9.598], [0, 0]], "o": [[0, 0], [32.445, -17.257], [10.876, -9.598], [0, 0]], "v": [[-69.774, 42.887], [-16.499, 26.876], [38.62, -11.399], [69.774, -44.133]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.545098039216, 0.545098039216, 0.545098039216, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [5.898, 82.471], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "シェイプレイヤー 6", "sr": 1, "ks": {"o": {"a": 0, "k": 80, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [211.5, 354.375, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, 0], [0, 0], [0, 3.826]], "v": [[124.708, 67.981], [-124.708, 211.981], [-129.904, 208.052], [-129.904, -63.638], [129.904, -213.638], [129.904, 58.052]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.974984681373, 0.974984681373, 0.974984681373, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-80.506, -70.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "シェイプレイヤー 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.072, 313.312, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-129.904, 61.65], [-129.904, 88.35], [129.904, -61.65], [129.904, -88.35]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.078, -180.336], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "シェイプレイヤー 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [29.5, 195.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607858097, 0.776470648074, 0.270588235294, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.478, -48.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "シェイプレイヤー 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.5, 156.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.964705942191, 0.764705942191, 0.188235309077, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "シェイプレイヤー 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20, 164.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.372549019608, 0.235294132607, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "シェイプレイヤー 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144, 311, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0], [2.87, -1.657], [0, 0], [0, 3.826]], "v": [[124.708, 101.205], [-124.708, 245.205], [-129.904, 241.277], [-129.904, -91.277], [-124.708, -101.205], [124.708, -245.205], [129.904, -241.277], [129.904, 91.277]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.87450986376, 0.882353001015, 0.898039275525, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.212, -61.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "シェイプレイヤー 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187, 305, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.986, -2.879], [0, 0], [0, 4.986], [0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0]], "o": [[0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0], [4.986, -2.879], [0, 0], [0, 4.986]], "v": [[35.971, 24.232], [-35.971, 65.768], [-45, 61.952], [-45, -9.99], [-35.971, -24.232], [35.971, -65.768], [45, -61.952], [45, 9.991]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.962867647059, 0.690320602118, 0.690320602118, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "シェイプレイヤー 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [72, 372.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.986, -2.879], [0, 0], [0, 4.986], [0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0]], "o": [[0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0], [4.986, -2.879], [0, 0], [0, 4.986]], "v": [[35.971, 24.232], [-35.971, 65.768], [-45, 61.952], [-45, -9.99], [-35.971, -24.232], [35.971, -65.768], [45, -61.952], [45, 9.991]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.635079597024, 0.925540101294, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "シェイプレイヤー 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187, 186, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.986, -2.879], [0, 0], [0, 4.986], [0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0]], "o": [[0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0], [4.986, -2.879], [0, 0], [0, 4.986]], "v": [[35.971, 24.232], [-35.971, 65.768], [-45, 61.952], [-45, -9.99], [-35.971, -24.232], [35.971, -65.768], [45, -61.952], [45, 9.991]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.61946997549, 0.519674921971, 0.415722446816, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "シェイプレイヤー 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [72, 251.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.986, -2.879], [0, 0], [0, 4.986], [0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0]], "o": [[0, 0], [-4.986, 2.879], [0, 0], [0, -4.986], [0, 0], [4.986, -2.879], [0, 0], [0, 4.986]], "v": [[35.971, 24.232], [-35.971, 65.768], [-45, 61.952], [-45, -9.99], [-35.971, -24.232], [35.971, -65.768], [45, -61.952], [45, 9.991]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.821262853286, 0.635079597024, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "シェイプレイヤー 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [211.5, 354.375, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, 0], [0, 0], [0, 3.826]], "v": [[124.708, 67.981], [-124.708, 211.981], [-129.904, 208.052], [-129.904, -63.638], [129.904, -213.638], [129.904, 58.052]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.974984681373, 0.974984681373, 0.974984681373, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-80.506, -70.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "シェイプレイヤー 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.072, 313.312, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-129.904, 61.65], [-129.904, 88.35], [129.904, -61.65], [129.904, -88.35]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.078, -180.336], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "シェイプレイヤー 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [29.5, 195.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607858097, 0.776470648074, 0.270588235294, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.478, -48.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "シェイプレイヤー 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.5, 156.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.964705942191, 0.764705942191, 0.188235309077, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "シェイプレイヤー 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20, 164.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.109, -1.795], [0, 0], [0, 4.145], [0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0]], "o": [[0, 0], [-3.109, 1.795], [0, 0], [0, -4.145], [0, 0], [3.109, -1.795], [0, 0], [0, 4.145]], "v": [[0, 7.506], [0, 7.506], [-5.629, 3.25], [-5.629, 3.25], [0, -7.506], [0, -7.506], [5.629, -3.25], [5.629, -3.25]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254961799, 0.372549019608, 0.235294132607, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "シェイプレイヤー 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144, 311, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.87, -1.657], [0, 0], [0, 3.826], [0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0]], "o": [[0, 0], [-2.87, 1.657], [0, 0], [0, -3.826], [0, 0], [2.87, -1.657], [0, 0], [0, 3.826]], "v": [[124.708, 101.205], [-124.708, 245.205], [-129.904, 241.277], [-129.904, -91.277], [-124.708, -101.205], [124.708, -245.205], [129.904, -241.277], [129.904, 91.277]], "c": true}, "ix": 2}, "nm": "パス 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.87450986376, 0.882353001015, 0.898039275525, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "塗り 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-13.212, -61.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "トランスフォーム"}], "nm": "シェイプ 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "window 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [485.03, 361.8, 0], "ix": 2}, "a": {"a": 0, "k": [131, 249, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [100, 100, 100]}, {"t": 77, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "w": 262, "h": 498, "ip": 23, "op": 109, "st": 23, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "window 5", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [416.615, 361.8, 0], "ix": 2}, "a": {"a": 0, "k": [131, 249, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 17, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 75, "s": [100, 100, 100]}, {"t": 82, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "w": 262, "h": 498, "ip": 17, "op": 103, "st": 17, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "window 4", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [348.201, 361.8, 0], "ix": 2}, "a": {"a": 0, "k": [131, 249, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 11, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 79, "s": [100, 100, 100]}, {"t": 86, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "w": 262, "h": 498, "ip": 11, "op": 97, "st": 11, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "window 3", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [279.787, 361.8, 0], "ix": 2}, "a": {"a": 0, "k": [131, 249, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 5, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 83, "s": [100, 100, 100]}, {"t": 90, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "w": 262, "h": 498, "ip": 5, "op": 91, "st": 5, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "window", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [211.373, 361.8, 0], "ix": 2}, "a": {"a": 0, "k": [131, 249, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 262, "h": 498, "ip": 0, "op": 240, "st": 0, "bm": 0}], "markers": [{"tm": 42, "cm": "1", "dr": 0}]}