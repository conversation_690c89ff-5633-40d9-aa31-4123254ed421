import { EventEmitter, Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SystemCheckService {
  videoStream: any;
  audioStream: any;
  errorDetails: any;
  reportDetails: any;
  errorStatus: Subject<number> = new Subject();
  cameraUsed = ``;
  microphoneUsed = ``;
  chromeCamera = ``;
  chromeMicrophone = ``;
  chromeCookies = ``;
  firefoxCamera = ``;
  firefoxMicrophone = ``;
  firefoxCookies = ``;

  constructor() {
    this.mapValues();
  }

  extractVersion(uastring, expr, pos) {
    var match = uastring.match(expr);
    return match && match.length >= pos && parseInt(match[pos], 10);
  }

  mapValues() {
    this.cameraUsed = `
    <p>To fix it please follow the instructions</p>
    <p>1. Please find the application which is currently using camera.</p>
    <p>2. Close that particular application.</p>
    <p>3. Reload the browser page using the reload icon. <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    `;

    this.microphoneUsed = `
    <p>To fix it please follow the instructions</p>
    <p>1. Please find the application which is currently using microphone.</p>
    <p>2. Close that particular application.</p>
    <p>3. Reload the browser page using the reload icon. <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    `;

    this.chromeCamera = `
    <p>To fix it please follow the instructions</p>
    <p>1. Locate the padlock icon on your browser address bar (see image above).</p>
    <p>2. Click on the padlock to open up camera options.</p>
    <p>3. Click on the camera drop-down menu and select <strong>Allow</strong>.</p>
    <p>4. Reload the browser page using the reload icon <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    <p>Do you need manuals? Please <span><a class="primary" href="https://docs.examroom.ai/usermanual/chrome/#how-to-enable-if-the-camera-access-is-disabled-by-the-browser-" target="_blank">click here</a></span></p>
    `;

    this.chromeMicrophone = `
    <p>To fix it please follow the instructions</p>
    <p>1. Locate the padlock icon on your browser address bar (see image above).</p>
    <p>2. Click on the padlock to open up microphone options.</p>
    <p>3. Click on the microphone drop-down menu and select <strong>Allow</strong>.</p>
    <p>4. Reload the browser page using the reload icon <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    <p>Do you need manuals? Please <span><a class="primary" href="https://docs.examroom.ai/usermanual/chrome/#how-to-enable-if-the-microphone-access-is-disabled-by-the-browser-" target="_blank">click here</a></span></p>
    `;

    this.chromeCookies = `
    <p>To fix it please follow the instructions</p>
    <p>1. Locate the padlock icon on your browser address bar (see image above).</p>
    <p>2. Click on the cookie icon to open up cookies options.</p>
    <p>3. Click on Always allow to set cookies</p>
    <p>4. Reload the browser page using the reload icon <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    <p>Do you need manuals? Please <span><a class="primary" href="https://docs.examroom.ai/usermanual/chrome/#how-to-enable-cookies-" target="_blank">click here</a></span></p>
    `;

    this.firefoxCamera = `
    <p>To fix it please follow the instructions</p>
    <p>1. Locate the padlock icon on your browser address bar (see image above).</p>
    <p>2. Click on the padlock to open up camera options.</p>
    <p>3. Click on the camera drop-down menu and select <strong>Allow</strong>.</p>
    <p>4. Reload the browser page using the reload icon <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    <p>Do you need manuals? Please <span><a class="primary" href="https://docs.examroom.ai/usermanual/firefox/#how-to-enable-if-the-camera-access-is-disabled-by-the-browser-" target="_blank">click here</a></span></p>
    `;

    this.firefoxMicrophone = `
    <p>To fix it please follow the instructions</p>
    <p>1. Locate the padlock icon on your browser address bar (see image above).</p>
    <p>2. Click on the padlock to open up microphone options.</p>
    <p>3. Click on the microphone drop-down menu and select <strong>Allow</strong>.</p>
    <p>4. Reload the browser page using the reload icon <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    <p>Do you need manuals? Please <span><a class="primary" href="https://docs.examroom.ai/usermanual/firefox/#how-to-enable-if-the-microphone-access-is-disabled-by-the-browser-" target="_blank">click here</a></span></p>
    `;

    this.firefoxCookies = `
    <p>To fix it please follow the instructions</p>
    <p>1. Locate on your browser address bar (see image above).</p>
    <p>2. Paste 'about:preferences#privacy' and now you'll be navigated to browsers privacy and security segment..</p>
    <p>3. Click on the 'Standard' radio button.</p>
    <p>4. Reload the browser page using the reload icon <i class="fa fa-repeat ml-2" aria-hidden="true"></i></p>
    <p>Do you need manuals? Please <span><a class="primary" href="https://docs.examroom.ai/usermanual/firefox/#how-to-enable-cookies-" target="_blank">click here</a></span></p>
    `;
  }
}
