.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: 0.1rem 0.5rem;
  font-weight: 600;
}
.title {
  background: var(--background-base2);
  margin-left: -16px;
  margin-right: -16px;
  margin-top: -16px !important;
  .text {
    color: #11263c;
  }
}
.status {
  color: var(--text-color5);
}
.status1 {
  color: var(--text-color1);
}

.node-text {
  display: inline-block;
  width: 16em;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.welc-note {
  color: var(--text-color1);
}

.active {
  color: var(--text-color2);
  border: 1px solid var(--text-color2);
}
.active2 {
  color: var(--text-color2);
}
.submit {
  width: 130px;
}
.active1 {
  color: #ee9400;
}
.content {
  padding-top: 16px;
  padding-bottom: 16px;
  padding-right: 16px;
}
.content > mat-card {
  margin-bottom: 16px;
}
.cradHeight {
  height: 300px;
}

.example-card {
  max-width: 400px;
}
.example-header-image {
  background-image: url('https://material.angular.io/assets/img/examples/shiba1.jpg');
  background-size: cover;
}
mat-card {
  max-width: 400px;
}
.add {
  background-color: var(--text-color2);
  color: #fff;
  width: 146px;
  height: 33px;
  font-size: 12px;
}
.iconSize {
  width: 12px;
  height: 12px;
}
.buttonBackground {
  background-color: var(--background-base3);
  // opacity: 0.1;
  border-radius: 4px;
  // height: 2rem;
}
.button {
  color: var(--text-color2);
  align-items: center;
  height: 2rem;
  font-weight: 700;
}
mat-card-title {
  margin-bottom: 5px;
}

.bg-color {
  background-color: var(--background-base2);
}

.text_size {
  font-size: 0.6rem;
  color: var(--text-color1);
}
.text_size1 {
  // font-size: 10px !important;
  // font-size: 0.6em !important;
  // font-weight: 400;
  font-weight: bolder;
    font-size: 0.85em !important;
  
}

.c {
  color: var(--text-color2);
  align-items: center;
  height: 2rem;
}
.buttonBackground {
  background-color: var(--background-base3);
  border-radius: 4px;
}
.button_font {
  font-size: 12px;
  font-style: bold;
}
.text_color {
  color: #11263c;
}

.text_color2 {
  color: var(--text-color1);
}
.text_color3 {
  color: var(--text-color5);
}
.text_style1 {
  font-style: italic;
}

.btnText {
  background: var(--sidenav-item-background-active);
  border-left-color: var(--sidenav-item-border-color-active);
}
.textColor {
  color: var(--text-color4);
  font-family: Roboto;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  text-align: center;

  align-items: center;
}
.size {
  height: 70px;
  width: 70px;
}
.addBtn {
  color: var(--text-color2);
  height: 33px;
  background-color: var(--background-base3);
}
.content1 {
  color: var(--text-color4);
}
::ng-deep .mat-menu-panel {
  min-height: 0px !important;
  margin-left: 8px;
}
::ng-deep .mat-menu-content {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.menu_color {
  color: var(--sing-out);
}
.icon_size {
  font-size: 16px;
}
.backGround_color {
  background-color: var(--background-base2);
}

::ng-deep .mat-menu-item {
  line-height: 41px !important;
  height: 40px !important;
}
.details_padding{
  padding-left: 1rem;
  padding-right: 0.5rem;
  
}
// .status_padding{
//   padding-left: 0.5rem;
// }
.button-disabled {
  opacity: 0.2;
  pointer-events: none;
}

::ng-deep .mat-button.mat-primary.mat-button-disabled, .mat-button.mat-accent.mat-button-disabled, .mat-button.mat-warn.mat-button-disabled, .mat-button.mat-button-disabled.mat-button-disabled, .mat-icon-button.mat-primary.mat-button-disabled, .mat-icon-button.mat-accent.mat-button-disabled, .mat-icon-button.mat-warn.mat-button-disabled, .mat-icon-button.mat-button-disabled.mat-button-disabled, .mat-stroked-button.mat-primary.mat-button-disabled, .mat-stroked-button.mat-accent.mat-button-disabled, .mat-stroked-button.mat-warn.mat-button-disabled, .mat-stroked-button.mat-button-disabled.mat-button-disabled {
  color: rgba(0, 0, 0, 0.26);
  background-color: #4444 !important;
}
.titleFont {
  font-size: 1.1em;
  // font-weight: bolder;
  font-family: "Roboto", sans-serif;
}
.btn-4{
  line-height: 25px !important;
}

.minimise {
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}