import { ChangeDetectorRef, Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { feildType, FEILD_TYPES, paramTypes, requestParam} from '../form-builder.types';
import { FormBuilderService } from '../form-builder.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { v4 as uuidv4 } from 'uuid';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Editor, Toolbar } from 'ngx-editor';
// commenting for better reach
@Component({
  selector: "exai-ques-grp-popup",
  templateUrl: "./ques-grp-popup.component.html",
  styleUrls: ["./ques-grp-popup.component.scss"],
})
export class QuesGrpPopupComponent implements OnInit, OnDestroy {

  paramTypesKeys = Object.keys(paramTypes).filter(x => !(parseInt(x) >= 0))
  paramTypes = paramTypes;
  feildTypes: Array<feildType> = FEILD_TYPES;
  basicQuestionGrpDetails: FormGroup;
  feildDetails: FormGroup;

  basicValidationsForm: FormArray;

  LayoutFormQuesGrp: FormArray;
  LayoutFormFeild: FormArray;

  optionsDetails: FormArray;

  requestParams:FormArray;

  displayIndex: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  displayIndex$: Observable<number> = this.displayIndex.asObservable();

  isUserEditingFeild: boolean = false;
  editIndex: number = -1;

  quesGrpDetails: any;
  curFeildType: feildType;
  allSelectedFeildType: Array<feildType> = [];
  allFeildDetails: Array<any> = [];

  basicQuestionGrpDetailsValueChangesSubscription: Subscription;
  feildDetailsNameSubs: Subscription;

  identifiers: Array<any> = [];
  constructor(
    private formBuilder: FormBuilder,
    public dialogRef: MatDialogRef<QuesGrpPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public formBuilderService: FormBuilderService,
    private http: HttpClient,
    public cdr :ChangeDetectorRef
  ) {


    this.basicQuestionGrpDetails = this.formBuilder.group({
      // Question group can also have many more properties
      id: [data.id ? data.id : ''],
      name: ["", [Validators.required]],
      label: [""],
      replicable: [""],
      removeLabel: [""],
      addLabel: [""],
      initialCount: [""],
      totalCount: [""],
      AbRelated: [""],
      relatedFeildID: [""],
      showInstructions: [""],
      instructions: [""],
      parentModelID:[""]
    });


    this.basicQuestionGrpDetailsValueChangesSubscription = this.basicQuestionGrpDetails.controls.name.valueChanges.subscribe((value: string) => {
      this.basicQuestionGrpDetails.controls.id.setValue(
        (data.id ? data.id : '') + value?.split(' ').join('_').split('/').join('').split('&').join('').toLowerCase());
    })

    //this.identifiers = ["TrainingInstituteId","CourseCompletionDate"];
    this.http.get(environment.baseUrl + 'formmsvc/api/form/identifiers').subscribe((response: Array<any>) => {
      this.identifiers = [...response,""];
    }, (error: any) => {
      // handle error here
    })
  }

  editor: Editor;
  toolbar: Toolbar = [
    ["bold", "italic"],
    ["underline", "strike"],
    ["code", "blockquote"],
    ["ordered_list", "bullet_list"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["link", "image"],
    ["text_color", "background_color"],
    ["align_left", "align_center", "align_right", "align_justify"]
  ];


  ngOnInit(): void {
    if (this.data.editData) {
      this.parseData(this.data.editData);
    }
    else {
      this.setLayoutFormQuesGrp();
    }
    this.setOptionsForm();
    this.setRequestParamForm();
    this.setValidationForm();
    this.editor = new Editor();
  }


  removeFeild(index: number) {
    this.allFeildDetails.splice(index, 1);
    this.allSelectedFeildType.splice(index, 1);
  }

  backFromFeild() {
    this.isUserEditingFeild ? this.addFeildtoQuestionGroup() : this.allSelectedFeildType.pop();
    this.curFeildType = null;
    this.displayIndex.next(1)
  }

  next() {
    if (this.basicQuestionGrpDetails.valid && this.LayoutFormQuesGrp.valid) {
      this.quesGrpDetails = this.basicQuestionGrpDetails.value;
      this.quesGrpDetails["name"] = this.basicQuestionGrpDetails.controls.name.value;
      this.quesGrpDetails["label"] = this.basicQuestionGrpDetails.controls.label.value;
      this.quesGrpDetails["replicable"] = this.basicQuestionGrpDetails.controls.replicable.value;
      this.quesGrpDetails["initialCount"] = this.basicQuestionGrpDetails.controls.initialCount.value;
      this.quesGrpDetails["totalCount"] = this.basicQuestionGrpDetails.controls.totalCount.value;
      this.quesGrpDetails["parentModelID"] = this.basicQuestionGrpDetails.controls.parentModelID.value;
      this.quesGrpDetails["AbRelated"] = this.basicQuestionGrpDetails.controls.AbRelated.value;
      this.quesGrpDetails["relatedFeildID"] = this.basicQuestionGrpDetails.controls.relatedFeildID.value;
      this.quesGrpDetails["showInstructions"] = this.basicQuestionGrpDetails.controls.showInstructions.value;
      this.quesGrpDetails["instructions"] = this.basicQuestionGrpDetails.controls.instructions.value;
      this.quesGrpDetails["removeLabel"] = this.basicQuestionGrpDetails.controls.removeLabel.value;
      this.quesGrpDetails["addLabel"] = this.basicQuestionGrpDetails.controls.addLabel.value;
      this.quesGrpDetails["styling"] = {};
      this.quesGrpDetails["styling"]["element"] = this.LayoutFormQuesGrp.value[0];
      this.quesGrpDetails["styling"]["grid"] = this.LayoutFormQuesGrp.value[1];
      this.displayIndex.next(1);
    }
  }
  itemSelected(feildType: feildType) {
    this.feildThatCouldBeRelatedTo = this.formBuilderService.getAllFieldIdsOfASection(this.data.sectionIndex);
    this.curFeildType = feildType;
    this.setLayoutFormFeild(this.data.feildCssClasses[this.feildTypes.indexOf(feildType)]);
    this.setFeildDetailsForm();
    this.displayIndex.next(2);
  }
  feildThatCouldBeRelatedTo: Array<any> = [];
  editFeildDetails(index: number) {
    this.feildThatCouldBeRelatedTo = this.formBuilderService.getAllFieldIdsOfASection(this.data.sectionIndex);
    this.curFeildType = this.allSelectedFeildType[index];
    this.setFeildDetailsForm(this.allFeildDetails[index]);
    this.setValidationForm(this.allFeildDetails[index].validations);
    this.setLayoutFormFeild(this.allFeildDetails[index].styling);
    if (this.allFeildDetails[index].options) this.setOptionsForm(this.allFeildDetails[index].options);
    if(this.allFeildDetails[index].requestParams) this.setRequestParamForm(this.allFeildDetails[index].requestParams);
    this.isUserEditingFeild = true;
    this.editIndex = index;
    this.displayIndex.next(2);
    this.removeFeild(index);

  }
  addFeildtoQuestionGroup() {
    this.editIndex != -1 ? (this.allSelectedFeildType.splice(this.editIndex, 0, this.curFeildType), this.allFeildDetails.splice(this.editIndex, 0, this.feildDetails.value))
      : (this.allSelectedFeildType.push(this.curFeildType), this.allFeildDetails.push(this.feildDetails.value));

    let index = this.editIndex == -1 ? this.allFeildDetails.length - 1 : this.editIndex;


    if ([8, 6, 5].includes(this.curFeildType.id))
      this.allFeildDetails[index]["options"] =
        this.optionsDetails.value;

    if (this.allFeildDetails[index].fetchOptionsFromApi || this.allFeildDetails[index].disableBasedOnApiRes)
      this.allFeildDetails[index]['requestParams'] = this.requestParams.value;

    this.allFeildDetails[index]["validations"] =
      this.basicValidationsForm.value;
    this.allFeildDetails[index]["styling"] = {};
    this.allFeildDetails[index]["styling"]["element"] = this.LayoutFormFeild.value[0];
    this.allFeildDetails[index]["styling"]["grid"] = this.LayoutFormFeild.value[1];

    // reseting the form groups
    this.setFeildDetailsForm();
    this.setValidationForm();
    this.setOptionsForm();
    this.setRequestParamForm();

    this.setLayoutFormFeild();
    this.isUserEditingFeild = false;
    this.editIndex = -1;
    this.displayIndex.next(1);
  }
  addOption() {
    this.optionsDetails.push(
      this.formBuilder.group({
        label: ["", Validators.required],
        value: ["", Validators.required],
        defaultValue: ["", Validators.required],
      })
    );
  }
  addParam(){
    this.requestParams.push(
      this.formBuilder.group({
      paramType:[""],
      paramValue:[""],
      paramName:[""],
      elementPropertyToBeExtracted:[""],
      extractedFromGlobal:[""],
      extractedFromElement:[""],
      position:[""]
    }
    ))
  }
  close() {
    if (this.basicQuestionGrpDetails.valid) {
      let temp = {
        quesGrpDetails: this.quesGrpDetails,
        selectedFeildTypes: this.allSelectedFeildType,
        feildDetails: this.allFeildDetails,
      }

      this.dialogRef.close(temp);
    }
  }
  setValidationForm(details: any = null) {
    this.basicValidationsForm = new FormArray([]);
    for (let i = 0; i < this.basicValidations.length; i++) {
      this.basicValidationsForm.push(
        this.formBuilder.group({
          validationSelected: [details ? details[i].validationSelected : "", Validators.required],
          validationName: [this.basicValidations[i], Validators.required],
          inputVal: [details ? details[i].inputVal : "", Validators.required],
          errorMessage: [
            details ? details[i].errorMessage : this.basicValidationsErrorMessages[i],
            Validators.required,
          ],
        })
      );
    }
  }
  setLayoutFormQuesGrp() {
    // Oth position is for the "element", 1st position is for "grid"
    this.LayoutFormQuesGrp = new FormArray([
      this.formBuilder.group(this.data.quesGrpClasses ? this.data.quesGrpClasses : this.LayoutFormGroupControlsEmpty),
      this.formBuilder.group(this.LayoutFormGroupControlsEmpty)
    ]);
  }
  setLayoutFormFeild(feildStyling: any = null) {
    // Oth position is for the "element", 1st position is for "grid"
    this.LayoutFormFeild = new FormArray([
      this.formBuilder.group({
        children: [feildStyling ? feildStyling.element.children : ""],
        container: [feildStyling ? feildStyling.element.container : ""],
        control: [feildStyling ? feildStyling.element.control : ""],
        errors: [feildStyling ? feildStyling.element.errors : ""],
        group: [feildStyling ? feildStyling.element.group : ""],
        hint: [feildStyling ? feildStyling.element.hint : ""],
        host: [feildStyling ? feildStyling.element.host : ""],
        label: [feildStyling ? feildStyling.element.label : ""],
        option: [feildStyling ? feildStyling.element.option : ""]
    }),
      this.formBuilder.group({
        children: [feildStyling ? feildStyling.grid.children : ""],
        container: [feildStyling ? feildStyling.grid.container : ""],
        control: [feildStyling ? feildStyling.grid.control : ""],
        errors: [feildStyling ? feildStyling.grid.errors : ""],
        group: [feildStyling ? feildStyling.grid.group : ""],
        hint: [feildStyling ? feildStyling.grid.hint : ""],
        host: [feildStyling ? feildStyling.grid.host : ""],
        label: [feildStyling ? feildStyling.grid.label : ""],
        option: [feildStyling ? feildStyling.grid.option : ""]
      })]);
  }
  setFeildDetailsForm(feildDetails: any = null) {
    let autoGeneratedIdAndName = uuidv4().split('-').join('');
    this.feildDetails = this.formBuilder.group({
      // can see about which more properties are needed
      id: [feildDetails ? feildDetails.id : autoGeneratedIdAndName],
      name: [feildDetails ? feildDetails.name : autoGeneratedIdAndName],
      placeholder: [feildDetails ? feildDetails.placeholder : "",],
      label: [feildDetails ? feildDetails.label : "", Validators.required],
      updateOn: [feildDetails ? feildDetails.updateOn : "Change", Validators.required],
      required: [feildDetails ? feildDetails.required : ""],
      // for select field
      fetchOptionsFromApi: [feildDetails ? feildDetails.fetchOptionsFromApi : ""],
      apiUrl: [feildDetails ? feildDetails.apiUrl : ""],
      method: [feildDetails ? feildDetails.method : ""],
      filterable: [feildDetails ? feildDetails.filterable : ""],
      prefix: [feildDetails ? feildDetails.prefix : ""],
      suffix: [feildDetails ? feildDetails.suffix : ""],
      //  for slider
      min: [feildDetails ? feildDetails.min : ""],
      max: [feildDetails ? feildDetails.max : ""],
      step: [feildDetails ? feildDetails.step : ""],
      vertical: [feildDetails ? feildDetails.vertical : ""],
      //  for switch/toggle
      offLabel: [feildDetails ? feildDetails.offLabel : ""],
      onLabel: [feildDetails ? feildDetails.onLabel : ""],
      labelPosition: [feildDetails ? feildDetails.labelPosition : ""],
      //for rating
      // max:[""] already there, have to use the same
      // for text area
      cols: [feildDetails ? feildDetails.cols : ""],
      rows: [feildDetails ? feildDetails.rows : ""],
      wrap: [feildDetails ? feildDetails.wrap : ""],
      //for file upload
      url: [feildDetails ? feildDetails.url : ""],
      removeUrl: [feildDetails ? feildDetails.removeUrl : ""],
      autoUpload: [feildDetails ? feildDetails.autoUpload : ""],
      minSize: [feildDetails ? feildDetails.minSize : ""],
      maxSize: [feildDetails ? feildDetails.maxSize : ""],
      showFileList: [feildDetails ? feildDetails.showFileList : ""],
      accept: [feildDetails ? feildDetails.accept : ""],
      multiple: [feildDetails ? feildDetails.multiple : ""],
      //for form upload files
      type:[feildDetails ? feildDetails.subtype : ""],
      // for implementing hidden feilds
      hidden: [feildDetails ? feildDetails.hidden : ""],
      disable:[feildDetails ? feildDetails.disable : ""],
      relatedFeildId: [feildDetails ? feildDetails.relatedFeildId : ""],
      relatedFeildValueAtWhichThisFeildHasToBeShown: [feildDetails ? feildDetails.relatedFeildValueAtWhichThisFeildHasToBeShown : ""],
      orientation: [feildDetails ? feildDetails.orientation : "horizontal"],
      mergeToNextRow: [feildDetails ? feildDetails.mergeToNextRow : ""],
      colspan: [feildDetails ? feildDetails.colspan : "1"],
      identifier: [feildDetails ? feildDetails.identifier : ""],
      //for implementing date
      futureOffset:[feildDetails ? feildDetails.futureOffset : ""],
      pastOffset: [ feildDetails ? feildDetails.pastOffset : ""],
      pastDisabled: [feildDetails ? feildDetails.pastDisabled : ""],
      todateDisabled:[feildDetails?feildDetails.todateDisabled:""],
      futureDisabled: [feildDetails ? feildDetails.futureDisabled:""],
      disableBasedOnApiRes: [feildDetails ? feildDetails.disableBasedOnApiRes:""],
      fetchUrl: [feildDetails ? feildDetails.fetchUrl : ""],
      fetchOptionsBasedOnResponseAnotherField: [feildDetails ? feildDetails.fetchOptionsBasedOnResponseAnotherField : ""],
      fetchOptionsRelatedFeildId: [feildDetails ? feildDetails.fetchOptionsRelatedFeildId : ""],
      prefillValue: [feildDetails ? feildDetails.prefillValue : ""],
      displayCurrentDate:[feildDetails ? feildDetails.displayCurrentDate : ""],
      displayClosedDate:[feildDetails ? feildDetails.displayClosedDate : ""],
      elementPropertyToBeExtractedForPrefill: [feildDetails ? feildDetails.elementPropertyToBeExtractedForPrefill : ""],
    });
    // this.feildDetailsNameSubs = this.feildDetails.controls.name.valueChanges.subscribe((value: string) => {
    //   this.feildDetails.controls.id.setValue(
    //     (this.basicQuestionGrpDetails.value.id ? this.basicQuestionGrpDetails.value.id + '_' : '') +
    //     value?.split(' ').join('_').split('/').join('').split('&').join('').toLowerCase());
    // })
  }
  setOptionsForm(optionsDetails: any = null) {
    if (optionsDetails) {
      this.optionsDetails = new FormArray([]);
      for (let option of optionsDetails)
        this.optionsDetails.push(this.formBuilder.group({
          label: [option.label, Validators.required],
          value: [option.value, Validators.required],
          defaultValue: [option.defaultValue, Validators.required],
        })
        )
    }
    else {
      this.optionsDetails = new FormArray([
        this.formBuilder.group({
          label: ["", Validators.required],
          value: ["", Validators.required],
          defaultValue: ["", Validators.required],
        }),
      ]);
    }
  }

  setRequestParamForm(requestParam: Array<requestParam> = null) {
    if (requestParam) {
      this.requestParams = new FormArray([]);
      for (let param of requestParam)
        this.requestParams.push(this.formBuilder.group({
          paramType:[param.paramType],
          paramValue:[param.paramValue],
          paramName:[param.paramName],
          elementPropertyToBeExtracted:[param.elementPropertyToBeExtracted],
          extractedFromGlobal:[param.extractedFromGlobal],
          extractedFromElement:[param.extractedFromElement],
          position:[param.position]
        })
        )
    }
    else {
      this.requestParams = new FormArray([
        this.formBuilder.group({
          paramType:[""],
          paramValue:[""],
          paramName:[""],
          elementPropertyToBeExtracted:[""],
          extractedFromGlobal:[""],
          extractedFromElement:[""],
          position:[""]
        }),
      ]);
    }
  }
  drop(event: CdkDragDrop<any[]>,) {
    moveItemInArray(this.allSelectedFeildType, event.previousIndex, event.currentIndex);
    moveItemInArray(this.allFeildDetails, event.previousIndex, event.currentIndex);
  }
  // area for constants
  LayoutFormGroupControlsEmpty: any = {
    children: [""],
    container: [""],
    control: [""],
    errors: [""],
    group: [""],
    hint: [""],
    host: [""],
    label: [""],
    option: [""],
  };
  // min,max are only for number type inputs
  // minLength,maxLength are only for string type inputs
  // pattern needs a valid regex as input
  // all of these need input arguments except email

  basicValidations: Array<string> = [
    "min",
    "max",
    "minLength",
    "maxLength",
    "email",
    "pattern",
  ];
  basicValidationsErrorMessages: Array<string> = [
    "Value is less than the allowed minimum value!",
    "Value is greater than the allowed maximum value!",
    "Length is less than the allowed minimum length!",
    "Length is more than the allowed maximum length!",
    "Not a valid email!",
    "Invalid Value!",
  ];

  ngOnDestroy(): void {
    this.basicQuestionGrpDetailsValueChangesSubscription?.unsubscribe();
    this.feildDetailsNameSubs?.unsubscribe();
  }

  parseData(data: any) {
    // setting layout form for ques grp is one concern;
    // this.setLayoutFormQuesGrp(data.quesGrpDetails);

    this.basicQuestionGrpDetails = this.formBuilder.group({
      // Question group can also have many more properties
      id: [data.quesGrpDetails.id ? data.quesGrpDetails.id : ''],
      name: [{ value: data.quesGrpDetails.name ? data.quesGrpDetails.name : '', disabled: true }, [Validators.required]],
      label: [{ value: data.quesGrpDetails.label ? data.quesGrpDetails.label : "", disabled: false }],
      replicable: [{ value: data.quesGrpDetails.replicable ? data.quesGrpDetails.replicable : "", disabled: false }],
      removeLabel: [{ value: data.quesGrpDetails.removeLabel ? data.quesGrpDetails.removeLabel : "", disabled: false }],
      addLabel: [{ value: data.quesGrpDetails.addLabel ? data.quesGrpDetails.addLabel : "", disabled: false }],
      initialCount: [{
        value: data.quesGrpDetails.initialCount != null
          &&
          data.quesGrpDetails.initialCount != undefined
          ? data.quesGrpDetails.initialCount : "", disabled: false
      }],
      totalCount: [{ value: data.quesGrpDetails.totalCount ? data.quesGrpDetails.totalCount : "", disabled: false }],
      parentModelID: [{ value: data.quesGrpDetails.parentModelID ? data.quesGrpDetails.parentModelID : "", disabled: false }],
      AbRelated: [{ value: data.quesGrpDetails.AbRelated ? data.quesGrpDetails.AbRelated : "", disabled: true }],
      relatedFeildID: [{ value: data.quesGrpDetails.relatedFeildID ? data.quesGrpDetails.relatedFeildID : "", disabled: true }],
      showInstructions: [{ value: data.quesGrpDetails.showInstructions ? data.quesGrpDetails.showInstructions : "", disabled: true }],
      instructions: [{ value: data.quesGrpDetails.instructions ? data.quesGrpDetails.instructions : "", disabled: true }],
    });
    // setting layout form for quesGrp
    this.LayoutFormQuesGrp = new FormArray([this.formBuilder.group({
      children: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.children : this.data.quesGrpCssClasses.children[0]],
      container: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.container : this.data.quesGrpCssClasses.container[0]],
      control: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.control : this.data.quesGrpCssClasses.control[0]],
      errors: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.errors : this.data.quesGrpCssClasses.errors[0]],
      group: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.group : this.data.quesGrpCssClasses.group[0]],
      hint: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.hint : this.data.quesGrpCssClasses.hint[0]],
      host: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.host : this.data.quesGrpCssClasses.host[0]],
      label: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.label : this.data.quesGrpCssClasses.label[0]],
      option: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.element.option : this.data.quesGrpCssClasses.option[0]]
    }),
      this.formBuilder.group({
      children: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.children : ""],
      container: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.container : ""],
      control: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.control : ""],
      errors: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.errors : ""],
      group: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.group : ""],
      hint: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.hint : ""],
      host: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.host : ""],
      label: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.label : ""],
      option: [data.quesGrpDetails.styling ? data.quesGrpDetails.styling.grid.option : ""]
    })]);

    this.allFeildDetails = data.feildDetails;
    this.allSelectedFeildType = data.selectedFeildTypes;

  }
}



