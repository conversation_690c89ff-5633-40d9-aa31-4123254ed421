import { createFeatureSelector, createSelector } from "@ngrx/store";
import { ApplicationState } from "./application.state";

export const APPLICATION_STATE_NAME = "FormsWrapperModuleState";

const getApplicationState = createFeatureSelector<ApplicationState>(APPLICATION_STATE_NAME);

export const selectForm = createSelector(
  getApplicationState, (state) => {
    return state.form
});

export const selectUserResponse = createSelector(
  getApplicationState, (state) => {
    return state.userResponse;
  }
)

export const selectCandidateId = createSelector(getApplicationState, (state) => {
  return state.candidateId
})

export const selectPersonEventId = createSelector(getApplicationState, (state) => {
  return state.personEventId
})

export const selectLatestPersonFormId = createSelector(getApplicationState, (state) => {
  return state.latestPersonFormId;
})

export const selectPersonFormLogs = createSelector(getApplicationState, (state) => {
  return state.personFormLogs;
})
export const RenewelResponseCart = createSelector(getApplicationState, (state) => {
  return state.renewelFee;
})

export const renewelCart = createSelector(getApplicationState, (state) => {
  return state.registrySave;
  ;
})