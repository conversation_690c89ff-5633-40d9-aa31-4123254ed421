import { Location } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { FileUploadService } from '../dialogs/services/file-upload.service';
import { DynamicFileUploadModel, DynamicFormControlModel, DynamicFormGroupModel, DynamicFormHook, DynamicFormService } from "@ng-dynamic-forms/core";
import { Subscription } from 'rxjs';
import { HelpService } from '../help.service';
import { Router } from '@angular/router';
import { tap } from 'rxjs/operators';
import { SnackbarService } from 'src/app/core/snackbar.service';

@Component({
  selector: 'exai-support-ticket',
  templateUrl: './support-ticket.component.html',
  styleUrls: ['./support-ticket.component.scss']
})
export class SupportTicketComponent implements OnInit {

  constructor(
    private fb: FormBuilder,
    private fileUploadService: FileUploadService,
    private location: Location,
    private router: Router,
    private helpService: HelpService,
    private dynamicFormService: DynamicFormService,
    private snackbarService:SnackbarService
  ) { }

  @Input() selectedCategoryName
  @Input() name
  @Input() eligibilityRouteName
  @Input() id
  @Input() applicationId
  @Input() status
  @Input() createdDate
  @Input() changedDate
  @Input() getHelp
  @Input() ticketRaised


  submitFormSub: Subscription
  updateTicketSub: Subscription
  setSelectedTicketSub: Subscription

  formGroupUpload 
  formGroupModel

  dataUpload = new DynamicFileUploadModel({
    required: false,
    id:'1',
    name:'2',
    label: 'Upload Supporting Documents',
    accept: ['.pdf', '.doc', '.docx'],
    multiple: true,
    updateOn: DynamicFormHook.Change,
    showFileList:true,
    additional: {
      appearance: 'outline'
    }
  })

  formGroup = this.fb.group({
    subject: [null, [Validators.required, Validators.maxLength(50)]],
    description: [null, Validators.required]
  })
   ngOnInit(): void {
    this.formGroupModel = [this.dataUpload]
    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel)

    this.setSelectedTicketSub = this.helpService.getTicket(this.helpService.selectedTicketId.value).pipe(
      tap(data => {})
    ).subscribe()
  }

  ngOnDestroy() {
    if (this.submitFormSub) {
      this.submitFormSub.unsubscribe()
    }
    this.setSelectedTicketSub.unsubscribe()
  }

  submit() {
    this.submitFormSub = this.fileUploadService.raiseTicket({a: {...this.formGroup.value}, b: this.formGroupUpload.value["1"]? this.formGroupUpload.value["1"] : ''}).subscribe(
      data => {        
        // this.location.back()
        this.router.navigateByUrl('/help')
        this.formGroup.reset()
        this.formGroupUpload.reset()
        this.snackbarService.callSnackbaronSuccess("Ticket Raised Successfully")
      }
    )
  }

  cancel() {
    this.location.back()    
  }

}

