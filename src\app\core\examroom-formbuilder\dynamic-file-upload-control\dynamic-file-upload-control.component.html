<ng-container [formGroup]="group">
    <exai-file-upload-control [formControlName]="model.id" [name]="model.name" [disabled]="model.disabled" [label]="model.label" [showFileList]="model.showFileList" [multiple]="model.multiple" [accept]="model.accept" [autoUpload]="model.autoUpload" [required]="model.required" (blur)="onBlur($event)"
        (change)="onChange($event)" (focus)="onFocus($event)"></exai-file-upload-control>
</ng-container>