import { HttpErrorResponse } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { LanguageService } from 'src/app/core/language.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { loadAllStates, setSelectedState } from '../state/registry.actions';
import { StateModel } from '../state/registry.model';
import { selectorLoadAllStates } from '../state/registry.selectors';

@Component({
  selector: 'exai-registry-state-popup',
  templateUrl: './registry-state-popup.component.html',
  styleUrls: ['./registry-state-popup.component.scss']
})
export class RegistryStatePopupComponent implements OnInit {

  id: any
  states$: Observable<Array<any>>
  allStatesList: StateModel[] = [];
  selectedState: StateModel;

  constructor(private router: Router,
    private dialogRef: MatDialogRef<RegistryStatePopupComponent>, @Inject(MAT_DIALOG_DATA)
    public lngSrvc: LanguageService,
    private store: Store,
    private global: GlobalUserService) {
  }

  ngOnInit(): void {
    this.getStates();
  }

  getStates() {
    this.store.dispatch<Action>(loadAllStates({ clientId: this.global.clientId }))
    this.states$ = this.store.select(selectorLoadAllStates)
    this.states$.subscribe((data: any) => {
      this.allStatesList = data;
    }, (err: HttpErrorResponse) => {
    })
  }
  selectedStateEvent() {
    this.store.dispatch<Action>(setSelectedState({ data: this.selectedState[0] }))
    this.dialogRef.close({
      confirmed: true,
      data: this.selectedState[0]
    });
  }

  close(){
    this.dialogRef.close();
  }

}