import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicFormUploadComponent } from './dynamic-form-upload.component';

describe('DynamicFormUploadComponent', () => {
  let component: DynamicFormUploadComponent;
  let fixture: ComponentFixture<DynamicFormUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DynamicFormUploadComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicFormUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
