import { Injectable } from "@angular/core";
import { Store } from "@ngrx/store";
import { ta } from "date-fns/locale";
import { Observable, of } from "rxjs";
import { catchError, take } from "rxjs/operators";
import { PersonDetails } from "src/app/core/Dto/persondetail";
import { PersonFormLogModel } from "src/app/core/Dto/personform-log.model";
import { PersonFormModel } from "src/app/core/Dto/personform.model";
import { TemplateStatusModel } from "src/app/core/Dto/template-status.model";
import { GlobalUserService } from "src/app/core/global-user.service";
import { HttpAccountService } from "src/app/core/http-services/http.account.service";
import { ProfileState } from "./state/profile-form.model";

@Injectable({
    providedIn: "root",
})
export class ManageProfileRepository {

    constructor(private _accountService: HttpAccountService, private global: GlobalUserService,
        private store: Store<{ MANAGE_PROFILE_STATE: ProfileState }>) {
    }

    public getAccountPersonDetails(): Observable<PersonDetails> {
        return this._accountService.getAccountPersonDetails().pipe(
            take(1),
            catchError(() => {
                return of(new PersonDetails());
        }));
    }

    public editProfile(personDetails: PersonDetails): Observable<TemplateStatusModel>{
        return this._accountService.editProfile(personDetails).pipe(take(1));
    }

    public uploadProfilePic(formData: FormData): Observable<boolean>{
        return this._accountService.uploadProfilePic(formData).pipe(take(1));
    }

    public getCorrectionFormValue(personFormId: number): Observable<PersonFormModel[]>{
        return this._accountService.getCorrectionFormValue(personFormId).pipe(take(1));
    }

    public getCorrectionlogs(personFormId: number): Observable<PersonFormLogModel[]> {
        return this._accountService.getCorrectionlogs(personFormId).pipe(take(1))
    }



}