export interface PurchasedSkill {
  practiceSkillPersonEventId: number;
  skillName: string;
  skillDescription: string;
  duration: number;
  steps: number;
  totalAttempt: number;
  numberAttempt: number;
  validity: number;
  validDateTimeUTC: string;
  price: number;
  eventStatusId: number;
  eventStatusName: string;
  clientEventId: string;
  clientSessionId: string;
  skillTypeId: number;
  skillTypeName: string;
  scheduleDateTimeUTC: string;
  createdOn: string;
  modifiedOn: string;
}
