<div class="px-gutter pt-2" gdColumns="1fr  " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <h5 class="titleFont"><strong>Excused Absence</strong></h5>
        </div>
        <!-- <div class="-mt-1"> -->
        <app-ng-dynamic-breadcrumb class="" [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
            [fontSize]="'0.65rem'">
        </app-ng-dynamic-breadcrumb>
    </div>
    <div class="py-3 " gdGap="16px" exaiContainer *ngIf="!hideGrievanceForm">
        <div class="content touch-auto overflow-auto -mt-5">
            <div fxLayout="row wrap" fxLayoutGap="16px grid">
                <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                    *ngFor="let item of PersonGrievanceForm">
                    <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                        <div class="bg-color px-4 py-3">
                            <div class="flex justify-between pt-2" fxLayout="row">
                                <div class="">
                                    <strong class="text_size1 ">{{ item.examName }}</strong>
                                </div>                               
                            </div>
                            <div fxLayout="row" class="t-xs pt-1 text_color2 t-xs">
                                {{ item.eligiblityRouteName }}
                            </div>
                        </div>
                        <div fxLayout="column">
                            <h4 class="px-4 pt-2 status t-xs details_padding">Current Status</h4>
                            <div class="small-container ml-4 " fxFlexFill class="details_padding">
                                <img src="{{ item.iconUrl }}" class="inline iconSize " />
                                <span class="t-xs ml-2 active2 "> {{ item.status }}</span>
                                <div class="text_style1">
                                    <span class="t-xs status1 -mt-3" *ngIf="item.statusId != 1">
                                        {{ item.submittedDateN }}
                                    </span>
                                </div>

                                <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr"
                                    exaiContainer>
                                    <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                        <!-- <div class="h4 status t-xs">Exam Mode</div> -->
                                        <div  *ngIf="item.mode=='Online' " class="h4 status t-xs">Exam Mode</div>
                                        <div  *ngIf="item.mode=='Test Center'"class="h4 status t-xs">Test Center Name</div>

                                    </div>
                                    <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                        <div class="h4 status t-xs">Exam Date</div>
                                    </div>
                                    <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                        <div class="h4 status t-xs">Exam Time</div>
                                    </div>
                                </div>
                                <div class="pt-1" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr"
                                    exaiContainer>
                                    <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                        <!-- <div class="h4 status1 t-xs">
                                            {{ item.mode }}
                                        </div> -->
                                        <div *ngIf="item.mode== 'Online'" class="h4 status1 t-xs">{{item.mode}}</div>
                                        <div  *ngIf="item.mode=='Test Center'" class="h4 status1 t-xs">{{item.testCenterDetails.testCenterName}}</div>
                                    </div>
                                    <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                        <div class="h4 status1 t-xs">{{ item.examDateN }}</div>
                                    </div>
                                    <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                        <div class="h4 status1 t-xs mb-3">
                                            {{item.examDate |date:'shortTime':'+0000'}} {{item.timeZoneAbbreviation}}
                                        </div>
                                    </div>
                                </div>
                                <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr"
                                        gdColumns.lt-sm="1fr 1fr" exaiContainer *ngIf="item.status == 'Rejected'">
                                        <div gdColumn="1/-1" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                                            <div class="" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
                                                <div class="h4 text_size status text_color3">
                                                    Absence Response
                                                </div>
                                            </div>
                                            <div class="h4 text_size status1 pb-2 text_color2 minimise" matTooltip="{{item.comment}}">
                                                {{ item.comment }}
                                            </div>
                                        </div>
                                </div>





                                <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr"
                                        gdColumns.lt-sm="1fr 1fr" exaiContainer *ngIf="item.statusId == 7">
                                        <div gdColumn="1/-1" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                                            <div class="" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
                                                <div class="h4 text_size status text_color3">
                                                    Absence Response
                                                </div>
                                            </div>
                                            <div class="h4 text_size status1 pb-2 text_color2 minimise" matTooltip="{{item.comment}}">
                                                {{ item.comment }}
                                            </div>
                                        </div>
                                </div>


           <div class="flex justify-end" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2">                                   
                                    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="mb-2">
                                        <button mat-flat-button mat-button color="var(--text-color2)" *ngIf="item.statusId == 1 || item.statusId == 4"
                                            class="btn-4 font-bold t-xs mt-4 mx-4 " (click)="editAbsense(item)">
                                            Edit  Absence Form
                                        </button>
                                       <button mat-button color="var(--text-color2)" *ngIf="item.statusId == 2" class="btn-4 font-bold t-xs mt-2 mr-5" (click)="viewAbsense(item)">
                                            View Absence Form
                                        </button>
                                    </div>                                  
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="px-gutter pt-2" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="card shadow-none cardBorder h-full" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
            fxFlex="auto" *ngIf="hideGrievanceForm">
            <div class="flex justify-center">
                <img class="mt-8 justify-center size" src="assets/img/register-exam1.svg" /><br />
            </div>
            <div class="textColor pb-6">
                <section class="mr-5 ml-5">
                    <div class="backGround_color">
                        <span class="welc-note flex justify-center text-center text-xs mx-4 mt-4">
                            <span class="p-2">
                                Absence form will appear only  if the exam status is No show
                            </span>

                        </span>
                        <br />
                    </div>
                </section>
                <div class="flex justify-center -mt-5 mb-4 t-xs">
                </div>
            </div>
        </div>
    </div>
</div>
