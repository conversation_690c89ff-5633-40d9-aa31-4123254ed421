
import { Component, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, NgForm, Validators } from '@angular/forms';
import { PersonDetails } from 'src/app/core/Dto/persondetail';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { Router } from '@angular/router';
import * as actions from '../state/profile-form.action';
import { Action, select, Store } from '@ngrx/store';
import { ProfileState } from '../state/profile-form.model';
import { Observable } from 'rxjs';
import { MANAGE_PROFILE_STATE } from '../state/profile-form.state';
import { PersonForm } from '../../application/application.types';
import { alphabetRegexWithSpace } from 'src/app/core/regex';
import { DataService } from '../../shared/data-service';
import { successUpdate } from '../state/profile-form.selectors';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { FormTypes } from '../../forms-wrapper/forms-wrapper.types';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'exai-profile-edit',
  templateUrl: './profile-edit.component.html',
  styleUrls: ['./profile-edit.component.scss']
})
export class ProfileEditComponent implements OnInit {
  @ViewChild('updateForm') updateForm: NgForm;
  public personDetails: PersonDetails;
  public PersonForm = new FormData();
  private dataDetails : PersonDetails;
  editForm: FormGroup;
  correctionButton: boolean = true;
  personFormId: any;
  manageProfile$: Observable<ProfileState>;

  validations = {
    'zipCode': [
      { type: 'required', message: 'Zip Code is required' },
      { type: 'pattern', message: 'Must contain 5 digits' }
    ],
    'city': [
      { type: 'pattern', message: 'City should not contain numbers' },
      { type: 'required', message: 'City is required' },
    ],
    'phoneNumber': [
      { type: 'required', message: 'Phone number is required' },
      { type: 'minlength', message: 'Enter a valid phone number' },
      { type: 'maxlength', message: 'Enter a valid phone number' }
    ],
    'emailId': [
      { type: 'pattern', message: 'email must be valid' }
    ],
    'address': [
      { type: 'required', message: 'Address is required' }
    ],
    'gender': [
      { type: 'required', message: 'Gender is required' }
    ],
  };

  constructor(public global: GlobalUserService,
    private formBuilder: FormBuilder,
    private router: Router,
    private store: Store<{ MANAGE_PROFILE_STATE: ProfileState }>,
    private dataService: DataService,
    public snackbar: SnackbarService) {
    this.store.dispatch<Action>(actions.getPersonDetails());   
    this.store.dispatch<Action>(actions.getPersonForm());
    this.store.dispatch<Action>(actions.clearDemographicData());
    this.store.dispatch<Action>(actions.clearPersonFormlogsState());
    this.store.dispatch<Action>(actions.resetUpdateProfile());
  }

  public ngOnInit(): void {
    this.manageProfile$ = this.store.pipe(select(MANAGE_PROFILE_STATE));
    this.manageProfile$.subscribe((data: ProfileState) => {
      this.dataDetails = JSON.parse(JSON.stringify(data.personDetails));
      this.personDetails = {...this.dataDetails};
      this.initializeForm();
      this.correctionFormStatus(data.personForm);
    });
  }

  private correctionFormStatus(data: Array<PersonForm>): void {
    let isButtonenable = data.filter(x => x.status == 'Drafted' || x.status == 'Pending' || x.status == 'Submitted' || x.status == 'Change Request');
    this.correctionButton = isButtonenable.length > 0 ? false : true;
  }

  private initializeForm(): void {
    const fullName = `${this.dataDetails.firstName} ${this.dataDetails.middleName!=null?this.dataDetails.middleName:''} ${this.dataDetails.lastName}`;
    this.editForm = this.formBuilder.group({
      gender: [this.dataDetails?.gender ? this.dataDetails.gender : "", [Validators.required]],
      phoneNumber: [this.dataDetails?.phoneNumber ? this.dataDetails.phoneNumber : "", [Validators.required,
      Validators.minLength(12), Validators.maxLength(14)]],
      emailId: [{ value: this.dataDetails?.emailId ? this.dataDetails.emailId : "", disabled: true }],
      zipCode: [this.dataDetails?.zipCode ? this.dataDetails.zipCode : "", [Validators.required, Validators.pattern("^[0-9]{5}(?:-[0-9]{4})?$")]],
      address: [this.dataDetails?.address ? this.dataDetails.address : "", Validators.compose([Validators.required,])],
      city: [this.dataDetails?.city ? this.dataDetails.city : "", [Validators.required, Validators.pattern(alphabetRegexWithSpace), Validators.minLength(3), Validators.maxLength(25)]],
      ssn: [{ value: this.dataDetails?.ssn ? this.dataDetails.ssn : 0, disabled: true }],
      firstName: [{ value: this.dataDetails?.firstName ? fullName : "", disabled: true }],
      dateOfBirth: [{ value: this.dataDetails?.dateofBirth ? (new DatePipe("en-US")).transform(this.dataDetails.dateofBirth, 'MM/dd/yyyy', '+0000') : "", disabled: true }],
    });
  }

  public submit(): void {
      this.editForm.markAllAsTouched();
      if (this.editForm.valid) {
        this.personDetails.address = this.editForm.controls.address.value;
        this.personDetails.phoneNumber = this.editForm.controls.phoneNumber.value;
        this.personDetails.zipCode = this.editForm.controls.zipCode.value;
        this.personDetails.city = this.editForm.controls.city.value;
        this.personDetails.gender = this.editForm.controls.gender.value;
        this.personDetails.emailId = this.editForm.controls.emailId.value;        
        this.personDetails.tenantId = this.global.tenantId;
        this.personDetails.modifiedBy = this.global.candidateId;
        this.personDetails.ssn = ''
        this.editProfile();
      }      
  }

  private editProfile(): void {
    this.store.dispatch(actions.passPersonDetail({ personDetails: this.personDetails })); 
    this.store.pipe(select(successUpdate)).subscribe((data: boolean) => {
      if (data) {
        this.snackbar.callSnackbaronSuccess('Successfully Saved Data.')
        this.router.navigateByUrl('/manage-profile');
      }
    });
  }

  public formClick() {
    let url: string = '/manage-profile/correction-form';
    this.dataService.setPersonForm("0");
    // this.router.navigateByUrl(url);
    this.router.navigate(['manage-profile', 'correction-form', FormTypes.Demographic, this.global.candidateId, 0, this.global.stateId, '0','']);
  };

   public onChange(gender: string) {
     this.personDetails.gender = gender;
  }
}