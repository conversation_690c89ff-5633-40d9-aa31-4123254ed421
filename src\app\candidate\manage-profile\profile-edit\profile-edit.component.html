<div class="px-gutter pt-2" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="2px"
  exaiContainer>
  <div class="w-full" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
    <div  fxLayout="column">
      <h5 class="titleFont"><strong>Edit Profile</strong></h5>
    </div>
    <!-- <div class="-mt-1"> -->
      <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
      </app-ng-dynamic-breadcrumb>
    <!-- </div> -->
  </div>
  <div class="w-full mt-2" gdColumn="3 / -1" gdColumn.lt-md="3 / -1" gdColumn.lt-sm="1">
    <div class="flex justify-end" fxLayout="">
      <div *ngIf="correctionButton">
        <button mat-button class="add-new text-xs" (click)="formClick()">
          Correction Form
        </button>
      </div>
    </div>
  </div>

  <div class="py-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px"
    exaiContainer>
    <!-- 1st Card Eligibility Route -->
    <div class="justify-start pt-2 overflow-auto touch-auto" gdColumn="1 / 1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
      <div class="card shadow-none cardBorder touch-auto overflow-auto" fxFlex="auto">
        <form #userForm="ngForm" [formGroup]="editForm">
          <div class="pl-4 pt-2" fxLayout="row" fxLayoutAlign="start">
            <h6 class="mx-2 pt-2 pb-2 font-bold">General Information</h6>
          </div>
          <div class="pl-6 pr-6 state eligibility-list edit eligible" fxLayout="column" fxLayoutGap="9px">
            <div fxLayout="row">
              <mat-form-field appearance="outline" fxFlex="auto">
                <mat-label class="text-xs text1">Full Name</mat-label>
                <input matInput placeholder="firstName" formControlName="firstName" />
                <mat-icon matTooltip="To edit NAME you have to submit correction form" matTooltipPosition="right"
                  class="mt-2" matSuffix>error_outline</mat-icon>
                <mat-error>
                  <div class="invalid-feedback">
                    <div>Name is Required</div>
                  </div>
                </mat-error>
              </mat-form-field>
            </div>

            <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="10px" fxLayoutGap.lt-sm="0">
              <div fxLayout="row" fxFlex="100">
                <mat-form-field class="pr-2" appearance="outline" fxFlex="50">
                  <mat-label class="text-xs text1">Date Of Birth</mat-label>
                  <input class="form-control" matInput placeholder="Date Of Birth" formControlName="dateOfBirth"
                    [value]="
                      this.editForm.value.dateOfBirth | date: 'dd/MM/yyyy'
                    " />
                  <mat-icon matTooltip="To edit DOB you have to submit correction form" matTooltipPosition="right"
                    class="mt-2" matSuffix>error_outline</mat-icon>
                  <mat-error>
                    <div class="invalid-feedback">
                      <div>Date is Required</div>
                    </div>
                  </mat-error>
                </mat-form-field>
                <mat-form-field class="pl-2 textbox" fxFlex="50" appearance="outline">
                  <mat-label class="text-xs text1">Gender</mat-label>
                  <mat-select class="h-3.5" formControlName="gender" #myselect (valueChange)="onChange(myselect.value)">
                    <mat-option class="text-xs text" value="Male">Male</mat-option>
                    <mat-option class="text-xs text" value="Female">Female</mat-option>
                    <mat-option class="text-xs text" value="other">Other</mat-option>
                    <mat-option *ngIf="this.global.stateId === 15" class="text-xs text" value="Prefer Not to Say">Prefer Not to Say</mat-option>
                    <mat-option *ngIf="this.global.stateId === 15"  class="text-xs text" value="X">X</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="px-6" fxLayout="row" fxLayoutAlign="start">
            <h6 class="mb-2 font-bold pt-4">Contact Information</h6>
          </div>

          <div class="px-6 eligibility-list edit textbox" fxLayout="column" fxLayoutGap="13px">
            <mat-form-field appearance="outline">
              <mat-label class="text-xs text1">Address</mat-label>
              <input matInput class="t-xs text" formControlName="address" [ngClass]="{
                  'is-invalid':
                    !editForm.get('address').valid &&
                    editForm.get('address').touched,
                  'is-valid':
                    editForm.get('address').valid &&
                    editForm.get('address').touched
                }" />
              <mat-error *ngFor="let validation of validations.address">
                <mat-error class="error-message pt-2" *ngIf="
                    editForm.get('address').hasError(validation.type) &&
                    (editForm.get('address').dirty ||
                      editForm.get('address').touched)
                  ">
                  {{ validation.message }}</mat-error>
              </mat-error>
            </mat-form-field>
            <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="9px" fxLayoutGap.lt-sm="0">
              <div fxLayout="row" fxFlex="100">
                <mat-form-field class="pr-2" fxFlex="50" appearance="outline">
                  <mat-label class="text-xs text1">Zip Code</mat-label>
                  <input matInput class="t-xs text" formControlName="zipCode" [ngClass]="{
                      'is-invalid':
                        !editForm.get('zipCode').valid &&
                        editForm.get('zipCode').touched,
                      'is-valid':
                        editForm.get('zipCode').valid &&
                        editForm.get('zipCode').touched
                    }" />
                  <mat-error *ngFor="let validation of validations.zipCode">
                    <mat-error class="error-message pt-3" *ngIf="
                        editForm.get('zipCode').hasError(validation.type) &&
                        (editForm.get('zipCode').dirty ||
                          editForm.get('zipCode').touched)
                      ">
                      {{ validation.message }}</mat-error>
                  </mat-error>
                </mat-form-field>
                <mat-form-field class="pl-2" fxFlex="50" appearance="outline">
                  <mat-label class="text-xs text1">City</mat-label>
                  <input matInput class="t-xs text" formControlName="city" [ngClass]="{
                      'is-invalid':
                        !editForm.get('city').valid &&
                        editForm.get('city').touched,
                      'is-valid':
                        editForm.get('city').valid &&
                        editForm.get('city').touched
                    }" />
                  <mat-error *ngFor="let validation of validations.city">
                    <mat-error class="error-message pt-3" *ngIf="
                        editForm.get('city').hasError(validation.type) &&
                        (editForm.get('city').dirty ||
                          editForm.get('city').touched)
                      ">
                      {{ validation.message }}</mat-error>
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <mat-form-field appearance="outline" class="exai-flex-form-field phNumberInput">
              <mat-label class="text-xs text pl-8 ml-8">Phone number</mat-label>
              <ngx-mat-intl-tel-input matInput [preferredCountries]="['us']" [enablePlaceholder]="false"
                [enableSearch]="true" formControlName="phoneNumber" class="t-xs text">
              </ngx-mat-intl-tel-input>

              <mat-error *ngFor="let validation of validations.phoneNumber">
                <mat-error class="error-message pt-3" *ngIf="
                    editForm.get('phoneNumber').hasError(validation.type) &&
                    (editForm.get('phoneNumber').dirty ||
                      editForm.get('phoneNumber').touched)
                  ">
                  {{ validation.message }}</mat-error>
              </mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label class="text-xs text">Email</mat-label>
              <input matInput class="t-xs text" formControlName="emailId" [ngClass]="{
                  'is-invalid':
                    !editForm.get('emailId').valid &&
                    editForm.get('emailId').touched,
                  'is-valid':
                    editForm.get('emailId').valid &&
                    editForm.get('emailId').touched
                }" />
              <mat-icon matTooltip="To edit EMAIL you have to submit correction form" matTooltipPosition="right"
                class="mt-2 state" matSuffix>error_outline</mat-icon>
              <mat-error *ngFor="let validation of validations.emailId">
                <mat-error class="error-message pt-3" *ngIf="
                    editForm.get('emailId').hasError(validation.type) &&
                    (editForm.get('emailId').dirty ||
                      editForm.get('emailId').touched)
                  ">
                  {{ validation.message }}</mat-error>
              </mat-error>
            </mat-form-field>

            <div class="state eligibility-list edit general eligible" fxLayout="column">
              <mat-form-field appearance="outline" fxFlex="auto">
                <mat-label class="t-xs text1">SSN</mat-label>
                <input matInput placeholder="ssn" class="text-xs text1" formControlName="ssn" />
                <mat-icon matTooltip="To edit SSN you have to submit correction form" matTooltipPosition="right"
                  class="mt-2" matSuffix>error_outline</mat-icon>
                <mat-error>
                  <div class="invalid-feedback">
                    <div>SSN is Required</div>
                  </div>
                </mat-error>
              </mat-form-field>
              <div class="flex justify-end mb-2">
                <button class="add-new text-xs" mat-button type="button"
                  (click)="submit()">
                  Submit
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>