<div *ngIf="model.parent.id != sectionName">
  <div class="mt-6" *ngIf="!disabled">
    <!-- <button *ngIf="!disabled" (click)="getFormListData(true)" 
    class="hover:bg-gray-100 text-gray-800 font-semibold border-gray-400 rounded  m-0.5">
    <mat-icon class="cursor-pointer text-base align-text-top">add</mat-icon>
    Add a form
</button> -->

    <div *ngIf="enableFormList">
      <div class="flex justify-end addressDetails1">
        <mat-icon class="cursor-pointer pr-6" (click)="closeList()"
          >close</mat-icon
        >
      </div>
      <div class="custom1 card addressDetails overflow-auto">
        <!-- <p class="flex justify-center pt-8 pb-6">List of avilable Forms</p> -->
        <ng-container *ngIf="availableForm.length != '0'">
          <p
            *ngFor="let item of availableForm"
            (click)="addForms(item)"
            class="border p-3 cursor-pointer"
          >
            {{ item.formName }}
          </p>
        </ng-container>
        <div class="flex justify-center">
          <h5 *ngIf="availableForm.length == '0'">No Data Found</h5>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="addedFormListId?.length > 0">
    <!-- <p class="custom">List Of Forms Added</p> -->
    <div class="custom">
      <ng-container *ngFor="let forms of addedFormListId">
        <button (click)="viewForm(forms, 11, false)" class="p-2">
          {{ forms.formName }} | {{ forms.status ? forms.status : "" }} |
          {{ forms.description ? forms.description : "NO Description" }} |
          {{
            forms.abuseDescription
              ? forms.abuseDescription
              : "NO Abuse Description"
          }}
          | {{ forms.date ? (forms.date | date: "MM-dd-yyyy") : "NO Date" }}
          <mat-icon class="cursor-pointer text-base align-text-top"
            >visibility</mat-icon
          ></button
        ><br />
        <!-- <button *ngIf="!disabled" (click)="removeFormlist(forms)"
                    class="hover:bg-gray-100 text-gray-800 font-semibold border-gray-400 rounded  m-0.5">
                    <mat-icon class="cursor-pointer text-base align-text-top">close</mat-icon>
                </button> -->
      </ng-container>
    </div>
  </div>
</div>

<div *ngIf="model.parent.id == sectionName">
  <button
    *ngIf="!disabled"
    mat-button
    class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2"
    (click)="viewForm(null, 12, true)"
  >
    <mat-icon>add</mat-icon>
    Add Form
  </button>
  <!-- <div *ngIf="actionForm?.length>0">
        <p class="custom">List Of Action Forms Added</p>
        <div class="custom">
        <ng-container *ngFor="let forms of actionForm">
            <ng-container *ngFor="let value of gotDetails">
                <ng-container *ngIf="value.formId==forms.personformId">
            <span>Action Form</span>
            <div *ngFor="let formresponse of value.response">
                <span *ngIf="typeOf(formresponse)">{{formresponse|date:'MM/dd/yyyy'}}</span>
                <span *ngIf="!typeOf(formresponse)">{{formresponse}}</span>
                </div>
            <button  (click)="viewForm(forms,12,false)" class="p-2">
            <mat-icon class="cursor-pointer text-base align-text-top">visibility</mat-icon>
        </button>
        <button *ngIf="!disabled" (click)="viewForm(forms,12,true)"
        class="hover:bg-gray-100 text-gray-800 font-semibold border-gray-400 rounded  m-0.5">
        <mat-icon class="cursor-pointer text-base align-text-top">edit</mat-icon>
    </button>
        <button *ngIf="!disabled" (click)="deleteAttchedForm(forms)"
                        class="hover:bg-gray-100 text-gray-800 font-semibold border-gray-400 rounded  m-0.5">
                        <mat-icon class="cursor-pointer text-base align-text-top">delete</mat-icon>
                    </button><br>
        </ng-container>
        </ng-container>
        </ng-container>
        </div>
        </div> -->
  <div></div>
  <table mat-table [dataSource]="dataSource" class="w-full p-8 mb-4 add-table">
    <!--- Note that these columns can be defined in any order.
          The actual rendered columns are set as a property on the row definition" -->

    <!-- Position Column -->

    <ng-container matColumnDef="Date">
      <th mat-header-cell *matHeaderCellDef>Date</th>
      <td mat-cell *matCellDef="let element">
        {{ element.date | date: "MM/dd/yyyy" }}
      </td>
    </ng-container>

    <ng-container matColumnDef="type">
      <th mat-header-cell *matHeaderCellDef>Action Type</th>
      <td mat-cell *matCellDef="let element">{{ element.type }}</td>
    </ng-container>

    <ng-container matColumnDef="description">
      <th mat-header-cell *matHeaderCellDef>Letter Description</th>
      <td mat-cell *matCellDef="let element">{{ element.description }}</td>
    </ng-container>

    <ng-container matColumnDef="additional">
      <th mat-header-cell *matHeaderCellDef>Additional</th>
      <td mat-cell *matCellDef="let element">{{ element.additional }}</td>
    </ng-container>

    <ng-container matColumnDef="Received">
      <th mat-header-cell *matHeaderCellDef>Received Date</th>
      <td mat-cell *matCellDef="let element">
        {{ element.Received | date: "MM/dd/yyyy" }}
      </td>
    </ng-container>

    <ng-container matColumnDef="Due">
      <th mat-header-cell *matHeaderCellDef>Due Date</th>
      <td mat-cell *matCellDef="let element">
        {{ element.Due | date: "MM/dd/yyyy" }}
      </td>
    </ng-container>

    <ng-container matColumnDef="Comment">
      <th mat-header-cell *matHeaderCellDef>Comment</th>
      <td
        matTooltip="{{ element.commentPreview }}"
        mat-cell
        class="table_column"
        *matCellDef="let element"
      >
        {{ (element.Comment !="" && element.Comment !=null)? element.Comment.slice(0, 15):element.Comment
        }}<span
          ><button *ngIf="element.Comment !='' && element.Comment !=null"
            class="expand-comment"
            (click)="showComment(element.Comment)"
          >
            (...)
          </button></span
        >
      </td>
    </ng-container>

    <ng-container matColumnDef="File">
      <th mat-header-cell *matHeaderCellDef>Files</th>

      <td matTooltip="{{ element.Files }}" mat-cell *matCellDef="let element">
        {{ element.Files }}
      </td>
    </ng-container>

    <ng-container matColumnDef="Action">
      <th mat-header-cell *matHeaderCellDef class="delete-cell">Action</th>
      <td mat-cell *matCellDef="let element" class="delete-cell">
        <mat-icon
          *ngIf="!disabled"
          class="cursor-pointer delete-icon"
          (click)="deleteAttchedForm(element)"
        >
          delete</mat-icon
        >
        <mat-icon
          class="cursor-pointer delete-icon"
          (click)="viewForm(element, 12, false)"
        >
          visibility</mat-icon
        >
        <mat-icon
          *ngIf="!disabled"
          class="cursor-pointer delete-icon"
          (click)="viewForm(element, 12, true)"
        >
          edit</mat-icon
        >
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <ng-template #callAPIDialog class="overflow-hidden">
    <h2 matDialogTitle class="pb-4 flex justify-center t-xs confirm">
      Are You Sure You Want to Delete
    </h2>
    <mat-dialog-actions>
      <button mat-button class="btn-3 justify-center" matDialogClose="no">
        No
      </button>
      <button mat-button class="btn-1" color="primary" matDialogClose="yes">
        Yes
      </button>
    </mat-dialog-actions>
  </ng-template>
</div>
