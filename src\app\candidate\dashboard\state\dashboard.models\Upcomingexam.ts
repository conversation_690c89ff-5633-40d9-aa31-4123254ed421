export class upcomingExam {
  id: number;
  examId: number;
  candidateId: number;
  examName: string;
  candidateEmailId: string;
  candidateName: string;
  examDateTime: Date;
  examDateTimeUtc: Date;
  timeZoneOffset?: any;
  examStatusId: number;
  examStatus: string;
  stateCode: string;
  eligibilityRouteCode: string;
  eligibilityRouteName: string;
  examModeId?:number
  iconUrl: string;
  registeredDateTime: Date;
  eventDataDetail:any
  timezone: string;
  timezoneCode: string;
  examMode: string;
  allowReschedule: string;
  allowCancel: string;
  isPrimary: boolean;
  reschedule:boolean;
  cancel:boolean
  isGrievanceFormSubmitted: boolean;
  testCenterDetails:TestDetails
  stateName: string;
  examDateTimePDT: string;
  allowLaunchExam: boolean;
  timeZoneAbbreviation: string;
  clientExamId: number; //To bind to ExamID sending in queryParams to LoginURL on click of StartExam
}
export class TestDetails {
  testCenterAddress: string
  testCenterCity: string
  testCenterId: string
  testCenterName: string
  testCenterPostalCode: string
  testCenterState: string
}
