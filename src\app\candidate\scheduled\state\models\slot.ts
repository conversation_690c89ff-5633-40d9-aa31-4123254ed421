
export interface slot {
city: string
directions:string
postalCode: string
slots: Slot[]

state: string
testSiteAddress: string
testSiteId: string
testSiteName: string
}
export interface Slot {
    slotId: number;
    slotDate: any;
    strSlotDate: string;
    strSlotTime: string;
    totalSlots: number;
    bookedSlots: number;
    availableSlots: number;
    slotDateUtc:string
}

export interface slotDates{
    orderId:number,
    slotDate:string,
    strSlotDate:string,
    examdate?:string,
    siteSlot?:[]
}
