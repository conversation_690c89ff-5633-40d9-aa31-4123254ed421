import { environment } from 'src/environments/environment';
import { HttpHeaders } from "@angular/common/http";

export const httpHeader = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json'
    })
};

export const headerOptions = new HttpHeaders({
    'Content-Type': 'application/json'
});

export class URL {

    public static readonly BASE_URL_SHORTER: string = environment.baseUrl ;
    public static readonly BASE_URL_SHORTER1: string = environment.baseUrl + 'client/api/' ;
    public static readonly BASE_URL: string = environment.baseUrl + 'candidate/api/';
    public static readonly ACCOUNT_BASE_URL: string = environment.baseUrl + 'login';
    public static readonly EditProfile_URL: string = environment.baseUrl + 'formmsvc/api/';
    public static readonly REDIRECT_URL: string = environment.redirectUrl;
    public static readonly Sponsor: string = environment.baseUrl + 'sponsor/api/';
    public static readonly EXAM_BASE_URL: string = environment.baseUrl + 'exam/';
    public static readonly V1_BaseUrl: string = environment.examroomapiUrl;
    public static readonly V1_scheduleStartExam_Url: string = environment.examroomapiUrl + 'ScheduleStartExam';

}