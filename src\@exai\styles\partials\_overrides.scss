.mat-icon,
.mat-icon-button .mat-icon {
    font-size: var(--default-icon-size);
    height: unset;
    width: unset;
}

.ic-inline>svg {
    display: inline-block;
}

.trainingUploadCandidate .mat-form-field-appearance-outline .mat-form-field-outline{
    top: 12px !important;
}

ic-icon:not(.ic-inline)>svg,
.iconify:not(.ic-inline)>svg {
    margin: 0 auto;
    vertical-align: middle;
}

.exai-scrollblock {
    position: fixed;
    width: 100%;
}

.mat-sidenav-fixed {
    position: absolute!important;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--text-color2)!important;
}

.mat-radio-label-content {
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
    user-select: auto;
    display: inline-block;
    /* order: 0; */
    line-height: inherit;
    /* padding-left: 8px; */
    /* padding-right: 0; */
    margin-top: -6px;
    margin-left: -4px;
    // margin-top: 1px;
}

// Checkbox
.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
.mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: none!important;
}

// .mat-checkbox-checked:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element,
// .mat-checkbox:active:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element {
//   background: var(--check-box-checked-color);
// }
// .mat-checkbox .mat-ripple-element {
//   background: var(--check-box-checked-color);
// }
.mat-checkbox-inner-container:hover .mat-checkbox-persistent-ripple {
    opacity: 0;
}

.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element,
.mat-checkbox:active:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element {
    background: transparent!important;
}

.mat-checkbox .mat-ripple-element {
    background: transparent!important;
}

.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-button.cdk-program-focused .mat-button-focus-overlay,
.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,
.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,
.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-flat-button.cdk-program-focused .mat-button-focus-overlay {
    opacity: 0;
}

.mat-checkbox-inner-container:hover .mat-checkbox-persistent-ripple {
    opacity: 0;
}

.mat-checkbox-inner-container {
    height: 13px !important;
    width: 13px !important;
}

// paginator
.paginator .mat-form-field-appearance-fill .mat-form-field-flex {
    background-color: transparent;
}

.paginator .mat-form-field-appearance-fill .mat-form-field-infix {
    padding: 0.25em 0 0.25em 0;
}

.paginator .mat-form-field-appearance-fill .mat-select-arrow-wrapper {
    padding-top: 1em !important;
}

.mat-paginator-container {
    justify-content: space-between !important;
}

// icon-button
.button.active[_ngcontent-qsf-c163] {
    background: none;
}

.mat-icon-button .mat-button-wrapper>*,
.mat-menu-item .mat-icon,
.mat-button .mat-icon {
    font-size: 1.25rem;
}

.eligibility-list .mat-stroked-button {
    padding: 0 0 0 12px !important;
    line-height: 27px;
    font-size: 0.65rem;
}

.eligibility-list .mat-stroked-button:not(.mat-button-disabled) {
    // border-color: var(--theader)!important;
    white-space: initial;
    text-align: initial;
}

.eligibility-list .mat-form-field-wrapper {
    padding-bottom: 0.25em;
}

.eligibility-list .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0 0.55em 0;
    border-top: 0.4em solid transparent;
}

.contact .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0 0.55em 0;
    border-top: 0.4em solid transparent;
}

.exam .mat-stroked-button {
    padding: 0 12px !important;
    // line-height: 27px;
    font-size: 0.65rem;
}

.exam .mat-stroked-button:not(.mat-button-disabled) {
    border-color: var(--theader) !important;
}

.exam .mat-form-field-wrapper {
    padding-bottom: 0.50em;
}

.exam .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.85em 0 0.65em 0;
    border-top: 0.2em solid transparent;
}

.exam-1 .mat-form-field-wrapper {
    // padding-bottom: 0.50em;
}

.exam-1 .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.85em 0 0.65em 0;
    border-top: 0.2em solid transparent;
}

.payment .mat-form-field-appearance-outline .mat-form-field-infix {
    // padding: 1em ;
    border-top: 0.2em solid transparent;
}

.payment .mat-form-field-wrapper {
    padding-bottom: 1em;
}

.codeNumber .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0 0.5em 0;
    border-top: 0.5em solid transparent;
}

.codeNumber input.mat-input-element {
    margin-top: 0.75em;
}

.codeNumber .mat-form-field-wrapper {
    // padding-bottom: 1em;
}

.dropdown-heading .material-icons-round {
    font-size: 18px;
}

.custom-bread-crumb {
    padding: 0px !important;
    list-style: none;
    background-color: transparent !important;
}

.custom-bread-crumb li a {
    text-decoration: none;
    font-size: 0.65rem;
}

.t-xs {
    font-size: 0.65rem;
}

.add-new.mat-button {
    line-height: 28px !important;
}

.cardBorder {
    border: var(--border);
    border-radius: var(--border-radius);
}

// buttons
.btn-1 {
    background-color: var(--button-background) !important;
    color: var(--button-color) !important;
    border: 1px solid var(--button-background) !important;
    border-radius: 4px;
    padding: 0rem 1rem !important;
    font-weight: 600;
}

.btn-2 {
    background-color: var(--button-color) !important;
    color: var(--button-background) !important;
    border: var(--save-draft-border) !important;
    border-radius: 4px;
    padding: 0rem 1rem !important;
    font-weight: 600;
}

.btn-3 {
    background-color: var(--button-color) !important;
    color: var(--text-delete) !important;
    border: var(--delete-border) !important;
    border-radius: 4px;
    padding: 0rem 1rem !important;
    font-weight: 600;
}

.btn-4 {
    color: var(--text-color2);
    background-color: var(--background-base3);
    border: 1px solid var(--background-base3);
    border-radius: 4px;
    font-weight: 600;
}

.btn-5 {
    color: var(--sing-out);
    background-color: var(--sing-out-background);
    border: 1px solid var(--sing-out-background);
    border-radius: 4px;
    font-weight: 600;
}

.btn-1.mat-button {
    line-height: 25px !important;
}

.btn-2.mat-button {
    line-height: 25px !important;
}

.btn-3.mat-button {
    line-height: 25px !important;
}

.btn-4.mat-button {
    line-height: 25px !important;
}

.btn-5.mat-button {
    line-height: 25px !important;
}

.icon-eligible.mat-form-field-prefix .mat-icon,
.mat-form-field-suffix .mat-icon {
    font-size: 100%;
    line-height: 1.125;
}

.application-expansion .mat-expansion-panel-content {
    overflow: auto !important;
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 17.1rem);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 17.1rem);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 17.1rem);
    }
}

.application-expansion1 .mat-expansion-panel-content {
    overflow: auto !important;
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 12.1rem);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 12.1rem);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 12.1rem);
    }
}

.application-expansion1.mat-expansion-panel-spacing {
    margin: 0px!important;
}

// mat-card
.mat-dialog-container {
    // padding: 0px !important;
}

// .mat-expansion-panel-body{
//   // border-bottom: 0.3px solid #6D6D6D !important;
//   // border-left: 0.3px solid #6D6D6D !important;
//   // border-right: 0.3px solid #6D6D6D !important;
//   border: var(--border);
//   // border-radius: var(--border-radius);
// }
.mat-button-wrapper {
    display: contents !important;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle,
.mat-radio-outer-circle {
    // border-color: var(--text-color2) !important;
    width: 14px !important;
    height: 14px !important;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: var(--text-color2) !important;
    width: 14px !important;
    height: 14px !important;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle :hover {
    background-color: #ffff;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle,
.mat-radio-outer-circle :hover {
    background-color: #ffff;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle :hover,
:focus {
    background-color: none !important;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle,
.mat-radio-outer-circle :hover,
:focus {
    background-color: none !important;
}

.mat-radio-button-checked:not(.mat-radio-button-disabled).mat-accent .mat-ripple-element,
.mat-radio-button:active:not(.mat-radio-button-disabled).mat-accent .mat-ripple-element {
    background: none;
}

.mat-radio-button .mat-ripple-element {
    background: none;
}

.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-datepicker-toggle-default-icon,
.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-datepicker-toggle-default-icon {
    margin: auto;
    font-size: 12px;
}

.mat-expansion-indicator::after {
    /* border-style: solid; */
    border-width: 0 2px 2px 0;
    content: "";
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    vertical-align: middle;
    /* color: black !important; */
    border-color: black;
}

.accord .mat-expansion-panel:not([class*=mat-elevation-z]) {
    border: 1px solid #4444 !important;
}

.accord1 .mat-expansion-panel:not([class*=mat-elevation-z]) {
    border: 1px solid #4444 !important;
    // width: max-content;
}

// 
// .mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start, .mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end, .mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap
// {
//     border-width: 1px;
// }
//snack bar started
.my-custom-dialog-class .mat-dialog-container {
    overflow: hidden;
}

element.style {
    transform: scale(1);
    opacity: 1;
}

.error-snackbar.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: 0.3px solid var(--snackbar-error);
    min-width: 0;
}

.warning-snackbar.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: 0.3px solid var(--snackbar-warning);
    min-width: 0;
}

.success-snackbar.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: var(--btn-border);
    min-width: 0;
}

.success-snackbars.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: var(--btn-border);
    min-width: 0;
}

.success-snackbar .mat-icon-button,
.warning-snackbar .mat-icon-button,
.error-snackbar .mat-icon-button {
    padding: 0 16px;
    min-width: 0;
    width: 40px;
    height: 27px;
    flex-shrink: 0;
    line-height: 26px;
    border-radius: unset;
}

.success-snackbar .grid-auto:first-child {
    background-image: url(../../../assets/img/success1.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36px 100px;
}

.warning-snackbar .grid-auto:first-child {
    background-image: url(../../../assets/img/warning.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36px 100px;
}

.error-snackbar .grid-auto:first-child {
    background-image: url(../../../assets/img/error.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36px 100px;
}

//snack bar end
//spinner
.card-div {
    height: 2rem;
    width: 3rem;
    position: absolute;
}

.spinner {
    margin: 20px auto 0;
    width: 70px;
    text-align: center;
}

.spinner>div {
    width: 18px;
    height: 18px;
    background-color: var(--text-color2);
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
    }
}

@keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

input.country-search {
    padding: 0px 5px 9px !important;
}

.country-list-button {
    font-size: 12px !important;
    padding: 12px 9px !important;
}

input.country-search:focus-visible {
    outline: -webkit-focus-ring-color auto 0px !important;
}

.flag.US[_ngcontent-xoo-c278] {
    background-position: 0 -3360px;
    //   margin-right: 10ex;
}

.ellipsisinput.mat-input-element {
    font-size: 10px;
    width: 100%;
}

.userDropdown {
    color: var(--text-color2);
}

.mat-tab-group.mat-primary .mat-ink-bar,
.mat-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: var(--text-color2);
}

.mat-badge-content {
    background: var(--text-color2);
}

// systemcheck popup
.systemCheckDialog.mat-button .mat-button-wrapper>*,
.systemCheckDialog.mat-flat-button .mat-button-wrapper>*,
.systemCheckDialog.mat-stroked-button .mat-button-wrapper>*,
.systemCheckDialog.mat-raised-button .mat-button-wrapper>*,
.systemCheckDialog.mat-icon-button .mat-button-wrapper>*,
.systemCheckDialog.mat-fab .mat-button-wrapper>*,
.systemCheckDialog.mat-mini-fab .mat-button-wrapper>* {
    vertical-align: top !important;
}

.systemCheckDialog .mat-button-ripple.mat-ripple,
.systemCheckDialog .mat-button-focus-overlay {
    top: -3px;
    left: 4.5px;
    width: 30px;
    height: 30px;
}

.mat-icon-button .mat-ripple-element {
    display: none !important;
}

.mat-dialog-container {
    padding: 0px!important;
    border: var(--save-draft-border);
    border-radius: var(--border-radius);

}

// #systemCheck .mat-dialog-container {
//     padding: 12px!important;
// }

.mat-form-field-appearance-outline .mat-form-field-suffix {
    top: 0.1em !important;
}

.mat-form-field-label-wrapper {
    top: -0.95em !important;
}

// .mat-select-trigger{
//   top: -0.4rem;
// }
.mat-form-field-appearance-outline .mat-select-arrow-wrapper {
    transform: none !important;
}

.mat-select-arrow-wrapper {
    vertical-align: middle !important;
}

.eligibility-list-btn .mat-stroked-button {
    // padding: 0 0 0 12px !important;
    line-height: 30px;
    font-size: 0.65rem;
}

.eligibility-list-btn .mat-stroked-button:not(.mat-button-disabled) {
    // border-color: var(--theader)!important;
    white-space: initial;
    text-align: initial;
}

.certificate-search .mat-form-field-wrapper {
    padding-bottom: 0em;
}

.certificate-search .mat-input-element {
    padding-bottom: 0em !important;
}

.certificate-search .mat-form-field-infix {
    border-top: 0.4em solid transparent;
    padding: 0.8em 0 0.6em 0;
}

.certificate-search.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0 0.6em 0;
}

.mat-datepicker-content .mat-calendar {
    @screen sm {
        width: 238px!important;
        height: 300px!important;
    }
    @screen xs {
        width: 238px!important;
        height: 300px!important;
    }
    @screen xxs {
        width: 238px!important;
        height: 300px!important;
    }
}

.promo-code .mat-input-element {
    width: 56% !important;
}

.mat-select-panel {
    min-width: 112px;
    max-width: 500px!important;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    padding-top: 0;
    padding-bottom: 0;
    max-height: 256px;
    min-width: 100%;
    border-radius: 4px;
    outline: 0;
}

.mat-select-panel .mat-optgroup-label, .mat-select-panel .mat-option {
    font-size: x-small!important;
    line-height: 1.5em!important;
    height: 4em!important;
}

.spinner.mat-progress-spinner {
    width: 50%!important;
    height: 50%!important;
}

.mat-option {
    white-space: unset!important;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    line-height: 48px;
    height: 48px;
    padding: 0 16px;
    text-align: left;
    text-decoration: none;
    max-width: 100%;
    position: relative;
    cursor: pointer;
    outline: none;
    display: flex;
    flex-direction: row;
    max-width: 100%;
    box-sizing: border-box;
    align-items: center;
    -webkit-tap-highlight-color: transparent;
}

