import {
  setLoadingSpinner,
  setErrorMessage,
  gotCartItems,
  setUserDetails,
  gotUserDetails,
  getUserDetails,
  deleteCartItem,
  gotCartPracticwItems,
  addCartItem,
  deleteCartItemById,
} from "./shared.actions";
import { Action, createReducer, on } from "@ngrx/store";
import { initialState } from "./shared.state";
import { DecodedIdentityToken, UserDetails } from "../../candiate.types";
import { environment } from "src/environments/environment";
import jwt_decode from "jwt-decode";
import { Actions } from "@ngrx/effects";

const _sharedReducer = createReducer(
  initialState,
  on(setLoadingSpinner, (state, action) => {
    return {
      ...state,
      showLoading: action.status,
    };
  }),
  on(setErrorMessage, (state, action) => {
    return {
      ...state,
      errorMessage: action.message,
    };
  }),
  on(setUserDetails, (state, action) => {
    var userData: DecodedIdentityToken = null;
    if (sessionStorage.getItem("UserData")) {
      userData = JSON.parse(sessionStorage.getItem("UserData"));
    } else if (action.token) {
      // userData.roles=[{personTenantRoleId:Number(action.id),tenantId:Number(action.tenantId)}];
      userData = jwt_decode(action.token);
      // userData.personId=Number(action.personId);
      sessionStorage.setItem("userData", JSON.stringify(userData));
      sessionStorage.setItem("token", action.token);
    } else {
      // window.location.href = environment.redirectUrl;
      // userData = { "accessToken": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "identityToken": "eyJraWQiOiJIWDMxNFhhdDdcLzJRdm9jblNON3pkeWNYZW03cUI5OWMrbkRtTUJkTmpCWT0iLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Hd3_kFKcbutpK2ED28sJ__NXIXNYvBG2EMKrsGjojQH3Imk8uKgHqlPeAMCM_qvmjcP1o_WBi0EXORRHCuvchggzKhFo8fJM1BBxVAh8fLIu98cqgB397VPkqvJrow2DD_5svXty7JMVzluQBd5t9ku24MdCEXdYkBmuLb6tyyueLJGxjbXmbALmMPTf7MofSWUmePQeYZXkSOfGzVNxnI1021-Gmvxn8x2E2VUKS4zlOJUhPhyXigBKadTrU4VjJMrrD1bxACEixfxhr7F1p280MtAvSXhL8-z_r2GTfVo74twSQW2xxsCDDh_6eLGTVOiaZBuaXypGQgh1P-wReQ", "refreshToken": "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.F0baZ6WMclVB8SG6LjU8c_H85wrO64l5hvPvnRV29_d0jMXgFGAANiFUsMXRLIa4Q8VP5NH274VV_VCoLrZaYyEkDC1HGdIeD30vlohsGOpRna4VGeltLRMAgATGl8F8674mGv0rDqQbltPMzqSGwd4iM3F5NGQUJ0AL0TGf3WBCovVZ2Rsf0pI6oQXdKhTHB_o_H9xfBHiAfIPlTmF-8_w6aiswLQD1p9EKJh8Oz3QYFBDYd-d4aBKteGdJfjpEBPJOLA3zdlVQ4yPAcPqYxYiF8HK4I-b7CBc1_6ozbq6DhvhqCGwk7MPzn4JVwlrNnM15cuOcs-cn6UXNbW1gfQ.5Ea9rjD1PVPIFung.aEYVYWc3FT7G5LSJ6cFZusfxrm5yJb_6N1z2WhSUmSxykbzrCPWzer3E9Pq9wwY8jsDkL-nFVAp25pG8BlRytC8GoFj4h08oxLJcganLu71-zqBEWAXPD-zCM0TtKxlAeOUdD9KUIRYWoTK868l5pYk1GlS5-x007ZUK3qxiiWBeQP42egoNx-2PqIQ24jj5IfQa9trO30cCHnsF0CQxfNwrxBSXCqLPcLWQ7OFJmJMEVr6H0j_1-lomeLEWp6en-bkNmnlZW7TqmUMu-VOrGnuX_kWkSQ1uld5uciPBDn2qcI4LbycPiAtJf9DM06HXNaSFuvxqV-wdhjH1uTAVlWBpEm5nmvMsJgnIa8TRXIschNfdPFNsKQtD1TG6x2i-zvIgvpfj7a5R34nMj6XxAPOjosCs-QLP5_FPQkdIpBT_eD6mYoNGq21q6ugj70vn1QxLVwf-4Bf9JZk6Is9lhXZhq7AHtK4ckRcU6nkN1dOebvBKReIie1S9Bjj4ajpcly6ls8ywnj2cyaKpocDyZYlzsY9igIKv6k54dgLf2YzJxV5yEf0MHZqC7fdlO0KfHIWGGKJXQXM21fLhqMIHrq6qbMbqWRsKiXDaHuRPMfjimlqF5N1D8h15_v53_BgWKM7FZj-w3dHDPPectRw4KMCoqisOfuZJIbxcYZPSBo7K456mxOp7MwWGDXAFMTKTuZqjhYeOq9YiMvhWqiPw_xqn-VTUJ6FwDlv7KBnhdAE3aqylPBA7h4wnOtveyTxF2iaSKajSs1ZRbMGRhLej3486SCIHXOGrN3eAcXgwjzkQtDaCqbwhgmb44BqFe80L1BCyW4nMhcsjB_z3WVt9buLb8A0VFmeT4JBaN3Nzw9UQ_834gkWeTK_XjGUYJT_YHZRNoiPJk9EY0zqgICfbchsIPUf0ztF9wdQ9HX8Lx85_bzECMxpVBaSO3913F4KA3FK2jfceb3ka-_1GYujsMQlA4miT4titg0fGw97IoDYmVplNtAfFwwAAgBYIqQiFDBAbLV5_e9kn9BFNqgr1IW5LO4j00JvxpcOOLgx7Tf1cAT2huSCLR1_jUCagMBOOxHVO4tDISwqLHIFCQH9vBkvObJ5Voin0nyWOetaCs4uIn-XNQRnSl26eWpIGZficiFEv8-mDNF_7dF2nQtdjayfpCGw6hy8f124DZ4LfZ5o3Ea6FIhAxfzojSPJGXJbLF7yfUZaJx5ygQHDmWReZuLB--3eBauvVlhN8nTcuSjIKvvbrFVg1TuRyZAwpeX-sxgiCI8AiIJzbJGbwqwjDSx26cH0hq7Y79FMkzNTHu1aM8DUNj6-Rv7Apag.niRh3cKRQEgpLVZ_Nkynbw", "tokenType": "Bearer", "decodedIdentityToken": { "sub": "7352e610-52a6-400d-a9c2-9a70ca6d1a6b", "cognito:groups": ["Candidate"], "email_verified": true, "address": { "formatted": "kundapur" }, "birthdate": "2021-10-04", "gender": "Female", "iss": "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_CPPQEiOer", "phone_number_verified": false, "cognito:username": "7352e610-52a6-400d-a9c2-9a70ca6d1a6b", "given_name": "archana", "middle_name": "k", "origin_jti": "e16c0de9-9a41-4d7f-95c2-054dbd498ce8", "cognito:roles": ["arn:aws:iam::973238313667:role/service-role/ExamRoomUserPool-SMS-Role"], "aud": "70q8foddi4ej5m48ujqlicpq6n", "event_id": "83738925-c4ad-4522-a7d0-e1b02e760eef", "token_use": "id", "auth_time": 1634193951, "phone_number": "+19741813820", "exp": 1634197551, "iat": 1634193951, "family_name": "h", "jti": "ab225b56-b62e-4c60-ad4c-0cc661f646e6", "email": "<EMAIL>" }, "state": "karnataka", "personId": 350, "roles": [{ "roleId": 1, "tenantId": 3, "roleName": "Candidate", "personTenantRoleId": 158 }] }
    }

    return {
      ...state,
      decodeInfo: userData,
    };
  }),
  on(gotUserDetails, (state, action) => {
    return {
      ...state,
      userdetails: action.userDetails,
    };
  }),
  on(addCartItem, (state, action) => {
    return {
      ...state,
      cartItems: [...state.cartItems, action.cartItems],
    };
  }),
  on(gotCartItems, (state, action) => {
    return {
      ...state,
      cartItems: action.cartItems,
    };
  }),
  on(gotCartPracticwItems, (state, action) => {
    return {
      ...state,
      PracticecartItems: action.practicecartItems,
    };
  }),
  on(deleteCartItem, (state, action) => {
    const items = JSON.parse(JSON.stringify(state.cartItems));
    items.splice(action.index, 1);
    return {
      ...state,
      cartItems: items,
    };
  }),
  on(deleteCartItemById, (state, action) => {
    const items = JSON.parse(JSON.stringify(state.cartItems));
    const index = items.findIndex(
      (item) => item.personEventCartId === action.cartItemId
    );
    if (index !== -1) {
      items.splice(index, 1);
    }
    return {
      ...state,
      cartItems: items,
    };
  })
);

export function SharedReducer(state, action) {
  return _sharedReducer(state, action);
}
