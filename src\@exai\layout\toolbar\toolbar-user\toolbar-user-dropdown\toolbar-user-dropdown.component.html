<div class="" 
 [ngClass]="isMobile ?  'dropdown' : 'dropdown2'">
  <div class="dropdown-header">
    <div >
      <!-- <div class="dropdown-heading-icon" fxLayout="row" fxLayoutAlign="center center">
        <mat-icon [icIcon]="ic<PERSON>erson"></mat-icon>
      </div> -->
      <div *ngIf="isMobile" class="flex bg-c py-4 bg; info px-3">
        <div class="">
          <span class="material-icons-round profile-icon tex"> account_circle </span>
        </div>
        <div class="tex pl-3">
          <h3 class="tex">{{ userName }}</h3>
          <span class="tex">Candiate ID: {{ personId }}</span>
        </div>
        <div (click)="closeInformation()" class="ml-auto text-xl">
          <span class="material-icons-round tex"> close </span>
        </div>
      </div>

<div class="p-4">
      <div class="flex text-left text-base pb-2 cursor-pointer" (click)="openEdit(); closeInformation()" >
        <span class="material-icons-round icon"> account_circle </span>
        &nbsp;&nbsp; Edit Profile
        <span class="material-icons-round ml-auto"> chevron_right </span>
        
      </div>
      <!-- <hr class="horzl">
      <div class="flex text-left text-base py-2 cursor-pointer">
        <span class="material-icons-round icon"> brightness_6 </span>
        &nbsp;&nbsp; Theme
      </div> -->
      <hr *ngIf="isMobile" class="horzl">
      <!-- <div class="flex text-left text-base py-2 cursor-pointer">
        <span class="material-icons-round icon"> import_contacts </span>
        &nbsp;&nbsp; User Manual
      </div> -->
      <!-- <hr class="horzl"> -->

      <div *ngIf="isMobile" class="flex text-left text-base pt-2 pb-2 cursor-pointer">
        <span class="material-icons-round icon"> notifications_none </span>
        &nbsp;&nbsp; Notifications
        <span class="material-icons-round ml-auto"> chevron_right </span>
      </div>

      <hr class="horzl" *ngIf="isMobile">

      <div *ngIf="isMobile" class="flex text-left text-base pt-2 pb-2 cursor-pointer" mat-icon-button (click)="redirectToCredentialTicketForm()">
        <span class="material-icons-round icon"> email </span>
        &nbsp;&nbsp; Email
        <span class="material-icons-round ml-auto"> chevron_right </span>
      </div>

      <hr class="horzl">

      <div class="flex text-left text-base pt-2 icon1 cursor-pointer pb-2" (click)="close();closeInformation()">
        <span class="material-icons-round icon1"> exit_to_app </span>
        &nbsp;&nbsp; Sign Out
      </div>

      <div *ngIf="isMobile" [matMenuTriggerFor]="belowMenu" class="flex text-left text-base pt-2 icon1 cursor-pointer pb-2" >
        <span   class="material-icons-round icon1"> supervised_user_circle </span>
        &nbsp;&nbsp; User Roles
      </div>

      <mat-menu class="h-48" #belowMenu="matMenu" fxLayoutAlign="flex-end center" overlapTrigger="false" xPosition="before" yPosition="below">
        <button (click)="SelectRole(role, this.global.getUserIdToken(),this.global.stateId)" *ngFor="let role of Roles" mat-menu-item>
          <span>{{role.roleName}}</span>
          <hr>
        </button>
      </mat-menu>
    </div>

  
  </div>
    <!-- <button [matMenuTriggerFor]="settingsMenu"
            mat-icon-button
            matTooltip="Settings"
            matTooltipPosition="before"
            type="button">
      <mat-icon [icIcon]="icSettings" class="notifications-header-icon"></mat-icon>
    </button> -->
  </div>

  <!-- <div class="dropdown-content">
    <a (click)="close()"
       *ngFor="let item of items; trackBy: trackById"
       [routerLink]="item.route"
       class="notification"
       fxLayout="row"
       fxLayoutAlign="start center"
       matRipple>
      <mat-icon [icIcon]="item.icon"
                [ngClass]="item.colorClass"
                class="notification-icon"
                fxFlex="none"></mat-icon>
      <div fxFlex="auto">
        <div class="notification-label">{{ item.label }}</div>
        <div class="notification-description">{{ item.description }}</div>
      </div>
      <mat-icon [icIcon]="icChevronRight" class="notification-chevron" fxFlex="none"></mat-icon>
    </a>
  </div> -->

  <!-- <div class="dropdown-footer" fxLayout="row" fxLayoutAlign="space-between center">
    <button [matMenuTriggerFor]="statusMenu" class="dropdown-footer-select" mat-button type="button">
      <ng-container *ngFor="let status of statuses; trackBy: trackById">
        <span *ngIf="status === activeStatus">
          <mat-icon [icIcon]="status.icon" [ngClass]="status.colorClass"></mat-icon>
          <span>{{ status.label }}</span>
          <mat-icon [icIcon]="icArrowDropDown" class="dropdown-footer-select-caret"></mat-icon>
        </span>
      </ng-container>
    </button>
    <a (click)="close()" [routerLink]="['/login']" color="primary" mat-button>LOGOUT</a>
  </div>
</div> -->

<!-- <mat-menu #statusMenu="matMenu" xPosition="before" yPosition="below">
  <button (click)="setStatus(status)"
          *ngFor="let status of statuses; trackBy: trackById"
          mat-menu-item>
    <ic-icon [icon]="status.icon" [ngClass]="status.colorClass" inline="true" size="24px"></ic-icon>
    <span>{{ status.label }}</span>
  </button>
</mat-menu> -->


<!-- <mat-menu #settingsMenu="matMenu" xPosition="before" yPosition="below">
  <button mat-menu-item>
    <mat-icon [icIcon]="icBusiness"></mat-icon>
    <span>Change Address</span>
  </button>

  <button mat-menu-item>
    <mat-icon [icIcon]="icVerifiedUser"></mat-icon>
    <span>Change Username</span>
  </button>

  <button mat-menu-item>
    <mat-icon [icIcon]="icLock"></mat-icon>
    <span>Change Password</span>
  </button>

  <button mat-menu-item>
    <mat-icon [icIcon]="icNotificationsOff"></mat-icon>
    <span>Disable Notifications</span>
  </button>
</mat-menu> -->
