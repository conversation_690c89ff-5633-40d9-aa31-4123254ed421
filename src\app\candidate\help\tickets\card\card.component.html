<div class="flex title items-center justify-between">
    <div class="">
        <h3 class="text-xs font-bold fontColor1">{{ name }}</h3>
        <!-- not sure if this is the correct data -->
        <p class="text-xs item-label1">State/Eligibility Route: {{ eligibilityRouteName }}</p>
    </div>
    <button *ngIf="getHelp" class="px-4 button-help text-xs" (click)="navigateToGetHelp()">Get Help</button>
    <!-- <a *ngIf="getHelp" class="px-4 button-help text-xs" [routerLink]="['../',  id, 'get-help']">Get Help</a> -->
    <div *ngIf="!ticketRaised" class="raisedBtn_container">
        <button *ngIf="!getHelp" class="px-4 button-help text-xs"  (click)="navigateToRaiseTicket()">Raise Ticket</button>
    </div>
</div>   
<!-- <button (click)="getTicket(id)">Get Help</button> -->
<div *ngIf="applicationId" class="flex justify-between">
    <div class="p-4">
        <p class="text-xs label-tckt">Application ID</p>
        <!-- not sure if this is the correct data -->
        <p class="text-xs">{{ applicationId}}</p>
    </div>
    <div class="p-4">
        <p class="text-xs label-tckt">Submitted Date</p>
        <!--  missing in the api -->
        <p class="text-xs">{{ createdDate | date:'MM/dd/yyyy' }}</p>
    </div>
    <div class="p-4">
        <p class="text-xs label-tckt">Current Status</p>
        <p class="text-xs" [style.color]="
        status == 'Drafted' || status == 'Pending'
            ? '#EE9400'
            : status == 'Approved'
            ? '#00AB72'
            : '#F7685B'
        ">{{ status }}</p>
        <!-- missing in the api -->
        <p class="text-xs">{{ changedDate | date:'MM/dd/yyyy' }}</p>
    </div>
</div>
<p *ngIf="ticketRaised" class="text-xs p-4" style="color:#00AB72">
    Ticket Raised
</p>

