import { createReducer, on } from "@ngrx/store";
import { ClearCandidateChatState, gotAvailableStaffInfo, staffNotAvailable } from './candidate-chat.actions';
import { CandidateChatState,initialCandidateChatState } from "./candidate-chat.state"; 

const _candidateChatReducer = createReducer<CandidateChatState>(
  initialCandidateChatState,
  on(ClearCandidateChatState, (state, action) => {
    return {
      ...state,
      connectedSupportStaffInfo:null
    }
  }), on(gotAvailableStaffInfo, (state, action) => {
    return {
      ...state,
      connectedSupportStaffInfo:action
    }
  }), 
  on(staffNotAvailable, (state, action) => {
    return {
      ...state,
      errorMessage:action.message
    }
  }),
)

export function CandidateChatReducer(state, action) {
  return _candidateChatReducer(state, action);
}

