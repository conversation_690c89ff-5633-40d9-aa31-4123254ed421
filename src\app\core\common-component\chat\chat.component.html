<div class="flex-1 p:1 sm:p-3 justify-between flex flex-col rounded shadow-6" [style.background]="'rgba('+(248-index*10 > 0 ? 248-index*10 : 0).toString()+','+(248-index*10 > 0 ? 248-index*10 :
     0).toString()+','+(248-index*10 > 0 ? 248-index*10 : 0).toString()+')'">
    <div class="flex sm:items-center justify-between py-3 border-b-2 border-gray-200">
        <div class="flex items-center space-x-4">
            <div class="flex flex-col leading-tight">
                <div class="text-2l mt-1 flex flex-col items-center">
                    <span class="text-gray-700 mr-3">{{
                        senderID == connection.agentPeerjsID ?
                        (connection.clientORcandidateName ? connection.clientORcandidateName:
                        connection.clientORcandidatePeerjsID.substr(0,7))
                        :
                        (connection.agentName ? connection.agentName: connection.agentPeerjsID)}}</span>
                    <span class="text-gray-300 mr-3 font-light text-sm">
                        {{connection.$chatConnectionStatus | async}}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <button type="button" matBadge="!" matBadgeColor="warn" [matBadgeHidden]="!($newMsgBadge | async)" class="inline-flex items-center justify-center rounded-full h-10 w-10 transition duration-500 ease-in-out text-gray-500 hover:bg-gray-300 focus:outline-none">
                <mat-icon [matTooltip]="'Expand'"
                          class="h-6 w-6"
                          *ngIf="($collapsed |async)"
                          (click)="collapsed.next(false);newMsgBadge.next(false);">fullscreen
                </mat-icon>
                <mat-icon [matTooltip]="'Collapse'"
                          class="h-6 w-6"
                          *ngIf="!($collapsed |async)"
                          (click)="collapsed.next(true);">fullscreen_exit
                </mat-icon>
            </button>
            <button *ngIf="senderID == connection.clientORcandidatePeerjsID" type="button" class="inline-flex items-center justify-center rounded-full h-10 w-10 transition duration-500 ease-in-out text-gray-500 hover:bg-gray-300 focus:outline-none">
                <mat-icon [matTooltip]="'Share Screen'"
                          class="h-6 w-6"
                          *ngIf="!connection.clientORcandidateRemoteStream.value"
                          (click)="shareCandidateScreen()">screen_share
                </mat-icon>
                <mat-icon [matTooltip]="'Stop Share Screen'"
                          class="h-6 w-6"
                          *ngIf="connection.clientORcandidateRemoteStream.value"
                          (click)="stopScreenShare()">stop_screen_share
                </mat-icon>
            </button>
            <button *ngIf="senderID == connection.agentPeerjsID" type="button" class="inline-flex items-center justify-center rounded-full h-10 w-10 transition duration-500 ease-in-out text-gray-500 hover:bg-gray-300 focus:outline-none">
                <mat-icon [matTooltip]="'Enlarge Candidate Screen Share'"
                          class="h-6 w-6"
                          *ngIf="!fullscreenStream.value"
                          (click)="fullscreenStream.next(connection.clientORcandidateRemoteStream.value)">
                    photo_size_select_large
                </mat-icon>
                <mat-icon [matTooltip]="'Minimise Candidate Screen Share'"
                          class="h-6 w-6"
                          *ngIf="fullscreenStream.value"
                          (click)="removeFullScreenStream()">
                    photo_size_select_small
                </mat-icon>
            </button>
            <button type="button" class="inline-flex items-center justify-center rounded-full h-10 w-10 transition duration-500 ease-in-out text-gray-500 hover:bg-gray-300 focus:outline-none">
                <mat-icon class="h-6 w-6"
                          [matTooltip]="'Close Chat Connection'"
                          (click)="closeChat()">
                    cancel
                </mat-icon>
            </button>
        </div>
    </div>
    <div class="w-full flex" [ngClass]="(!($collapsed |async) && senderID != connection.clientORcandidatePeerjsID && connection.clientORcandidateRemoteStream.value) ? 'h-48':'h-0'" *ngIf="connection.clientORcandidateRemoteStream" [id]="connection.clientORcandidatePeerjsID+'remote-video'">
    </div>
    <div *ngIf="!($collapsed |async)" id="messages" class="flex flex-col space-y-4 py-3 overflow-y-auto scrollbar-thumb-blue scrollbar-thumb-rounded scrollbar-track-blue-lighter scrollbar-w-2 scrolling-touch" [style.max-height]="'58vh'">
        <div class="chat-message" *ngFor="let msg of connection.conversation;let msgIndex = index;" [id]="'chatMessage'+msgIndex.toString()">
            <div *ngIf="msg.sender.senderPeerjsID == this.senderID" class="flex flex-col space-y-2 text-s  mx-2 order-2 items-start text-right">
                <div class="w-full p-2 m-0 flex flex-col item-start msg-background">
                    <span class="rounded-lg inline-block rounded-bl-none text-green" *ngIf="msg.sender.senderPeerjsID == this.senderID; else client" class="text-green">You</span>
                    <ng-template #client>
                        <span class="rounded-lg inline-block rounded-bl-none text-red">{{senderID ==
                            connection.clientORcandidatePeerjsID ? connection.agentName :
                            connection.clientORcandidateName}}</span>
                    </ng-template>
                    <span class="rounded-lg inline-block rounded-bl-none" [innerHTML]="msg.text">
                        <!-- {{msg.text}} -->
                    </span>
                    <span class="rounded-lg inline-block rounded-bl-none  text-2xs">
                        {{msg.timestamp | date:'medium' }}
                    </span>
                </div>

            </div>
            <div *ngIf="msg.sender.senderPeerjsID != this.senderID" class="flex flex-col space-y-2 text-s  mx-2 order-2 items-end text-left">
                <div class="w-full p-2 m-0 flex flex-col item-end ">
                    <span class="rounded-lg inline-block rounded-bl-none text-green" *ngIf="msg.sender.senderPeerjsID == this.senderID; else client" class="text-green">You</span>
                    <ng-template #client><span class="rounded-lg inline-block rounded-bl-none text-red">{{senderID ==
                            connection.clientORcandidatePeerjsID ? connection.agentName : connection.clientORcandidateName}}</span>
                    </ng-template>
                    <span class="rounded-lg inline-block rounded-bl-none " [innerHTML]="msg.text">
                        <!-- {{msg.text}} -->
                    </span>
                    <span class="rounded-lg inline-block rounded-bl-none  text-2xs">
                        {{msg.timestamp | date:'medium' }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="!($collapsed |async)" class="text-black border-t-2 border-gray-200 px-4 pt-4 mb-2 sm:mb-0">
        <div class="relative flex">
            <div class="w-full flex flex-col">
                <!-- <ngx-editor-menu class="m-2" [editor]="editor" [toolbar]="toolbar">
                </ngx-editor-menu> -->
                <ngx-editor class="w-full h-full" [editor]="editor" [formControl]="messageControl"  class="w-full focus:outline-none focus:placeholder-gray-400 text-gray-600 placeholder-gray-600 pl-6 bg-gray-200 rounded-full py-3">
                </ngx-editor>
            </div>
            <!-- <input (keydown.enter)="sendMessageHandler()" [formControl]="messageControl" type="text" placeholder="Write Something" class="w-full focus:outline-none focus:placeholder-gray-400 text-gray-600 placeholder-gray-600 pl-6 bg-gray-200 rounded-full py-3"> -->
            <div class="absolute right-0 items-center inset-y-0 sm:flex">
                <button (click)="sendMessageHandler()" type="button" class="inline-flex items-center justify-center rounded-full h-10 w-10 transition duration-500 ease-in-out text-gray-500 hover:bg-gray-300 focus:outline-none">
                    <mat-icon>send</mat-icon>
                </button>
            </div>
        </div>
        <div *ngIf="senderID == connection.agentPeerjsID">
            <mat-autocomplete (optionSelected)="selectResponse($event)" class="w-full" #auto="matAutocomplete">
                <mat-option
                *ngFor="let c of  responsesList"
                [value]="c.value"
                >{{c.label}}</mat-option>
              </mat-autocomplete>
            
              <input
              [(ngModel)]="searchText"
              [formControl]="searchControl"
              class="px-3 py-4 outline-none w-full bg-transparent xs"
              type="search"
              autofocus
              (input) = "filterUsers()"
              placeholder="Type here for suggestion's"
              [matAutocomplete]="auto"
            />
        </div>
    </div>
</div>