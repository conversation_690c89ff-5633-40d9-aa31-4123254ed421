import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgDynamicBreadcrumbService } from 'ng-dynamic-breadcrumb';
import { LanguageService } from 'src/app/core/language.service';
import { bundleList } from '../practice-skills.data';

@Component({
  selector: 'exai-view-bundle',
  templateUrl: './view-bundle.component.html',
  styleUrls: ['./view-bundle.component.scss']
})
export class ViewBundleComponent implements OnInit {

  bundleData: any = null;

  constructor(
    private route: ActivatedRoute,
    public lngSrvc: LanguageService,
    private _ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
  ) {}

  ngOnInit(): void {
    // this._ngDynamicBreadcrumbService.updateBreadcrumb([
    //   { label: 'Home', url: '/' },
    //   { label: this.lngSrvc.curLangObj.value.practice_skills, url: '/candidate/practice-skills/' },
    //   { label: this.lngSrvc.curLangObj.value.practice_skills_view, url: '' }
    // ]);
  

    const bundleId = this.route.snapshot.paramMap.get('id');
    if (!this.bundleData && bundleId) {
      const idNum = +bundleId;
      this.bundleData = bundleList.find(b => b.id === idNum);
      if (!this.bundleData) {
        console.warn(`No bundle found with id ${bundleId}`);
      }
    }
  }
}
