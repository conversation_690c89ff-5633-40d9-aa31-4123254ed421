import { createAction, props } from "@ngrx/store";
import { GrievanceFormsList, ReportGrievance, SaveGrievnaceForm, StatusLog, upcomingExam, ViewGrievanceForm } from "./grievance.model";

export const loadAll = createAction('[GRIEVANCE] Load All');

export const loadAllSuccess = createAction(
  '[GRIEVANCE] Load All Success',
  props<{ data: GrievanceFormsList[] }>()
);

export const loadAllFailure = createAction(
  '[GRIEVANCE] Load All Failure',
  props<{ error: any }>()
);

export const loadReportGrievance = createAction('[GRIEVANCE] Load Report');

export const loadReportGrievanceSuccess = createAction(
  '[GRIEVANCE] Load Report Success',
  props<{ data: ReportGrievance }>()
)

export const loadReportGrievanceFailure = createAction(
  '[GRIEVANCE] Load Report Failure',
  props<{ error: any }>()
)

export const requestReportGrievance = createAction(
  '[GRIEVANCE] Request Save Report Grievance',
  props<{ params: SaveGrievnaceForm }>()
);

export const postReportGrievanceSuccess = createAction(
  '[GRIEVANCE] Post Save Report Grievance Success',
  props<{ data: any }>()
)

export const postReportGrievanceFailure = createAction(
  '[GRIEVANCE] Post Save Report Grievance Failure',
  props<{ error: any }>()
)

export const requestSaveDraftReportGrievance = createAction(
  '[GRIEVANCE] Request Save Draft Report Grievance',
  props<{ params: SaveGrievnaceForm }>()
);

export const postSaveDraftReportGrievanceSuccess = createAction(
  '[GRIEVANCE] Post Save Draft Report Grievance Success',
  props<{ data: any }>()
)

export const postSaveDraftReportGrievanceFailure = createAction(
  '[GRIEVANCE] Post Save Draft Report Grievance Failure',
  props<{ error: any }>()
)

export const requestViewGrievance = createAction(
  '[GRIEVANCE] Request View Grievance',
  props<{ params: ViewGrievanceForm }>()
);

export const postViewGrievanceSuccess = createAction(
  '[GRIEVANCE] Post View Grievance Success',
  props<{ data: any }>()
)

export const postViewGrievanceFailure = createAction(
  '[GRIEVANCE] Post View Grievance Failure',
  props<{ error: any }>()
)

export const requestPersonProgress = createAction(
  '[GRIEVANCE] Get Request Person Progress',
  props<{ personFormId: number }>()
)

export const getPersonProgressSuccess = createAction(
  '[GRIEVANCE] Get Request Person Progress Success',
  props<{ data: Array<StatusLog> }>()
)

export const getPersonProgressFailure = createAction(
  '[GRIEVANCE] Get Request Person Progress Failure',
  props<{ error: any }>()
)
export const cancelExam = createAction('[Cancel] cancel Exam', props<{personFormId:number, candidateId: number}>())


export const Examcancelled = createAction('[Cancel] FormCancelled',
props<{isCancelled:number}>())



export const getupcomingExam = createAction('[Froms] GET upcomingExam',
  props<{ candidateId: number }>())

export const gotupcomingExam = createAction('[Froms] GOT upcomingExam',
  props<{ upcomingExam: upcomingExam[] }>())


  

export const clearGrievanceState = createAction('[CLEAR] Clearing Grievance Store');