import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnInit,
  Renderer2,
  ViewChild,
} from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MatMenuTrigger } from "@angular/material/menu";
import { Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import moment from "moment";

import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { getCartItems } from "../../state/shared/shared.actions";
import {
  get_cartItems,
  get_decodeInfo,
} from "../../state/shared/shared.selectors";
import { ScheduledService } from "../scheduled.service";
import {
  addCartTC,
  clearTimeSlotsTestCenter,
  getEligibilityroute,
  getExamByERIdId,
  getTimeSlotsTestCenter,
  getTimezones,
  gotTimeSlotsTestCenter,
  removeCartItem,
  reschedule,
} from "../state/scheduled.actions";
import {
  selectorGetTimezones,
  selectorGetTimeSlotsTestCenter,
  selectorGetCart,
  selectorGetExamId,
  selectorGetCartDeleteStatus,
  selectorGetRescheduledResponse,
  selectorGetEligibilityroute,
  selectorGetTimeSlotsTestCenterStatus,
} from "../state/scheduled.selectors";
import { ScheduledState } from "../state/scheduled.state";
import { DatePipe, Location } from "@angular/common";
import { Slot, slot, slotDates } from "../state/models/slot";
import {
  BehaviorSubject,
  interval,
  Observable,
  Subject,
  Subscription,
} from "rxjs";
import { ExamModel } from "src/app/core/Dto/exam.model";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { ReschudelPaymentComponent } from "../reschudel-payment/reschudel-payment.component";
import { HttpService } from "src/app/core/http-services/http.service";
import { schedule } from "../state/models/Exam";
import { DecodedIdentityToken } from "../../candiate.types";
import { FormTypes } from "src/app/core/Dto/enum";
import { StateList } from "../../forms-wrapper/forms-wrapper.types";
import { MapsAPILoader } from "@agm/core";
import { StateAllowTestCenterforOnline } from "src/app/core/examroom-formbuilder/form-builder.types";
import { parseISO } from "date-fns";
import { CartSummaryPopupComponent } from "../cart-summary-popup/cart-summary-popup.component";
import { filter } from "rxjs/operators";

@Component({
  selector: "exai-test-center",
  templateUrl: "./test-center.component.html",
  styleUrls: ["./test-center.component.scss"],
})
export class TestCenterComponent implements OnInit {
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;
  @Input() events: Observable<any>;
  checkbox: boolean = false;
  isChecked: boolean = false;
  @Input() data: Observable<any>;
  radioselect = new FormControl("Search a Test Center by Mileage Radius");
  radioSelectTypeSlotViewselect = new FormControl("List View");
  // paymentModeStatus = new FormControl('MI');
  TSMSLOTS: boolean;
  AcceptCondidtion:string
  expanded = -1;
  markerclickLan: number;
  latitude;
  selectedTypeViewSlot: number;
  longitude;
  value: number = 0;
  loading: boolean = false;
  rescheduleloading: boolean = false;
  scheduleloading: boolean = false;
  selectedDayTestcenter: object = { testCenter: "" };
  allowRetryschedule = allowSchedule;
  minDate = new Date();
  FilterTestcenterSlot: slot[] = [];
  cartId: any;
  SlotDates: Array<object> = [];
  step: any = 0;
  userdata;
  slotsDates = [];
  TestCenterDetails: boolean = false;
  scheduleagainEnable: number;
  selectedSlots: any;
  timeslots: any[] = [];
  Validators: FormGroup;
  testCenterLocation: FormGroup;
  cardnumber: HTMLInputElement;
  range: FormGroup;
  timezones: any[] = [];
  testCenters: any;
  selectedExamTime: Date;
  selectedTimeSlot: any;
  selectedTestCenter: any;
  allUniqueDates: any[] = [];
  selectedDateTestCenters: Array<object> = [];
  @Input() selectedExam: any;
  @Input() Examtype: number;
  @Input() eplasedDateTime;
  isClicked = false;
  daySlots: any[] = [];
  selectedTestCenterSoltDate: string;
  timeSlots: any[] = [];
  cannotSearch: boolean = true;
  maxDate = new Date();
  onlyDates: any[] = [];
  displayPaymentMethods: boolean = false;
  examModels: ExamModel[];
  payment: boolean = true;
  Addcart: boolean = true;
  rescheduled: boolean = false;
  datetime: any;
  private currentSlide = 0;
  initialTabSelection: boolean = false;
  ExamDateTime: any;
  selectedtestSiteId: any;
  showStepper: boolean = false;
  avaiableslots: Array<any> = [];
  private unsubscribe: Subject<void> = new Subject<void>();
  availableDates: Slot[] = [];
  Inf: string = "";
  testsiteId: any;
  cartItemList: any;
  isSelectDate: boolean = false;
  cardnumber_mask: any;
  time;
  Date;
  GetUserDetails;
  examCenter: "No Direction Found";
  validation_messages = {
    testId: [{ type: "pattern", message: this.global.INF_Validation_message }],
  };
  examTypeModels = [
    { id: 1, name: "Search a Test Center by Mileage Radius", checked: false },
    { id: 2, name: "Search a Test Center by Code", checked: false },
  ];
  SelectedTypeSlotView = [
    { id: 1, name: "List View", checked: false },
    { id: 2, name: "Map View", checked: false },
  ];

  examName: any;
  isSearching: boolean = false;
  constructor(
    private fb: FormBuilder,
    private store: Store<ScheduledState>,
    private renderer: Renderer2,
    public global: GlobalUserService,
    private datepipe: DatePipe,
    private elem: ElementRef,
    private router: Router,
    private snackbar: SnackbarService,
    private _services: ScheduledService,
    private location: Location,
    private dialogRef: MatDialogRef<ReschudelPaymentComponent>,
    private http: HttpService,
    private mapsAPILoader: MapsAPILoader,
    private cdr: ChangeDetectorRef,
    private dialogs: MatDialog
  ) {
  
  }

  /**
   * Setting initial values for time zones and validators.
   */

  next() {
    // if (this.currentSlide + 1 === this.items.length) return;
    this.currentSlide = (this.currentSlide + 1) % this.allUniqueDates.length;
    // this.transitionCarousel();
  }
  prev() {
    // if (this.currentSlide === 0) return;
    this.currentSlide =
      (this.currentSlide - 1 + this.allUniqueDates.length) %
      this.allUniqueDates.length;
    // this.transitionCarousel();
  }
  ngOnInit(): void {
    // Stop searching spinner when API call is done with success
    this.store
      .select(selectorGetTimeSlotsTestCenterStatus)
      .pipe(filter((status) => status === "success" || status === "error"))
      .subscribe(() => {
        // Stop searching spinner
        this.isSearching = false;
      });

    //  console.log(sessionStorage.getItem(''))

    this.http.getAccountPersonDetails().subscribe((data: any) => {
      if (data) {
        this.GetUserDetails = data;
        this.setValidators();
      }

      this.store.select(selectorGetEligibilityroute).subscribe((route) => {
        if (route) {
          this.examName = route;
        }
      });
    });
    this.events.subscribe((data) => {
      if (data) {
        this.Validators.reset();
        this.range.reset();
        this.availableDates = [];
        this.selectedDateTestCenters = [];
        this.displayPaymentMethods = false;
        this.testCenterLocation.patchValue({ testRadius: "100" });
      }
    });
    this.setValidators();
    this.getTimeZones();
    this.showStepper = false;

    this.store.select(selectorGetExamId).subscribe((exams: ExamModel[]) => {
      if (exams != null && exams.length > 0) {
        this.examModels = exams;
        if (this._services.rescheduleInformation) {
          this.scheduleagainEnable =
            this._services.rescheduleInformation.examStatusId ||
            this._services.rescheduleInformation.statusId;
          const data = exams.find(
            (ele) => ele.title == this._services.rescheduleInformation.examName
          );
          if (data) {
            this.payment = false;
            this.Addcart = false;
            this.rescheduled = true;
          } else {
            this.payment = true;
            this.rescheduled = false;
            this.Addcart = true;
          }
        }
      }
    });

    this.getSelectedTypeSlotViewRoute(1);

    this.store.select(selectorGetTimeSlotsTestCenter).subscribe(
      (data: any) => {
        if (data) {
          this.TSMSLOTS = data.isTSM;
          if (data.isTSM == false) {
            this.avaiableslots = [];
            this.avaiableslots.push(data.conductor);
            let tempDatesSlots: slotDates[] = [];
            if (this.avaiableslots[0].slots != null) {
              this.avaiableslots.forEach((item) =>
                item.slots.forEach((x) => {
                  tempDatesSlots.push(x);
                })
              );
              this.slotsDates = data.conductor.slotDates;
              this.testCenters = this.avaiableslots[0].slots;
              if (this.avaiableslots.length > 0) {
                this.isSelectDate = true;
                this.allUniqueDates = this.setAllUniqueDates(
                  this.createDateArray(data.conductor.slots)
                );
                const time: any = tempDatesSlots.filter(
                  (ele: any) => ele.slots.length > 0
                );
                time.length > 0
                  ? (this.setTestCentersForSelectedDate(
                      time[0].slots[0].slotDate
                    ),
                    this.selectedDate(time[0].slots[0]),
                    this.selectedDateTime(time[0].slots[0]),
                    this.setStep(0))
                  : null;
              }
            } else {
              this.snackbar.callSnackbaronWarning(
                "No slots available for the selected date"
              );
              this.selectedDateTestCenters = [];
              this.selectedDate(data);
              this.displayPaymentMethods = false;
              this.isSelectDate = false;
            }
          } else if (data.isTSM == true) {
            this.avaiableslots = [];
            this.slotsDates = [];
            this.selectedDateTestCenters = [];
            this.FilterTestcenterSlot = [];
            this.global.No_slot_avaiable.next(null);
            this.avaiableslots.push(data.tsm);
            let tempDatesSlots: slotDates[] = [];
            if (this.avaiableslots[0].length > 0) {
              const sortedArray = [...this.avaiableslots[0]].sort((a, b) => {
                const dateA = new Date(a.day).getTime();
                const dateB = new Date(b.day).getTime();
                if (isNaN(dateA)) return 1; // Handle invalid dates
                if (isNaN(dateB)) return -1; // Handle invalid dates

                return dateA - dateB;
              });
              this.avaiableslots[0] = sortedArray;
              this.avaiableslots[0].forEach((item) =>
                item.testCenters.forEach((x) => {
                  tempDatesSlots.push(x);
                })
              );
              data.tsm.forEach((x) => {
                this.slotsDates.push(x);
                this.slotsDates = this.slotsDates.sort(
                  (a, b) =>
                    new Date(a.day).getTime() - new Date(b.day).getTime()
                );
              });
              this.testCenters = tempDatesSlots;
              if (this.avaiableslots.length > 0) {
                this.isSelectDate = true;
                this.allUniqueDates = this.setAllUniqueDates(
                  this.createDateArray(tempDatesSlots)
                );
                if (this.selectedTypeViewSlot == 1) {
                  var time: any = tempDatesSlots.filter(
                    (ele: any) => ele.siteSlot.length > 0
                  );
                  time.length > 0
                    ? (this.setTestCentersForSelectedDate(
                        time[0].siteSlot[0].examdate
                      ),
                      this.selectedDate(time[0].siteSlot[0]),
                      this.selectedDateTime(time[0].siteSlot[0]),
                      this.setStep(0))
                    : null;
                }
              }
            } else {
              this.snackbar.callSnackbaronWarning(
                "No slots available for the selected date"
              );
              this.global.No_slot_avaiable.next(
                "No slots available for the selected date"
              );
              this.selectedDateTestCenters = [];
              this.selectedDate(data);
              this.displayPaymentMethods = false;
              this.isSelectDate = false;
            }
          }
        }
      },
      (err) => {
        this.snackbar.callSnackbaronWarning(`${err.message.message.error}`);
      }
    );
    this.store
      .select(get_decodeInfo)
      .subscribe((data: DecodedIdentityToken) => {
        if (data) this.userdata = `${data.given_name} ${data.family_name}`;
      });
    setTimeout(() => {
      if (
        this.eplasedDateTime != "" &&
        this.eplasedDateTime != null &&
        this.eplasedDateTime != undefined
      ) {
        this.maxDate = parseISO(this.eplasedDateTime);
        this.maxDate.setDate(this.maxDate.getDate() - 1);
      } else {
        this.maxDate = null;
      }
    }, 1200);
    let n = Intl.DateTimeFormat().resolvedOptions();

    this.time = moment(new Date()).tz(n.timeZone).format("h:mm a z");
    this.Date = moment(new Date()).format("DD/MM/YYYY");
  }

  event(i, item: any) {
    this.selectedtestSiteId = i;
    this.ExamDateTime =
      item.slotDateUtc != null ? item.slotDateUtc : item.examdateUTC;
    this.checkbox = (item.examCode === "VS-PR" || item.examCode ==='CBT-WR') ? true : false;
    this.AcceptCondidtion = item.examCode ==="VS-PR"?'I acknowledge and agree that my skills exam will take place at a test center, with the scoring conducted remotely by a live evaluator, and the entire exam will be recorded for quality purposes.':'I acknowledge and agree that my written exam will be administered at a test center in a group setting, where outside noise may occur. Additionally, the entire exam will be recorded for quality assurance purposes. '
    this.isChecked = (item.examCode != "VS-PR" && item.examCode !='CBT-WR')  ? false : false;
    this.selectedSlots = item;
    this.displayPaymentMethods =
      (item.examCode === "VS-PR" || item.examCode ==='CBT-WR') && this.isChecked
        ? true
        : (item.examCode === "VS-PR" || item.examCode ==='CBT-WR') && this.isChecked == false
        ? false
        : true;
    if (this.data) {
      this.payment = false;
    }
  }

  showOptions($event) {
    if ($event.checked === true) {
      this.isChecked = true;
      this.displayPaymentMethods = true;
    } else {
      this.isChecked = false;
      this.displayPaymentMethods = false;
    }
  }

  getSelectedTypeSlotViewRoute(id: number, type?: string) {
    this.selectedTypeViewSlot = id;
    if (id == 2) {
      this.findCoordinates();
    } else {
      this.markerclickLan = null;
      type != "" && type != null
        ? this.radioSelectTypeSlotViewselect.setValue(type)
        : null;
      type != "" && type != null ? this.cdr.detectChanges() : null;
    }
  }
  eventHandler(event) {
    if (
      (event.target.value.length == 3 &&
        (event.code == "Backspace" || event.code == "Delete")) ||
      (event.target.value.length > 3 && event.code == "Delete") ||
      event.code === "KeyX" ||
      (event.target.value == 3 && event.code === "KeyV") ||
      event.code == "KeyA"
    ) {
      return false;
    }
  }

  findCoordinates() {
    let userAddress = `${this.GetUserDetails?.address} ${this.GetUserDetails?.city} ${this.GetUserDetails?.zipCode}`;
    let address =
      userAddress == this.testCenterLocation.value.testAddress
        ? this.GetUserDetails.zipCode
        : this.testCenterLocation.value.testAddress;
    if (!address) {
      return;
    }

    this.mapsAPILoader.load().then(() => {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ address: address }, (results, status) => {
        if (status === google.maps.GeocoderStatus.OK) {
          this.SlotDates = [];
          this.latitude = results[0].geometry.location.lat();
          this.longitude = results[0].geometry.location.lng();
          this.slotsDates.forEach((x: any) =>
            x.testCenters.forEach((y: any) => {
              x.day != ""
                ? this.SlotDates.push({
                    day: x.day,
                    testCenters: [
                      {
                        distance: y.distance,
                        latitude: y.latitude,
                        longitude: y.longitude,
                        siteSlot: y.siteSlot,
                        testCenter: y.testCenter,
                        testCenterId: y.testCenterId,
                        timeZone: y.timeZone,
                        address: y.address,
                        iconUrl: {
                          url: "assets/img/testcenter_icon.gif",
                          scaledSize: {
                            width: 30,
                            height: 40,
                          },
                        },
                      },
                    ],
                  })
                : null;
            })
          );

          this.slotsDates = [
            {
              day: "",
              testCenters: [
                {
                  latitude: this.latitude,
                  longitude: this.longitude,
                  siteSlot: [],
                  testCenter: `${this.GetUserDetails.firstName} ${this.GetUserDetails.middleName} ${this.GetUserDetails.lastName}`,
                  iconUrl: {
                    url: "assets/img/candidate_icon.png",
                    scaledSize: {
                      width: 30,
                      height: 40,
                    },
                  },
                },
              ],
            },
            ...this.SlotDates,
          ];
        } else {
          this.snackbar.callSnackbaronError(
            "Coordinates not found for the given ZIP code."
          );
          setTimeout(() => {
            this.getSelectedTypeSlotViewRoute(1, "List View");
          }, 2500);
        }
      });
    });
  }

  isActive(item: any) {
    return this.selectedSlots === item;
  }
  selectedDate(event) {
    this.ExamDateTime = null;
    this.step = event.strSlotDate
      ? event.strSlotDate
      : event.examdate
      ? event.examdate
      : event.day;
  }

  /**
   * Getting time zones from API
   */
  getTimeZones() {
    this.store.dispatch<Action>(getTimezones());
    this.store.select(selectorGetTimezones).subscribe((timezones) => {
      this.timezones = timezones;
    });
  }
  /**
   * API call for getting timeSlots
   */
  public getTimeSlots(): void {
    if (this.isSearching) return; // prevent multiple clicks

    let userAddress = `${this.GetUserDetails.address} ${this.GetUserDetails.city} ${this.GetUserDetails.zipCode}`;
    if (
      (this.testCenterLocation.value.testAddress !== "" ||
        this.testCenterLocation.value.testRadius !== "") &&
      (this.testCenterLocation.value.testId !== "" ||
        this.testCenterLocation.value.testName !== "")
    ) {
      this.showStepper = false;
      if (this.Validators.valid && this.range.valid) {
        this.isSearching = true; // Start searching spinner
        this.store.dispatch<Action>(
          getTimeSlotsTestCenter({
            timezone: "Eastern Standard Time",
            examId: this.selectedExam.id,
            startDate: moment(this.range.value.start).format("YYYY-MM-DD"),
            endDate: moment(this.range.value.end).format("YYYY-MM-DD"),
            testCenterName: this.testCenterLocation.value.testName,
            testCenterId: `${this.Inf}${this.testCenterLocation.value.testId}`,
            testCenterAddress:
              userAddress === this.testCenterLocation.value.testAddress
                ? this.GetUserDetails.zipCode
                : this.testCenterLocation.value.testAddress,
            radius: this.testCenterLocation.value.testRadius,
            candidateId: this.global.personId,
            isOnline:
              this.selectedExam.title === "Nurse Aide Skills Exam" &&
              this.Examtype === 1 &&
              StateAllowTestCenterforOnline.includes(
                this.global.userDetails.getValue().stateId
              )
                ? true
                : false,
          })
        );
      } else {
        this.range.reset();
        this.snackbar.callSnackbaronWarning("Please Select Date Range");
      }
    } else {
      this.isSelectDate = false;
      this.snackbar.callSnackbaronWarning("Please enter any one of the fields");
    }
  }

  /**
   *
   * @param data - from API about all test-centers
   * @returns all available dates for exam
   */
  createDateArray(data) {
    let array = [];
    data.forEach((elem1) => {
      let elem = elem1.slots != null ? elem1.slots : elem1.siteSlot;
      elem.forEach((elem2) => {
        let date = new Date(
          Date.parse(elem2?.slotDate != null ? elem2.slotDate : elem2.examdate)
        );
        array.push(date);
      });
    });
    return array;
  }

  /**
   *
   * @param data - all available dates for exam
   * @returns - unique dates
   */
  setAllUniqueDates(data: any[]) {
    data.forEach((element) => {
      element.setHours(0, 0, 0, 0);
    });
    //data.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    return Array.from(new Set(data));
  }

  selectedDateTime(slot) {
    this.ExamDateTime = slot.slotDateUtc
      ? slot.slotDateUtc
      : slot.examdate
      ? slot.examdate
      : slot.day;
    this.avaiableslots = slot.strSlotTime;
    this.selectedTestCenterSoltDate = slot.strSlotDate
      ? slot.strSlotDate
      : slot.examdate
      ? slot.examdate
      : slot.day;
    this.displayPaymentMethods = false;
  }

  /**
   *
   * @param slot - slected date for exam
   * fills selectedDateTestCenters for filtering
   */
  setTestCentersForSelectedDate(slotDate: string) {
    var datePipe = new DatePipe("en-US");
    let slotDates =
      this.TSMSLOTS == true
        ? datePipe.transform(slotDate, "MM/dd/yyyy", "+0000")
        : slotDate;
    let date = new Date(Date.parse(slotDates));
    const slot = this.allUniqueDates.find(
      (x) => x.toDateString() === date.toDateString()
    );
    if (slot) {
      this.selectedDateTestCenters = [];

      for (let i = 0; i < this.testCenters.length; i++) {
        let testcenter =
          this.testCenters[i].slots != null
            ? this.testCenters[i].slots
            : this.testCenters[i]?.siteSlot;

        for (let j = 0; j < testcenter.length; j++) {
          let slotdate =
            testcenter[j].slotDate != null
              ? testcenter[j].slotDate
              : testcenter[j].examdate;

          let comparedDate = new Date(Date.parse(slotdate)).setHours(
            0,
            0,
            0,
            0
          );

          if (comparedDate == slot.getTime()) {
            let Values =
              this.testCenters[i].testCenter == this.selectedDayTestcenter;
            Values && this.selectedTypeViewSlot == 2 && this.TSMSLOTS == true
              ? this.selectedDateTestCenters.push(this.testCenters[i])
              : this.TSMSLOTS == false
              ? this.selectedDateTestCenters.push(this.testCenters[i])
              : this.selectedTypeViewSlot == 1 && this.TSMSLOTS == true
              ? this.selectedDateTestCenters.push(this.testCenters[i])
              : null;

            break;
          }
        }
      }
    }
  }

  /**
   *
   * @param slot - selected time slot
   * @param item - selected testCenter
   */
  setSelectedTestCenterAndTime(slot, item) {
    this.selectedTestCenter = item;
    this.selectedTimeSlot = slot;
  }
  /**
   * Function for adding exam into cart.
   */
  addToCart() {
    if (this.selectedExam.page == "PopPage") {
      // this.store.dispatch<Action>(
      //   getCartItems({ personTenantRoleId: this.global.candidateId })
      // )

      // let cartCall=this.store.select(get_cartItems).subscribe((cartItems) => {
      //   if (cartItems != null) {
      //     var cartList=cartItems.filter(item=>item.examId==this.selectedExam.id)
      //     if(cartItems.find(item=>item.examId==this.selectedExam.id)){
      //       this.store.dispatch<Action>(
      //         removeCartItem({ tetantId: this.global.candidateId, cartItemsId: Number(cartList[0].personEventCartId) })
      //       );
      //     }
      //   }

      //   });

      //   this.store.select(selectorGetCartDeleteStatus).subscribe((data: any) => {
      //     if (data) {
      //       setTimeout(()=>{
      //         cartCall.unsubscribe();
      if (this.TSMSLOTS == false) {
        this.store.dispatch<Action>(
          addCartTC({
            body: {
              personTenantRoleId: this.global.candidateId,
              amount: this.selectedExam.price,
              cartItemTypeId: 1,
              currencyId: 1,
              examDetail: {
                candidateId: this.global.candidateId,
                examId: this.selectedExam.id,
                timeZone: this.selectedSlots.timezoneId,
                // offSet: this.Validators.value.timezone.offset,
                examModeId:
                  this.selectedExam.title === "Nurse Aide Skills Exam" &&
                  this.Examtype == 1 &&
                  StateAllowTestCenterforOnline.includes(
                    this.global.userDetails.getValue().stateId
                  )
                    ? 1
                    : 2, // 1 = online, 2 = testCenter
                personTenantRoleId: this.global.candidateId,
                testCenterId: this.selectedtestSiteId.testSiteId,
                ExamDateTime: this.ExamDateTime,
                testCenterName: this.selectedtestSiteId.testSiteName,
                testCenterAddress: this.selectedtestSiteId.testSiteAddress,
                testCenterCity: this.selectedtestSiteId.city,
                testCenterState: this.selectedtestSiteId.state,
                testCenterPostalCode: this.selectedtestSiteId.postalCode,
                TestCenterDirections: this.selectedtestSiteId.directions,
                //testSiteName:this.selectedtestSiteId.testSiteName
              },
            },
          })
        );
      } else {
        this.store.dispatch<Action>(
          addCartTC({
            body: {
              personTenantRoleId: this.global.candidateId,
              amount: this.selectedExam.price,
              cartItemTypeId: 1,
              currencyId: 1,
              examDetail: {
                candidateId: this.global.candidateId,
                examId: this.selectedExam.id,
                timeZone: this.selectedtestSiteId.timeZone,
                Slotime: moment(this.ExamDateTime).format("hh:mm A"),
                SlotId: this.selectedSlots.siteslotid,
                // offSet: this.Validators.value.timezone.offset,
                examModeId: (this.selectedSlots.examCode == "VS-PR") ? 1 : 2,
                // 1 = online, 2 = testCenter
                personTenantRoleId: this.global.candidateId,
                testCenterId: this.selectedtestSiteId.testCenterId.toString(),
                ExamDateTime: this.ExamDateTime,
                testCenterName: this.selectedtestSiteId.testCenter,
                testCenterAddress: this.selectedtestSiteId.address,
                testCenterCity: this.selectedtestSiteId.city,
                testCenterState: this.selectedtestSiteId.state,
                testCenterPostalCode: this.selectedtestSiteId.postalCode,
                TestCenterDirections: this.selectedtestSiteId.getMap,
                testSiteId: this.selectedtestSiteId.testSiteId,
                testCenterCode: this.selectedtestSiteId.testCenterCode,

                //testSiteName:this.selectedtestSiteId.testSiteName
              },
              examClientEventId: this.selectedSlots.clientEventCode,
              examCode:this.selectedSlots.examCode
            },
          })
        );
      }

      this.store.select(selectorGetCart).subscribe((data) => {
        if (data !=null) {
          this.store.dispatch<Action>(
            getCartItems({ personTenantRoleId: this.global.candidateId })
          );
        }
      });

      //     },1000)
      //   }
      // });

      // this.store.select(get_cartItems).subscribe((cartItems) => {
      //   if(cartItems!=null){
      //     this.cartItemList=cartItems
      //   }
      // })

      this.testCenterLocation.reset();
    } else {
      let valid = false;
      let selectedSlots =
        this.selectedtestSiteId.slots != null
          ? this.selectedtestSiteId.slots
          : this.selectedtestSiteId.siteSlot;
      for (let slot of selectedSlots) {
        let slotDates =
          slot.slotDateUtc != null ? slot.slotDateUtc : slot.examdateUTC;
        if (this.ExamDateTime == slotDates) {
          valid = true;
        }
      }

      if (valid) {
        if (this.TSMSLOTS == false) {
          this.store.dispatch<Action>(
            addCartTC({
              body: {
                personTenantRoleId: this.global.candidateId,
                amount: this.selectedExam.price,
                cartItemTypeId: 1,
                currencyId: 1,
                examDetail: {
                  candidateId: this.global.candidateId,
                  examId: this.selectedExam.id,
                  timeZone: this.selectedSlots.timezoneId,
                  // offSet: this.Validators.value.timezone.offset,
                  examModeId:
                    this.selectedExam.title === "Nurse Aide Skills Exam" &&
                    this.Examtype == 1 &&
                    StateAllowTestCenterforOnline.includes(
                      this.global.userDetails.getValue().stateId
                    )
                      ? 1
                      : 2, // 1 = online, 2 = testCenter
                  personTenantRoleId: this.global.candidateId,
                  testCenterId: this.selectedtestSiteId.testSiteId,
                  ExamDateTime: this.ExamDateTime,
                  testCenterName: this.selectedtestSiteId.testSiteName,
                  testCenterAddress: this.selectedtestSiteId.testSiteAddress,
                  testCenterCity: this.selectedtestSiteId.city,
                  testCenterState: this.selectedtestSiteId.state,
                  testCenterPostalCode: this.selectedtestSiteId.postalCode,
                  TestCenterDirections: this.selectedtestSiteId.directions,

                  //testSiteName:this.selectedtestSiteId.testSiteName
                },
              },
            })
          );
        } else {
          this.store.dispatch<Action>(
            addCartTC({
              body: {
                personTenantRoleId: this.global.candidateId,
                amount: this.selectedExam.price,
                cartItemTypeId: 1,
                currencyId: 1,
                examDetail: {
                  candidateId: this.global.candidateId,
                  examId: this.selectedExam.id,
                  timeZone: this.selectedtestSiteId.timeZone,
                  Slotime: moment(this.ExamDateTime).format("hh:mm A"),
                  SlotId: this.selectedSlots.siteslotid,
                  // offSet: this.Validators.value.timezone.offset,
                  examModeId: (this.selectedSlots.examCode == "VS-PR") ? 1 : 2,
                  personTenantRoleId: this.global.candidateId,
                  testCenterId: this.selectedtestSiteId.testCenterId.toString(),
                  ExamDateTime: this.ExamDateTime,
                  testCenterName: this.selectedtestSiteId.testCenter,
                  testCenterAddress: this.selectedtestSiteId.address,
                  testCenterCity: this.selectedtestSiteId.city,
                  testCenterState: this.global.userDetails.value.state,
                  testCenterPostalCode: this.selectedtestSiteId.zipCode,
                  TestCenterDirections: this.selectedtestSiteId.getMap,
                  testSiteId: this.selectedtestSiteId.testSiteId,
                  testCenterCode: this.selectedtestSiteId.testCenterCode,

                  //testSiteName:this.selectedtestSiteId.testSiteName
                },
                examClientEventId: this.selectedSlots.clientEventCode,
                examCode:this.selectedSlots.examCode
              },
            })
          );
        }

        this.store.select(selectorGetCart).subscribe((data) => {
          if (data !=null) {
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.global.candidateId })
            );

            this.testCenterLocation.reset();
            setTimeout(() => {
              this.store.dispatch(
                getExamByERIdId({
                  eligibilityRouteId: this.examName.id,
                  personTenantRoleId: this.global.candidateId,
                })
              );
              this.cartItems();
            }, 3000);
          }
        });
    
      } else {
        this.snackbar.callSnackbaronError(
          "Invalid Date: Please check that the selected exam time belongs to the selected exam date"
        );
      }
    }
  }
  /**
   * Getting cart information items etc.
   */
  getCart() {
    this.store.dispatch(
      getCartItems({ personTenantRoleId: this.global.candidateId })
    );
    this.store.select(get_cartItems).subscribe((data) => {
      this.cartId = data;
    });
  }
  /**
   * Function for rerouting to payment
   * Called when "Pay Now" button is pressed
   */
  getPayment() {
    this.getCart();
    if (
      this.selectedExam.page != undefined &&
      this.selectedExam.page == "PopPage"
    ) {
      this.store.dispatch(
        getCartItems({ personTenantRoleId: this.global.candidateId })
      );
      if (this.cartId.length > 0) {
        this.dialogRef.close({ confirmed: true });
        this.router.navigateByUrl("/exam-scheduled/payment/1");
      } else {
        this.snackbar.callSnackbaronError("No item in Cart to make a Payment");
      }
    }
    if (this.cartId.length > 0) {
      this.router.navigateByUrl("/exam-scheduled/payment");
    } else {
      this.snackbar.callSnackbaronError("No item in Cart to make a Payment");
    }
  }
  // ---------------------------------- SETTER FUNCTIONS ----------------------------------
  /**
   * Initial setup for validators for search
   */
  setValidators() {
    let userAddress = `${this.GetUserDetails?.address} ${this.GetUserDetails?.city} ${this.GetUserDetails?.zipCode}`;
    this.Validators = this.fb.group({
      timezone: new FormControl(""),
      testCenter: new FormControl("", [Validators.required]),
    });
    this.Validators.controls["testCenter"].disable(); //Input only trough mat-menu
    this.range = new FormGroup({
      start: new FormControl("", [Validators.required]),
      end: new FormControl("", [Validators.required]),
    });
    this.testCenterLocation = new FormGroup({
      testId: new FormControl("", [Validators.pattern("[0-9]{5}")]),
      testAddress: new FormControl(userAddress, [Validators.required]),
      testName: new FormControl(null),
      testRadius: new FormControl(100, [Validators.required]),
    });

    // this.testCenterLocation.setValidators(this.atLeastOneValidator());
  }
  /**
   * Setup testCenter value from mat-menu
   */
  setData() {
    this.testCenterLocation.value.testAddress = this.testCenterLocation.value
      .testAddress
      ? JSON.stringify(this.testCenterLocation.value.testAddress)
      : null;
    this.testCenterLocation.value.testRadius = this.testCenterLocation.value
      .testRadius
      ? JSON.stringify(this.testCenterLocation.value.testRadius)
      : null;
    let stringToPatch = "";
    if (
      this.testCenterLocation.value.testId != null &&
      this.testCenterLocation.value.testId.length > 0
    )
      stringToPatch += this.testCenterLocation.value.testId;
    if (
      this.testCenterLocation.value.testName != null &&
      this.testCenterLocation.value.testName.length > 0
    )
      stringToPatch +=
        stringToPatch.length > 0
          ? ";" + this.testCenterLocation.value.testName
          : this.testCenterLocation.value.testName;
    if (
      this.testCenterLocation.value.testAddress != null &&
      this.testCenterLocation.value.testAddress.length > 0
    )
      stringToPatch +=
        stringToPatch.length > 0
          ? ";" + this.testCenterLocation.value.testAddress
          : this.testCenterLocation.value.testAddress;
    if (
      this.testCenterLocation.value.testRadius != null &&
      this.testCenterLocation.value.testRadius.length > 0
    )
      stringToPatch +=
        stringToPatch.length > 0
          ? ";" + this.testCenterLocation.value.testRadius
          : this.testCenterLocation.value.testRadius;
    this.Validators.patchValue({ testCenter: stringToPatch });
    this.trigger.closeMenu();
  }

  extractNumbersFromString(input: string): string {
    // Use regular expression to extract only numeric characters
    return input.replace(/\D/g, "");
  }

  /**
   *
   * @param $event - clicked button.
   * @param date - to set in selectedExamTime for API call
   */
  setSelectedDaySlot($event, date) {
    this.datetime = date;
    // CSS
    this.selectedExamTime = date;
    // Set selected
    this.selectedTimeSlot = null;
    this.displayPaymentMethods = false;
    // Fill test centers from API
  }
  /**
   *
   * @param $event - clicked button.
   * @param time - to set in selectedTimeSlot for API call
   */
  setSelectedTimeSlot($event, time) {
    this.displayPaymentMethods = true;
    this.selectedTimeSlot = time;
  }
  /**
   * For mat-expansion-panel
   * @param index
   */
  setStep(index: number) {
    this.expanded = index;
  }

  /**
   * For displaying calendar footer
   * @param event
   */
  changeCalendar(event: any): void {
  
    
    const body = Array.from(
      document.querySelectorAll<HTMLDivElement>(
        ".mat-calendar .mat-calendar-content"
      )
    );
    const html = `
        <div class="inline-flex" id="calendarFooter">
        <p class="inline-flex" style="font-size: 8px;">
        <span class="inline-flex"><img src="assets/img/black_icon.svg">Available</span>&nbsp; &nbsp;
        <span class="inline-flex"><img src="assets/img/gray_icon.svg">Not available</span>&nbsp;&nbsp;
        <span class="inline-flex"><img src="assets/img/lightGreen_icon.svg">Today</span>&nbsp; &nbsp;
        <span class="inline-flex"><img src="assets/img/greenTheme_icon.svg">Selected</span>&nbsp; &nbsp;
        </p>
        </div>
        `;
    body.forEach((ele: HTMLDivElement) => {
      const element = document.createElement("div");
      element.setAttribute("class", "availability-status");
      element.innerHTML = html;
      // el.innerHTML="<img src=\'https://domain.com/adv/banner.jpg\' width=\'400px\' height=\'150px\'>";
      ele.appendChild(element);
    });
  }

  

  // ---------------------------------- UTILITY FUNCTIONS ----------------------------------
  /**
   *
   * @param array - Array to have classes removed from
   * @param name - Id of selector
   */
  removeClassesFromArray(array, name) {
    for (let i = 0; i < array.length; i++) {
      this.elem.nativeElement
        .querySelector("#" + name + i)
        .classList.remove("active");
    }
  }
  /**
   *
   * @param date to be formatted
   * @returns formated date, example: "26. Nov, Mon"
   */
  formatDate(date: Date) {
    return moment(date).format("DD. MMM, ddd");
  }
  formatTime(date: Date) {
    return moment(date).format("hh:mm A");
  }
  /**
   * toggle the mat-menu from test center location input
   */
  toggleMenu() {
    this.trigger["_menuOpen"]
      ? this.trigger.openMenu()
      : this.trigger.closeMenu();
  }

  onPickerOpened(): void {
    const today = new Date();
    this.minDate =  today;
    (sessionStorage.getItem('title') ==='Nurse Aide Skills Exam' || sessionStorage.getItem('title') ==='Home Health Aide Skills Exam' || sessionStorage.getItem('title') ===' Nurse Aide Spanish Skills Exam' )? this.minDate.setDate(
      this.minDate.getDate() + this.global.appointment_Leadtime_for_Offline
    ):  this.minDate.setDate(
      this.minDate.getDate() + this.global.appointment_Leadtime_for_Online
    )
  }
  
  onPickerClosed(): void {
    this.minDate = null;
  }
  /**
   * function for rescheduled exam for test center
   */

  
  reschedule() {
    this.rescheduleloading = true;

    if (this.ExamDateTime == null) {
      this.snackbar.callSnackbaronError(
        "Invalid Date: Please check that the selected exam time belongs to the selected exam date"
      );
    } else {
      if (this.TSMSLOTS == false) {
        this.store.dispatch<Action>(
          reschedule({
            rescheduleBody: {
              candidateId: this.global.candidateId,
              examId: this.selectedExam.id,
              slotId: 0,
              timeZone: this.selectedSlots.timezoneId,
              // offSet: this.Validators.value.timezone.offset,
              examModeId:
                this.selectedExam.title === "Nurse Aide Skills Exam" &&
                StateAllowTestCenterforOnline.includes(
                  this.global.userDetails.getValue().stateId
                )
                  ? 1
                  : 2, // 1 = online, 2 = testCenter
              scheduleId: this._services.rescheduleInformation.id,
              personTenantRoleId: this.global.candidateId,
              testCenterId: this.selectedtestSiteId.testSiteId,
              ExamDateTime: this.ExamDateTime,
              testCenterName: this.selectedtestSiteId.testSiteName,
              testCenterAddress: this.selectedtestSiteId.testSiteAddress,
              testCenterCity: this.selectedtestSiteId.city,
              testCenterState: this.selectedtestSiteId.state,
              testCenterPostalCode: this.selectedtestSiteId.postalCode,
              isGrievanceFilled:
                this._services.rescheduleInformation.formTypeId ==
                FormTypes.Grievance
                  ? true
                  : false,
            },
          })
        );
      } else {
        this.store.dispatch<Action>(
          reschedule({
            rescheduleBody: {
              candidateId: this.global.candidateId,
              examId: this.selectedExam.id,
              slotId: this.selectedSlots.siteslotid,
              timeZone: this.selectedtestSiteId.timeZone,
              Slotime: moment(this.ExamDateTime).format("hh:mm A"),
              // offSet: this.Validators.value.timezone.offset,
              examModeId: (this.selectedSlots.examCode == "VS-PR") ? 1 : 2,
              scheduleId: this._services.rescheduleInformation.id,
              personTenantRoleId: this.global.candidateId,
              testCenterId: this.selectedtestSiteId.testCenterId.toString(),
              ExamDateTime: this.ExamDateTime,
              testCenterName: this.selectedtestSiteId.testCenter,
              testCenterAddress: this.selectedtestSiteId.address,
              testCenterCity: this.selectedtestSiteId.city,
              testCenterState: this.global.userDetails.value.state,
              testCenterPostalCode: this.selectedtestSiteId.zipCode,
              isGrievanceFilled:
                this._services.rescheduleInformation.formTypeId ==
                FormTypes.Grievance
                  ? true
                  : false,
              testSiteId: this.selectedtestSiteId.testSiteId,
              TestCenterDirections: this.selectedtestSiteId.getMap,
              testCenterCode: this.selectedtestSiteId.testCenterCode,
              examClientEventId: this.selectedSlots.clientEventCode,
              examCode:this.selectedSlots.examCode
            },
          })
        );
      }

      this.store.select(selectorGetRescheduledResponse).subscribe((data) => {
        if (data !=null) {
          this.rescheduleloading = false
          var datePipe = new DatePipe("en-US");
          this.time = datePipe.transform(
            this._services.rescheduleInformation.examDateTime,
            "shortTime",
            "+0000"
          );
          let body = {
            body: `Test Center ${this.selectedExam.title} scheduled  ${
              this._services.rescheduleInformation.testCenterDetails
                .testCenterName
                ? "for " +
                  this._services.rescheduleInformation.testCenterDetails
                    .testCenterName
                : ""
            } on ${moment(
              this._services.rescheduleInformation.examDateTime
            ).format("MM/DD/YYYY")} at ${this.time} ${
              this._services.rescheduleInformation.timeZoneAbbreviation
            } was rescheduled by ${this.global.roleName}`,
            candidateId: this.global.candidateId,
            files: [],
            id: 0,
            noteTypeid: 5,
            title: `Test Center ${this.selectedExam.title} Rescheduled`,
            userId: this.global.candidateId,
            userName: this.userdata,
          };
          this.http.getAddnotes(body).subscribe((data) => {
            if (data) {
            }
          });
        }else{
          this.rescheduleloading = false

        }
      });
      
    }
  }

  onMarkerClickEvent(event, i) {
    this.markerclickLan = event.latitude;
    if (i.siteSlot.length > 0) {
      this.selectedDayTestcenter = i.testCenter;
      let timeSlots = [];
      this.FilterTestcenterSlot = this.slotsDates.filter((x: any) =>
        x.testCenters.find((y) => y.testCenter == i.testCenter)
      );
      const time: any = this.FilterTestcenterSlot.filter((ele: any) =>
        ele.testCenters.filter((y) => y.siteSlot.length > 0)
      );
      time.forEach((x) =>
        x.testCenters.forEach((y) => {
          if (y.testCenter === i.testCenter) {
            timeSlots.push(y);
          }
        })
      );
      timeSlots.length > 0
        ? (this.setTestCentersForSelectedDate(
            timeSlots[0].siteSlot[0].examdate
          ),
          this.selectedDate(timeSlots[0].siteSlot[0]),
          this.selectedDateTime(timeSlots[0].siteSlot[0]),
          this.setStep(0))
        : null;
    }
  }

  cartItems() {
    const dialogRef = this.dialogs.open(CartSummaryPopupComponent, {
      width: "360px",
      height: "550px",
    });
  }

  scheduleAagin() {
    this.scheduleloading = true;

    if (this.ExamDateTime == null) {
      this.snackbar.callSnackbaronError(
        "Invalid Date: Please check that the selected exam time belongs to the selected exam date"
      );
    } else {
      let body: schedule =
        this.TSMSLOTS == true
          ? {
              candidateId: this.global.candidateId,
              examId: this.selectedExam.id,
              slotId: this.selectedSlots.siteslotid,
              timeZone: this.selectedtestSiteId.timeZone,
              Slotime: moment(this.ExamDateTime).format("hh:mm A"),
              // offSet: this.Validators.value.timezone.offset,
              examModeId: (this.selectedSlots.examCode == "VS-PR") ? 1 : 2,
              personTenantRoleId: this.global.candidateId,
              accommodationType: "",
              accommodationItems: [],
              clientExamId: 0,
              testCenterId: this.selectedtestSiteId.testCenterId.toString(),
              testCenterName: this.selectedtestSiteId.testCenter,
              testCenterAddress: this.selectedtestSiteId.address,
              testCenterCity: this.selectedtestSiteId.city,
              testCenterState: this.global.userDetails.value.state,
              testCenterPostalCode: this.selectedtestSiteId.zipCode,
              examDateTime: this.ExamDateTime,
              scheduleId: this._services.rescheduleInformation.id,
              testSiteId: this.selectedtestSiteId.testSiteId,
              TestCenterDirections: this.selectedtestSiteId.getMap,
              testCenterCode: this.selectedtestSiteId.testCenterCode,
              examClientEventId: this.selectedSlots.clientEventCode,
              examCode:this.selectedSlots.examCode
            }
          : {
              candidateId: this.global.candidateId,
              examId: this.selectedExam.id,
              slotId: 0,
              timeZone: this.selectedSlots.timezoneId,
              // offSet: this.Validators.value.timezone.offset,
              examModeId:
                this.selectedExam.title === "Nurse Aide Skills Exam" &&
                StateAllowTestCenterforOnline.includes(
                  this.global.userDetails.getValue().stateId
                )
                  ? 1
                  : 2, // 1 = online, 2 = testCenter
              personTenantRoleId: this.global.candidateId,
              testCenterId: this.selectedtestSiteId.testSiteId,
              accommodationType: "",
              accommodationItems: [],
              clientExamId: 0,
              testCenterName: this.selectedtestSiteId.testSiteName,
              testCenterAddress: this.selectedtestSiteId.testSiteAddress,
              testCenterCity: this.selectedtestSiteId.city,
              testCenterState: this.selectedtestSiteId.state,
              testCenterPostalCode: this.selectedtestSiteId.postalCode,
              ExamDateTime: this.ExamDateTime,
              scheduleId: this._services.rescheduleInformation.id,
            };
      this.http.Schedule(body).subscribe(
        (data: any) => {
          if (data != null && data.isScheduled) {
            var datePipe = new DatePipe("en-US");
            this.time = datePipe.transform(
              this._services.rescheduleInformation.examDateTime,
              "shortTime",
              "+0000"
            );
            let body = {
              body: `Test Center ${this.selectedExam.title} scheduled  ${
                this._services.rescheduleInformation.testCenterDetails
                  .testCenterName
                  ? "for " +
                    this._services.rescheduleInformation.testCenterDetails
                      .testCenterName
                  : ""
              } on ${moment(
                this._services.rescheduleInformation.examDateTime
              ).format("MM/DD/YYYY")} at ${this.time} ${
                this._services.rescheduleInformation.timeZoneAbbreviation
              } was rescheduled by ${this.global.roleName}`,
              candidateId: this.global.candidateId,
              files: [],
              id: 0,
              noteTypeid: 5,
              title: `Test Center ${this.selectedExam.title} Rescheduled`,
              userId: this.global.candidateId,
              userName: this.userdata,
            };
            this.http.getAddnotes(body).subscribe((data) => {
              if (data) {
                this.snackbar.callSnackbaronSuccess(
                  "Exam Rescheduled Successfully"
                );
              }
            });
            this.router.navigateByUrl("/exam-scheduled");
          }
        },
        (err) => {
          if (err) {
            // this.router.navigateByUrl("/exam-scheduled");
          }
        }
      );
    }
  }

  getDirection(url: string) {
    window.open(url, "_blank");
  }
  getSelectedRoute(event) {
    if (event == 1) {
      this.Validators.reset();
      this.testCenterLocation.patchValue({
        testId: null,
        testName: null,
        testAddress: `${this.GetUserDetails?.address} ${this.GetUserDetails?.city} ${this.GetUserDetails?.zipCode}`,
        testRadius: 100,
      });
      this.isSelectDate = false;
      this.displayPaymentMethods = false;
      this.Inf = "";
    } else if (event == 2) {
      this.Validators.reset();
      this.Inf = "INF";
      this.testCenterLocation.patchValue({
        testId: "",
        testName: "",
        testAddress: null,
        testRadius: null,
      });
      this.displayPaymentMethods = false;
      this.isSelectDate = false;
    }
  }
  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }
  paste(event: any) {
    const pastedData = event.clipboardData.getData("text/plain");
    const onlyNumbers = this.extractNumbersFromString(pastedData);
    event.target.value = onlyNumbers;
  }

  onMouseOver(infoWindow, $event: MouseEvent) {
    infoWindow.open();
  }

  onMouseOut(infoWindow, $event: MouseEvent) {
    infoWindow.close();
  }

  ngOnDestroy(): void {
    //
    this.store.dispatch(clearTimeSlotsTestCenter());
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}
export const allowSchedule = [
  70, 68, 9, 77, 78, 79, 80, 81, 84, 85, 86, 87, 96,
];
