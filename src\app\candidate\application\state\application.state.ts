import { eligibilityRoute, eligibilityRouteDetails, Form, PersonForm, PersonFormLog, response, ShowRegisterExamModel } from '../application.types';
import { validatedSubmission } from '../../../core/examroom-formbuilder/form-builder.types';

export interface ApplicationState {
  eligibilityRoutes: Array<eligibilityRoute>;
  activeEligibilityRouteIndex: number;
  eligiblityRouteDetails: eligibilityRouteDetails,
  showRegisterExamStatus: ShowRegisterExamModel;
  personForms: PersonForm[],
  loading: boolean
 

}

export const intialApplicationState: ApplicationState = {
  eligibilityRoutes: [],
  activeEligibilityRouteIndex: null,
  eligiblityRouteDetails:null,
  personForms: [],
  showRegisterExamStatus:null,
  loading:null
}

export class dashboardModel {
  eligibilityRoutes: Array<eligibilityRoute>;
  activeEligibilityRouteIndex: number;
  form: Form;
  userResponse: response;
  accomodationFormResponse: validatedSubmission;
  savedResponseId: number;
  saveAccResponseId: number;
  accomodationForm: Form;
  personformlogs: PersonFormLog[];
  eligiblityRouteDetails: eligibilityRouteDetails;
  personForms: PersonForm[];
  curPersonFormIndex: number;
  isApplicationEditingAllowed: boolean;
  isAccomodationEditingAllowed: boolean;
  
}