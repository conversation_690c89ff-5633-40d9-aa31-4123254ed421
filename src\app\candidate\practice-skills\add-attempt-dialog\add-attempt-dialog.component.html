<!-- <PERSON>alog Header -->
<div class="p-2">
    <div class="flex justify-between items-center px-4 py-2 shadow">
        <h2 class="text-sm font-semibold">Add More Attempts</h2>
        <button class="close-btn" (click)="closeDialog()">✕</button>
    </div>
</div>

<!-- Dialog Content -->
<div class="px-2 mb-2 space-y-4">

    <!-- Skill Info Block -->
    <div class="dialog-body rounded-md shadow p-4 ">
        <h3 class="text-sm font-semibold mb-2">{{ skill.title }}</h3>
        <p class="description_text text-xs leading-relaxed tracking-wide text-justify mb-4">
            {{ skill.description }}
        </p>

        <!-- Metadata -->
        <div class="flex flex-wrap justify-between text-center text-xs mt-2 px-3">
            <div class="w-1/5">
                <div class="meta-label">Duration</div>
                <div class="meta-value">{{ skill.duration }}</div>
            </div>
            <div class="w-1/5">
                <div class="meta-label">Steps</div>
                <div class="meta-value">{{ skill.steps }}</div>
            </div>
            <div class="w-1/5">
                <div class="meta-label">Attempts</div>
                <div class="meta-value">{{ skill.attempts }}</div>
            </div>
            <div class="w-1/5">
                <div class="meta-label">Validity</div>
                <div class="meta-value">{{ skill.validity }}</div>
            </div>
            <div class="w-1/5">
                <div class="meta-label">Price</div>
                <div class="meta-value">{{ skill.price }}</div>
            </div>
        </div>
    </div>

    <!-- Attempts Info & Quantity Selector -->
    <div class="p-4 space-y-4 rounded-md shadow">

        <div class="text-xs">
            You currently have <strong>{{ skill.attempts }}</strong> attempts.
            Add more attempts if needed.
        </div>

        <div class="flex items-center gap-3 text-xs">
            <span class="mt-1 px-2">Additional Attempts</span>
            <button class="qty-btn" (click)="decreaseAttempts()">-</button>
            <span class="w-6 text-center mt-1">{{ quantity }}</span>
            <button class="qty-btn px-2" (click)="increaseAttempts()">+</button>
            <span class="mt-1 px-2">
                x ${{skill.additionalAttemptsPrice}} <span class="italic per_attempt">per attempt</span>
            </span>

        </div>
        <div class="h-8"></div>
    </div>

    <!-- Footer -->
    <div class="p-4 flex flex-wrap  justify-between items-center shadow">
        <div class="text-sm font-semibold">
            Subtotal: <span class="subtotal-text">${{ quantity * 5 }}</span>
        </div>
        <div class="space-x-2">
            <button mat-button (click)="closeDialog()" class="cancel-btn">Cancel</button>
            <button mat-button class="proceed-btn">Proceed</button>
        </div>
    </div>

</div>