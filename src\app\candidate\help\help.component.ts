import { Component, Inject, OnD<PERSON>roy, OnInit} from '@angular/core';
import { MatDialog, MatDialogConfig} from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { ViewPreviousTicketsComponent } from './dialogs/view-previous-tickets/view-previous-tickets.component';
import { HelpService } from './help.service';



@Component({
  selector: "exai-register-for-exam",
  templateUrl: "./help.component.html",
  styleUrls: ["./help.component.scss"],
})
export class HelpComponent implements OnInit, OnDestroy {

  constructor(
    private globalUserService: GlobalUserService,
    private helpService: HelpService,
    private dialog: MatDialog
  ) { }

  roleIdSub: Subscription


  ngOnInit() {  
    this.roleIdSub = this.globalUserService.userDetails.subscribe(data => {
      const Data = data.roles[0]
      this.helpService.roleId = Data.roleId
      this.helpService.userId = this.globalUserService.userId
      this.helpService.candidateId = this.globalUserService.candidateId
      this.helpService.personTenantRoleId = Data.personTenantRoleId
      this.helpService.createdBy = Data.personId
      this.helpService.assignedToRoleId = Data.roleId
      this.helpService.roleId = Data.roleId

    })
  }

  ngOnDestroy() {
    this.roleIdSub.unsubscribe()
  }
  openDialog() {

    const dialogConfig = new MatDialogConfig();

    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.data = {
      id: 1,
      title: 'Angular For Beginners'
  };

    this.dialog.open(ViewPreviousTicketsComponent, dialogConfig);
}
}
export enum Roles {
  Candidate = 1,
  ClientAdmin = 2,
  Faculty = 3,
  Proctor = 4,
  OnboardingAgent = 5,
  ShiftManager = 6,
  Facilitator = 7,
  SupportingStaff = 8,
  PanelMember = 9,
  Reviewer = 10,
  PlatformAdmin = 11,
  ExamroomSuperAdmin = 12,
  SuperAdmin = 13,
  StateClient = 14,
  TrainingInstitue = 15,
  ExamroomAdmin = 16,
  OperationStaff = 17,
  Sponsor = 18,
}
