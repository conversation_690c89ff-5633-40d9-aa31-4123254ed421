<mat-icon class="text-base flex close mt-4" (click)="onNoClick(0)">close</mat-icon>


<div class="exam">
    <h2 class="px-5 text-xs font-bold fontColor1">
        Selected exam no longer available please select a new appointment
    </h2>
</div>

<mat-accordion>
    <mat-expansion-panel *ngFor="let list of data.List" class="card card-border">
        <mat-expansion-panel-header (click)="getData(list,null)">
            <mat-panel-title>
                {{PracticeNumberExam == 1?list.examName:list.examNameDesc}}
            </mat-panel-title>
        </mat-expansion-panel-header>
        <!-- ONLINE -->
        <div  *ngIf="list.testCenterId==null">
            <mat-radio-group>
                <ng-container>
                    <mat-radio-button class="-ml-1 px-6 mb-3 pt-1 t-xs">Online
                    </mat-radio-button>
                </ng-container>
            </mat-radio-group>
        <div class="t-xs" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px"
            exaiContainer>
            <form [formGroup]="Validators">
                <div class="justify-start w-full" gdColumn="1 / 3" gdColumn.lt-md="1" gdColumn.lt-sm="1">
                    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" class="px-4 exam -mt-2"
                        fxLayoutGap.lt-sm="0">

                        <!-- TIME ZONE PICKER -->
                        <mat-form-field class="pt-3" appearance="outline" fxFlex="50%">
                            <mat-label class="text-xs  fontColor2">Selected Time Zone</mat-label>
                            <mat-select class="text-xs" [(value)]="list.timeZoneCode">
                                <mat-option class="text-xs" *ngFor="let time of timezones" [value]="time.id"
                                    (click)="examEvent(time,list)">
                                    {{ time.timeZoneStandardName }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <!-- END TIME ZONE PICKER -->

                        <!-- DATE PICKER -->
                        <mat-form-field class="pt-3" appearance="outline" fxFlex="50%">
                            <mat-label class="text-xs fontColor2">Select Date</mat-label>
                            <input matInput class="text-xs" formControlName="date" [value]="list.eventDate" [matDatepicker]="picker" [min]="minDate" (dateChange)="getData(list,$event)" />
                            <mat-datepicker-toggle matSuffix [for]="picker">
                            </mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                        </mat-form-field>
                        <!-- END DATE PICKER -->
                    </div>
                </div>
            </form>
        </div>
        <hr class="mr-5 ml-5 line" />

        <div *ngIf="disableDateRange">
        <div class="px-2 -mt-2" fxLayout="row" fxLayoutAlign="start center">
            <h2 class="-mt-2 px-3 pt-4 text-xs font-bold fontColor1">
                Select Range
            </h2>
        </div>

        <div class="ml-2">
            <ng-container *ngFor="let time of timeSlot; let i = index">
                <button *ngIf="time.data.length > 0" class="px-4 ml-3 mb-3 pt-3 pb-3 state slots2 buttom6"
                    mat-stroked-button color="light" [ngClass]="{ active: step == time.id }"
                    (click)="selectrangeactive(time)">
                    {{ time.title }}<br />
                    <!-- <span [ngClass]="{'active' : step == time.id}"
             class="time ">{{time.heddle}}</span> -->
                </button>
            </ng-container>
        </div>
        </div>

        <div *ngIf="disableTimeSlot">
        <div class="px-2 -mt-3" fxLayout="row" fxLayoutAlign="start center">
            <h2 class="m-0 px-3 pt-2 mb-1 text-xs font-bold fontColor1">
                Available Slots
            </h2>
        </div>

        <div class="mat-testCenter touch-auto overflow-auto">
            <div class="ml-2">
                <ng-container *ngFor="let slot of slots">
                    <button [ngClass]="
  bookedslot == slot.slotId
    ? 'select-slot-btn'
    : slot.availableSlots == slot.totalSlots
    ? 'Limited_Slots'
    : 'avaiable'
" class="ml-3 mb-3 pt-1 pb-1 state slots2" mat-stroked-button color="light" matTooltip="Avaiable Slots - {{
  slot.totalSlots - slot.bookedSlots
}}" (click)="bookslot(slot)" [attr.id]="slot.slotId" *ngIf="slot.totalSlots - slot.bookedSlots > 0">
                        {{ slot.strSlotTime }}
                    </button>
                </ng-container>
            </div>
        </div>

        </div>


        <div *ngIf="cartPayment" fxLayout="row" fxLayoutAlign="end center" class="flex justify-end" fxLayoutGap="8px">
            <button mat-raised-button type="button"  class="buuton1 mr-4" (click)="addToCart(list)">
      Add Cart
    </button>
            <!-- <button mat-raised-button class="mr-5 buuton2 height"  type="button" (click)="payNow()">
      Procced To Pay
    </button> -->
    </div>
    
        </div>
        <!-- ONLINE ENDS -->


        <!-- TEST CENTERS -->
        <div *ngIf="list.testCenterId!=null">
        <mat-radio-group>
            <ng-container>
                <mat-radio-button class="-ml-1 px-6 mb-3 pt-1 t-xs">Test Center
                </mat-radio-button>
            </ng-container>
        </mat-radio-group>
        <exai-test-center [data]="data"  [selectedExam]="selectedExam" [events]="scheduleEvent.asObservable()" ></exai-test-center>
        </div>
        <!-- TEST CENTERS ENDS -->
    </mat-expansion-panel>
</mat-accordion>