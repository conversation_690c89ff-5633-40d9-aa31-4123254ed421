import { DynamicFormControlModel, DynamicFormLayout } from "@ng-dynamic-forms/core";

export type feildType = {
  displayName: string;
  id: number;
  multiple?: boolean;
  // mostly if the type is input , subtype can be 'number','text','file' or other type avilable in html input  
  subType?: 'number' | 'file' | 'text' | 'password' | 'url' | 'date'| 'time'|'form';
}


export class fieldTypeValue  {
  editFormtypeId:any
}
export class sectionsEdit{
  QuestionGroups: Array<any> = [];
}



export class editData{
  sections: Array<sectionsEdit> = [];
}

export class form{
  id:string;
  name: string;
  description: string;
  instructions: string;// rich text string , need to provide a rich text editor,
  showInstructionInIButton: boolean;
  fetchFromApi?: boolean;
  requestDetails?: requestDetails;
  sections: Array<section>;
  formLayout: DynamicFormLayout;
  
  constructor(form:any) {
    this.id = form.id;
    this.name = form.name;
    this.description = form.description;
    this.instructions = form.instructions;
    this.showInstructionInIButton = form.showInstructionInIButton;
    this.sections = [];
    this.formLayout = {};
    this.fetchFromApi = form.fetchFromApi,
    this.requestDetails = form.requestDetails
  }
}

export class section{
  id: string;
  name: string;
  description: string;
  showInstructionInIButton?:boolean;
  instructions: string;// rich text string , need to provide a rich text editor
  QuestionGroups: DynamicFormControlModel[]|any[];
  QuestionGroupsMetaData: questionGroupMetaData;

  constructor(section: any) {
    this.id = section.id;
    this.name = section.name;
    this.description = section.description;
    this.instructions = section.instructions;
    this.showInstructionInIButton = section.showInstructionInIButton;
    this.QuestionGroups = [];
    this.QuestionGroupsMetaData = new questionGroupMetaData();
  }
}

export class questionGroupMetaData{
  formArrayModelIds: Array<replicable> = [];
  ABrelatedFeilds: Array<AbRelatedFeilds> = [];
  optionsFromApi: Array<Array<fetchOptionsFromApi>> = [];
  parsingMetaData: Array<parsingMetaData> = [];
  qGpWithInstructions: Array<QInstruc> = [];
  dateAttributes: Array<DateAttr> = [];
}

export interface DateAttr{
  feildID: string;
  futureOffset?:number;
  pastOffset?:number;
  pastDisabled?:boolean;
  todateDisabled?:boolean;
  futureDisabled?:boolean;
  disableBasedOnApiRes?:boolean;
  requestDetails?: requestDetails;
  parentQuesGrpId?: string;
  displayCurrentDate?:any;
  displayClosedDate?:any;
}

export interface requestDetails {
  fetchUrl: Array<string>,
  requestParams: Array<requestParam>,
  requestBody: any;
  method: "GET" | "POST" | "PUT" | "DELETE";// more can be included here;
}

export type requestParam = {
  paramName: string,
  paramValue: string | number | [],
  paramType: paramTypes,
  // this string refers to the property of element (of linkClickEvent) whose value has to be put here
  // the below property will only be accessed if paramType is 'dependent'
  extractedFromElement?: boolean;
  extractedFromGlobal?: boolean;
  elementPropertyToBeExtracted?: string | null;
  // position will be accessed only if paramtype is required, and 
  position?: number;
}

export enum paramTypes {
  Required = 'required',
  Optional = 'optional',
  RouteParam = 'routeParam'
}
export interface replicable {
  // this has to be provided, if add button has to be shown even when initial count is 0
  parentModelID?: string;
  modelID: string;
  removeLabel: string;
  addLabel: string;
  totalCount:number;
}
export class QInstruc {
  qgpID: string;
  label: string;
  instructions: string;
}
export interface parsingMetaData{
  feildID?: string;
  parentQuesGrpId?: string;
  identifier?: string;
  orientation?: 'vertical' | 'horizontal';
  mergeToNextRow?: boolean;
  colspan?: number;
  prefillValue?: boolean;
  elementPropertyToBeExtracted?: string;
}
export class fetchOptionsFromApi{
  feildID: string;
  apiUrl?: string;
  fetchOptionsBasedOnResponseAnotherField?: boolean;
  relatedFieldId?: string;
  requestDetails?: requestDetails;
  method?: 'POST' | 'GET';
}

export class AbRelatedFeilds{
  feild1ID: string;
  feild2ID: string;
  constructor(one:string,two:string) {
    this.feild1ID = one;
    this.feild2ID = two;
  }
}

export interface validatedSubmission{
  valid: boolean;
  errorMessages: Array<string>;
  formValue: Array<any>;
}

export interface sectionCompleteEvent {
  sectionIndex: number;
  sectionDesc: string;
  sectionName: string;
  status: 'VALID' | 'INVALID' | 'UNTOUCHED';
}

export interface saveFormEvent{
  formJSONstring: string;
  formEditData: string;
}

export const FEILD_TYPES = [
  <feildType>{
    displayName: "Text Input",
    id: 1,
    multiple: false,
    subType: "text",
    type:'INPUT'
  },
  <feildType>{
    displayName: "Number Input",
    id: 2,
    multiple: false,
    subType: "number",
    type:'INPUT'
  },
  <feildType>{
    displayName: "Multi Text Input",
    id: 3,
    multiple: true,
    subType: "text",
    type:'INPUT'
  },
  <feildType>{
    displayName: "Multi Number Input",
    id: 4,
    multiple: true,
    subType: "number",
    type:'INPUT'
  },
  <feildType>{
    displayName: "Select",
    id: 5,
    multiple: false,
    subType: "text",
    type:'SELECT'
  },
  <feildType>{
    displayName: "Multi Select",
    id: 6,
    multiple: true,
    subType: "text",
    type:'SELECT'
  },
  <feildType>{
    displayName: "Checkbox",
    id: 7,
    multiple: false,
    subType: "text",
    type:'CHECKBOX'
  },
  <feildType>{
    displayName: "Radio Group",
    id: 8,
    multiple: false,
    type:'RADIO_GROUP'
  },
  <feildType>{
    displayName: "Date",
    id: 9,
    multiple: false,
    subType: "date",
    type:'DATEPICKER'
  },
  <feildType>{
    displayName: "File Upload",
    id: 10,
    multiple: false,
    subType: "file",
    type:"FILE_UPLOAD",
  },
  <feildType>{
    displayName: "Slider",
    id: 11,
    type:'SLIDER',
  },
  <feildType>{
    displayName: "Switch",
    id: 12,
    type:'SWITCH'
  },
  <feildType>{
    displayName: "Text Area",
    id: 14,
    subType: "text",
    type:'TEXT_AREA'
  },
  <feildType>{
    displayName: "Time Picker",
    id: 15,
    subType: "time",
    type:'INPUT'
  },
  <feildType>{
    displayName: "SensitiveData(Password, CVV,etc.)",
    id: 16,
    subType: "password",
    type:'INPUT',
  },
  <feildType>{
    displayName: "Rating",
    id: 17,
    multiple: false,
    subType: "form",
    type:"RATING",
  },
  <feildType>{
    displayName: "Form Link",
    id: 18,
    multiple: false,
    subType: "form",
    type:"COLORPICKER",
  },
];
export interface observableApiCallsMetaData{
  sectionIndex: number,
  questionGrpIndex: number,
  optionsToFetch:fetchOptionsFromApi
}

// index wise the following css might be loaded, 
// when form with all the design scenarios is done
export const defaultFeildCss = [
  // text input 
  {
    element: {
      children: "",
      container: "genesis-form-feild",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // number input
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Multi Text Input
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Multi Number Input
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Select
  {
    element: {
      children: "",
      container: "ml-8",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "text-gray flex m-3 ml-6",
      option: "ml-6"
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Multi Select
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Checkbox
  {
    element: {
      children: "",
      container: "m-3",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "font-medium m-1",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Radio Group
  {
    element: {
      children: "",
      container: "flex flex-row mb-3",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "flex m-3 -mt-5 ml-6 font-medium",
      option: "mr-2 ml-6"
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Date
  {
    element: {
      children: "",
      container: "ml-8",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "text-gray",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // File Upload
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Slider
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Switch
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Text Area
  {
    element: {
      children: "",
      container: "ml-8",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "text-gray flex m-3 ml-6",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // Time Picker
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
  // SensitiveData(Password, CVV,etc.)
  {
    element: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    },
    grid: {
      children: "",
      container: "",
      control: "",
      errors: "",
      group: "",
      hint: "",
      host: "",
      label: "",
      option: ""
    }
  },
    //Form Upload
    {
      element: {
        children: "",
        container: "",
        control: "",
        errors: "",
        group: "",
        hint: "",
        host: "",
        label: "",
        option: ""
      },
      grid: {
        children: "",
        container: "",
        control: "",
        errors: "",
        group: "",
        hint: "",
        host: "",
        label: "",
        option: ""
      }
    },
]

export interface candidateDetails{
  candidateName?:string,
  registryNumber?:number,
  candidateId?:number,
  AddressName?:string;
  City?:string;
  State?:string;
  Zipcode?:string;
  EmployerName?:string;
  mobilePhoneNumber?:string|number
}

export enum CertStatus{
  "Pending"=2,
  "Drafted"=1,
  "Change_Request"=4,
  "Approved"=7,
  "Rejected"=3
}
export const PARenewalCheckbox=[
  "********************************",
]
export const feildName={
  E9Eligibility:"b43e9991305f4081b6eeb16f2dc9ed47",
  problemReportAction:"7cb0c611ec4b4be6b82d0872e7f895fb",
  actionOfOther:"63e93167be224272afd87caef0adb346",
  SCE6Eligibility:"c48b973b241842f38cf681a326df1b25",
  SCE7Eligibility:"41861e296fc143a08b899f05138e9b0f",
  Problemreportsection3ClosedDropDown:"aaa902d4630f4e3487452e430de56e37",
  Problemreportsection3Description:"66d37162178849ba8e9258228003c1b2",
  Problemreportsection3ClosedDate:"afc436789a3f4826983fd2d6e49f09de",
 
  E2EligibilityNV:"1f8cdf3c3a2c406dbfc0823ad0fcb343",
  E3eligibilityNV:"a92e74a569e24cbdb2de446579da0b76",
  E4eligibilityNV:"94e6a8b5c33945c79314baf405d27f18",
  PARenewal :"7ec19bfac07d48558e0807c9f251a9e0"


}

export const sectionName={
  problemReport:"problem_report_section_1_q1s1",
  SCE7SectionName:"south_carolina_nurse_aide_testing_application_section_1_s1q1",
  SCE6SectionName:"south_carolina_nurse_aide_testing_application_section_1_q1",
  MSTempE9Eligibility:"mississippi_nurse_aide_testing_application_section_1_q1s1",
  ProblemreportSction6:"problem_report_section_6",
  ProblemReportId:"problem_report",
  ProblemreportSction4:"problem_report_section_4section_5_q4s1",
  FromAccomadation:"accomodation_accomodation_dates",
  NVE2:"nevada_nurse_aide_testing_application_section_1_q1s1",
  NVE3:"nevada_nurse_aide_testing_application_section_1_q1s1",
  NVE4:"nevada_nurse_aide_testing_application_section_1_s1q1",
  AccomodationExams:"44fb1386ceed4ff3b429f7ea3f85ed1b",
  PARenewal:'e75e0b3101a04273a2bd0f4a374f93a6',
  Employer:'197637c815704235a8126b2f4503a40a'
 
}

export const Healthcare_Co_s3=[
  "colorado_nurse_aide_testing_application_section_3_q12s3",
  "colorado_nurse_aide_testing_application_section_3_q13s3",
  "colorado_nurse_aide_testing_application_section_3_q14s3",
  "colorado_nurse_aide_testing_application_section_3_q15s3",
  "colorado_nurse_aide_testing_application_section_3_q16s3",
  "colorado_nurse_aide_testing_application_section_3_q17s3",
  "colorado_nurse_aide_testing_application_section_3_q18s3",
  "colorado_nurse_aide_testing_application_section_3_q19s3",
  "colorado_nurse_aide_testing_application_section_3_q20s3",
  "colorado_nurse_aide_testing_application_section_3_q21s3"
]
export const HealthCare_CO_S3_E8=[
  "colorado_nurse_aide_testing_application_section__3_q12s3",
  "colorado_nurse_aide_testing_application_section__3_q13s3",
  "colorado_nurse_aide_testing_application_section__3_q14s3",
  "colorado_nurse_aide_testing_application_section__3_q15s3",
  "colorado_nurse_aide_testing_application_section__3_q16s3",
  "colorado_nurse_aide_testing_application_section__3_q17s3",
  "colorado_nurse_aide_testing_application_section__3_q18s3",
  "colorado_nurse_aide_testing_application_section__3_q19s3",
  "colorado_nurse_aide_testing_application_section__3_q20s3"
]
export const HealthCare_CO_S3_E7=[
  "colorado_nurse_aide_testing_application_section_4section_3_q12s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q13s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q14s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q15s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q16s3___",
  "colorado_nurse_aide_testing_application_section_4section_3_q17s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q18s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q19s3",
  "colorado_nurse_aide_testing_application_section_4section_3_q20s3",
]

export const PARenewal =[
   "985a249fec204cf197fd75f71a01818c",
   "0e3c7f2f8ea649f6bc06c858088b0cd2",
   "9b9f04ade1014e0aa7f972177bfc94e1"
]

export const PARenewalEncahement=[
  "e5fd49654a98464887826172e031ba3e",
  "e75e0b3101a04273a2bd0f4a374f93a6"
]

export const SCRECIPORATING={
  SCRECSect1 :"1a3b7a09863041d4a3b8705f38e3d286",
  SCRECSect2 :"075f19275daf42fb8e0ef2f74538a379",
  SCRECSect3 :"b8adaedb08d74a33b8d8119f5c934b86",
  SCRECSect4 :"01615443c7c84f8cbb89df86a1029d4b",
  SCRECSect5 :"a8be7a37c7e6493c8f20e5bcb5f4baf2",
}

export const SCRECIVALUES={
  SCRECVALUESSect1 :"Please upload your Employment verification Information:",
  SCRECVALUESSect2 :"Option 2: If you have less than 100 hours of training, then you will need to submit proof that you worked 6 months as a nurse aide within  the previous 24 months.",
  SCRECVALUESSect3 :"Option 1: Submit proof you have completed at least 100-hours of state approved Nurse Aide training that includes both <br> classroom and clinical training- proof of training may be either a certificate/diploma, a  transcript, or a notarized letter <br> on your training school's letterhead confirming that you completed 100  hours of training.",
  SCRECVALUESSect4 :"<div >A  letter on employer letterhead stating I completed 6 months of employment. This  letter must include the Nurse Aide's name,<br> current  state  and license number, dates the candidate  performed nurse aide duties for the employer, verification  that the candidate <br> worked  under  the  supervision  of an RN, and include the title and contact information of the person  writing the  letter.</div>",
}

export const VAFormsId={
  SCRECSect1 :"virginia_nurse_aide_testing_application_section_2_q1s2",
  SCRECSect2 :"virginia_nurse_aide_testing_application_section_2_q2s2",
  SCRECSect3 :"virginia_nurse_aide_testing_application_section_2_q5s2",
  SCRECSect4 :"virginia_nurse_aide_testing_application_section_2_q6s2",
  SCRECSect5 :"virginia_nurse_aide_testing_application_section_2_q7s2",
  SCRECSect6 :"virginia_nurse_aide_testing_application_section_2_q8s2",
}

export const VAFORMS={
     Section2:"SCREENING QUESTIONS",
     Section3:"ACCOMMODATIONS"
}

export const StateClientMaceAL_Allow=[8]
  

export enum StateLists{
  "SC"=7,
  "CO" =9,
  "PA" =4,
  "CO_ER8"=56,
  "CO_ER7"=54,
  "VA" =14,
  "CA"=16,
  "NV"=18,
  "AK"=19,
  "VA_E1"=87,
  "WA"=15,
  "WAState" ="Washington",
  "DC"=5,
  "AL"=8,
  "KYC" = 22463
}

export const FormTypeID=[15]

export enum Role {
  "OperationStaff"=17,
  "Candidate"=1,
  "Training"=15,
  "superadmin"=13,
  "QAC"=28
}



export const StateAllowTestCenterforOnline =[10,9,4,15]

export const StateAllow = [
  4,6,7,5
]
export const SpanishRemovingExam = [
  4,7,6,19,18,16
]
export const VAEligilibity=[
 "87"
]

export const AllowWritten=['Nurse Aide Written Exam']
export const Alloworalskills=['Nurse Aide Skills Exam','Nurse Aide Oral English Exam','Nurse Aide Oral Spanish Exam','Home Health Aide Skills Exam','Nurse Aide Spanish Skills Exam']
export const Allonlyskills=['Nurse Aide Skills Exam','Home Health Aide Skills Exam','Nurse Aide Spanish Skills Exam']
export const Allowonlyoral=['Nurse Aide Oral English Exam','Nurse Aide Oral Spanish Exam']

export const HideShredButtonforparticularstate=[]
export const FormTypesAllow =[5,6,14,15]
export const RolesList=[17,15,18,1,4,8,13,14,19,22,28]
export const MACESC=[7]

export interface Details{
  stateId:number | string;
  formTypeId:number | string
  certStatus:number | string
}

export const UrlAllowed=['https://gisapiuat.examroom.ai/api/Event/GetEventsWithOfferToEmail']


export const GAdemographic= ["70942a2c1cdd4448ad690aa9ed80887d"]

export const ShowOnboardforParticularstate=["NC","CA",'SC',"SCA"]



