import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { DynamicMenuItem } from "./model/dynamic-menu-item";

@Component({
  selector: "exai-menu",
  templateUrl: "./menu.component.html",
  styleUrls: ["./menu.component.scss"],
})
export class MenuComponent implements OnInit {
  @Input() menuItems: DynamicMenuItem[] = [];
  @Input() isOpen = false;
  @Input() align: "left" | "right" = "right";
  @Output() itemClicked = new EventEmitter<any>();
  constructor() {}

  ngOnInit(): void {}

  onItemClick(item: DynamicMenuItem) {
    this.itemClicked.emit(item);
  }

  onToggleClick() {
    this.isOpen = !this.isOpen;
  }
}
