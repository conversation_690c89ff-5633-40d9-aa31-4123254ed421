
export class UserDetails {
  accessToken: string;
  identityToken: string;
  refreshToken: string;
  decodedIdentityToken: DecodedIdentityToken;
  tokenType: string;
  state: string;
  personId: number;
  roles: Array<role>;
  _roles?: role;
  clientId: number;
  stateId: number;
}

export interface role {
  roleId?: number;
  tenantId?: number;
  roleName?: string;
  personTenantRoleId?: number;
}
export interface Address {
  formatted: string;
}
export interface DecodedIdentityToken {
  sub: string;
  "cognito:groups": string[];
  email_verified: boolean;
  address: Address;
  birthdate: string;
  gender: string;
  iss: string;
  phone_number_verified: boolean;
  "cognito:username": string;
  given_name: string;
  middle_name?: string;
  origin_jti: string;
  "cognito:roles": string[];
  aud: string;
  event_id: string;
  token_use: string;
  auth_time: number;
  phone_number?: string;
  exp: number;
  iat: number;
  family_name: string;
  jti: string;
  email: string;
}