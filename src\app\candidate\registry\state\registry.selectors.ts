import { RegistryModuleState } from './registry.model';
import { createFeatureSelector, createSelector } from "@ngrx/store";


export const REGISTRY_MODULE_STATE = "REGISTRY_MODULE_STATE";
const registryModuleState = createFeatureSelector<RegistryModuleState>(REGISTRY_MODULE_STATE);


export const selectorLoadAllCertificates = createSelector(
  registryModuleState,
  (state) => {
    return state.certificates;
  }
);

export const selectorLoadAllRequests = createSelector(
  registryModuleState,
  (state) => {
    return state.requests;
  }
);
export const selectorDo_check = createSelector(
  registryModuleState,
  (state) => {
    return state.data;
  }
);

export const selectorLoadLogs = createSelector(
  registryModuleState,
  (state) => {
    return state.logs;
  }
);
export const selectorLoadCertificatePath = createSelector(
  registryModuleState,
  (state) => {
    return state.certificatePath
  }
);

export const selectorLoadForms = createSelector(
  registryModuleState,
  (state) => {
    return state.form
  }
);

export const selectorLoadDraftedForm = createSelector(
  registryModuleState,
  (state) => {
    return state.draftedForm
  }
)
export const selectorLoadAllStates = createSelector(
  registryModuleState,
  (state) => {
    return state.states;
  }
);


// export const selectorLoadSavePersonFormRegistry = createSelector(
//   registryModuleState,
//   (state) => {
//     return state.form
//   }
// )

export const setSelectedCertificate = createSelector(
  registryModuleState,
  (state) => {
    return state.selectedCertificate
  }
);



export const getSelectedCertificate = createSelector(
  registryModuleState,
  (registryModuleState) => {
    return registryModuleState.selectedCertificate
  }
);


export const setSelectedRequest = createSelector(
  registryModuleState,
  (state) => {
    return state.selectedRequest
  }
);

export const getSelectedRequest = createSelector(
  registryModuleState,
  (registryModuleState) => {
    return registryModuleState.selectedRequest
  }
);

export const setSelectedState = createSelector(
  registryModuleState,
  (state) => {
    return state.selectedState
  }
);
export const getSelectedState = createSelector(
  registryModuleState,
  (registryModuleState) => {
    return registryModuleState.selectedState
  }
);

export const getDeletedRenewedFormStateState = createSelector(
  registryModuleState,
  (registryModuleState) => {
    return registryModuleState.deletedRenewedForm
  }
);
