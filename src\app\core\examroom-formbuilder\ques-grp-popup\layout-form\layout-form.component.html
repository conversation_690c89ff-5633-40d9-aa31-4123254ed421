<form [formGroup]="formgroup">
    <div class="row">
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>children</mat-label>
            <input matInput formControlName="children" type="text">
        </mat-form-field>
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>container</mat-label>
            <input matInput formControlName="container" type="text">
        </mat-form-field>
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>control</mat-label>
            <input matInput formControlName="control" type="text">
        </mat-form-field>
    </div>
    <div class="row">
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>errors</mat-label>
            <input matInput formControlName="errors" type="text">
        </mat-form-field>
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>group</mat-label>
            <input matInput formControlName="group" type="text">
        </mat-form-field>
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>hint</mat-label>
            <input matInput formControlName="hint" type="text">
        </mat-form-field>
    </div>
    <div class="row">
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>host</mat-label>
            <input matInput formControlName="host" type="text">
        </mat-form-field>
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>label</mat-label>
            <input matInput formControlName="label" type="text">
        </mat-form-field>
        <mat-form-field appearance="outline" class="genesis-form-feild">
            <mat-label>option</mat-label>
            <input matInput formControlName="option" type="text">
        </mat-form-field>
    </div>
</form>