<div class="w-full">
    <div class="mb-2">
        <div class="dialog-body flex justify-between items-center px-4 py-2">
            <h2 class="text-sm font-semibold">Skill Details</h2>
            <button class="close-btn" (click)="closeDialog()">✕</button>
        </div>
    </div>

    <div class="px-2 mb-2 space-y-4">

        <div class="dialog-body rounded-md p-4 ">
            <h3 class="text-sm font-semibold mb-2">{{ skill.skillTitle }}</h3>
            <p class="description_text text-xs leading-relaxed tracking-wide text-justify mb-4">
                {{ skill.description }}
            </p>

            <div class="flex flex-wrap justify-between text-center text-xs mt-2 px-3">
                <div class="w-1/5">
                    <div class="meta-label">Duration</div>
                    <div class="meta-value">{{ skill.duration }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Steps</div>
                    <div class="meta-value">{{ skill.skillStepCount }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Attempts</div>
                    <div class="meta-value">{{ skill.defaultAttempt }}</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Validity</div>
                    <div class="meta-value">{{ skill.validity }}d</div>
                </div>
                <div class="w-1/5">
                    <div class="meta-label">Price</div>
                    <div class="meta-value1">${{ skill.priceUsd }}</div>
                </div>
            </div>
        </div>

        <div class="p-4 space-y-4 rounded-md">

            <div class="description_text text-xs">
                You currently have <strong>{{ skill.attempts }}</strong> attempts.
                Add more attempts if needed.
            </div>

            <div class="flex items-center gap-3 text-xs">
                <span class="mt-1 px-2">Additional Attempts</span>
                <button class="qty-btn" (click)="decreaseAttempts()">-</button>
                <span class="w-6 text-center mt-1">{{ quantity }}</span>
                <button class="qty-btn px-2" (click)="increaseAttempts()">+</button>
                <span class="mt-1 px-2">
                    x ${{ skill.additionalAttemptsPrice }} <span class="italic per_attempt">per attempt</span>
                </span>

            </div>
            <div class="h-8"></div>
        </div>
    </div>

    <div class="dialog-body py-3 px-4 flex flex-wrap  justify-between items-center ">
        <div class="description_text text-xs">
            Subtotal: <span class="subtotal-text">${{ subtotal }}</span>
        </div>
        <div class="space-x-2 flex flex-wrap justify-between">
            <button mat-button (click)="closeDialog()" class="cancel-btn">Cancel</button>
            <button mat-button (click)="openSelectModeDialog()" class="proceed-btn">Next</button>
        </div>
    </div>
</div>