export class PersonDetails {
    public constructor(init?: Partial<PersonDetails>) {
        Object.assign(this, init);
    }
    id: number = 0;
    firstName: string = '';
    middleName: string = '';
    lastName: string = '';
    dateofBirth: Date = new Date();
    gender: string = '';
    phoneNumber: number = 0;
    emailId: string = '';
    ssn: any = 0;
    address: string = '';
    zipCode: number = 0;
    city: string = '';
    profile: string = '';
    profileImageUrl: string = '';
    modifiedBy: number = 0;
    tenantId: number = 0;
}