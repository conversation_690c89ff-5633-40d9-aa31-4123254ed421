<!-- Before Filling Application -->
<div *ngIf="BeforeFillingApplication && upcomingExams.length < 1 && PracticeExams.length == 0" class="flex justify-center dashboard"
  gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
  gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdRows="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
  gdRows.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdRows.lt-sm="1fr" gdGap="4px">
  <div class="db flex item-center justify-center" fxLayoutAlign="start center" gdColumn="2 / 8" gdColumn.lt-md="2 / 8"
    gdColumn.lt-sm="1 / 9" gdRow="2 / 8" gdRow.lt-md=" 2 / 8" gdRow.lt-sm="1/-1" fxLayout="column">
    <div class="flex w-full" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
      <div class="card cardBorder shadow-none justify-center w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1"
        gdColumn.lt-sm="1 / -1">
        <div class="">
          <div class="-mt-2 flex justify-center text-center topmargin" fxLayout="">
            {{ this.lngSrvc.curLangObj.value.welcome }}
            <b class="welc">
              &nbsp;{{ FirstName}} {{ MiddleName }}
              {{ LastName }}</b>
          </div>
        </div>
        <div class="pt-4" fxLayout="column">
          <div class="welc-note flex justify-center text-center text-xs" fxLayout="column">
            The Credentia team is committed to providing you with a convenient and <br />
            hassle-free experience throughout the credentialing process!
          </div>
        </div>


        <!-- WebView -->
        <div fxHide.lt-sm class="py-6">
          <div class="flex justify-center pt-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
            gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
            gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr">
            <div class="flex item-center" fxLayoutAlign="start center" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
              gdColumn.lt-sm="2/ 3" fxLayout="column">
              <img class="w-1/2" src="assets/img/fill-application1.svg" alt="" />
            </div>
            <div class="flex item-center arrow" fxLayoutAlign="start center" gdColumn="3 / 4" gdColumn.lt-md="3 / 4"
              gdColumn.lt-sm="3 / 4" fxLayout="column">
              <img class="" src="assets/img/Line 48.svg" alt="" />
            </div>
            <div class="flex" fxLayoutAlign="start center" gdColumn="4 / 5" gdColumn.lt-md="4 / 5"
              gdColumn.lt-sm="4 / 5" fxLayout="column">
              <img class="w-1/2" src="assets/img/register-exam1.svg" alt="" />
            </div>
            <div class="flex item-center arrow" fxLayoutAlign="start center" gdColumn="5 / 6" gdColumn.lt-md="5 / 6"
              gdColumn.lt-sm="5 / 6" fxLayout="column">
              <img class="" src="assets/img/Line 48.svg" alt="" />
            </div>
            <div class="flex" fxLayoutAlign="start center" gdColumn="6 / 7" gdColumn.lt-md="6 / 7"
              gdColumn.lt-sm="6 / 7" fxLayout="column">
              <img class="w-1/2" src="assets/img/start-exam1.svg" alt="" />
            </div>
            <div class="flex item-center arrow" fxLayoutAlign="start center" gdColumn="7 / 8" gdColumn.lt-md="7 / 8"
              gdColumn.lt-sm="7 / 8" fxLayout="column">
              <img class="" src="assets/img/Line 48.svg" alt="" />
            </div>
            <div class="flex" fxLayoutAlign="start center" gdColumn="8 / 9" gdColumn.lt-md="8 / 9"
              gdColumn.lt-sm="8 / 9" fxLayout="column">
              <img class="w-1/2" src="assets/img/scores1.svg" alt="" />
            </div>
            <div class="flex item-center arrow" fxLayoutAlign="start center" gdColumn="9 / 10" gdColumn.lt-md="9 / 10"
              gdColumn.lt-sm=" 9 / 10" fxLayout="column">
              <img class="" src="assets/img/Line 48.svg" alt="" />
            </div>
            <div class="flex" fxLayoutAlign="start center" gdColumn="10 / 11" gdColumn.lt-md="10 / 11"
              gdColumn.lt-sm="10 / 11" fxLayout="column">
              <img class="w-1/2" src="assets/img/certificate1.svg" alt="" />
            </div>
          </div>
          <div class="flex justify-center pt-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
            gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
            gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr">
            <div class="flex item-center" fxLayoutAlign="start" gdColumn="2 / 4" gdColumn.lt-md="2 / 4"
              gdColumn.lt-sm="1/ -1" fxLayout="column">
              <h5 class="text-xs">{{ this.lngSrvc.curLangObj.value.fillApp }}</h5>
            </div>
            <div class="flex item-center" fxLayoutAlign="start end" gdColumn="4 / 6" gdColumn.lt-md="4 / 6"
              gdColumn.lt-sm="4 / 6" fxLayout="column">
              <h5 class="text-xs ml-4">
                {{ this.lngSrvc.curLangObj.value.registerExam }}
              </h5>
            </div>
            <div class="flex item-center" fxLayoutAlign="start end" gdColumn="6 / 8" gdColumn.lt-md="6 / 8"
              gdColumn.lt-sm="7 / 8" fxLayout="column">
              <h5 class="text-xs">
                {{ this.lngSrvc.curLangObj.value.startExam }}
              </h5>
            </div>
            <div class="flex item-center" fxLayoutAlign="start center" gdColumn="9 / 11" gdColumn.lt-md="9 / 11"
              gdColumn.lt-sm="9 / 11" fxLayout="column">
              <h5 class="text-xs">
                {{ this.lngSrvc.curLangObj.value.checkExamScore }}
              </h5>
            </div>
            <div class="flex item-center" fxLayoutAlign="start" gdColumn="12 / 14" gdColumn.lt-md="12 / 14"
              gdColumn.lt-sm="12 / 14" fxLayout="column">
              <h5 class="text-xs -ml-4">
                {{ this.lngSrvc.curLangObj.value.getCertified }}
              </h5>
            </div>
          </div>
        </div>
        <!-- WebView -->

        <!-- Mobile View -->
        <div class="py-6" *ngIf="mobile">
          <div class="flex item-center" fxLayoutAlign="start center" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
            gdColumn.lt-sm="1/ -1" fxLayout="column">
            <img class="w-1/12" src="assets/img/fill-application1.svg" alt="" />
            <h5 class="text-xs">{{ this.lngSrvc.curLangObj.value.fillApp }}</h5>
            <img src="assets/img/rotated_line.svg" alt="" />
          </div>
          <div class="flex item-center pt-2" fxLayoutAlign="start center" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
            gdColumn.lt-sm="1/ -1" fxLayout="column">
            <img class="w-1/12" src="assets/img/register-exam1.svg" alt="" />
            <h5 class="text-xs">{{ this.lngSrvc.curLangObj.value.registerExam }}</h5>
            <img src="assets/img/rotated_line.svg" alt="" />
          </div>
          <div class="flex item-center pt-2" fxLayoutAlign="start center" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
            gdColumn.lt-sm="1/ -1" fxLayout="column">
            <img class="w-1/12" src="assets/img/start-exam1.svg" alt="" />
            <h5 class="text-xs">{{ this.lngSrvc.curLangObj.value.startExam }}</h5>
            <img src="assets/img/rotated_line.svg" alt="" />
          </div>
          <div class="flex item-center pt-2" fxLayoutAlign="start center" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
            gdColumn.lt-sm="1/ -1" fxLayout="column">
            <img class="w-1/12" src="assets/img/scores1.svg" alt="" />
            <h5 class="text-xs">{{ this.lngSrvc.curLangObj.value.checkExamScore }}</h5>
            <img src="assets/img/rotated_line.svg" alt="" />
          </div>
          <div class="flex item-center pt-2" fxLayoutAlign="start center" gdColumn="2 / 3" gdColumn.lt-md="2 / 3"
            gdColumn.lt-sm="1/ -1" fxLayout="column">
            <img class="w-1/12" src="assets/img/certificate1.svg" alt="" />
            <h5 class="text-xs">{{ this.lngSrvc.curLangObj.value.getCertified }}</h5>
          </div>

        </div>
        <!-- Mobile View -->

        <div class="py-4 flex justify-center" fxLayout="" matTooltip="{{applicationMessage}}">
          <button mat-button class="add-new text-xs" data-application-type="Start application"
            [ngClass]="{'button-disabled' : showApplication}" (click)="addNewApplication()">
            <mat-icon>add</mat-icon>&ensp;
            {{ this.lngSrvc.curLangObj.value.addNewApp }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Before Filling Application -->

<!-- After Approving Application -->
<div *ngIf="!BeforeFillingApplication && activeForms.length > 0" class="px-gutter py-2" gdColumns="1fr "
  gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
  <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
    <div fxLayout="column">
      <div>
        <div class="flex text-center">
          {{ this.lngSrvc.curLangObj.value.welcome }}
          <b class="welc">
            &nbsp;{{ FirstName }} {{ MiddleName }}
            {{ LastName }}</b>
        </div>
      </div>
      <div class="pb-1" fxLayout="column">
        <div class="welc-note flex text-xs" fxLayout="column">
          Below is the list of your active applications...
        </div>
      </div>
    </div>
  </div>

  <!-- Registered Dashboard -->
  <div *ngIf="showRegisteredDashboard" class="pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap=""
    exaiContainer>
    <exai-registered-dashboard (ExamChanged)="handleExamChange($event)" [upcomingExam]="dashboardDetail"
      [upcomingExams]="upcomingExams">
    </exai-registered-dashboard>
  </div>

  <!-- Registered Dashboard -->

  <div class="pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
    <div class="justify-start" gdColumn="1 / 1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
      <div fxFlex="auto">
        <div class="pt-1" gdGap="12px" exaiContainer>
          <div class="content">
            <div fxLayout="row wrap" fxLayoutGap="10px grid">


              <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                *ngFor="let activeForm of activeForms; let index = index">
                <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                  <div class="bg-color px-4 py-3">
                    <div class="flex justify-between" fxLayout="row">
                      <div class="t-xs title-hed">
                        <strong>{{ activeForm.stateName }}&nbsp;{{
                          activeForm.eligiblityRouteName
                          }}</strong>
                      </div>
                    </div>
                    <div class="t-xs state-elig pt-1" fxLayout="row">
                      <strong> {{ activeForm.name }}</strong>
                    </div>
                  </div>
                  <div fxLayout="column">
                    <div class="px-4" fxFlexFill>
                      <span class="status t-xs">{{
                        this.lngSrvc.curLangObj.value.currentStatus
                        }}</span><br />
                      <span><img src="{{ activeForm.iconUrl }}" class="inline iconSize" /></span>
                      <span class="t-xs ml-2 -mt-3" [style.color]="
                          activeForm.status == 'Drafted' ||
                          activeForm.status == 'Pending'
                            ? '#EE9400'
                            : activeForm.status == 'Approved'
                            ? '#00AB72'
                            : '#F7685B'
                        ">
                        {{ activeForm.status }}</span>
                      <div class="italic t-xs state-elig mt-2" *ngIf="activeForm.statusId != 1">
                        {{ activeForm.submittedDateN }}
                      </div>
                      <div class="italic t-xs state-elig pb-3 pt-1" *ngIf="activeForm.statusId == 1">

                        Yet to Submit
                      </div>



                      <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <div class="h4 status t-xs">
                            {{ this.lngSrvc.curLangObj.value.appId }}
                          </div>
                        </div>
                        <div gdColumn="3/6" gdColumn.lt-md="3/6" gdColumn.lt-sm="3/6" *ngIf="activeForm.statusId != 1">
                          <div class="status t-xs">
                            {{ this.lngSrvc.curLangObj.value.submitDate }}
                          </div>
                        </div>
                      </div>
                      <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <div class="h4 status1 t-xs">
                            {{ activeForm.personFormId }}
                          </div>
                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5" *ngIf="activeForm.statusId != 1">
                          <div class="status1 t-xs">
                            {{ activeForm.submittedDateOnly }}
                          </div>

                        </div>
                      </div>
                      <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr"
                        gdColumns.lt-sm="1fr 1fr" exaiContainer
                        *ngIf="activeForm.status == 'Rejected'||activeForm.statusId== 4 || activeForm.statusId == 14">
                        <div gdColumn="1/-1" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                          <div class="" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
                            <div class="t-xs status" *ngIf="activeForm.status == 'Rejected'">
                              {{ this.lngSrvc.curLangObj.value.reason }}
                            </div>
                            <div class="t-xs status" *ngIf="activeForm.statusId == 4 || activeForm
                            .statusId === 14">
                              {{ this.lngSrvc.curLangObj.value.reason2 }}
                            </div>
                          </div>
                          <div class="status1 t-xs minimise" matTooltip="{{activeForm.comment}}">{{ activeForm.comment
                            }}</div>
                        </div>
                      </div>
                      <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2">
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="activeForm.status == 'Approved'">
                          <button mat-button color="var(--text-color2)" class="btn-4 text-xs"
                            (click)="summary(activeForm, index)">
                            {{ this.lngSrvc.curLangObj.value.summary }}
                          </button>
                        </div>
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="activeForm.status == 'Pending'">
                          <button mat-button color="var(--text-color2)" (click)="summary(activeForm, index)"
                            class="btn-4 text-xs">
                            {{ this.lngSrvc.curLangObj.value.summary }}
                          </button>
                        </div>
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="activeForm.status == 'Rejected' && activeForm.allowApplyAgain">
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="applyAgain()">
                            {{ this.lngSrvc.curLangObj.value.apply }}
                          </button>
                        </div>
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="activeForm.status == 'Drafted'">
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="editApplication(activeForm, index)">
                            Edit Form
                          </button>
                        </div>
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="activeForm.statusId == 4 || activeForm.statusId == 14 ">
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="editApplication(activeForm, index)">
                            Edit Form
                          </button>
                        </div>
                      </div>
                      <!-- <div></div> -->
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="startNewApplicationFlag
              " class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%">
                <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                  <div class="flex justify-center pt-4">
                    <img class="pt-5 pb-2 w-3/12" src="assets/img/NoApplication-Blue.svg" /><br />
                  </div>
                  <div class="content1 pb-4">
                    <section class="mr-5 ml-5">
                      <div class="bg-color">
                        <span class="welc-note
                        flex
                        justify-center
                        text-center text-xs mx-4 mt-4">
                          <span class="pt-3 pb-6"> Fill Application Form. </span>
                        </span>
                      </div>
                    </section>
                    <div class="flex justify-center mb-4 -mt-5 t-xs" matTooltip="{{applicationMessage}}">
                      <button class="add-new text-xs" mat-button color="primary"
                        data-application-type="Start application" [ngClass]="{'button-disabled' : showApplication}"
                        (click)="addNewApplication()">
                        {{ this.lngSrvc.curLangObj.value.addNewApp }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>




              <div *ngFor="let upcomingexam of listExam" class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'"
                fxFlex.xs="100%" fxFlex.sm="33%">
                <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                  <div class="bg-color px-4 py-3">
                    <div class="flex justify-between space" fxLayout="row">
                      <div class="t-xs title-hed">
                        <strong>{{ upcomingexam.examName }}</strong>
                      </div>
                    </div>
                    <div class="t-xs state-elig pt-1" fxLayout="row">
                      <strong> {{ upcomingexam.eligibilityRouteName }}</strong>
                    </div>
                  </div>
                  <div fxLayout="column">
                    <div class="px-4 t-xs" fxFlexFill>
                      <span class="status t-xs">{{
                        this.lngSrvc.curLangObj.value.currentStatus
                        }}</span><br />
                      <span><img src="{{ upcomingexam.iconUrl }}" class="inline iconSize" /></span>
                      <span class="t-xs ml-2 -mt-3 active2" [style.color]="
                      upcomingexam.examStatus == 'Exam Scheduled' ? '#00AB72'
                      : '#F7685B' ">
                        {{ upcomingexam.examStatus }}</span>
                      <div class="italic t-xs state-elig mt-2">
                        {{ upcomingexam.registeredDateTime }}
                      </div>
                      <div *ngIf="enableTestCenterDirections(upcomingexam)"
                        (click)="openTestCenterDirections(upcomingexam)" class="pt-2 active2 onhover" gdColumns="1fr">
                        Test Center Directions
                      </div>
                      <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <!-- <div class="status t-xs">Exam Mode</div> -->

                          <div *ngIf="upcomingexam.mode === 'Online'; else testCenter" class="h4 status t-xs">Exam Mode
                          </div>
                          <ng-template #testCenter>
                            <div class="h4 status t-xs"> Test Center Name</div>
                          </ng-template>
                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                          <div class="status t-xs">Exam Date</div>
                        </div>
                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                          <div class="status t-xs">Exam Time</div>
                        </div>
                      </div>
                      <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <!-- <div class="status1 t-xs">
                            {{ upcomingexam.examMode }}
                          </div> -->

                          <div *ngIf="upcomingexam.mode==='Online'; else testCenterValue" class="h4 status1 t-xs">
                            {{upcomingexam.mode}}</div>
                          <ng-template #testCenterValue>
                            <div class="h4 status1 t-xs">{{upcomingexam.testCenterDetails.testCenterName}}</div>
                          </ng-template>

                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                          <div class="status1 t-xs">
                            {{ upcomingexam.examDateTime | date: "MM/dd/yyyy":'+0000' }}
                          </div>
                        </div>
                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                          <div class="h4 status1 t-xs">
                            {{upcomingexam.examDateTime |date:'shortTime':'+0000' }}
                            {{upcomingexam.timeZoneAbbreviation}}
                          </div>
                        </div>
                      </div>
                      <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2"
                        *ngIf="
                              upcomingexam.examStatusId == 1 ||
                              upcomingexam.examStatus == 'Waiting for Onboarding' ||
                              upcomingexam.examStatus == 'Onboarding' ||
                              upcomingexam.examStatus == 'Exam Cancelled'
                            ">
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="upcomingexam.allowReschedule && !NotAllowScheduleforCheating">
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="getConfirmation(upcomingexam)"
                            *ngIf="upcomingexam.cancel && !(upcomingexam.examStatus == 'Exam Cancelled')">
                            Cancel
                          </button>
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="reSchedule(upcomingexam)" *ngIf="upcomingexam.allowReschedule && upcomingexam.reschedule">
                            Reschedule
                          </button>
                        </div>


                      </div>
                      <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2" *ngIf="
                      upcomingexam.examStatus == 'ExamCancelled && !NotAllowScheduleforCheating'
                    ">
                <div  fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" >
                  <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                    (click)="reSchedule(upcomingexam)" >
                    Reschedule
                  </button>
                </div>
              </div>

                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                        *ngIf="upcomingexam.examStatusId === this.global.scheduling_error && upcomingexam.allowScheduleAgain && !NotAllowScheduleforCheating">
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-8"
                          (click)="reSchedule(upcomingexam)">
                          Schedule Again
                        </button>
                      </div>
                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                      fxLayoutGap="8px" *ngIf="rescheduleallow.includes(upcomingexam.examStatusId) && upcomingexam.allowReschedule && !NotAllowScheduleforCheating">
                      <button mat-button color="var(--text-color2)"
                          class="btn-4 font-bold t-xs mt-2" (click)="reSchedule(upcomingexam)"
                          >
                          Reschedule
                      </button>
                  </div>
                    

                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="mt-4 "
                        *ngIf="upcomingexam.examStatusId ==  this.global.no_Show">
                        <button
                          *ngIf="upcomingexam.allowExcuseAbsence && upcomingexam.isExcuseAbsenceSubmitted == false"
                          (click)="absence(upcomingexam,upcomingexam.personFormId)" mat-button
                          color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2">
                          Submit Excused Absence
                        </button>
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2"
                          *ngIf="upcomingexam.isExcuseAbsenceSubmitted"
                          (click)="absence(upcomingexam, upcomingexam.personFormId)">
                          View Absence
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div *ngFor="let upcomingexam of PracticeExams" class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'"
              fxFlex.xs="100%" fxFlex.sm="33%">
              <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                <div class="bg-color px-4 py-3">
                  <div class="flex justify-between space" fxLayout="row">
                    <div class="t-xs title-hed">
                      <strong>{{ upcomingexam.examName }}</strong>
                    </div>
                  </div>
                  <div class="t-xs state-elig pt-1" fxLayout="row">
                    <strong> {{ upcomingexam.eligibilityRouteName }}&ensp;<span *ngIf="upcomingexam.examStatus ==='Exam Completed'">(Attempt {{upcomingexam.attemptsMade }})</span></strong>
                  </div>
                </div>
                <div fxLayout="column">
                  <div class="px-4 t-xs" fxFlexFill>
                    <span class="status t-xs">{{
                      this.lngSrvc.curLangObj.value.currentStatus
                      }}</span><br />
                    <span><img src="{{ upcomingexam.iconUrl }}" class="inline iconSize" /></span>
                    <span class="t-xs ml-2 -mt-3 active2" [style.color]="
                    upcomingexam.examStatus == 'Exam Scheduled' ? '#00AB72'
                    : '#F7685B' ">
                      {{ upcomingexam.examStatus }}</span>
                    <div class="italic t-xs state-elig mt-2">
                      {{ upcomingexam.registeredDateTime }}
                    </div>
                    <div *ngIf="enableTestCenterDirections(upcomingexam)"
                      (click)="openTestCenterDirections(upcomingexam)" class="pt-2 active2 onhover" gdColumns="1fr">
                      Test Center Directions
                    </div>
                    <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                      gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                      <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                        <!-- <div class="status t-xs">Exam Mode</div> -->

                        <div *ngIf="upcomingexam.mode === 'Online'; else testCenter" class="h4 status t-xs">Exam Mode
                        </div>
                        <ng-template #testCenter>
                          <div class="h4 status t-xs"> Test Center Name</div>
                        </ng-template>
                      </div>
                      <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                        <div class="status t-xs">Exam Date</div>
                      </div>
                      <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                        <div class="status t-xs">Exam Time</div>
                      </div>
                    </div>
                    <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                      gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                      <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                        <!-- <div class="status1 t-xs">
                          {{ upcomingexam.examMode }}
                        </div> -->

                        <div *ngIf="upcomingexam.mode==='Online'; else testCenterValue" class="h4 status1 t-xs">
                          {{upcomingexam.mode}}</div>
                        <ng-template #testCenterValue>
                          <div class="h4 status1 t-xs">{{upcomingexam.testCenterDetails.testCenterName}}</div>
                        </ng-template>

                      </div>
                      <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                        <div class="status1 t-xs">
                          {{ upcomingexam.examDateTime | date: "MM/dd/yyyy":'+0000' }}
                        </div>
                      </div>
                      <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                        <div class="h4 status1 t-xs">
                          {{upcomingexam.examDateTime |date:'shortTime':'+0000' }}
                          {{upcomingexam.timeZoneAbbreviation}}
                        </div>
                      </div>
                    </div>
                    <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2"
                      >
                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                        *ngIf="upcomingexam.allowReschedule">
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                          (click)="getConfirmation(upcomingexam)"
                          *ngIf="upcomingexam.cancel && !(upcomingexam.examStatus == 'Exam Cancelled') && upcomingexam.examStatusId != 10">
                          Cancel
                        </button>
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                          (click)="reSchedule(upcomingexam)" *ngIf="upcomingexam.allowReschedule">
                          {{((upcomingexam.examStatusId === 1 && upcomingexam.showStatus ===1) || upcomingexam.examStatusId === 10 )?'Schedule Next Attempt':'Reschedule'}}
                        </button>
                      </div>


                    </div>

                    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                      *ngIf="upcomingexam.examStatusId === this.global.scheduling_error && upcomingexam.allowScheduleAgain && !NotAllowScheduleforCheating">
                      <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-8"
                        (click)="reSchedule(upcomingexam)">
                        Schedule Again
                      </button>
                    </div>
                    <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2"
                      *ngIf="
                    upcomingexam.examStatus == 'ExamCancelled'
                  ">
                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                          (click)="reSchedule(upcomingexam)">
                          Reschedule
                        </button>
                      </div>
                    </div>

                    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="mt-4 "
                      *ngIf="upcomingexam.examStatusId ==  this.global.no_Show">
                      <button
                        *ngIf="upcomingexam.allowExcuseAbsence && upcomingexam.isExcuseAbsenceSubmitted == false"
                        (click)="absence(upcomingexam,upcomingexam.personFormId)" mat-button
                        color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2">
                        Submit Excused Absence
                      </button>
                      <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2"
                        *ngIf="upcomingexam.isExcuseAbsenceSubmitted"
                        (click)="absence(upcomingexam, upcomingexam.personFormId)">
                        View Absence
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

              

              <div *ngIf="upcomingExams.length < 1" class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'"
                fxFlex.xs="100%" fxFlex.sm="33%">
                <div class="card shadow-none cardBorder h-full">
                  <div class="flex justify-center pt-4">
                    <img class="pt-5 pb-2" src="assets/img/register-exam1.svg" /><br />
                  </div>
                  <div class="content1 pb-6">
                    <section class="mr-5 ml-5 ">
                      <div class="bg-color">
                        <span class="
                          welc-note
                          flex
                          justify-center
                          text-center text-xs mx-4 mt-4
                        ">
                          <span class="pt-3 pb-6">
                            {{errors}}
                          </span>
                        </span>
                      </div>
                    </section>
                    <div class="flex justify-center mb-4 -mt-5 t-xs">
                      <button *ngIf="!NotAllowScheduleforCheating" class="add-new text-xs " mat-button color="primary" (click)="register()"
                        [ngClass]="{'button-disabled' : examStatus  }">
                        {{ this.lngSrvc.curLangObj.value.registerExam }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf=" WithoutActiveApplication?.length == 0 && PracticeExams.length > 0" class="px-gutter py-2" gdColumns="1fr " gdColumns.lt-md="1fr"
  gdColumns.lt-sm="1fr" exaiContainer>
  <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
    <div fxLayout="column">
      <div>
        <div class="flex text-center">
          {{ this.lngSrvc.curLangObj.value.welcome }}
          <b class="welc">
            &nbsp;{{ FirstName }} {{ MiddleName }}
            {{ LastName }}</b>
        </div>
      </div>
      <div class="pb-1" fxLayout="column">
        <div class="welc-note flex text-xs" fxLayout="column">
          Below is the list of your active applications...
        </div>
      </div>
    </div>
  </div>
  <!-- Registered Dashboard -->
  <div *ngIf="showRegisteredDashboard" class="pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap=""
    exaiContainer>
    <exai-registered-dashboard (ExamChanged)="handleExamChange($event)" [upcomingExam]="dashboardDetail"
      [upcomingExams]="upcomingExams">
    </exai-registered-dashboard>
  </div>

  <!-- Registered Dashboard -->

  <div class="pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
    <div class="justify-start" gdColumn="1 / 1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
      <div fxFlex="auto">
        <div class="pt-1" gdGap="12px" exaiContainer>
          <div class="content">
            <div fxLayout="row wrap" fxLayoutGap="10px grid">

              <div 
               class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%">
                <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                  <div class="flex justify-center pt-4">
                    <img class="pt-5 pb-2 w-3/12" src="assets/img/NoApplication-Blue.svg" /><br />
                  </div>
                  <div class="content1 pb-4">
                    <section class="mr-5 ml-5">
                      <div class="bg-color">
                        <span class="welc-note
                        flex
                        justify-center
                        text-center text-xs mx-4 mt-4">
                          <span class="pt-3 pb-6"> Fill Application Form. </span>
                        </span>
                      </div>
                    </section>
                    <div class="flex justify-center mb-4 -mt-5 t-xs" matTooltip="{{applicationMessage}}">
                      <button class="add-new text-xs" mat-button color="primary"
                        data-application-type="Start application" [ngClass]="{'button-disabled' : showApplication}"
                        (click)="addNewApplication()">
                        {{ this.lngSrvc.curLangObj.value.addNewApp }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <ng-container *ngIf="listExam?.length > 0">
                <div *ngFor="let upcomingexam of listExam" class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'"
                fxFlex.xs="100%" fxFlex.sm="33%">
                <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                  <div class="bg-color px-4 py-3">
                    <div class="flex justify-between space" fxLayout="row">
                      <div class="t-xs title-hed">
                        <strong>{{ upcomingexam.examName }}</strong>
                      </div>
                    </div>
                    <div class="t-xs state-elig pt-1" fxLayout="row">
                      <strong> {{ upcomingexam.eligibilityRouteName }}</strong>
                    </div>
                  </div>
                  <div fxLayout="column">
                    <div class="px-4 t-xs" fxFlexFill>
                      <span class="status t-xs">{{
                        this.lngSrvc.curLangObj.value.currentStatus
                        }}</span><br />
                      <span><img src="{{ upcomingexam.iconUrl }}" class="inline iconSize" /></span>
                      <span class="t-xs ml-2 -mt-3 active2" [style.color]="
      upcomingexam.examStatus == 'Exam Scheduled' ? '#00AB72'
      : '#F7685B' ">
                        {{ upcomingexam.examStatus }}</span>
                      <div class="italic t-xs state-elig mt-2">
                        {{ upcomingexam.registeredDateTime }}
                      </div>
                      <div *ngIf="enableTestCenterDirections(upcomingexam)"
                        (click)="openTestCenterDirections(upcomingexam)" class="pt-2 active2 onhover" gdColumns="1fr">
                        Test Center Directions
                      </div>
                      <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <!-- <div class="status t-xs">Exam Mode</div> -->

                          <div  class="h4 status t-xs">Exam
                            Mode</div>
                         
                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                          <div class="status t-xs">Exam Date</div>
                        </div>
                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                          <div class="status t-xs">Exam Time</div>
                        </div>
                      </div>
                      <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <!-- <div class="status1 t-xs">
            {{ upcomingexam.examMode }}
          </div> -->

                          <div  class="h4 status1 t-xs">
                            {{upcomingexam.mode}}</div>
                         

                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                          <div class="status1 t-xs">
                            {{ upcomingexam.examDateTime | date: "MM/dd/yyyy":'+0000' }}
                          </div>
                        </div>
                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                          <div class="h4 status1 t-xs">
                            {{upcomingexam.examDateTime |date:'shortTime':'+0000' }}
                            {{upcomingexam.timeZoneAbbreviation}}
                          </div>
                        </div>
                      </div>
                      <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2"
                        *ngIf="
              upcomingexam.examStatusId == 1 ||
              upcomingexam.examStatus == 'Waiting for Onboarding' ||
              upcomingexam.examStatus == 'Onboarding' ||
              upcomingexam.examStatus == 'Exam Cancelled'
            ">
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="upcomingexam.allowReschedule && !NotAllowScheduleforCheating">
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="getConfirmation(upcomingexam)"
                            *ngIf="upcomingexam.cancel && !(upcomingexam.examStatus == 'Exam Cancelled')">
                            Cancel
                          </button>
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="reSchedule(upcomingexam)" *ngIf="upcomingexam.reschedule && !NotAllowScheduleforCheating">
                            Reschedule
                          </button>
                        </div>


                      </div>

                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                        *ngIf="upcomingexam.examStatusId === this.global.scheduling_error && upcomingexam.allowScheduleAgain && !NotAllowScheduleforCheating">
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-8"
                          (click)="reSchedule(upcomingexam)">
                          Schedule Again
                        </button>
                      </div>
                  

                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="mt-4 "
                        *ngIf="upcomingexam.examStatusId ==  this.global.no_Show && !NotAllowScheduleforCheating">
                        <button
                          *ngIf="upcomingexam.allowExcuseAbsence && upcomingexam.isExcuseAbsenceSubmitted == false"
                          (click)="absence(upcomingexam,upcomingexam.personFormId)" mat-button
                          color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2">
                          Submit Excused Absence
                        </button>
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2"
                          *ngIf="upcomingexam.isExcuseAbsenceSubmitted"
                          (click)="absence(upcomingexam, upcomingexam.personFormId)">
                          View Absence
                        </button>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
              </ng-container>
            
               <ng-container *ngIf="PracticeExams?.length > 0">
                <div *ngFor="let upcomingexam of PracticeExams" class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'"
                fxFlex.xs="100%" fxFlex.sm="33%">
                <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                  <div class="bg-color px-4 py-3">
                    <div class="flex justify-between space" fxLayout="row">
                      <div class="t-xs title-hed">
                        <strong>{{ upcomingexam.examName }}</strong>
                      </div>
                    </div>
                    <div class="t-xs state-elig pt-1" fxLayout="row">
                      <strong> {{ upcomingexam.eligibilityRouteName }}&ensp;<span *ngIf="upcomingexam.examStatus ==='Exam Completed'">(Attempt {{upcomingexam.attemptsMade }})</span></strong>
                    </div>
                  </div>
                  <div fxLayout="column">
                    <div class="px-4 t-xs" fxFlexFill>
                      <span class="status t-xs">{{
                        this.lngSrvc.curLangObj.value.currentStatus
                        }}</span><br />
                      <span><img src="{{ upcomingexam.iconUrl }}" class="inline iconSize" /></span>
                      <span class="t-xs ml-2 -mt-3 active2" [style.color]="
      upcomingexam.examStatus == 'Exam Scheduled' ? '#00AB72'
      : '#F7685B' ">
                        {{ upcomingexam.examStatus }}</span>
                      <div class="italic t-xs state-elig mt-2">
                        {{ upcomingexam.registeredDateTime }}
                      </div>
                      <div *ngIf="enableTestCenterDirections(upcomingexam)"
                        (click)="openTestCenterDirections(upcomingexam)" class="pt-2 active2 onhover" gdColumns="1fr">
                        Test Center Directions
                      </div>
                      <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <!-- <div class="status t-xs">Exam Mode</div> -->
  
                          <div  class="h4 status t-xs">Exam
                            Mode</div>
                         
                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                          <div class="status t-xs">Exam Date</div>
                        </div>
                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                          <div class="status t-xs">Exam Time</div>
                        </div>
                      </div>
                      <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                          <!-- <div class="status1 t-xs">
            {{ upcomingexam.examMode }}
          </div> -->
  
                          <div  class="h4 status1 t-xs">
                            {{upcomingexam.mode}}</div>
                         
  
                        </div>
                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                          <div class="status1 t-xs">
                            {{ upcomingexam.examDateTime | date: "MM/dd/yyyy":'+0000' }}
                          </div>
                        </div>
                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                          <div class="h4 status1 t-xs">
                            {{upcomingexam.examDateTime |date:'shortTime':'+0000' }}
                            {{upcomingexam.timeZoneAbbreviation}}
                          </div>
                        </div>
                      </div>
                      <div class="flex justify-end pt-3" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2"
                   >
                        <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                          *ngIf="upcomingexam.allowReschedule">
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="getConfirmation(upcomingexam)"
                            *ngIf="upcomingexam.cancel && !(upcomingexam.examStatus == 'Exam Cancelled') && upcomingexam.examStatusId != 10">
                            Cancel
                          </button>
                          <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs"
                            (click)="reSchedule(upcomingexam)" *ngIf="upcomingexam.allowReschedule && !NotAllowScheduleforCheating">
                            {{((upcomingexam.examStatusId === 1 && upcomingexam.showStatus ===1) || upcomingexam.examStatusId === 10 )?'Schedule Next Attempt':'Reschedule'}}
                           
                          </button>
                        </div>
                      </div>
  
                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                        *ngIf="upcomingexam.examStatusId === this.global.scheduling_error && upcomingexam.allowScheduleAgain && !NotAllowScheduleforCheating">
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-8"
                          (click)="reSchedule(upcomingexam)">
                          Schedule Again
                        </button>
                      </div>
                
  
                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="mt-4 "
                        *ngIf="upcomingexam.examStatusId ==  this.global.no_Show">
                        <button
                          *ngIf="upcomingexam.allowExcuseAbsence && upcomingexam.isExcuseAbsenceSubmitted == false"
                          (click)="absence(upcomingexam,upcomingexam.personFormId)" mat-button
                          color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2 mb-3">
                          Submit Excused Absence
                        </button>
                        <button mat-button color="var(--text-color2)" class="btn-4 font-bold t-xs mt-2 mr-2"
                          *ngIf="upcomingexam.isExcuseAbsenceSubmitted"
                          (click)="absence(upcomingexam, upcomingexam.personFormId)">
                          View Absence
                        </button>
                      </div>
                    </div>
  
                  </div>
                </div>
              </div>
               </ng-container>
            
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
<!-- After Approving  Application -->


<!-- PA/SC Specific Dashboard -->
<div *ngIf="showPaScDashboard && (global.stateId === StateList.PA || global.stateId === StateList.SC)" 
     class="px-gutter py-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
  <!-- Registry Section -->
  <div class="cardBorder p-4 mt-4">
    <h3 class="text-lg font-semibold mb-4 border-b pb-2">Registry</h3>
    
    <div class="pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
      <div class="justify-start" gdColumn="1 / 1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
        <div fxFlex="auto">
          <div class="pt-1" gdGap="12px" exaiContainer>
            <div class="content">
              <div fxLayout="row wrap" fxLayoutGap="10px grid">

           

                <!-- Show existing registry cards and reciprocity cards together -->
                <div fxLayout="row wrap" fxLayoutGap="10px grid" class="w-full">
                  <!-- Existing registry cards if applications exist -->
                  <ng-container *ngIf="hasRegistryApplications">
                    <div class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                         *ngFor="let item of listCertificates">
                      <exai-cr-cards class="h-full" [data]="item" [requestData]="listRequests"></exai-cr-cards>
                    </div>
                    <div class="shadow-none justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                         *ngFor="let item of listRequest">
                      <exai-cr-cards class="h-full" [data]="item" [requestData]="listRequest"></exai-cr-cards>
                    </div>
                  </ng-container>
                 <ng-container  *ngIf="reciprocityTiles.length > 0" >
                  <div *ngFor="let tile of reciprocityTiles"
                  class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%">
               <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                 <div fxLayout="column">
                   <div class="px-4 py-6 text-center" fxFlexFill>
                     <div class="flex justify-center pb-4">
                       <img class="pt-5 pb-2 w-3/12" [src]="tile.icon" [alt]="tile.title" />
                     </div>
                    
                     <div class="t-xs state-elig mb-4">
                       {{ tile.description }}
                     </div>
                    
                    
                     <button mat-flat-button
                             color="primary"
                             class="btn-4 font-bold t-xs w-full"
                             (click)="handleTileAction(tile.action)"
                             [disabled]="showApplication">
                       <mat-icon class="mr-2">add</mat-icon>
                       {{ tile.title }}
                     </button>
                   </div>
                 </div>
               </div>
             </div>
                 </ng-container>
                  <!-- Reciprocity Cards using tile format -->
               
                  
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- PA/SC Specific Dashboard -->


<ng-template #systemCheck>
  <exai-system-check></exai-system-check>
</ng-template>
