import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { createEffect, Actions, ofType } from "@ngrx/effects";
import { of } from "rxjs";
import {
  switchMap,
  map,
  catchError,
  concatMap,
  take,
  filter,
  tap,
} from "rxjs/operators";
import { Router } from "@angular/router";
import { setErrorMessage } from "../../state/shared/shared.actions";
import { URL } from "src/app/core/url";
import {
  cancelExam,
  cartItemRemoved,
  examCancelled,
  getCart,
  // getCartItems,
  getEligibilityroute,
  getMakePayment,
  getMonthlySlots,
  getPaymentCustomerId,
  getPaymentMethod,
  getPersonForm,
  getRegisteredExam,
  getTimeSlots,
  getTimezones,
  getScheduled,
  getVoucher,
  getVoucherApply,
  getVoucherAssign,
  gotCart,
  // gotCartItems,
  gotEligibilityRoutes,
  gotExamId,
  gotMakePayment,
  gotMonthlySlots,
  gotPaymentCustomerId,
  gotPaymentMethod,
  gotPersonForm,
  gotRegisteredExam,
  gotRescheduled,
  gotTimeSlots,
  gotTimezones,
  gotScheduled,
  gotVoucher,
  gotVoucherApply,
  gotVoucherAssign,
  madeCharge,
  makeCharge,
  removeCartItem,
  reschedule,
  getTimeSlotsTestCenter,
  gotTimeSlotsTestCenter,
  addCartTC,
  createPaymentMethod,
  paymentMethodCreated,
  createPaymentCustomerId,
  paymentCustomerIdCreated,
  getExamByERIdId,
  createupdate,
  createUpdate,
  getShowRegisterExam,
  gotShowRegisterExam,
  deleteCard,
  cardDeleted,
  setPaymentErrorMessage,
  getClearCart,
  gotClearData,
  getPracticeCart,
  gotPracticeCart,
  getPracticeRegisteredExam,
  gotPracticeRegisteredExam,
  makePracticeExamCharge,
  madePracticeExamCharge,
  removePracticeCartItem,
  cartItemPracticeRemoved,
  ReschedulePracticeExam,
  gotPracticeRescheduled,
  PracticeretrySchedule,
  gotPracticeRetryscheduled,
  getPracticeExamByERIdId,
  gotPracticeExamId,
  cancelPracticeExam,
  examPracticeCancelled,
  getVoucher_validate_apply,
  gotVoucher_validate_apply,
  getTimeSlotsTestCenterFailure,
} from "./scheduled.actions";
import { SnackbarService } from "src/app/core/snackbar.service";
import { GlobalUserService } from "src/app/core/global-user.service";
import { HttpService } from "src/app/core/http-services/http.service";
import { RegisteredExamsModel } from "src/app/core/Dto/registered-exams.model";
import { HttpExamService } from "src/app/core/http-services/http.exams.service";
import { ExamModel } from "src/app/core/Dto/exam.model";
import { slot, Slot } from "./models/slot";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class ScheduledEffects {
  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private router: Router,
    private services: SnackbarService,
    private global: GlobalUserService,
    private http: HttpService,
    private _examHttpService: HttpExamService
  ) {}

  effectivelyGetTimeZones$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getTimezones),
      switchMap((action) => {
        return this.httpClient.get<any>(URL.BASE_URL + `Exam/timezones`).pipe(
          map((timezones) =>
            gotTimezones({
              Timeszones: timezones,
            })
          ),
          take(1)
        );
      })
    )
  );

  effectivelyGetTimeSlots$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getTimeSlots),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `Exam/slots/online?examId=${action.examId}&slotDate=${new Date(
                action.startDate
              ).toDateString()}&timeZone=${action.timezone}&offSet=${
                action.offset
              }&candidateId=${action.candidateId}`
          )
          .pipe(
            map((timeslots: Slot[]) =>
              gotTimeSlots({
                Slots: timeslots.filter((x) => x.strSlotTime !== null),
              })
            ),
            take(1)
          );
      })
    )
  );

  effectivelyVocherUpdate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createupdate),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.Sponsor + `Candidate/update-used-vouchers`,
            action.VocherUpdateDetails
          )
          .pipe(
            map((rescheduled) => {
              return createUpdate({
                response: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );
  effectivelyClearcartdata$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getClearCart),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL + `Exam/cart/clear?personId=${action.personId}`,
            action.body
          )
          .pipe(
            map((rescheduled) => {
              return gotClearData({
                Response: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );
  effectivelyGetCart$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getCart),
      concatMap((action) => {
        return this.httpClient
          .post<any>(URL.BASE_URL + "exam/cart", action.details)
          .pipe(
            map((cart) => {
              if (cart) {
                this.services.callSnackbaronSuccess(
                  "Added to cart successfully"
                );
              }
              return gotCart({
                cart: cart,
                isPayment: action.isPayment,
              });
            }),
            take(1),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err }));
            })
          );
      })
    )
  );

  effectivelyGetPracticeCart$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPracticeCart),
      concatMap((action) => {
        return this.httpClient
          .post<any>(URL.BASE_URL + "Exam/cart/PracticeExam", action.details)
          .pipe(
            map((cart) => {
              if (cart) {
                this.services.callSnackbaronSuccess(
                  "Added to cart successfully"
                );
              }
              return gotPracticeCart({
                cart: cart,
                isPayment: action.isPayment,
              });
            }),
            take(1),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err }));
            })
          );
      })
    )
  );

  effectivelyAddCart$ = createEffect(() =>
    this.actions$.pipe(
      ofType(addCartTC),
      concatMap((action) => {
        return this.httpClient
          .post<any>(URL.BASE_URL + "Exam/cart", action.body)
          .pipe(
            map((cart) => {
              if (cart) {
                this.services.callSnackbaronSuccess(
                  "Added to cart successfully"
                );
              }
              return gotCart({
                cart: cart,
              });
            }),
            take(1),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err }));
            })
          );
      })
    )
  );

  effectivelyGetMonthlySlots$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getMonthlySlots),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `exam/monthlySlots?month=${action.month}&year=${action.year}&timeZone=${action.timezone}`
          )
          .pipe(
            map((monthlySlots) =>
              gotMonthlySlots({
                monthlySlots: monthlySlots,
              })
            ),
            take(1)
          );
      })
    )
  );

  effectivelyGetExamId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getExamByERIdId),
      switchMap((action) => {
        return this._examHttpService
          .getExamsByERId(action.eligibilityRouteId, action.personTenantRoleId)
          .pipe(
            map((examdata: ExamModel[]) =>
              gotExamId({
                examdata: examdata,
              })
            ),
            take(1)
          );
      })
    )
  );

  effectivelyGetPracticeExamId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPracticeExamByERIdId),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `Exam/getpracticeexams?eligibilityRouteId=${action.eligibilityRouteId}&personTenantRoleId=${action.personTenantRoleId}`
          )
          .pipe(
            map((examdata: ExamModel[]) =>
              gotPracticeExamId({
                examdata: examdata,
              })
            ),
            take(1)
          );
      })
    )
  );
  effectivelyGetVocherAssign$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getVoucherAssign),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.Sponsor +
              `Voucher/assigned-to-person?personId=${action.personId}`
          )
          .pipe(
            map((examdata) =>
              gotVoucherAssign({
                VocherAssignResponse: examdata,
              })
            ),
            take(1)
          );
      })
    )
  );

  effectivelyGetEligibilityRoute$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getEligibilityroute),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `eligibilityroute/active?candidateId=${action.candidateId}`
          )
          .pipe(
            map((route) =>
              gotEligibilityRoutes({
                route: route,
              })
            ),
            take(1)
          );
      })
    )
  );

  effectivelyGetRegisteredExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getRegisteredExam),
      switchMap((action) => {
        return this.http.scheduledExam(action.candidateId).pipe(
          map((registeredexams: RegisteredExamsModel[]) =>
            gotRegisteredExam({
              registeredExams: registeredexams,
            })
          ),
          take(1)
        );
      })
    )
  );

  effectivelyGetPracticeRegisteredExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPracticeRegisteredExam),
      switchMap((action) => {
        return this.http.scheduledPracticeExam(action.candidateId).pipe(
          map((registeredexams: RegisteredExamsModel[]) =>
            gotPracticeRegisteredExam({
              registeredPracticeExams: registeredexams,
            })
          ),
          take(1)
        );
      })
    )
  );

  effectivelyMakePayment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getMakePayment),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL +
              `Exam/makepayment?personTenantRoleId=${action.tentantId}`,
            action.makepaymentBody
          )
          .pipe(
            map(
              (makepaymentresponse) =>
                gotMakePayment({
                  makePaymentResponse: makepaymentresponse,
                })
              // this.services.callSnackbaronSuccess("Added to cart successfully.")
            ),
            catchError((err) => of(setPaymentErrorMessage({ message: err }))),
            take(1)
          );
      })
    )
  );

  effectivelyReschedule$ = createEffect(() =>
    this.actions$.pipe(
      ofType(reschedule),
      switchMap((action) => {
        return this.httpClient
          .post<any>(URL.BASE_URL + "exam/reschedule", action.rescheduleBody)
          .pipe(
            map((rescheduled) => {
              if (reschedule) {
                this.services.callSnackbaronSuccess(
                  "Exam Rescheduled Successfully"
                );
                this.router.navigateByUrl("/exam-scheduled");
              }
              return gotRescheduled({
                rescheduleResponse: rescheduled,
              });
            }),
            take(1),
            catchError((err) => {
           
              return of(
                setErrorMessage({ message: err.message.message.error })
              );
            })
          );
      })
    )
  );

  effectivelyPracticeReschedule$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReschedulePracticeExam),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL + "Exam/reschedule-practice",
            action.PracticerescheduleBody
          )
          .pipe(
            map((rescheduled) => {
              if (reschedule) {
                setTimeout(() => {
                  this.router.navigateByUrl("/practice_exam");
                }, 1500);
              }
              return gotPracticeRescheduled({
                PracticerescheduleResponse: rescheduled,
              });
            }),
            take(1),
            catchError((err) =>
              of(setErrorMessage({ message: err.message.message.error }))
            )
          );
      })
    )
  );

  effectivelyPracticeRetryschedule$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PracticeretrySchedule),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL + "Exam/retry-practice-schedule",
            action.retryScheduleResponse
          )
          .pipe(
            map((scheduled) => {
              if (scheduled) {
                this.services.callSnackbaronSuccess(
                  "Exam scheduled Successfully"
                );
                this.router.navigateByUrl("/practice_exam");
              }
              return gotPracticeRetryscheduled({
                PracticeretryscheduleResponse: scheduled,
              });
            }),
            take(1),
            catchError((err) =>
              of(setErrorMessage({ message: err.message.message.error }))
            )
          );
      })
    )
  );

  effectivelyVocher$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getVoucher),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.Sponsor + `Voucher/validate-voucher`,
            action.VocherValidator
          )
          .pipe(
            map((rescheduled) => {
              return gotVoucher({
                VocherResponse: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyVocher_validate_apply$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getVoucher_validate_apply),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            environment.baseUrl + `candidate/api/Exam/validate-apply-voucher`,
            action.VocherValidator
          )
          .pipe(
            map((rescheduled) => {
              return gotVoucher_validate_apply({
                VocherResponse: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyScheduled$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getScheduled),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL +
              `Exam/makepaymentwithzero?personTenantRoleId=${action.personTentantRole}&cartId=${action.cartId}&PersonID=${action.PersonID}`,
            null
          )
          .pipe(
            map((rescheduled) => {
              return gotScheduled({
                ScheduledResponse: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyVocherApply$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getVoucherApply),
      switchMap((action) => {
        return this.httpClient
          .put<any>(
            URL.BASE_URL + `Exam/cartitem/applyvoucher`,
            action.VocherDetails
          )
          .pipe(
            map((vocher) => {
              return gotVoucherApply({
                VocherApplyResponse: vocher,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyCancelExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(cancelExam),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL +
              `exam/cancel?examScheduleId=${action.examScheduleId}&candidateId=${action.candidateId}`
          )
          .pipe(
            map((isCancelled) => {
              if (isCancelled) {
                this.services.callSnackbaronSuccess(
                  "Exam cancelled successfully"
                );
              }
              return examCancelled({ isCancelled: isCancelled });
            }),
            take(1)
          );
      })
    )
  );

  effectivelyPracticeCancelExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(cancelPracticeExam),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            environment.baseUrl +
              `schedulemsvc/api/scheduler/Practicecancel?examScheduleId=${action.examScheduleId}&candidateId=${action.candidateId}`
          )
          .pipe(
            map((isCancelled) => {
              if (isCancelled) {
                this.services.callSnackbaronSuccess(
                  "Exam cancelled successfully"
                );
              }
              return examPracticeCancelled({ isCancelled: isCancelled });
            }),
            take(1)
          );
      })
    )
  );

  effectivelyGetPersonForm$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPersonForm),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `form/personform?candidateId=${action.candidateId}&formTypeId=${action.formTypeId1}&formTypeId=${action.formTypeId2}`
          )
          .pipe(
            map((personForms) =>
              gotPersonForm({
                personForms: personForms,
              })
            ),
            take(1)
          );
      })
    )
  );
  effectivelyDeleteCartItems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(removeCartItem),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL +
              `exam/cart-item?personTenantRoleId=${action.tetantId}&cartItemId=${action.cartItemsId}`
          )
          .pipe(
            map((data) => {
              if (data) {
                this.services.callSnackbaronSuccess(
                  "Cart item deleted successfully!"
                );
                return cartItemRemoved({ isDeleted: data });
              }
            }),
            take(1),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );
  effectivelyDeletePracticeCartItems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(removePracticeCartItem),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL +
              `Exam/practiceexamcart-item?personTenantRoleId=${action.personTenantRoleId}&cartItemId=${action.cartItemsId}`
          )
          .pipe(
            map((data) => {
              if (data) {
                this.services.callSnackbaronSuccess(
                  "Cart item deleted successfully!"
                );
                return cartItemPracticeRemoved({ isPracticeDeleted: data });
              }
            }),
            take(1),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );
  effectivelyGetPaymentMethods$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPaymentMethod),
      concatMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              `paymentmsvc/api/getPaymentMethods?customerId=${action.customerId}`
          )
          .pipe(
            map((paymentMethod) =>
              gotPaymentMethod({
                paymentmethods: paymentMethod,
              })
            ),
            take(1),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );
  effectivelyMakeCharge$ = createEffect(() =>
    this.actions$.pipe(
      ofType(makeCharge),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL +
              `Exam/makepayment?personTenantRoleId=${this.global.candidateId}`,
            action.chargeBodu
          )
          .pipe(
            map((res) =>
              madeCharge({
                chargeResponse: res,
              })
            ),
            take(1),
            catchError((err) => of(setPaymentErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyMakePracticeCharge$ = createEffect(() =>
    this.actions$.pipe(
      ofType(makePracticeExamCharge),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL +
              `Exam/makepaymentForPracticeExam?personTenantRoleId=${this.global.candidateId}`,
            action.chargePracticeBodu
          )
          .pipe(
            map((res) =>
              madePracticeExamCharge({
                chargePracticeResponse: res,
              })
            ),
            take(1),
            catchError((err) => of(setPaymentErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyGetCustomerId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPaymentCustomerId),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              `paymentmsvc/api/getPaymentCustomerId?PersonTenantRoleId=${action.PersonTenantRoleId}`
          )
          .pipe(
            map((res) =>
              gotPaymentCustomerId({
                customerIdObj: res,
              })
            ),
            take(1),
            catchError((err) => {
              this.services.callSnackbaronError(err);
              return of(setErrorMessage({ message: err }));
            })
          );
      })
    )
  );

  effectivelyGetTimeSlotsTestCenter$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getTimeSlotsTestCenter),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `Exam/slots-testCenter-new?timezoneId=${action.timezone}&examId=${action.examId}&startDate=${action.startDate}&endDate=${action.endDate}&testCenterName=${action.testCenterName}&testCenterId=${action.testCenterId}&testCenterAddress=${action.testCenterAddress}&radius=${action.radius}&candidateId=${action.candidateId}&isOnline=${action.isOnline}`
          )
          .pipe(
            map((timeSlots: slot[]) =>
              gotTimeSlotsTestCenter({ slots: timeSlots })
            ),
            catchError((error) => {
              this.services.callSnackbaronError(
                "Failed to fetch time slots. Please try again."
              );
              return of(getTimeSlotsTestCenterFailure({ error }));
            }),
            take(1)
          );
      })
    )
  );

  effectivelyCreatePaymentCustomerId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createPaymentCustomerId),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER + `paymentmsvc/api/createPaymentCustomerId`,
            action.body
          )
          .pipe(
            map((res) => {
              return paymentCustomerIdCreated({
                response: res,
              });
            }),
            take(1),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyShowRegisterExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getShowRegisterExam),
      switchMap((action) => {
        return this._examHttpService
          .getshowregisterExam(action.personTenantRoleId)
          .pipe(
            map((data) =>
              gotShowRegisterExam({
                data: data,
              })
            ),
            take(1)
          );
      })
    )
  );
  effectivelyCreatePaymentMethod$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createPaymentMethod),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER +
              `paymentmsvc/api/fattmerchant/createpaymentmethod`,
            action.CreatePaymentMethod
          )
          .pipe(
            map((res) => {
              return paymentMethodCreated({
                response: res,
              });
            }),
            take(1),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyDeleteCard$ = createEffect(() =>
    this.actions$.pipe(
      ofType(deleteCard),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL_SHORTER + `paymentmsvc/api/delete-card/${action.id}`
          )
          .pipe(
            map(
              (data) => {
                if (data)
                  this.services.callSnackbaronSuccess(
                    "card sucessfully deleted"
                  );
                return cardDeleted();
              },
              catchError((err) => {
                this.services.callSnackbaronError("Error in deleting Card");
                return of(setErrorMessage({ message: err }));
              })
            )
          );
      })
    )
  );
}
