<div *ngIf="this.model.id!=feildName">
  <div *ngIf="prefilledValue.length=='0' || ShowRating">
    <div class="m-6">
      <mat-radio-group *ngIf="ShowRating">
        <ng-container *ngFor="let info of radioLabel">
          <mat-radio-button *ngIf="info.name !=''" [value]="info.value" (change)="updatedname($event)" color="primary"
            class="px-2">
            {{info.name}}
          </mat-radio-button>
        </ng-container>

      </mat-radio-group>
    </div>

    <!-- <div class="pl-8  overflow-auto">
      <form [formGroup]="candiateDetails" >
       
    <input  type="text"   formControlName="registryNumber" class= "p-2" placeholder="Search.."  [matAutocomplete]="auto"  [formControl]="registryNumber" [(ngModel)]="selectedUser" (input)="filterUsers1()" >

    <mat-autocomplete #auto="matAutocomplete"  >
      <mat-option (click)="appendData(item)"   *ngFor="let item of userData" [value]="item" >
        Employer Name:&nbsp;&nbsp;{{item.employerName}}&nbsp;&nbsp;&nbsp;&nbsp; Address:&nbsp;&nbsp;{{item.addressLine}}&nbsp;&nbsp;&nbsp;City:&nbsp;&nbsp;{{item.city}}&nbsp;&nbsp;&nbsp;State:&nbsp;&nbsp;{{item.state}}&nbsp;&nbsp;&nbsp;Zipcode:&nbsp;&nbsp;{{item.zipCode}})
      </mat-option>
    </mat-autocomplete>
    
   
    </form>
    </div> -->


    <div *ngIf="enableSerach && this.DetailsUser.formTypeId !='5'" class="pl-8">
      <form [formGroup]="candiateDetails">
        <input type="text" formControlName="registryNumber" class="p-2" placeholder="Search..">
        <button mat-button class="btn-1 text-xs ml-2" (click)="serachresult()">Search</button>
      </form>
    </div>

    <div *ngIf="enableSerach && this.DetailsUser.formTypeId =='5'" class="pl-8">

      <form [formGroup]="candiateDetails">

        <!-- <input type="text"  formControlName="registryNumber" class= "p-2" placeholder="Search..">
      
          <button  mat-button class="btn-1 text-xs ml-2"  (click)="serachresult()" >Search</button> -->

        <div gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px">

          <div class="InformationDetails" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1 /-1">

            <mat-autocomplete class="InformationDetails" #auto="matAutocomplete"
              (optionSelected)="onSelectionChange($event)">

              <mat-option (click)="appendData(view)" class="text-xs matoptions " *ngFor="let view of userData"
                [value]="view.employerName">

                <span *ngIf="(view.employerName !='' && view.employerName !=null)">Employer
                  Name:{{view.employerName}}|
                  Address:{{view.addressLine}}|
                  Zipcode:{{view.zipCode}}|
                  PhoneNumber:{{view.mobilePhoneNumber}}
                </span>
                <div class="flex justify-center">
                  <span *ngIf="(view.value !='' && view.value !=null)">{{view.value}}</span>
                </div>

              </mat-option>

            </mat-autocomplete>

            <mat-form-field appearance="outline" class="width">
              <input type="text" placeholder="Search..." matInput id="selction"
                class="custom-search shadow-none p-2 pb-2" [(ngModel)]="selectedUser" [formControl]="registryNumber"
                (input)="filter()" [matAutocomplete]="auto" (keydown.enter)="enterData($event)" required>

              <button mat-button *ngIf="selectedUser" matSuffix mat-icon-button aria-label="Clear"
                (click)=clearEmployerDetails($event)>

                <mat-icon>close</mat-icon>

              </button>

            </mat-form-field>

            <p class="alert" *ngIf="registryNumber.invalid">This field is required</p>
          </div>
        </div>

      </form>

    </div>
    <div *ngIf="enableSerachDetails && DetailsUser.formTypeId != '5'">
      <div class="flex justify-end   addressDetails1 ">
        <mat-icon class="cursor-pointer  pr-6  " (click)="closeList()">close</mat-icon>
      </div>
      <div class="custom borderclass card addressDetails overflow-auto">
        <p class="border p-3 cursor-pointer" *ngFor="let item of selecteddata" (click)="appendData(item)">
          Name:&nbsp;&nbsp;{{item.FullName}}&nbsp;&nbsp;&nbsp; Registry
          Number:&nbsp;&nbsp;{{item.CertNumber}}&nbsp;&nbsp;RegistryStatus:&nbsp;&nbsp;{{item.RegistryStatus}}</p>
        <div class="flex justify-center">
          <h5 *ngIf="selecteddata.length=='0'">No Data Found</h5>
        </div>

      </div>
    </div>

    <div *ngIf="enableSerachDetails && DetailsUser.formTypeId == '5'">
      <div class="flex justify-end   addressDetails1 ">
        <mat-icon class="cursor-pointer  pr-6  " (click)="closeList()">close</mat-icon>
      </div>
      <div class="custom borderclass card addressDetails overflow-auto">
        <p class="border p-3 cursor-pointer" *ngFor="let item of selecteddata" (click)="appendData(item)">Employer
          Name:&nbsp;&nbsp;{{item.employerName}}&nbsp;&nbsp;&nbsp;&nbsp;
          Address:&nbsp;&nbsp;{{item.addressLine}}&nbsp;&nbsp;&nbsp;City:&nbsp;&nbsp;{{item.city}}&nbsp;&nbsp;&nbsp;State:&nbsp;&nbsp;{{item.state}}&nbsp;&nbsp;&nbsp;Zipcode:&nbsp;&nbsp;{{item.zipCode}}
        </p>
        <div class="flex justify-center">
          <h5 *ngIf="selecteddata.length=='0'">No Data Found</h5>
        </div>

      </div>
    </div>
    <ng-container *ngIf="DetailsUser.formTypeId != '5'">
      <div class="mt-4 pl-8" *ngFor="let item of CandidateDetails">
        <p>Selected Candidate Details</p>
        <p>Candidate Name: {{item.candidatename}}</p>
        <p>Candidate Id: {{item.candidateId}}</p>
        <p>Registry Number: {{item.registryNumber}}</p>
      </div>
    </ng-container>

    <ng-container *ngIf="DetailsUser.formTypeId == '5'">
      <div class="mt-4 pl-8" *ngFor="let item of CandidateDetailsValue">
        <h6>Selected Employer Details</h6>
        <p>Employer Name: {{item.EmployerName}}</p>
        <p>Address: {{item.AddressName}}</p>
        <p>City: {{item.City}}</p>
        <p>State: {{item.State}}</p>
        <p>Zipcode: {{item.Zipcode}}</p>
        <p>PhoneNumber: {{item.mobilePhoneNumber}}</p>


        <!-- <button mat-button *ngIf="CandidateDetailsValue" matSuffix mat-icon-button aria-label="Clear"
                (click)=clearSelectedData()>

                <mat-icon>close</mat-icon>

      </button> -->
      </div>


    </ng-container>


  </div>
  <ng-container *ngIf="this.global.personEventId?.formTypeId != 5 && this.global.userDetails.getValue().roleId == 14">
    <div class="pl-8" *ngIf="prefilledValue.length!='0'">
      <div class="mt-4" *ngFor="let item of prefilledValue1">
        <p class="t-xs">Selected Candidate Details</p>
        <p>Candidate Name: {{item.candidatename}}</p>
        <p>Candidate Id: {{item.candidateId}}</p>
        <p>Registry Number: {{item.registryNumber}}</p>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="DetailsUser.formTypeId == '5'">
    <div class="pl-8" *ngIf="prefilledValue1.length!='0'">
      <div class="mt-4" *ngFor="let item of prefilledValue1">
        <p class="t-xs">Selected Employer Details</p>
        <p>Employer Name: {{item.EmployerName}}</p>
        <p>Address: {{item.AddressName}}</p>
        <p>City: {{item.City}}</p>
        <p>State: {{item.State}}</p>
        <p>Zipcode: {{item.Zipcode}}</p>
        <p>PhoneNumber: {{item.mobilePhoneNumber}}</p>
      </div>
      <button mat-button
        *ngIf="prefilledValue.length!='0' && (this.DetailsUser.certStatus !='2' && this.DetailsUser.certStatus !='7' )"
         class="btn-1 text-xs1  mt-3" aria-label="Clear" (click)=clearEmployerDetails($event)>
        Remove Employer
      </button>
    </div>
  </ng-container>

  <ng-container *ngIf="(this.global.userDetails.getValue().roleId == 17 || this.global.userDetails.getValue().roleId == 14 ) && (this.global.personEventId?.formTypeId == 5 || this.global.clickedviewIconDetails?.element?.formTypeId == 5)">
    <div class="pl-8" *ngIf="prefilledValue.length!='0'">
      <div class="mt-4" *ngFor="let item of prefilledValue">
        <p class="t-xs">Selected Employer Details</p>
        <p>Employer Name: {{item.EmployerName}}</p>
        <p>Address: {{item.AddressName}}</p>
        <p>City: {{item.City}}</p>
        <p>State: {{item.State}}</p>
        <p>Zipcode: {{item.Zipcode}}</p>
        <p>PhoneNumber: {{item.mobilePhoneNumber}}</p>

      </div>

    </div>
  </ng-container>


</div>

<button #divClick (click)="checkDisabled()"></button>