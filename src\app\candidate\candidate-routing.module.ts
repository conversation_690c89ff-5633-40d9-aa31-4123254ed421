import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '../core/auth.guard';
// import { AuthGuard } from '../core/auth.guard';
import { CustomLayoutComponent } from './custom-layout/custom-layout.component';
import { PaymentComponent } from './scheduled/payment/payment.component';

const routes: Routes = [
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
  {
    path: '', component: CustomLayoutComponent,
    children: [
      {
        path: 'dashboard',
        loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'application',
        loadChildren: () => import('./application/application.module').then(m => m.ApplicationModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'forms',
        loadChildren: () => import('./forms-wrapper/forms-wrapper.module').then(m => m.FormsWrapperModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'exam-scheduled',
        loadChildren: () => import('./scheduled/scheduled.module').then(m => m.ScheduledModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'grievance-form',
        loadChildren: () => import('./grievance-form/grievance-form.module').then(m => m.GrievanceFormModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'absense-form',
        loadChildren: () => import('./excused-absense/excused-absense.module').then(m => m. ExcusedAbsenseModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'manage-profile',
        loadChildren: () => import('./manage-profile/manage-profile.module').then(m => m.ManageProfileModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'help',
        loadChildren: () => import('./help/help.module').then(m => m.HelpModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'registry',
        loadChildren: () => import('./registry/registry.module').then(m => m.RegistryModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'practice_exam',
        loadChildren: () => import('./practice Exam/practice.module').then(m => m.PracticeExamModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'practice-skills',
        loadChildren: () => import('./practice-skills/practice-skills.module').then(m =>m.PracticeSkillsModule),
        canActivate: [AuthGuard]
      },
      {
        path: 'practice-skill/payment/:id',
        component: PaymentComponent,
        canActivate: [AuthGuard]
      }
    ],
  }

  
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CandidateRoutingModule { }
