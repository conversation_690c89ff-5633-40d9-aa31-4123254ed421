import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { FormGroup } from "@angular/forms";
import {Router,ActivatedRoute} from "@angular/router";
import {
  DynamicCheckboxModel,
  DynamicColorPickerModel,
  DynamicDatePickerModel,
  DynamicFileUploadModel,
  DynamicFormArrayModel,
  DynamicFormControlEvent,
  DynamicFormGroupModel,
  DynamicFormLayout,
  DynamicFormService,
  DynamicInputModel,
  DynamicRadioGroupModel,
  DynamicRatingModel,
  DynamicSelectModel,
  DynamicSliderModel,
  DynamicSwitchModel,
  DynamicTextAreaModel
} from "@ng-dynamic-forms/core";
import { DynamicFormComponentComponent } from "./dynamic-form-component/dynamic-form-component.component";
import { FormBuilderService } from "./form-builder.service";
import { editData, form, saveFormEvent, section, sectionCompleteEvent, validatedSubmission, DateAttr, parsingMetaData, fetchOptionsFromApi } from './form-builder.types';
@Component({
  selector: "exai-form-builder",
  templateUrl: "./form-builder.component.html",
  styleUrls: ["./form-builder.component.scss"],
})
export class FormBuilderComponent implements OnInit {
  
  @Input() public static existingForm?: any = null;
  @Input() public static existingEditData?: any = null;

  public static staticFormServiceInstance: DynamicFormService;

  @Input() existingForm?: any = null;
  @Input() existingEditData?: any = null;

  @Input() existingUserData?: any = null;
  @Input() elementId?: string = null;

  @Input() showSave?: boolean = true;
  @Input() showJSONS?: boolean = true;
  @Input() showToolbar?: boolean = true;
  @Input() showOnlyForm?: boolean = false;
  @Input() showStatic?: boolean = false;
  @Input() disabled?: boolean = false;
  @Input() element?: any = null;
  @Input() openAll?: boolean = false;
  @Input() quesGrpCss?: any = {
      children: [""],
      container: ["w-full my-2 mr-2"],
      control: ["w-full"],
      errors: [""],
      group: ["flex flex-row"],
      hint: [""],
      host: ["flex flex-row"],
      label: [""],
      option: [""],
  };
  @Input() feildCss?: any = {
      children: "",
      container: "w-full my-2 mr-2",
      control: "w-full",
      errors: "",
      group: "flex flex-row",
      hint: "",
      host: "flex flex-row",
      label: "",
      option: "",
  };

  @Input() submitButtonRef?: Array<ElementRef> = null;
  @Output() formSubmitEvent?:EventEmitter<validatedSubmission> = new EventEmitter<validatedSubmission>();

  @Output() saveForm?: EventEmitter<saveFormEvent> = new EventEmitter<saveFormEvent>();

  @Output() sectionCompleted?: EventEmitter<sectionCompleteEvent> = new EventEmitter<sectionCompleteEvent>();

  @Output('onBlur') exaiOnBlur: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('onChange') exaiOnChange: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('onFocus') exaiOnFocus: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('onMatEvent') exaiOnMatEvent: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('initalSectionValidationEvent') initalSectionValidationEvent: EventEmitter<Array<sectionCompleteEvent>>
    = new EventEmitter<Array<sectionCompleteEvent>>();

  @ViewChild('dynamicForm') dynamicForm: DynamicFormComponentComponent;
  constructor(
    public formBuilderService: FormBuilderService,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    public formService: DynamicFormService
  ) {
    FormBuilderComponent.staticFormServiceInstance = this.formService;
  }
  ngOnInit(): void {
    if(this.existingEditData) FormBuilderComponent.existingEditData = this.existingEditData
    if(this.existingForm) FormBuilderComponent.existingForm = this.existingForm
    this.formBuilderService.genesisForm = FormBuilderComponent.existingForm ? FormBuilderComponent.parseform(FormBuilderComponent.existingForm) : null;
    this.existingForm = FormBuilderComponent.existingForm ? FormBuilderComponent.parseform(FormBuilderComponent.existingForm) : null;

    this.formBuilderService.editData = FormBuilderComponent.existingEditData ? FormBuilderComponent.existingEditData : new editData();
    this.formBuilderService.defaultQGPcss = this.quesGrpCss;
    this.formBuilderService.defaultFeildCss = this.feildCss;
    this.filterAndAmmendMalformedMetaData();
  }


  forceSubmitWithValidation() {
    this.dynamicForm.validateSubmission();
  }

  private filterAndAmmendMalformedMetaData() {
    // <feildID,index> of parsing metaData
    // the below if check has been put to make sure that this method is not called when we are simply loading the buit form for viewing
    if (!this.showOnlyForm && FormBuilderComponent.existingEditData){
      var formLayoutMap: Map<string, number> = new Map([]);
      Object.keys(this.formBuilderService.genesisForm.formLayout).forEach((key: string, index: number) => {
        formLayoutMap.set(key, index);
      })
      for (let sectionIndex = 0; sectionIndex < this.formBuilderService.genesisForm.sections.length; sectionIndex++) {
        var optionsFromApiMap: Map<string, number> = new Map([]);
        var parsingMetaDataMap: Map<string, number> = new Map([]);
        var dateAttributesMap: Map<string, number> = new Map([]);
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.forEach((value: parsingMetaData, index: number) => {
          parsingMetaDataMap.set(value.feildID.toLowerCase(), index);
        })
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes?.forEach((value: parsingMetaData, index: number) => {
          dateAttributesMap.set(value.feildID.toLowerCase(), index);
        });
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi.forEach((value: Array<fetchOptionsFromApi>, index: number) => {
          value.forEach((x: fetchOptionsFromApi, i: number) => {
            optionsFromApiMap.set(x.feildID.toLowerCase(), i);
          });
        });
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroups.forEach((qgp: DynamicFormGroupModel & DynamicFormArrayModel, index: number) => {
          formLayoutMap.delete(qgp.id.toLowerCase());
          qgp.type == "ARRAY" ? 
          qgp.groupPrototype.forEach((value: any) => {
            parsingMetaDataMap.delete(value.id.toLowerCase());
            dateAttributesMap.delete(value.id.toLowerCase());
            optionsFromApiMap.delete(value.id.toLowerCase());
            formLayoutMap.delete(value.id.toLowerCase());
          })
            :
            qgp.group.forEach((value: any) => {
              parsingMetaDataMap.delete(value.id.toLowerCase());
              dateAttributesMap.delete(value.id.toLowerCase());
              optionsFromApiMap.delete(value.id.toLowerCase());
              formLayoutMap.delete(value.id.toLowerCase());
            })
        });
        this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData
          .filter((x: parsingMetaData) => {
            return !parsingMetaDataMap.has(x.feildID);
          })
        if (this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes) {
          this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes
            .filter((x: DateAttr) => {
              return !parsingMetaDataMap.has(x.feildID);
            })
        }
        for (let optIndex = 0; optIndex < this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi.length; optIndex++) {
          this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[optIndex] = this.formBuilderService.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.optionsFromApi[optIndex]
            .filter((x: fetchOptionsFromApi) => {
              return !optionsFromApiMap.has(x.feildID);
            })
        }
      }
      var tempFormLayout = {};
      Object.entries(this.formBuilderService.genesisForm.formLayout).forEach((entry) => {
        if (!formLayoutMap.has(entry[0])) tempFormLayout[entry[0]] = entry[1];
      })
      this.formBuilderService.genesisForm.formLayout = tempFormLayout;
    }
  }

  onBlur($event: DynamicFormControlEvent) {
    this.exaiOnBlur.emit($event);
  }
  onChange($event: DynamicFormControlEvent) {
    this.exaiOnChange.emit($event);
  }
  onFocus($event: DynamicFormControlEvent) {
    this.exaiOnFocus.emit($event);
  }
  onMatEvent($event: DynamicFormControlEvent) {
    this.exaiOnMatEvent.emit($event);
  }
  handleSaveEvent() {
    if (this.formBuilderService.genesisForm) {
      this.formBuilderService.formGroup.forEach((x: FormGroup) => {
        x.reset();
      })
      this.saveForm.emit({
        formJSONstring: JSON.stringify(this.formBuilderService.genesisForm),
        formEditData:JSON.stringify(this.formBuilderService.editData)
      });
    }
  }
  navigate(navigateTo: string): void {
    this.existingForm = this.formBuilderService.genesisForm;
    this.router.navigate([navigateTo],{relativeTo:this.activatedRoute});
  }
  handleSubmit(event: validatedSubmission) {
    this.formSubmitEvent.emit(event);
  }
  public static parseform(data: any) {
    
    let retForm = new form({
      id: data.id,
      name: data.name,
      description: data.description,
      instructions: data.instructions,
      showInstructionInIButton: data.showInstructionInIButton,
      fetchFromApi: data.fetchFromApi,
      requestDetails:data.requestDetails,
    })
    retForm.formLayout = data.formLayout as DynamicFormLayout;
    for (let sec of data.sections) {
      let sect = new section({
        id: sec.id,
        name: sec.name,
        description: sec.description,
        instructions: sec.instructions,
        showInstructionInIButton:sec.showInstructionInIButton
      })
      sect.QuestionGroupsMetaData = sec.QuestionGroupsMetaData;
      for (let quesGrp of sec.QuestionGroups) {
        sect.QuestionGroups.push(FormBuilderComponent.getGroupORArray(quesGrp));
      }
      retForm.sections.push(sect);
    }
    return retForm;
  }
  public static getGroupORArray(quesGrp: any) {
    return quesGrp.type === "ARRAY" ?
            new DynamicFormArrayModel({
                id: quesGrp.id,
                name: quesGrp.name,
                label: quesGrp.label,
                initialCount: quesGrp.initialCount,
                groupFactory: () => {
                    return FormBuilderComponent.getFeildValue( quesGrp )
                }
            })
            :
            new DynamicFormGroupModel({
              id: quesGrp.id,
              name: quesGrp.name,
              label: quesGrp.label,
              group: FormBuilderComponent.getFeildValue(
                quesGrp,
              ),
            });
  }
  private static getFeildValue(feildDetails: any) {
    let retValue = [];
    let dataDetail = feildDetails.type == "ARRAY" ? feildDetails.groupPrototype : feildDetails.group;
    for (let i = 0; i < dataDetail.length; i++) {
      switch (dataDetail[i].type) {
        // different types of input feilds
        case "INPUT":
          retValue.push(new DynamicInputModel(dataDetail[i]));
          break;
        case "SELECT":
          retValue.push(
            new DynamicSelectModel<any>(dataDetail[i])
          );
          break;
        case "CHECKBOX":
          retValue.push(
            new DynamicCheckboxModel(dataDetail[i])
          );
          break;
        case "RADIO_GROUP":
          retValue.push(
            new DynamicRadioGroupModel(dataDetail[i])
          );
          break;
        case "DATEPICKER":
          retValue.push(
            new DynamicDatePickerModel(dataDetail[i])
          )
          break;
        case "FILE_UPLOAD":
          retValue.push(
            new DynamicFileUploadModel(dataDetail[i])
          );
          break;
        case "SLIDER":
          retValue.push(
            new DynamicSliderModel(dataDetail[i])
          );
          break;
        case "SWITCH":
          retValue.push(
            new DynamicSwitchModel(dataDetail[i])
          );
          break;
        case "TEXTAREA":
          retValue.push(
            new DynamicTextAreaModel(dataDetail[i])
          );
          break;
          case "RATING":
            retValue.push(
              new DynamicRatingModel(dataDetail[i])
            );
            break;
            case "COLORPICKER":
            retValue.push(
              new DynamicColorPickerModel(dataDetail[i])
            );
            break;
      }
    }
    
    return retValue;
  }
}


