// @formatter:off
:root {
    // Generic
    --padding-gutter: 1rem;
    --padding: 1.5rem;
    --padding-16: 1rem;
    --padding-12: 0.75rem;
    --padding-8: 0.5rem;
    --padding-4: 0.25rem;
  
    --color-primary: 0, 118, 193;
    
    --color-primary-contrast: 255, 255, 255;
  
    --color-accent: 255, 64, 129;
    --color-accent-contrast: 255, 255, 255;
  
    --color-warn: 244, 67, 54;
    --color-warn-contrast: 255, 255, 255;
  
    @screen lg {
      --padding-gutter: 1.5rem;
    }
  
    // Typography
    --font: theme('fontFamily.sans');
    --font-weight-medium: 500;
    --font-caption: #{mat-font-weight($config, caption) #{mat-font-size($config, caption)}/#{mat-line-height($config, caption)} mat-font-family($config, caption)};
    --font-body-1: #{mat-font-weight($config, body-1) #{mat-font-size($config, body-1)}/#{mat-line-height($config, body-1)} mat-font-family($config, body-1)};
    --font-body-2: #{mat-font-weight($config, body-2) #{mat-font-size($config, body-2)}/#{mat-line-height($config, body-2)} mat-font-family($config, body-2)};
    --font-subheading-1: #{mat-font-weight($config, subheading-1) #{mat-font-size($config, subheading-1)}/#{mat-line-height($config, subheading-1)} mat-font-family($config, subheading-1)};
    --font-subheading-2: #{mat-font-weight($config, subheading-2) #{mat-font-size($config, subheading-2)}/#{mat-line-height($config, subheading-2)} mat-font-family($config, subheading-2)};
    --font-headline: #{mat-font-weight($config, headline) #{mat-font-size($config, headline)}/#{mat-line-height($config, headline)} mat-font-family($config, headline)};
    --font-title: #{mat-font-weight($config, title) #{mat-font-size($config, title)}/#{mat-line-height($config, title)} mat-font-family($config, title)};
    --font-display-1: #{mat-font-weight($config, display-1) #{mat-font-size($config, display-1)}/#{mat-line-height($config, display-1)} mat-font-family($config, display-1)};
    --font-display-2: #{mat-font-weight($config, display-2) #{mat-font-size($config, display-2)}/#{mat-line-height($config, display-2)} mat-font-family($config, display-2)};
    --font-display-3: #{mat-font-weight($config, display-3) #{mat-font-size($config, display-3)}/#{mat-line-height($config, display-3)} mat-font-family($config, display-3)};
    --font-display-4: #{mat-font-weight($config, display-4) #{mat-font-size($config, display-4)}/#{mat-line-height($config, display-4)} mat-font-family($config, display-4)};
  
    // Transitions
    --trans-ease-in-out: all var(--trans-ease-in-out-duration) var(--trans-ease-in-out-timing-function);
    --trans-ease-in-out-duration: #{$swift-ease-in-out-duration};
    --trans-ease-in-out-timing-function: #{$swift-ease-in-out-timing-function};
    --trans-ease-out: all var(--trans-ease-out-duration) var(--trans-ease-out-timing-function);
    --trans-ease-out-duration: #{$swift-ease-out-duration};
    --trans-ease-out-timing-function: #{$swift-ease-out-timing-function};
    --trans-ease-in: all var(--trans-ease-in-duration) var(--trans-ease-in-timing-function);
    --trans-ease-in-duration: #{$swift-ease-in-duration};
    --trans-ease-in-timing-function: #{$swift-ease-in-timing-function};
    --trans-shadow-duration: #{$mat-elevation-transition-duration};
    --trans-shadow-timing-function: #{$mat-elevation-transition-timing-function};
    --trans-shadow: box-shadow var(--trans-shadow-duration) var(--trans-shadow-timing-function);
  
    --text-color: #{$dark-primary-text};
    --text-color1: #7D7D7D;
    --text-color2: #0076C1;
    --text-color3: #27262C;
    --text-color4: #6D6D6D;
    --text-color5: #C4C4C4;
    --text-dropdown: #11263C;
    --text-popup: #5C5C5C;
    --text-edit:#A7A8AC;
    --text-toggle: #A7A8AC;
    --status-color: #209E91;
  
  
    --text-color-light: #{$light-primary-text};
    --text-secondary: #{$dark-secondary-text};
    --text-secondary-light: #{$light-secondary-text};
    --text-hint: #{$dark-disabled-text};
    --text-hint-light: #{$light-disabled-text};
  
    --text-copy: #6D6D6D;
    --text-profile: #C4C4C4;
    --text-input: #C9C9C9;
  
  
    // Foreground
    --foreground-divider: #{map-get(map-get($exai-theme, foreground), divider)};
  
    // Background
    // --background-base: rgb(245, 245, 248);
    --background-base: #FFFFFF;
    --background-base1: #e5e5e5a9;
    --background-base2: #FAFAFA;
    --background-base3: #E9F5F5;
    --background-card: #{map-get(map-get($exai-theme, background), card)};
    --background-app-bar: #{map-get(map-get($exai-theme, background), app-bar)};
    --background-hover: #{map-get(map-get($exai-theme, background), hover)};
  
    // Elevation
    --elevation-default: var(--elevation-z6);
    --elevation-z0: none;
    --elevation-z1: #{exai-elevation(1)};
    --elevation-z2: #{exai-elevation(2)};
    --elevation-z3: #{exai-elevation(3)};
    --elevation-z4: #{exai-elevation(4)};
    --elevation-z5: #{exai-elevation(5)};
    --elevation-z6: #{exai-elevation(6)};
    --elevation-z7: #{exai-elevation(7)};
    --elevation-z8: #{exai-elevation(8)};
    --elevation-z9: #{exai-elevation(9)};
    --elevation-z10: #{exai-elevation(10)};
    --elevation-z11: #{exai-elevation(11)};
    --elevation-z12: #{exai-elevation(12)};
    --elevation-z13: #{exai-elevation(13)};
    --elevation-z14: #{exai-elevation(14)};
    --elevation-z15: #{exai-elevation(15)};
    --elevation-z16: #{exai-elevation(16)};
    --elevation-z17: #{exai-elevation(17)};
    --elevation-z18: #{exai-elevation(18)};
    --elevation-z19: #{exai-elevation(19)};
    --elevation-z20: #{exai-elevation(20)};
  
    // Sidenav
    --sidenav-width: 210px;
    --sidenav-collapsed-width: 72px;
    --sidenav-background: #{$sidenav-background};
    // --sidenav-background: white;
    --sidenav-color: white;
  
    // Sidenav Item
    --sidenav-item-padding: var(--padding);
    --sidenav-toolbar-background: #{darken($sidenav-background, 1.5)};
    --sidenav-item-background-active: #{darken($sidenav-background, 3)};
    --sidenav-item-color: #A1A2B6;
    --sidenav-item-color-active: #{$light-primary-text};
    --sidenav-item-icon-color: #494B74;
    --sidenav-item-icon-color-active: rgb(var(--color-primary));
    --sidenav-item-icon-gap: 16px;
    --sidenav-item-icon-size: 17px;
    --sidenav-item-label-size: 0.75rem;
    --sidenav-item-border-color: transparent;
    --sidenav-item-border-color-active: rgb(var(--color-primary));
    --sidenav-item-dropdown-background: #{darken($sidenav-background, 3)};
    --sidenav-item-dropdown-background-hover: #{darken($sidenav-background, 4)};
    --sidenav-item-dropdown-gap: 12px;
  
    // Toolbar
    --toolbar-height: 60px;
    --toolbar-background: white;
    --toolbar-color: #{$dark-primary-text};
    --toolbar-icon-color: rgb(var(--color-primary));
  
    // Secondary Toolbar
    --secondary-toolbar-background: var(--background-card);
    --secondary-toolbar-height: 54px;
  
    // Navigation
    --navigation-height: 60px;
    --navigation-background: var(--background-card);
    --navigation-color: var(--text-secondary);
  
    // Footer
    --footer-height: 36px;
    --footer-z-index: 100;
    --footer-background: var(--background-card);
    --footer-color: var(--text-color);
    --footer-elevation: 0 -10px 30px 0 rgba(82, 63, 104, .06);
  
    // Page Layouts
    --page-layout-header-height: 200px;
    --page-layout-toolbar-height: 64px;
  
    // Misc
    --blink-scrollbar-width: 12px;
    --default-icon-size: 24px;
    --border-radius: 4px;
  
    // Border
    --border: 1px solid #7d7d7d66;
    --border-hrz: 0.3px solid #7d7d7d59;
    --border-radius: 4px;
    --btn-border: 1px solid #0076C1;
    --mat-border-color: black;
  
    // table
    --theader :#cdcdcd;
    --table-header:#F1F2F2;
    --text-register: #7D7D7D;
    --header-font:#11263C;
    --menu-color:#A7A8AC;
    
    // button
    --button-background: #0076C1;
    --button-color: #FFFFFF;
  
  
    // icon
    --icon-color: #c4c4c4;
    --mat-icon-color : #0076C1;
    --mat-panel-header: #11263C;
  
  
    // mat-check-box
    --check-box-color: #005691;
    
    // stroke-button
    --change-request: #EE9400;
    --change-request-border : 1px solid #EE9400;
    --delete-border : 1px solid #F7685B;
    --text-delete : #F7685B;
    --sing-out: #D80027;
    --sing-out-background : #d8002823;
    --save-draft-border: 1px solid #0076C1;
    --text-color22: #0077c141;
  
    //mat-expansion
    --dropdown-color: #11263C;
  
    // hover
    --hover-color: #e4e3e3;
    --snackbar-text: #5C5C5C;
    --snackbar-error: #E80400;
    --snackbar-warning: #EE9400;
  
  }
  
  
  // @formatter:on
  