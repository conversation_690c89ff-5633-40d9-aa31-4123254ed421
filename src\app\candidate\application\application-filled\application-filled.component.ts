import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import { LanguageService } from "src/app/core/language.service";
import { PersonForm, ShowRegisterExamModel } from "../application.types";
import {
  clearApplicationState,
  getPersonForms,
  getShowRegisterExam,
} from "../state/application.actions";
import { selectApplicationState, selectorShowRegisterExam$} from "../state/application.selectors";
import { GlobalUserService } from 'src/app/core/global-user.service';
import moment from "moment";
import { FormStatuses, FormTypes } from "src/app/core/Dto/enum";
import { ApplicationState } from "../state/application.state";
import { MatSnackBar } from "@angular/material/snack-bar";
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpService } from "src/app/core/http-services/http.service";
import { StateList } from "../../forms-wrapper/forms-wrapper.types";
@Component({
  selector: "exai-application-filled",
  templateUrl: "./application-filled.component.html",
  styleUrls: ["./application-filled.component.scss"],
})
export class ApplicationFilledComponent implements OnInit {
  gridColumns = 4;
  listExam: any;
  errors: any;
  exam: boolean = false;
  details: boolean = false;
  applicationList: PersonForm[] = [];
  hasExpired:boolean = false;
  examStatus: boolean = false;
  startNewApplicationFlag : boolean = false;
  registerExamFlag : boolean = false;
  applicationSubmittedDate: string;
  NotAllowScheduleforCheating:boolean = false
  appSubmittedDate: string;
  exampending: PersonForm;
  hasActiveApplications: boolean = false;
  error: any;
  hasAnyRejectedChangeRequestItems: boolean = false;
  showRegisterExam: ShowRegisterExamModel;
applicationMessage:string
showApplication:boolean = false

  constructor(
    private router: Router,
    public lngSrvc: LanguageService,
    public store: Store<ApplicationState>,
    private global: GlobalUserService,
    private snackbar:SnackbarService,
    private http:HttpService,
    private cdr :ChangeDetectorRef,
    private httpService:HttpService
  ) {
    this.store.dispatch<Action>(clearApplicationState());
    this.store.dispatch(getPersonForms({ candidateId: this.global.candidateId, formTypeId1: FormTypes.Accomodation, formTypeId2: FormTypes.Application }));
    this.store.dispatch<Action>(getShowRegisterExam({ personTenantRoleId: this.global.candidateId }));
  }

  public ngOnInit(): void {
    this.store.select(selectApplicationState).pipe().subscribe((applicationState: ApplicationState) => {
      if (!applicationState.loading && applicationState.personForms.length === 0) {

        this.examStatus = this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false ?false:this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved?false:true
        this.errors = (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false) || (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved)? 'Your application is approved and you may now register for the exam': 'Approved Application Required'
        this.startNewApplicationFlag = true;
      } else {
        if (applicationState.personForms.length > 0) {
          this.hasAnyRejectedChangeRequestItems = applicationState.personForms.filter(x=>x.status === 'Rejected' || x.status === 'Change Request').length > 0;
          let approvedApplicationCount = applicationState.personForms.filter(x => (x.formTypeId == FormTypes.Application && x.status == "Approved")).length;
          let accomodationFormCount = applicationState.personForms.filter(x => x.formTypeId == FormTypes.Accomodation && x.status == "Pending").length;
          let rejected = applicationState.personForms.filter(x => x.status == "Rejected").length;
          // if (accomodationFormCount > 0) {
          //   let approvedAccomodationCount = applicationState.personForms.filter(x => (x.formTypeId == FormTypes.Accomodation && x.status == "Approved")).length;
          //   if (approvedApplicationCount > 0 && approvedAccomodationCount > 0 && rejected > 0) {
          //     this.examStatus = false;
          //     this.errors = 'Your application is approved and you may now register for the exam'
          //   }
          //   else {
          //     this.examStatus = true;
          //     this.errors = 'Approved Application Required'
          //   }
          //   if (approvedApplicationCount > 0 && approvedAccomodationCount > 0) {
          //     this.examStatus = false;
          //     this.errors = 'Your application is approved and you may now register for the exam'
          //   }else if(approvedApplicationCount > 0 && rejected > 0){
          //     this.examStatus = false;
          //     this.errors = 'Your application is approved and you may now register for the exam'
          //   }
          //   else {
          //     this.examStatus = true;
          //     this.errors = 'Approved Application Required'
          //   }
          // }
          // else {
          //   if (approvedApplicationCount > 0 && rejected > 0) {
          //     this.examStatus = false;
          //     this.errors = 'Your application is approved and you may now register for the exam'
          //   }
          //   else {
          //     this.examStatus = true;
          //     this.errors = 'Approved Application Required'
          //   }
          //   if (approvedApplicationCount > 0) {
          //     this.examStatus = false;
          //     this.errors = 'Your application is approved and you may now register for the exam'
          //   } else {
          //     this.examStatus = true;
          //     this.errors = 'Approved Application Required'
          //   }
        // }
        // if(this.showRegisterExam?.showRegister == true && this.showRegisterExam.isApplicationApproved == true && this.showRegisterExam.isAccommodationApproved == true){
        //   this.examStatus = false
        //   this.errors =  'Your application is approved and you may now register for the exam'
        // }else if(this.showRegisterExam?.isApplicationApproved == true && accomodationFormCount == 0){
        //   this.examStatus = false
        //   this.errors =  'Your application is approved and you may now register for the exam'
        // }else{
        //   this.examStatus = true
        //   this.errors =  'Approved Application Required'
        // }
                this.examStatus = this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false ?false:this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved?false:true
        this.errors = (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false) || (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved)? 'Your application is approved and you may now register for the exam': 'Approved Application Required'
          
          let personForms = applicationState.personForms.map((ele) => (Object.assign({}, ele, { applicationSubmittedDate: moment(ele.lastUpdatedDate).format("MMMM Do, YYYY / h:mm A") }, { appSubmittedDate: moment(ele.submittedDate).format('MM/DD/YYYY') })))
          this.applicationList = personForms;

          //this.startNewApplicationFlag = (applicationState.personForms.filter(x => (x.status !== 'Rejected' && x.status !== 'Cancelled' && x.status !== 'Expired')).length === 0)
          
          if(applicationState.personForms.filter(x => (x.statusId !== FormStatuses.Rejected && x.statusId !== FormStatuses.Cancelled && x.statusId !== FormStatuses.Expired)).length === 0){
            this.startNewApplicationFlag = (applicationState.personForms[applicationState.personForms.length - 1].statusId !== FormStatuses.Rejected);
          }

          this.hasActiveApplications = this.applicationList.filter(x => x.status === 'Approved' ).length > 0;
        }
      }
    })

    this.store.select(selectorShowRegisterExam$).subscribe((data: ShowRegisterExamModel) => {
      if (data ) {
        this.showRegisterExam = data;
      }
    });

    this.http.getAllData(this.global.personId).subscribe((data:any)=>{
         if(data){
              data.result.forEach((x,i)=>{
                if(x.RegistryStatusId === FormStatuses.revoked || x.RegistryStatusId === FormStatuses.summary || x.RegistryStatusId === FormStatuses.suspension){
                  this.showApplication = true
                  this.applicationMessage =x.RegistryStatusId === FormStatuses.revoked?"Certificate as Revoked":x.RegistryStatusId === FormStatuses.summary?"Certificate as Summary Suspensed":x.RegistryStatusId === FormStatuses.suspension?"Certificate is Suspensed":null
                 }else{
                this.showApplication = false
                this.applicationMessage =""
               }
            })
           
         }
    })
    this.getIncidentstatus()

  }

  public addNewApplication(): void {
    this.router.navigate(["/application/select-application"]);
  }
  public summary(item: PersonForm, index: number): void {
    this.router.navigate(['application', 'application-form', FormTypes.Application, this.global.candidateId, item.eligibilityRouteId, this.global.stateId, '0', item.personFormId, item.formCode]);
    this.global.userstatus.next(item)
  }

  public editApplication(item: PersonForm, index: number): void {
    this.router.navigate(['application', 'application-form', FormTypes.Application, this.global.candidateId, item.eligibilityRouteId, this.global.stateId, '0', item.personFormId, item.formCode]);
    this.global.userstatus.next(item)

  }

  ngAfterViewInit(): void {
  }

  register() {

      this.router.navigate(["/exam-scheduled/register/"]);
    
  }

  getIncidentstatus() {
    this.http.getIncidentStatus(this.global.userDetails.value.personId).subscribe((data: boolean) => {
      if (data) {
        this.NotAllowScheduleforCheating = data
      }
    })
  }

  public applyAgain(): void {
    this.router.navigate(["/application/select-application"]);
  }
  ngOnDestroy(): void { }
}



