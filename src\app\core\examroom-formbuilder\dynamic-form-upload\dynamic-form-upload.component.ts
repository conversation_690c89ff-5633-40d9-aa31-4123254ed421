import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DynamicFileUploadModel, DynamicFormControlComponent, DynamicFormControlCustomEvent, DynamicFormControlLayout, DynamicFormLayout, DynamicFormLayoutService, DynamicFormValidationService, DynamicRatingModel } from '@ng-dynamic-forms/core';
import { FormUploadControlComponent } from '../form-upload-control/form-upload-control.component';

@Component({
  selector: 'exai-dynamic-form-upload',
  templateUrl: './dynamic-form-upload.component.html',
  styleUrls: ['./dynamic-form-upload.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DynamicFormUploadComponent extends DynamicFormControlComponent {
  @Input() group!: FormGroup;
  @Input() layout?: DynamicFormControlLayout;
  @Input() model!: DynamicRatingModel;

  @Output() blur: EventEmitter<any> = new EventEmitter();
  @Output() change: EventEmitter<any> = new EventEmitter();
  @Output() customEvent: EventEmitter<DynamicFormControlCustomEvent> = new EventEmitter();
  @Output() focus: EventEmitter<any> = new EventEmitter();

  @ViewChild(FormUploadControlComponent) myCustomFormControlComponent!: FormUploadControlComponent;

  constructor(protected layoutService: DynamicFormLayoutService,
    protected validationService: DynamicFormValidationService) {
    super(layoutService, validationService);
}
}
