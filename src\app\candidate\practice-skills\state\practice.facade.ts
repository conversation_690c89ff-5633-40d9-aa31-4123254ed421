import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { PracticeSkill } from "./models/practice-skill.model";
import {
  selectBundlesPageNo,
  selectBundlesPageSize,
  selectBundlesTotalRecords,
  selectPracticeSkillBundles,
  selectPracticeSkillBundlesLoading,
  selectPracticeSkillByGuidError,
  selectPracticeSkillByGuidLoading,
  selectPracticeSkillByGuidResponse,
  selectPracticeSkills,
  selectPracticeSkillsLoading,
  selectPracticeSkillsModes,
  selectPracticeSkillsModesLoading,
} from "./practice-skills.selectors";
import { Store } from "@ngrx/store";
import { PracticeBundleSkill } from "./models/practice-skill-bundle.model";
import * as PracticeActions from "../state/practice-skills.actions";
import { PracticeSkillResponse } from "./models/practice-skill-response.model";
import { map, tap } from "rxjs/operators";
import {
  PracticeSkillModeDetail,
  PracticeSkillMode,
} from "./models/practice-skill-mode.model";

@Injectable({ providedIn: "root" })
export class PracticeFacade {
  //Skills Mode Observable
  skillModes$: Observable<PracticeSkillMode> = this.store.select(
    selectPracticeSkillsModes
  );
  skillsModeLoading$: Observable<boolean> = this.store.select(
    selectPracticeSkillsModesLoading
  );

  //Skills Observable
  skill$: Observable<PracticeSkill[]> = this.store.select(selectPracticeSkills);
  skillsLoading$: Observable<boolean> = this.store.select(
    selectPracticeSkillsLoading
  );

  //Bundles Observables
  bundles$: Observable<PracticeBundleSkill[]> = this.store.select(
    selectPracticeSkillBundles
  );
  bundleLoading$: Observable<boolean> = this.store.select(
    selectPracticeSkillBundlesLoading
  );

  bundlesPageNo$: Observable<number> = this.store.select(selectBundlesPageNo);
  bundlesPageSize$: Observable<number> = this.store.select(
    selectBundlesPageSize
  );
  bundlesTotalRecords$: Observable<number> = this.store.select(
    selectBundlesTotalRecords
  );

  // Selector Observables
  skillByGuidResponse$: Observable<PracticeSkillResponse | null> =
    this.store.select(selectPracticeSkillByGuidResponse);

  skillByGuidLoading$: Observable<boolean> = this.store.select(
    selectPracticeSkillByGuidLoading
  );

  skillByGuidError$: Observable<any> = this.store.select(
    selectPracticeSkillByGuidError
  );

  constructor(private store: Store) {}

  // Dispatch Methods:

  loadPracticeSkills(pageNo: number, pageSize: number): void {
    this.store.dispatch(
      PracticeActions.loadPracticeSkills({ pageNo, pageSize })
    );
  }

  loadPracticeSkillsMode(): void {
    this.store.dispatch(PracticeActions.loadPracticeSkillsMode());
  }

  loadPracticeSkillBundles(pageNo: number, pageSize: number) {
    this.store.dispatch(
      PracticeActions.loadPracticeSkillBundles({ pageNo, pageSize })
    );
  }

  // New dispatch method to load skill by GUID
  loadPracticeSkillByGuid(practiceSkillGuid: string): void {
    this.store.dispatch(
      PracticeActions.loadPracticeSkillByGuid({ practiceSkillGuid })
    );
  }
}
