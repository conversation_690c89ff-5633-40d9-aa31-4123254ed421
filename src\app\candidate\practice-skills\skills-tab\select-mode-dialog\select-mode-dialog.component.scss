.dialog-body {
  border-radius: 4px;
  border-width: 0.5px;
  opacity: 1;
  border-color: #e5e5e5;
  background-color: #fafafa;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
}

/* Add this for selected state */
.dialog-body.selected {
  background-color: #e5f1f9; /* light blue background */
  border-color: #2563eb; /* blue border */
}

.select_text {
  color: #1b75bb;
}

.link-text {
  color: #1b75bb;
}

.close-btn {
  // color: var(--text-color2);
  background-color: transparent;
  border: none;
  cursor: pointer;
  width: 2rem;
  height: 2rem;
  transition: background-color 0.2s, color 0.2s, transform 0.1s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #d32f2f;
    transform: scale(1.05);
    border-radius: 50%;
  }

  &:focus {
    outline: none;
  }
}

.description_text {
  color: #7d7d7d;
}

.meta-value1 {
  color: #1b75bb;
}
.meta-label {
  color: #c4c4c4;
}
.cancel-btn {
  border: 1px solid #ef4444;
  color: #ef4444;
  border-radius: 0.375rem;
  background-color: transparent;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: #fef2f2;
}
.proceed-btn {
  background-color: #e5f1f9;
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: background-color 0.3s ease;
}

.proceed-btn:enabled {
  color: white;
  background-color: #1b75bb;
}
.per_attempt {
  color: var(--text-edit);
}
.proceed-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.go-back-btn {
  color: #1b75bb;
}
