import { DOCUMENT } from '@angular/common';
import { Directive, ElementRef, Inject, OnInit } from '@angular/core';
import { fromEvent, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Directive({
  selector: '[exaiFreeDrag]'
})
export class FreeDragDirective implements OnInit {

  private static curMaxZIndex: number = 0;

  private element: HTMLElement;
  private initialX: number = null;
  private initialY: number = null;
  private currentX = 0;
  private currentY = 0;

  private subscriptions: Subscription[] = [];

  constructor(
    private elementRef: ElementRef,
    @Inject(DOCUMENT) private document: any
  ) { }

  ngOnInit(): void {
    this.element = this.elementRef.nativeElement as HTMLElement;
    this.initialX = this.element.clientLeft;
    this.initialY = this.element.clientTop;
    this.element.style.zIndex = '0';
    this.initDrag();
  }

  initDrag(): void {
    this.element.addEventListener('click', () => {
      this.element.style.zIndex = String(Number(FreeDragDirective.curMaxZIndex) + 1);
      FreeDragDirective.curMaxZIndex = Math.max(FreeDragDirective.curMaxZIndex, Number(this.element.style.zIndex))
    });
    // 1
    const dragStart$ = fromEvent<MouseEvent>(this.element, "mousedown");
    const dragEnd$ = fromEvent<MouseEvent>(this.document, "mouseup");
    const drag$ = fromEvent<MouseEvent>(this.document, "mousemove").pipe(
      takeUntil(dragEnd$)
    );

    // 2
    let initialX: number,
      initialY: number;
    let dragSub: Subscription;

    // 3
    const dragStartSub = dragStart$.subscribe((event: MouseEvent) => {
      initialX = event.clientX - this.currentX;
      initialY = event.clientY - this.currentY;
      // if (this.initialX == null) this.initialX = event.clientX;
      // if (this.initialY == null) this.initialY = event.clientY;
      this.element.classList.add('free-dragging');

      // 4
      dragSub = drag$.subscribe((event: MouseEvent) => {
        event.preventDefault();

        this.currentX = event.clientX - initialX;
        this.currentY = event.clientY - initialY;

        this.element.style.transform =
          "translate3d(" + this.currentX + "px, " + this.currentY + "px, 0)";
      });
    });

    // 5
    const dragEndSub = dragEnd$.subscribe(() => {
      initialX = this.currentX;
      initialY = this.currentY;
      this.element.classList.remove('free-dragging');
      if (dragSub) {
        dragSub.unsubscribe();
      }
    });

    // 6
    this.subscriptions.push.apply(this.subscriptions, [
      dragStartSub,
      dragSub,
      dragEndSub,
    ]);
  }

  putToOriginalPlace() {
    if (this.initialX != null && this.initialY != null) {
      this.element.style.transform = "translate3d(" + this.initialX + "px, " + this.initialY + "px, 0)";
      this.currentX = 0;
      this.currentY = 0;
    }
  }
  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s?.unsubscribe());
  }

}
