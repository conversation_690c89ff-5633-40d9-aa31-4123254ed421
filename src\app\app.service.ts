import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { ActivatedRoute, DefaultUrlSerializer, NavigationStart, Router } from "@angular/router";
import { DecodedIdentityToken, UserDetails } from "./candidate/candiate.types";
import jwt_decode from 'jwt-decode';
import { HttpClient, HttpErrorResponse } from "@angular/common/http";
import { URL } from './core/url';
import { NavigationItem } from "src/@exai/interfaces/navigation-item.interface";
import { LanguageService } from "./core/language.service";
import { IconService } from './core/icon.service';
import { Settings } from "luxon";
import { GlobalUserService } from "./core/global-user.service";

@Injectable({
    providedIn: 'root'
})
export class AppService {
    authorizedInformation: DecodedIdentityToken = null;
    userInfomration: UserDetails = null;

    constructor(private route: ActivatedRoute, private http: HttpClient,
        public lngSrvc: LanguageService, private iconService: IconService,
        @Inject(LOCALE_ID) private localeId: string, private router: Router,
        private global: GlobalUserService) {
        Settings.defaultLocale = this.localeId;

        // this.router.events.subscribe(event => { //NavigationStart
        //     if (event instanceof NavigationStart) {
        //         ;
        //         if (event.url.indexOf("token=") !== -1) {
        //             let stringArray = event.url.split("token=");
        //             if (stringArray.length > 1) {
        //                 let token = stringArray[1];
        //                 if (token === 'null')
        //                     this.router.navigateByUrl('/denied');
        //                 else this.userAuthorize(token);
        //             }
        //         }
        //     }
        // });

        const querySubscription = this.route.queryParams.pipe().subscribe(params => {
            if (params && params.token) {
                this.userAuthorize(params.token);
                querySubscription.unsubscribe();
            } else {
                try {
                    const userInfo: DecodedIdentityToken = this.global.getUserData();
                    userInfo ? (this.authorizedInformation = userInfo, this.getUserInformation(userInfo.email)) : null;
                } catch (e) {
                    this.router.navigateByUrl('/denied');
                }
            }
        });
    }

    userAuthorize(token: string, email: string = null, firstName: string = null, lastName: string = null): void {
        ;
        var userInfo: DecodedIdentityToken = null;
        if (token) {
            userInfo = jwt_decode(token);
            if (email) userInfo.email = email;
            if (firstName) userInfo.given_name = firstName;
            if (lastName) userInfo.family_name = lastName;
            this.global.setUserData(userInfo);
            this.global.setUserIdToken(token);
        }
        this.authorizedInformation = userInfo;
        this.getUserInformation(userInfo.email);
    }

    getUserInformation(email: string): void {
        let result=encodeURIComponent(email);
        const params = { EmailId: result };
        this.http.get(URL.ACCOUNT_BASE_URL + `/getParameters?EmailId=${result}`)
            .subscribe((data: UserDetails) => {
                this.userInfomration = data;
                this.global.setUserInformation(data);
                this.global.clientId = data.clientId;
                this.global.stateId = data.stateId;
                this.global.personId = data.personId
                this.router.navigateByUrl('/dashboard');
            }, (error: HttpErrorResponse) => {
                this.router.navigateByUrl('/denied');
            })
    }

    getNavigationItems(): NavigationItem[] {
        return this.routes;
    }

    private routes:NavigationItem[] = [
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.dashboard,
            route: '/dashboard',
            icon: this.iconService.dashboardIcon,
            tooltip: this.lngSrvc.curLangObj.value.dashboard
        },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.application,
            route: '/application',
            icon: this.iconService.applicationIcon
        },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.practice_skills,
            route: '/practice-skills',
            icon: this.iconService.practice_skills
        },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.examscheduled,
            route: '/exam-scheduled',
            icon: this.iconService.scoresIcon
        },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.grievanceform,
            route: '/grievance-form',
            icon: this.iconService.grievienceIcon
        },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.excuseabsence,
            route: '/absense-form',
            icon: this.iconService.grievienceIcon
        },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.manageprofile,
            route: '/manage-profile',
            icon: this.iconService.demographicIcon
        },
        //  uncommment to see help on the side menu
        // {
        //     type: 'link',
        //     label: this.lngSrvc.curLangObj.value.help,
        //     route: '/help',
        //     icon: this.iconService.helpIcon
        // },
        {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.registry,
            route: '/registry',
            icon: this.iconService.demographicIcon
        }
        ,
         {
            type: 'link',
            label: this.lngSrvc.curLangObj.value.practice,
             route: '/practice_exam',
             icon: this.iconService.demographicIcon
         },
        
    ]; 
}

