import { createFeatureSelector, createSelector } from "@ngrx/store";
import { AbsenseModuleState } from "./excused.model";
import { DashboardState } from "./excused.store";
// import {getDashboardState}from "../../dashboard/state/dashboard.selectors";
// import {DashboardState} from "../../dashboard/state/dashboard.state";



export const ABSENSE_MODULE_STATE = "ABSENSE_MODULE_STATE";
const absenseModuleState = createFeatureSelector<AbsenseModuleState>(ABSENSE_MODULE_STATE);

export const DASHBOARD_STATE_NAME = 'DashboardModuleState';
export const APPLICATION_STATE_NAME = "ApplicationModuleState";
export const getDashboardState = createFeatureSelector<DashboardState>(DASHBOARD_STATE_NAME);

export const selectorLoadAllSuccess = createSelector(
  absenseModuleState,
  (state) => {
    return state.AbsenseFormList;
  }
);

export const selectorLoadReportGrievance = createSelector(
  absenseModuleState,
  (state) => {
    return state.excusedAbsense
  }
);

export const selectorLoadSavedReportGrievance = createSelector(
  absenseModuleState,
  (state) => {
    return state.saveGrievance
  }
)

export const selectorLoadSaveDraftReportGrievance = createSelector(
  absenseModuleState,
  (state) => {
    return state.saveDraftGrievance
  }
)


export const selectUpcommmingExam = createSelector(
  absenseModuleState, (state) =>{
    return state.upcomingExam
})
export const selectorViewGrievance = createSelector(
  absenseModuleState,
  (state) => {
    return state.viewFormStatus
  }
)

export const selectorViewGrievanceProgress = createSelector(
  absenseModuleState,
  (state) => {
    return state.viewFormProgress
  }
)

export const get_isCancelled = createSelector(absenseModuleState, (state)=>{
  return state.deletedGrievanceForm;
});