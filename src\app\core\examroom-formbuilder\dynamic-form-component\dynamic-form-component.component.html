<ng-container *ngIf="!showStatic">
    <div class="card shadow-none fb-ht-fixed w-full formBuilderCard" fxFlex fxFlex.xs="auto">
        <mat-card class="formCard" *ngIf="genesisForm && genesisForm.name">
            <h4 class="mt-0 mb-4 formName font-bold">{{this.genesisForm ? this.genesisForm.name : "Built Form"}}</h4>
            <div *ngIf="genesisForm">
                <h2>
                    <p class="header mr-10">{{this.genesisForm.description}}
                        <mat-icon class="cursor-pointer text-xl ml-2" (click)="formInstructions.classList.toggle('h-0')" *ngIf="this.genesisForm.showInstructionInIButton">
                            info_outline
                        </mat-icon>
                    </p>
                </h2><br>
                <div #formInstructions [innerHTML]="genesisForm.instructions" class="w-full overflow-auto" [ngClass]="'h-0'"></div>
            </div>
        </mat-card>
        <div class="w-full my-1" [id]="elementId ? elementId: 'dynform'">
            <mat-card #dynform class="form-card pt-0 w-full" *ngIf="genesisForm">
                <div class="form-card w-full">
                    <mat-accordion class="w-full" id="dynamicForm" [multi]="openAll">
                        <mat-expansion-panel class="w-full exapansionBody" *ngFor="let section of genesisForm.sections; let sectionIndex = index;" [expanded]="sectionIndex == 0 || openAll">
                            <mat-expansion-panel-header class="text-xs w-full expansion flex flex-row">
                                <span class="flex items-center" *ngIf="section.name">{{section.name}} &nbsp;</span>
                                <span class="flex items-center" *ngIf="section.description">{{section.description}}
                                    <mat-icon class="cursor-pointer text-base ml-2 mt-2"
                                              *ngIf="section.showInstructionInIButton"
                                              (click)="expandInstructions($event,'sectionInstructions'+sectionIndex.toString())">
                                        info_outline</mat-icon>
                                </span>
                            </mat-expansion-panel-header>
                            <div [id]="'sectionInstructions'+sectionIndex.toString()" style="display: none;" class="w-full overflow-auto border info-content mb-2 mt-2">
                                <p [innerHTML]="transformHTML(section.instructions)" class="p-2  t-xs t-gray"></p>
                            </div>
                            <div class="w-full" *ngIf="section.QuestionGroups && formGroupArray[sectionIndex]">
                                <form [formGroup]="formGroupArray[sectionIndex]" class="flex">
                                    <!-- <dynamic-material-form class="w-full flex flex-col flex-wrap relative" [group]="formGroupArray[sectionIndex]" [layout]="formLayout" [model]="section.QuestionGroups" (change)="onChange($event,sectionIndex)"  (keyup)="onKey($event,sectionIndex)" (paste)="onPaste(sectionIndex)"  (blur)="onBlur($event)" (focus)="onFocus($event)" (cut)="oncut(sectionIndex)" (matEvent)="onMatEvent($event)"
                                        #DynamicFormComponent> -->
                                        <dynamic-material-form class="w-full flex flex-col flex-wrap relative" [group]="formGroupArray[sectionIndex]" [layout]="formLayout" [model]="section.QuestionGroups" (change)="onChange($event,sectionIndex)"    (blur)="onBlur($event)" (focus)="onFocus($event)"  (matEvent)="onMatEvent($event)"  (input)="onKeydownMain($event.target.value)"
                                        #DynamicFormComponent>
                                        <ng-container *ngFor="let formArrayModel of section.QuestionGroupsMetaData.formArrayModelIds;let modeIdIndex=index">
                                            <ng-template [modelId]="formArrayModel.modelID" let-group let-index="index" let-context="context">
                                                <button *ngIf="!getSectionDisabled(sectionIndex)" mat-button class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2" (click)="removeItem(sectionIndex,modeIdIndex,context, index)">
                                                    <mat-icon>close</mat-icon>
                                                    {{formArrayModel.removeLabel}}
                                                </button>
                                                <button *ngIf="!getSectionDisabled(sectionIndex)" mat-button class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2" (click)="insertItem(sectionIndex,modeIdIndex,group.context, group.index + 1)">
                                                    <mat-icon>add</mat-icon>
                                                    {{formArrayModel.addLabel}}
                                                </button>
                                            </ng-template>
                                            <ng-template *ngIf="!getSectionDisabled(sectionIndex)" [modelId]="formArrayModel.parentModelID" let-group>
                                                <button mat-button class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2" (click)="insertItem(sectionIndex,modeIdIndex,null, 1)">
                                                    <mat-icon>add</mat-icon>
                                                    {{formArrayModel.addLabel}}
                                                </button>
                                            </ng-template>
                                        </ng-container>
                                        <ng-container *ngFor="let feildPairs of section.QuestionGroupsMetaData.ABrelatedFeilds">
                                            <ng-template [modelId]="feildPairs.feild1ID">
                                                <p><strong>OR</strong></p>
                                            </ng-template>
                                        </ng-container>
                                        <ng-container *ngFor="let quesGps of section.QuestionGroupsMetaData.qGpWithInstructions">
                                            <ng-template [modelId]="quesGps.qgpID" class="">
                                                <span class="flex text-xs f-medium">{{quesGps.label}}
                                                    <mat-icon class="cursor-pointer text-base ml-2 "
                                                              (click)="expandInstructions($event,'quesInstructions'+quesGps.qgpID)">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                                <div [id]="'quesInstructions'+quesGps.qgpID" style="display:none;" class="w-full overflow-auto border info-content mb-2 mt-2 ">
                                                    <p [innerHTML]="transformHTML(quesGps.instructions)" class="p-2 t-xs t-gray"></p>
                                                </div>
                                            </ng-template>
                                        </ng-container>
                                    </dynamic-material-form>
                                </form>
                            </div>
                        </mat-expansion-panel>
                    </mat-accordion>
                </div>
                <button *ngIf="!submitButtonRef" mat-button class="btn-1 py-2 text-xs font-semibold" (click)="validateSubmission()">
                    Submit
                </button>
            </mat-card>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="showStatic">
    <div class="card shadow-none fb-ht-fixed w-full h-full" fxFlex fxFlex.xs="auto">
        <div class="w-full " [id]="elementId ? elementId: 'dynform'">
            <mat-card #dynform class="form-card px-1 w-full " *ngIf="genesisForm">
                <div class="form-card w-full" id="dynamicForm">
                    <div class="w-full " *ngFor="let section of genesisForm.sections; let sectionIndex = index;">
                        <div class="flex-row flex">
                            <h4 class="  pl-6  mb-2 formName t-color  flex items-center" *ngIf="genesisForm.sections[sectionIndex].name">{{section.name}} &nbsp;</h4>
                            <mat-icon class="cursor-pointer text-base ml-2 " *ngIf="section.showInstructionInIButton" (click)="expandInstructions($event,'sectionInstructions'+sectionIndex.toString())">
                                info_outline
                            </mat-icon>
                        </div>
                        <div [id]="'sectionInstructions'+sectionIndex.toString()" style="display: none; width: 90%;" class="ml-6 overflow-auto border info-content mb-2 mt-2">
                            <p [innerHTML]="transformHTML(section.instructions)" class="p-2  t-xs t-gray"></p>
                        </div>

                        <div class="w-full" *ngIf="section.QuestionGroups && formGroupArray[sectionIndex]">
                            <form [formGroup]="formGroupArray[sectionIndex]" class="">
                                <dynamic-material-form-control *ngFor="let model of section.QuestionGroups" class="w-full flex flex-col flex-wrap relative px-6" [group]="formGroupArray[sectionIndex]" [layout]="formLayout" [model]="model" (change)="onChange($event,sectionIndex)" (blur)="onBlur($event)"
                                    (focus)="onFocus($event)" (matEvent)="onMatEvent($event)">
                                    <ng-container *ngFor="let formArrayModel of section.QuestionGroupsMetaData.formArrayModelIds;let modeIdIndex=index">
                                        <ng-template [modelId]="formArrayModel.modelID" let-group let-index="index" let-context="context">
                                            <button *ngIf="!getSectionDisabled(sectionIndex)" mat-button class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2" (click)="removeItem(sectionIndex,modeIdIndex,context, index)">
                                                <mat-icon>close</mat-icon>
                                                {{formArrayModel.removeLabel}}
                                            </button>
                                            <button *ngIf="!getSectionDisabled(sectionIndex)" mat-button class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2" (click)="insertItem(sectionIndex,modeIdIndex,group.context, group.index + 1)">
                                                <mat-icon>add</mat-icon>
                                                {{formArrayModel.addLabel}}
                                            </button>
                                        </ng-template>
                                        <ng-template *ngIf="!getSectionDisabled(sectionIndex)" [modelId]="formArrayModel.parentModelID" let-group>
                                            <button mat-button class="bg-white hover:bg-gray-100 text-color font-semibold px-3 py-1 border border-gray-400 rounded mr-4 my-2" (click)="insertItem(sectionIndex,modeIdIndex,null, 1)">
                                                <mat-icon>add</mat-icon>
                                                {{formArrayModel.addLabel}}
                                            </button>
                                        </ng-template>
                                    </ng-container>
                                    <ng-container *ngFor="let feildPairs of section.QuestionGroupsMetaData.ABrelatedFeilds">
                                        <ng-template [modelId]="feildPairs.feild1ID">
                                            <p><strong>OR</strong></p>
                                        </ng-template>
                                    </ng-container>
                                    <ng-container *ngFor="let quesGps of section.QuestionGroupsMetaData.qGpWithInstructions">
                                        <ng-template [modelId]="quesGps.qgpID" class="">
                                            <span class="flex text-xs f-medium">{{quesGps.label}}
                                                <mat-icon class="cursor-pointer text-base ml-6"
                                                          (click)="expandInstructions($event,'quesInstructions'+quesGps.qgpID)">
                                                    info_outline
                                                </mat-icon>
                                            </span>
                                            <div [id]="'quesInstructions'+quesGps.qgpID" style="display:none; width: 90%;" class="w-full overflow-auto border info-content mb-2 mt-2 ">
                                                <p [innerHTML]="transformHTML(quesGps.instructions)" class="p-2 t-xs t-gray"></p>
                                            </div>
                                        </ng-template>
                                    </ng-container>
                                </dynamic-material-form-control>
                            </form>
                        </div>
                    </div>
                </div>
                <button *ngIf="!submitButtonRef" mat-button class="btn-1 py-2 text-xs font-medium" (click)="validateSubmission()">
                    Submit
                </button>
            </mat-card>
        </div>
    </div>
</ng-container>