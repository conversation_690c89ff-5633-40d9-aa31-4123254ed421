import { PersonFormNoteModel } from "./personformnote.model";

export class PersonFormModel{
    public constructor(init?:Partial<PersonFormModel>) {
        Object.assign(this, init);
    }

    id : number = 0;
    personTenantRoleId : number = 0;
    formId : number = 0;
    code : string = '';
    active: boolean = false;
    reviewCompleted :   boolean = false;
    deleted : boolean = false;
    personEventId : number = 0;
    submittedDate: Date = null;
    dataDetail: string = '';
    formResponse: string = '';
    formTypeId: number = 0;
    grievanceReport: PersonFormNoteModel[] = [];
    changeRequestNotes: PersonFormNoteModel[] = [];
    statusId?: number = 0;
}

