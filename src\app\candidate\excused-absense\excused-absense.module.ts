import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExcusedAbsenseRoutingModule } from './excused-absense-routing.module';
import { ExcusedAbsenseComponent } from './excused-absense.component';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { MatCardModule } from '@angular/material/card';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { FormBuilderModule } from 'src/app/core/examroom-formbuilder/form-builder.module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { StoreModule } from '@ngrx/store';
import { ABSENSE_MODULE_STATE } from '../excused-absense/state/excused.selectors';
import { reducer } from '../excused-absense/state/excused.reducer';
import { EffectsModule } from '@ngrx/effects';
import { ExcusedEffects } from '../excused-absense/state/excused.effects';


@NgModule({
  declarations: [
    ExcusedAbsenseComponent
  ],
  imports: [
    CommonModule,
    ExcusedAbsenseRoutingModule,
    MatCardModule,
    FlexLayoutModule,
    ContainerModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    NgDynamicBreadcrumbModule,
    FormBuilderModule,
    MatTooltipModule,
    MatMenuModule,
    MatSnackBarModule,
    StoreModule.forFeature(ABSENSE_MODULE_STATE, reducer),
    EffectsModule.forFeature([ExcusedEffects]),
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class ExcusedAbsenseModule { }
