import { Form, AbsenseModuleState, Excusedlist,  upcomingExam, ExcusedAbsense } from "./excused.model";

export const store: AbsenseModuleState = {
    AbsenseFormList: [],
  excusedAbsense: new ExcusedAbsense(),
  saveGrievance: null,
  saveDraftGrievance: null,
  viewFormStatus: null,
  viewFormProgress: [],
  deletedGrievanceForm:null,
  upcomingExam:[],
  
}


export interface DashboardState {
  form: Form[];
  upcomingExam: upcomingExam[];
  // personForms: PersonForm[],
  isCancelled: boolean,

}


export const initDashboardState: DashboardState = {
  form: [],
  upcomingExam: [],
  // personForms: [],
  isCancelled: null,
};
