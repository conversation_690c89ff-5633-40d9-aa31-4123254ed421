export const environment = {
  production: true,
  name: "prod",
  baseUrl: "https://cna365api.examroom.ai/",
  redirectUrl: "https://cna365.examroom.ai/",
  redirectUrls: "https://cna365.examroom.ai/",
  candidate: "candidate",
  client: "client",
  training: "/training",
  sponsor: "/voucher",
  state:"/loader/manage-applications",
  Employees:"/loader/manage-all-users",
  Evalutor:"https://cna365.examroom.ai/gis/validate-gisaccess",
  Finance:"/training",
  examroomapiUrl:"https://api.examroom.ai/api/",
  taotaker:'https://oral.examroom.ai/taoTestTaker/',
  /// client updated id for paypal ////
  PayPalConfigurationId:"AT4WtY3pqlZReoVFkIrWngudLJhMcG9oXju25bsvZniEzsWBtpI1jYBcZO22nQaTV-CWPiroWePWljoC",
  config: {
    iceServers: [{
      urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
    },
    {
      username: "brucewayne",
      credential: "1234567890",
      urls: [
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
      ]
    }
    ]
  }
};

