export const environment = {
  production: true,
  name: "prod",
  baseUrl: "https://cna365api.examroom.ai/",
  redirectUrl: "https://cna365.examroom.ai/",
  examroomapiUrl:"https://api.examroom.ai/api/",
  config: {
    iceServers: [{
      urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
    },
    {
      username: "bruce<PERSON>",
      credential: "1234567890",
      urls: [
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
      ]
    }
    ]
  }
};

