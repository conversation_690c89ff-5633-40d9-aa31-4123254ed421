// Angular
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';

// RxJs
import { take, tap } from 'rxjs/operators';
import { BehaviorSubject, Observable } from 'rxjs'; 'rxjs';

// Env 
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-file-view',
    templateUrl: './file-view.component.html',
    styleUrls: ['./file-view.component.scss']
})
export class FileViewComponent implements OnInit {

    @Input() fileDetails;
    @Input() isResponseEditable;
    @Output() removeFileEvent = new EventEmitter();

    fileName: string;
    fileExtension: string;
    toggleFileView: boolean = false;

    viewUrl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
    viewUrl$: Observable<string> = this.viewUrl.asObservable();

    constructor(private http: HttpClient) {
    }

    ngOnInit(): void {
        // this.getDocUrls();
        this.fileName = this.fileDetails.name;
    }

    removeFile() {
        this.removeFileEvent.emit(this.fileDetails);
    }

    getDocUrls() {
        this.toggleFileView = !this.toggleFileView;
        if(this.toggleFileView) {
            this.fileExtension = this.fileName.split('.').pop();
            const url = environment.baseUrl + `formmsvc/api/File/url?systemFileName=${encodeURIComponent(this.fileDetails.filePath)}`;
            this.http.get(url).pipe(
                take(1),
                tap((response: any) => {
                    this.viewUrl.next(response.url);
                })
            ).subscribe();
        }
    }

    removeEvent() {
        // this.file.splice(fileIndex, 1);
        // this.global.remainigFiles=this.file;
        // // need to make api call here; to remove file from s3
        // this.propagateChange(this.file.join(','));
        // this.cdr.markForCheck();
      }
}
