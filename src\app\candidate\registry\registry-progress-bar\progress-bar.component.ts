import {
  animate,
  state,
  style,
  transition,
  trigger,
} from "@angular/animations";
import { Component, Input, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Store } from "@ngrx/store";
import { HttpService } from "src/app/core/http-services/http.service";


@Component({
  selector: "exai-progress-bar-registry",
  templateUrl: "./progress-bar.component.html",
  styleUrls: ["./progress-bar.component.scss"],
  animations: [
    trigger("openClose", [
      state("true", style({ height: "*" })),
      state("false", style({ height: "0px" })),
      transition("true <=> false", [animate(500)]),
    ]),
  ],
})
export class RegistryProgressBarComponent implements OnInit {
  @Input("data") performlogs: any[] = [];
  isOpen: boolean = false;
  isShow: boolean = false;
  public show_hide: any = "Show More";
  showmore: boolean;

  toggle() {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.show_hide = "Show Less";
    } else {
      this.show_hide = "Show More";
    }
  }
  Show() {
    this.isShow = !this.isShow;
  }

  start: any[] = [];
  middle: any[] = [];
  end: any[] = [];
  personFormId: number;

  constructor(
    private http: HttpService,
    private store: Store,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit(): void {
    if (this.performlogs?.length > 0) {
      if (this.performlogs.length > 4) {
        this.showmore = true;
        this.start = [...this.performlogs.slice(0, 3)];
        this.middle = [
          ...this.performlogs.slice(3, this.performlogs.length - 2),
        ];
        this.end = [...this.performlogs.slice(this.performlogs.length - 2)];
      } else {
        this.showmore = false;
        this.end = [...this.performlogs.slice(0)];
      }
    }
  }
}
