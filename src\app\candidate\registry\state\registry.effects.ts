import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import {  of } from 'rxjs';
import { switchMap, map, catchError, concatMap, take, tap } from "rxjs/operators";
import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { URL } from 'src/app/core/url';
import {
    loadAllCertificates,
    loadedAllCertificates,
    getForm,
    gotForm,
    loadCertificatePath,
    loadedCertificatePath,
    requestSaveForm,
    postSaveFormSuccess,
    catchErrorRegistry,
    loadAllRequests,
    loadedAllRequests,
    gotDraftedForm,
    getDraftedForm,
    loadAllStates,
    loadedAllStates,
    loadLogs,
    loadedLogs,
    deleteForm,
    updateDeletedRenewedFormState,
    makerequestnull,
    madenull,
    DO_Check_Registry,
    DO_Check_Registrys
} from './registry.actions';
import { StateModel } from "./registry.model";

@Injectable({
    providedIn: 'root'
})
export class RegistryEffects {

    constructor(private actions$: Actions, private router: Router,
        private http: HttpClient, private global: GlobalUserService, private snackbar: SnackbarService) { }

    registryAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(loadAllCertificates),
        switchMap((action) => {
            return this.http
                .get<any>(`${URL.BASE_URL_SHORTER1}registry/get-all?pageNumber=${action.pageNumber}&pageSize=${action.pageSize}&personId=${action.personId}`)
                .pipe(
                    map(data => loadedAllCertificates({ data })),
                    take(1),
                    catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                );
        })
    ));

    requestAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(loadAllRequests),
        switchMap((action) => {
            return this.http
                .get<any>(`${URL.BASE_URL_SHORTER}Formmsvc/api/form/personform?candidateId=${action.candidateId}&formTypeId=5&formTypeId=6&formTypeId=7&formTypeId=15&formTypeId=14&formTypeId=17 `)
                .pipe(
                    map(data => loadedAllRequests({ data })),
                    take(1),
                    catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                );
        })
    ));

    requestDo_check_registry$ = createEffect(() => this.actions$.pipe(
        ofType(DO_Check_Registry),
        switchMap((action) => {
            return this.http
                .get<any>(`${URL.BASE_URL_SHORTER1}registry/should-check-for-registry?PersonTenantRoleId=${action.candidateId}`)
                .pipe(
                    map(data => DO_Check_Registrys({ data })),
                    take(1),
                    catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                );
        })
    ));

    statesAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(loadAllStates),
        switchMap((action) => {
            return this.http
                .get<StateModel[]>(`${URL.BASE_URL}EligibilityRoute/states?clientId=${action.clientId}`)
                .pipe(
                    map((data: StateModel[]) => loadedAllStates({ data: data })),
                    take(1),
                    catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                );
        })
    ));


    formsAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(getForm),
        switchMap(
            (action) => {
                return this.http
                    .get<any>(`${URL.BASE_URL}form/formsbyformtypeid?formTypeId=${action.formTypeId}&clientId=${action.clientId}&stateId=${this.global.stateId}`)
                    .pipe(
                        map(form => gotForm({
                            form:
                            {
                                formID: form.id,
                                formTypeID: action.formTypeId,
                                stateID: this.global.stateId,
                                eligibilityID: 0,
                                formJSON: JSON.parse(form.formJson),
                                dataDetail: form.dataDetail,
                                formUrl: form.formUrl,
                                name: form.name,
                                personTenantRoleId: form.personTenantRoleId,
                                tenantId: form.tenantId
                            }
                        })),
                        take(1),
                        catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                    );
            })
    ));

    draftedFormAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(getDraftedForm),
        switchMap(
            (action) => {
                return this.http
                    .get<any>(`${URL.BASE_URL}Form/personform/list?personFormId=${action.personFormId}`)
                    .pipe(
                        map(form => gotDraftedForm({ draftedForm: form })),
                        take(1),
                        catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                    );
            })
    ));

    logsAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(loadLogs),
        switchMap(
            (action) => {
                return this.http
                    .get<any>(`${URL.BASE_URL}Form/personformlogs?personFormId=${action.personFormId}`)
                    .pipe(
                        map(data => loadedLogs({ logs: data })),
                        take(1),
                        catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                    );
            })
    ));


    public registryImageAPIcall$=createEffect(()=>{
        return this.actions$.pipe(
          ofType(loadCertificatePath),
          switchMap((action)=>{
            let header = new HttpHeaders();
            header= header.append('content-type', 'application/json');
              return this.http.post(`${URL.BASE_URL_SHORTER}registry/api/Certificate/view-cetificate?registryId=${action.Id}`,{},{responseType:'text'})
            .pipe(map((data:any)=>{
                
                return  loadedCertificatePath ({certificatePath:data});
              }));  
            }))
          });

    savePersonFormAPIcall$ = createEffect(() => this.actions$.pipe(
        ofType(requestSaveForm),
        concatMap(
            (action) => {
                return this.http.post(`${URL.BASE_URL_SHORTER}Formmsvc/api/form/savepersonform`, action.params)
                    .pipe(
                        map(data => {
                            if (!action.params.isSubmit) {
                                this.snackbar.callSnackbaronSuccess('Successfully Saved Form.');
                                setTimeout(() => { this.router.navigate(['registry']) }, 2000);
                            } else if (action.params.isSubmit) {
                                this.snackbar.callSnackbaronSuccess('Successfully Submitted Form.');
                                setTimeout(() => { this.router.navigate(['registry']) }, 2000);
                            }
                            return postSaveFormSuccess({ data })
                        }),
                        take(1)
                    )
            })
    ));


    effectivelyDeleteForm$ = createEffect(() => this.actions$.pipe(
        ofType(deleteForm),
        concatMap((action) => {
            return this.http.delete<any>(URL.BASE_URL_SHORTER + `client/api/form/personform?candidateId=${action.candidateId}&personFormId=${action.personFormId}`)
                .pipe(
                    map(() => updateDeletedRenewedFormState()),
                    tap(() => {
                        this.snackbar.callSnackbaronSuccess('Successfully deleted');
                    }),
                    catchError((err) => of(catchErrorRegistry({ error: "An error has occured" })))
                );
        }),
    ))

    makepaymentnull$ = createEffect(() => {
        return this.actions$.pipe(
          ofType(makerequestnull),
          map(() => {
            return madenull({ pass: null });
          })
        );
      });
}


