import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-file-view-popup',
  templateUrl: './file-view-popup.component.html',
  styleUrls: ['./file-view-popup.component.scss']
})
export class FileViewPopupComponent implements OnInit {

  viewUrl$ : Observable<string>;
  fileExtension:string;
  constructor(
    public dialogRef: MatDialogRef<FileViewPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) {
    this.viewUrl$ = data.viewUrl;
    this.fileExtension = data.fileExtension;
  }
  ngOnInit(): void {
  }

}
