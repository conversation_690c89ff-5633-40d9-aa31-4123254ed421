<!-- 



<div gdColumns="1fr" exaiContainer>
    <div class="tab-container">
        <mat-tab-group class=" rounded-md w-full" [(selectedIndex)]="selectedTab">
            <mat-tab>
                <ng-template mat-tab-label>
                    <span class="first-tab text-center w-full text-sm font-semibold block text-center">
                            {{ lngSrvc.curLangObj.value.practice_skills }}
                    </span>
                </ng-template>


                <div class="sub-tab-container flex justify-center">
                    <div class="sub-tab-buttons">
                        <button class="sub-tab-btn" [ngClass]="selectedSubTab === 0 
                                        ? 'text-active' 
                                        : 'border-transparent'" (click)="selectedSubTab = 0">
                            {{ lngSrvc.curLangObj.value.skills }}
                        </button>
                
                        <button class="sub-tab-btn" [ngClass]="selectedSubTab === 1 
                                        ? 'text-active' 
                                        : 'border-transparent'" (click)="selectedSubTab = 1">
                            {{ lngSrvc.curLangObj.value.bundles }}
                        </button>
                    </div>
                
                </div>

                <div class="all_cards  mt-4">
                    <ng-container *ngIf="selectedSubTab === 0">
                        <exai-skills-tab 
                        [skills]="skills$ | async" 
                        (addToCartEvent)="handleAddToCart($event)">>
                        </exai-skills-tab>
                    </ng-container>
                    <ng-container *ngIf="selectedSubTab === 1">
                        <exai-bundles-tab></exai-bundles-tab>
                    </ng-container>
                </div>
            </mat-tab>


            <mat-tab>
                <ng-template mat-tab-label>
                    <span class="second-tab text-center w-full text-sm font-semibold">
                        Purchased
                    </span>
                </ng-template>
                <div class="p-4">
                    Purchased Content
                </div>
            </mat-tab>
        </mat-tab-group>
    </div>
</div> -->


<div class="toolbar_layout px-gutter pt-2 pb-2 bg-white" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
  exaiContainer>
  <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">

    <div class="pt-2 titleFont" fxLayout="column">
      <h5><strong>{{this.lngSrvc.curLangObj.value.practice_skills}}</strong></h5>
      <app-ng-dynamic-breadcrumb class="bredacrumb" [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
        [fontSize]="'0.65rem'">
      </app-ng-dynamic-breadcrumb>
    </div>
  </div>
</div>

<div class="custom-tab-container">
  <div class="tabs text-sm">
    <div class="tab" [ngClass]="{ 'active-tab': selectedTab === 0, 'inactive-tab': selectedTab !== 0 }"
      (click)="selectedTab = 0">
      {{ lngSrvc.curLangObj.value.practice_skills }}
    </div>
    <div class="tab" [ngClass]="{ 'active-tab': selectedTab === 1, 'inactive-tab': selectedTab !== 1 }"
      (click)="selectedTab = 1">
      Purchased
    </div>
  </div>

  <div class="tab-content" *ngIf="selectedTab === 0">
    <div class="sub-tabs">
      <button class="sub-tab text-xs" [ngClass]="{ 'active-sub-tab': selectedSubTab === 0 }"
        (click)="selectedSubTab = 0">
        {{ lngSrvc.curLangObj.value.skills }}
      </button>
      <button class="sub-tab text-xs" [ngClass]="{ 'active-sub-tab': selectedSubTab === 1 }"
        (click)="selectedSubTab = 1">
        {{ lngSrvc.curLangObj.value.bundles }}
      </button>
    </div>

  </div>
  <div *ngIf="selectedTab === 0" class="tab-section pt-4">
    <ng-container *ngIf="selectedSubTab === 0">
      <exai-skills-tab [skills]="skills$ | async" [skillsMode]="skillsMode$ | async"
        (addToCartEvent)="handleAddToCart($event)">
      </exai-skills-tab>
    </ng-container>
    <ng-container *ngIf="selectedSubTab === 1">
      <exai-bundles-tab></exai-bundles-tab>
    </ng-container>
  </div>

  <div class="tab-content" *ngIf="selectedTab === 1">
    <div class="grid-layout p-4">

      <mat-card *ngFor="let skill of purchasedSkills" class="card-borders">
        <div class="card-content">

          <div class="flex justify-between items-center pr-2">
            <span class="title">
              {{ skill.skillName }}
            </span>
            <!--<exai-menu [menuItems]="menuItems" (itemClicked)="handleMenuAction($event, skill)"></exai-menu>-->
          </div>
          <mat-card-content class="flex-1">
            <p class="description-text relative leading-relaxed tracking-wide text-justify text-xs pr-4"
              [class.collapsed]="!skill.isExpanded" [class.expanded]="skill.isExpanded" #desc>
              {{ skill.skillDescription }}
            </p>

            <button *ngIf="skill.showToggle" class="viewmore pl-4 text-xs  underline focus:outline-none">
              {{ skill.isExpanded ? 'View Less' : 'View More' }}
            </button>
          </mat-card-content>
          <mat-card-content class="flex-1">
            <p class="description-text relative leading-relaxed tracking-wide text-justify text-xs pr-4"
              [class.collapsed]="!skill.isExpanded" [class.expanded]="skill.isExpanded" #desc>
              {{ skill.skillDescription }}
            </p>


            <button *ngIf="skill.showToggle" class="viewmore pl-4 text-xs underline focus:outline-none">
              {{ skill.isExpanded ? 'View Less' : 'View More' }}
            </button>


            <button *ngIf="skill.showToggle" class="viewmore pl-4 text-xs underline focus:outline-none">
              View More
            </button>
          </mat-card-content>


          <div class="meta flex flex-wrap justify-between text-center mt-2">
            <div class="w-1/5">
              <div class="meta-label">Duration</div>
              <div class="meta-value">{{ skill.duration }}</div>
            </div>
            <div class="w-1/5">
              <div class="meta-label">Steps</div>
              <div class="meta-value">{{ skill.steps }}</div>
            </div>
            <div class="w-1/5">
              <div class="meta-label">Attempts</div>
              <div class="meta-value">{{ skill.numberAttempt }}</div>
            </div>
            <div class="w-1/5">
              <div class="meta-label">Validity</div>
              <div class="meta-value">{{ skill.validity }}d</div>
            </div>
            <div class="w-1/5">
              <div class="meta-label">Price</div>
              <div class="meta-value1">${{ skill.price }}</div>
            </div>
          </div>
        </div>
        <mat-card-actions class="px-3 mt-auto flex justify-end">
          <button class="try-now-btn font-semibold flex items-center justify-center mb-4 mt-3 mr-2">
            {{skill.eventStatusId === 1 ? 'Start Now' : ''}}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>