# For instructions and new versions of this Gist go to:
# https://gist.github.com/julianpoemp/bcf277cb56d2420cc53ec630a04a3566
# Version 1.4.1
<IfModule mod_rewrite.c>
  RewriteEngine On
  # -- REDIRECTION to https (optional):
  # If you need this, uncomment the next two commands
  # RewriteCond %{HTTPS} !on
    # RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [L]
  # --
  RewriteCond %{DOCUMENT_ROOT}%{REQUEST_URI} -f [OR]
  RewriteCond %{DOCUMENT_ROOT}%{REQUEST_URI} -d
  RewriteRule ^.*$ - [NC,L]
  RewriteRule ^(.*) index.html [NC,L]
</IfModule>