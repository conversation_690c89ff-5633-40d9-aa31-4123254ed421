import { createReducer, on } from "@ngrx/store";
import { initialState } from "./profile-form.state";
import { fetchPersonData, getImageUrl, isPersonFormLogs, deletedCorrectionForm, deletedDemographicForm, passCorrectionFormData, getCorrectionFormData, getSubmitDemographicData, gotFormJSON, gotPersonDetails, passPersonDetail, submitedDemographicData, getPersonFormLogs, successMessage, gotupcomingExam, clearDemographicData, clearPersonFormlogsState, resetUpdateProfile } from "./profile-form.action";
import { ProfileState } from "./profile-form.model";

const _applicationReducer = createReducer<ProfileState>(
  initialState,
  on(gotFormJSON, (state, action: any) => {
    return {
      ...state,
      demographicForm: action
    }
  }),
  on(gotPersonDetails, (state, action) => {

    return {
      ...state,
      personDetails: action.value
    }
  }), 
  on(getImageUrl, (state, action: any) => {
    return {
      ...state,
      imageUrl: action
    }
  }),
  on(passPersonDetail, (state, action: any) => {
    return {
      ...state,
      personDetails: action.personDetails
    }
  }),
  on(successMessage, (state, action: any) => {
    return {
      ...state,
      updateSuccess: action.value
    }
  }),
  on(getSubmitDemographicData, (state, action: any) => {
    return {
      ...state,
      SubmitDemographic: action
    }
  }),
  on(submitedDemographicData, (state, action) => {
    return {
      ...state,
      savedDemographicResponseId: action.savedResId
    }
  }),
  on(fetchPersonData, (state, action: any) => {
    return {
      ...state,
      personForm: action.value
    }
  }),
  on(passCorrectionFormData, (state, action: any) => {
    return {
      ...state,
      getCorrectionData: action.value
    }
  }),
  // on(getCorrectionFormData, (state, action: any) => {
  //   return {
  //     ...state,
  //     getCorrectionData: action.value
  //   }
  // }),
  on(deletedCorrectionForm, (state, action: any) => {
    return {
      ...state,
      deletedDemographicForm: action.value
    }
  }),
  on(deletedDemographicForm, (state, action: any) => {
    return {
      ...state,
      deletedDemographicForm: action.value
    }
  }),
  on(isPersonFormLogs, (state, action: any) => {
    return {
      ...state,
      personFormLogs: action.personFormLogs
    }
  }),
  on(gotupcomingExam, (state, action) => {
    return {
      ...state, upcomingExams: action.upcomingExam
    }
  }),
  on(clearDemographicData, (state, action) => {
    return {
      ...state,
      getCorrectionData: null
    }
  }),
  on(clearPersonFormlogsState, (state, action) => {
    return {
      ...state,
      personFormLogs: []
    }
  }),
  on(resetUpdateProfile, (state, action) => {
    return {
      ...state,
      updateSuccess : false
    }
  })
)

export function MngProfileReducer(state, action) {
  return _applicationReducer(state, action);
}