import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicFormLinkComponent } from './dynamic-form-link.component';

describe('DynamicFormLinkComponent', () => {
  let component: DynamicFormLinkComponent;
  let fixture: ComponentFixture<DynamicFormLinkComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DynamicFormLinkComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicFormLinkComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
