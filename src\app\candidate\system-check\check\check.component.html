<section class="p-3">
    <div class="top-header text-center">
        <h5 class="font-bold" [ngClass]="!completeStatus ? 'dots mb-4' : ''">{{statusTitle}}
            <span *ngIf="!completeStatus">.</span>
            <span *ngIf="!completeStatus">.</span>
            <span *ngIf="!completeStatus">.</span>
        </h5>
    </div>

    <div gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="16px" exaiContainer>
        <div class="card bg-primary animate__animated animate__backInLeft" gdColumn="1 / 3" gdColumn.lt-md="1 / -1"
            gdColumn.lt-sm="1" >
            <div [ngClass]="activeStatus == '1' ? 'bar' : ''">
                <div class="indeterminate"></div>
            </div>
            <div class="px-4 pt-2" fxLayout="row" fxLayoutAlign="start center" fxFlex="auto" fxLayoutGap="5px">
                <div fxLayout="column" fxFlex="70%" fxLayoutAlign="start start">
                    <h6 class="m-0 title">Hardware Configurations</h6>
                    <p class="text-xs descColor">Our system is checking to ensure your hardware is compatible to
                        provide a
                        best experience for your exam.</p>
                </div>
                <div fxLayout="column" fxFlex="30%" fxLayoutAlign="end center">
                    <lottie-player class="cam-mic" [src]="animeIcons.hardware.path" [loop]="animeIcons.hardware.loop"
                        [autoplay]="animeIcons.hardware.autoplay">
                    </lottie-player>
                </div>
            </div>
            <div class="text-primary-contrast pb-2" fxLayout="column" fxLayoutAlign="start start">

                <div class="mt-4" fxLayout="row" fxLayoutAlign="start center">
                    <table class="table content">
                        <tbody class="text-xs descColor">
                            <tr>
                                <td>
                                    <!-- <mat-icon>camera_alt</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        photo_camera
                                    </span>
                                    <!-- <img src="assets/img/photo-camera.svg" alt="camera-icon"> -->
                                </td>
                                <td>Camera:</td>
                                <td [ngClass]="!camVerification.status ? 'fail' : 'successSysCheck'">
                                    {{camVerification.message}}&nbsp;
                                    <span *ngIf="camVerification.status && camVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span *ngIf="!camVerification.status && camVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>

                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <mat-icon>mic</mat-icon> -->
                                    <!-- <mat-icon>mic_none</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        keyboard_voice
                                    </span>
                                </td>
                                <td>Mic:</td>
                                <td [ngClass]="!microphoneVerification.status ? 'fail' : 'successSysCheck'">
                                    {{microphoneVerification.message}}&nbsp;
                                    <span
                                        *ngIf="microphoneVerification.status && microphoneVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!microphoneVerification.status && microphoneVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <mat-icon>memory</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        memory
                                    </span>
                                </td>
                                <td>RAM:</td>
                                <td [ngClass]="!ramVerification.status ? 'fail' : 'successSysCheck'">
                                    {{ramVerification.message}}&nbsp;
                                    <span *ngIf="ramVerification.status && ramVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span *ngIf="!ramVerification.status && ramVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>

                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <mat-icon>developer_board</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        developer_board
                                    </span>
                                </td>
                                <td>Processor:</td>
                                <td [ngClass]="!processorVerification.status ? 'fail' : 'successSysCheck'">
                                    {{processorVerification.message}}&nbsp;
                                    <span
                                        *ngIf="processorVerification.status && processorVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!processorVerification.status && processorVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card bg-primary animate__animated animate__backInLeft" gdColumn="3 / -1" gdColumn.lt-md="1 / -1"
            gdColumn.lt-sm="1">
            <div [ngClass]="activeStatus == '2' ? 'bar' : ''">
                <div class="indeterminate"></div>
            </div>
            <div class="px-4 pt-2" fxLayout="row" fxLayoutAlign="start start" fxFlex="auto" fxLayoutGap="5px">
                <div fxLayout="column" fxFlex="70%" fxLayoutAlign="start start">
                    <h6 class="m-0 title">Software Configurations</h6>
                    <p class="text-xs descColor">Our system is checking to ensure your software configurations are compatible for your
                        exam.</p>
                </div>
                <div fxLayout="column" fxFlex="30%" fxLayoutAlign="end center">
                    <lottie-player [src]="animeIcons.software.path" [loop]="animeIcons.software.loop"
                        [autoplay]="animeIcons.software.autoplay" class="cam-mic">
                    </lottie-player>
                </div>
            </div>
            <div class="text-primary-contrast pb-2" fxLayout="column" fxLayoutAlign="start start">

                <div class="mt-4" fxLayout="row" fxLayoutAlign="start center">
                    <table class="table content">
                        <tbody class="text-xs descColor">
                            <tr>
                                <td>
                                    <!-- <mat-icon>computer</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        computer
                                    </span>
                                </td>
                                <td>OS:</td>
                                <td [ngClass]="!osVerification.status ? 'fail' : 'successSysCheck'">
                                    {{osVerification.message}}&nbsp;
                                    <span *ngIf="osVerification.status && osVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span *ngIf="!osVerification.status && osVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <mat-icon>language</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        language
                                    </span>
                                </td>
                                <td>Browser Version:</td>
                                <td class="text-capitalize" [ngClass]="!browserVerification.status ? 'fail' : 'successSysCheck'">
                                    {{browserVerification.message}}&nbsp;
                                    <span
                                        *ngIf="browserVerification.status && browserVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!browserVerification.status && browserVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <mat-icon>open_in_browser</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        open_in_browser
                                    </span>
                                </td>
                                <td>Web Compatibility:</td>
                                <td [ngClass]="!browserCompatibility.status ? 'fail' : 'successSysCheck'">
                                    {{browserCompatibility.message}}&nbsp;
                                    <span
                                        *ngIf="browserCompatibility.status && browserCompatibility.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!browserCompatibility.status && browserCompatibility.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="16px" exaiContainer>
        <div class="card bottomCard bg-primary animate__animated animate__backInLeft" gdColumn="1 / 3" gdColumn.lt-md="1 / -1"
            gdColumn.lt-sm="1">
            <div [ngClass]="activeStatus == '3' ? 'bar' : ''">
                <div class="indeterminate"></div>
            </div>
            <div class="px-4 pt-2" fxLayout="row" fxLayoutAlign="start center" fxFlex="auto" fxLayoutGap="5px">
                <div fxLayout="column" fxFlex="70%" fxLayoutAlign="start start">
                    <h2 class="m-0 title">Browser Configurations</h2>
                    <p class="text-xs descColor">Our system is checking to ensure your browser configurations are enabled on your system
                        to take your exam.</p>
                </div>
                <div fxLayout="column" fxFlex="30%" fxLayoutAlign="end center">
                    <lottie-player [src]="animeIcons.browser.path" [loop]="animeIcons.browser.loop"
                        [autoplay]="animeIcons.browser.autoplay" class="cam-mic">
                    </lottie-player>
                </div>
            </div>
            <div class="text-primary-contrast pb-2" fxLayout="column" fxLayoutAlign="start start">

                <div class="mt-4" fxLayout="row" fxLayoutAlign="start center">
                    <table class="table content">
                        <tbody class="text-xs descColor">
                            <tr>
                                <td>
                                    <!-- <mat-icon>code</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        code
                                    </span>
                                </td>
                                <td>Javascript:</td>
                                <td class="successSysCheck">Enabled&nbsp;
                                    <span><i class="{{successIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <mat-icon>dvr</mat-icon> -->
                                    <span class="material-icons-outlined">
                                        dvr
                                    </span>
                                </td>
                                <td>Cookies:</td>
                                <td [ngClass]="!cookieVerification.status ? 'fail' : 'successSysCheck'">
                                    {{cookieVerification.message}}&nbsp;
                                    <span
                                        *ngIf="cookieVerification.status && cookieVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!cookieVerification.status && cookieVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <img src="assets/img/screen-share.svg" alt="screen-share"> -->
                                    <span class="material-icons-outlined">
                                        screen_share
                                    </span>
                                </td>
                                <td>Screenshare:</td>
                                <td [ngClass]="!screenShareVerification.status ? 'fail' : 'successSysCheck'">
                                    {{screenShareVerification.message}}&nbsp;
                                    <span
                                        *ngIf="screenShareVerification.status && screenShareVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!screenShareVerification.status && screenShareVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="card bottomCard bg-primary animate__animated animate__backInLeft" gdColumn="3 / -1" gdColumn.lt-md="1 / -1"
            gdColumn.lt-sm="1">
            <div [ngClass]="activeStatus == '4' ? 'bar' : ''">
                <div class="indeterminate"></div>
            </div>
            <div class="px-4 pt-2" fxLayout="row" fxLayoutAlign="start center" fxFlex="auto" fxLayoutGap="5px">
                <div fxLayout="column" fxFlex="70%" fxLayoutAlign="start start">
                    <h2 class="m-0 title">Network Configurations</h2>
                    <p class="text-xs descColor">Our system is checking to ensure your network configurations will sustain the stream for
                        your exam.</p>
                </div>
                <div fxLayout="column" fxFlex="30%" fxLayoutAlign="end center">
                    <lottie-player [src]="animeIcons.network.path" [loop]="animeIcons.network.loop"
                        [autoplay]="animeIcons.network.autoplay" class="cam-mic">
                    </lottie-player>
                </div>
            </div>
            <div class="text-primary-contrast pb-2" fxLayout="column" fxLayoutAlign="start start">

                <div class="mt-4" fxLayout="row" fxLayoutAlign="start center">
                    <table class="table content">
                        <tbody class="text-xs descColor">
                            <tr>
                                <td>
                                    <!-- <img src="assets/img/upload.svg" alt="upload"> -->
                                    <span class="material-icons-outlined">
                                        download
                                    </span>
                                </td>
                                <td>Download Speed:</td>
                                <td [ngClass]="!internetSpeedResults.download.status ? 'fail' : 'successSysCheck'">
                                    {{internetSpeedResults.download.message}}&nbsp;
                                    <span
                                        *ngIf="internetSpeedResults.download.status && internetSpeedResults.download.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!internetSpeedResults.download.status && internetSpeedResults.download.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <img src="assets/img/download.svg" alt="download"> -->
                                    <span class="material-icons-outlined">
                                        upload
                                    </span>
                                </td>
                                <td>Upload Speed:</td>
                                <td [ngClass]="!internetSpeedResults.upload.status ? 'fail' : 'successSysCheck'">
                                    {{internetSpeedResults.upload.message}}&nbsp;
                                    <span
                                        *ngIf="internetSpeedResults.upload.status && internetSpeedResults.upload.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!internetSpeedResults.upload.status && internetSpeedResults.upload.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- <img src="assets/img/web-sockets.svg" alt="websockets"> -->
                                    <span class="material-icons-outlined">
                                        settings_input_svideo
                                    </span>
                                </td>
                                <td>Websockets:</td>
                                <td [ngClass]="!webSocketVerification.status ? 'fail' : 'successSysCheck'">
                                    {{webSocketVerification.message}}&nbsp;
                                    <span
                                        *ngIf="webSocketVerification.status && webSocketVerification.message != 'Verifying..'"><i
                                            class="{{successIcon}}"></i></span>
                                    <span
                                        *ngIf="!webSocketVerification.status && webSocketVerification.message != 'Verifying..'"><i
                                            class="{{failIcon}}"></i></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>