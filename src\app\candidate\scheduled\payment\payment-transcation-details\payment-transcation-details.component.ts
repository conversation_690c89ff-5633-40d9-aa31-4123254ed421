import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';
import data from '@iconify/icons-ic/twotone-menu';
import moment from 'moment';

@Component({
  selector: 'exai-payment-transcation-details',
  templateUrl: './payment-transcation-details.component.html',
  styleUrls: ['./payment-transcation-details.component.scss'],
})
export class PaymentTranscationDetailsComponent implements OnInit {
  transcationId: any;
  Accounttype: any;
  accountNumber: any;
  totalAmount: number;
  emailId: string;
  mobileNum: number;
  transactionDate: string;
  submitted;
  OpenWindow: Window;

  constructor(
    private dialogRef: MatDialogRef<PaymentTranscationDetailsComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: any,
    private router: Router
  ) {
    this.Accounttype = data.response.transactionResponse.accountNumber;
    this.Accounttype = data.response.paymentReceipt.paymentType;
    this.emailId = data.response.paymentReceipt.emailId;
    this.mobileNum = data.response.paymentReceipt.mobileNumber;
    this.transactionDate = moment(
      data.response.paymentReceipt.transactionDateTime
    ).format('Do MMMM  YYYY , h:mm A');
    this.transcationId = data.response.transactionResponse.transId;
    this.totalAmount = data.total;
  }

  ngOnInit(): void {}

  print() {
    if (this.data) {
      window.print();
      this.router.navigateByUrl('/exam-scheduled');
      location.reload();
    }
  }
  popupcancel() {
    this.dialogRef.close()
    this.router.navigateByUrl('/exam-scheduled');
  }
}
