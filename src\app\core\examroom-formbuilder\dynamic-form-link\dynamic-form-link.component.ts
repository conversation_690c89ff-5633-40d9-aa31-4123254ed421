import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DynamicColorPickerModel, DynamicFormControlComponent, DynamicFormControlCustomEvent, DynamicFormControlLayout, DynamicFormLayoutService, DynamicFormValidationService } from '@ng-dynamic-forms/core';
import { FormLinkAttachComponent } from '../form-link-attach/form-link-attach.component';

@Component({
  selector: 'app-dynamic-form-link',
  templateUrl: './dynamic-form-link.component.html',
  styleUrls: ['./dynamic-form-link.component.scss']
})
export class DynamicFormLinkComponent extends DynamicFormControlComponent  {

  @Input() group!: FormGroup;
  @Input() layout?: DynamicFormControlLayout;
  @Input() model!: DynamicColorPickerModel;

  @Output() blur: EventEmitter<any> = new EventEmitter();
  @Output() change: EventEmitter<any> = new EventEmitter();
  @Output() customEvent: EventEmitter<DynamicFormControlCustomEvent> = new EventEmitter();
  @Output() focus: EventEmitter<any> = new EventEmitter();

  @ViewChild(FormLinkAttachComponent) myCustomFormControlComponent!: FormLinkAttachComponent;

  constructor(protected layoutService: DynamicFormLayoutService,
    protected validationService: DynamicFormValidationService) {
    super(layoutService, validationService);
  }

}
