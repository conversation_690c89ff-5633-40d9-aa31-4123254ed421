import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { createEffect, Actions, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, catchError, mergeMap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { setErrorMessage } from '../../state/shared/shared.actions';
import { Router } from '@angular/router';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { connectionCloseNotified, connectionNotified, getAvailableStaffInfo, gotAvailableStaffInfo, notifyConnection, notifyConnectionClose, staffNotAvailable } from './candidate-chat.actions';
import { SupportStaffInfo } from './candidate-chat.state';

@Injectable({
  providedIn: 'root',
})
export class CandidateChatEffects {

  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private globalUserService: GlobalUserService,
    private router: Router, private snackbar: SnackbarService
  ) { }

  effectivelyGetAvailableSupportStaff$ = createEffect(() => this.actions$.pipe(
    ofType(getAvailableStaffInfo),
    mergeMap((action) => {
      return this.httpClient
        .get<SupportStaffInfo>(
          environment.baseUrl +
          `customerservice/api/chat/available-staff`)
        .pipe(
          map(info =>
            gotAvailableStaffInfo(info)),
          catchError(err => {
            return of(staffNotAvailable({ message: err.error })) 
          }),
        );
    }),
  ));

  effectivelyNotifyConnection$ = createEffect(() => this.actions$.pipe(
    ofType(notifyConnection),
    mergeMap((action) => {
      return this.httpClient
        .post<SupportStaffInfo>(
          environment.baseUrl +
          `customerservice/api/chat/connect`,action)
        .pipe(
          map(info =>
            connectionNotified()),
          catchError(err =>
            of(setErrorMessage({ message: err.message }))),
        );
    }),
  ));

  effectivelyNotifyDisconnection$ = createEffect(() => this.actions$.pipe(
    ofType(notifyConnectionClose),
    mergeMap((action) => {
      return this.httpClient
        .post<SupportStaffInfo>(
          environment.baseUrl +
          `customerservice/api/chat/disconnect`, action)
        .pipe(
          map(info =>
            connectionCloseNotified()),
          catchError(err =>
            of(setErrorMessage({ message: err.message }))),
        );
    }),
  ));
}
