


.dialog-body{
  border-radius: 4px;
  border-width: 0.5px;
  opacity: 1;
  border-color: #e5e5e5;
  background-color: #FAFAFA;
}

.close-btn {
  // color: var(--text-color2);
  background-color: transparent;
  border: none;
  cursor: pointer;
  width: 2rem;
  height: 2rem;
  transition: background-color 0.2s, color 0.2s, transform 0.1s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05); 
    color: #d32f2f; 
    transform: scale(1.05);
    border-radius: 50%;
  }

  &:focus {
    outline: none;
  }
}



.description-text {
  color: #7D7D7D;

}
.meta-value1{
  color: #1B75BB;
}
.meta-label {
  color: #C4C4C4;
}

.try-now-btn {
  color: #1B75BB;
  background-color: #E5F1F9;
  border-radius: 4px;
    padding:10px 14px 10px 14px;
    margin: 16px;
}

.try-now-btn:hover {
  opacity: 0.9;
}