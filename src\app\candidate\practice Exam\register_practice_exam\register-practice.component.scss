.slot {
    display: contents;
    height: 50px;
  }
  .active1 {
    color: #ee9400;
    border: 1px solid #ee9400;
  }
  
  .midnight{
    color: #499b90 !important;
    border: 1px solid #499b90 !important;
    font-weight: bold;
  }
  
  .highlight1{
     color: #499b90 !important;
     font-weight: bold;
  }
  ::ng-deep .mat-button-focus-overlay {
    background: none !important;
  }
  .content > button {
    margin-bottom: 12px;
  }
  .texture {
    height: 400px;
  }
  
  .eligibility {
    @screen xl {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 14.5rem
      );
    }
    @screen lg {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 15.5rem
      );
    }
    @screen md {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 15.5rem
      );
    }
  }
  
  .eligibility1 {
    @screen xl {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9.5rem
      );
    }
    @screen lg {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9.5rem
      );
    }
    @screen md {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9.5rem
      );
    }
    @screen sm {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9.5rem
      );
    }
    @screen xs {
      height: 100%;
    }
  }
  .dashboard {
    @screen xl {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 80px
      );
    }
    @screen lg {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 80px
      );
    }
    @screen md {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 80px
      );
    }
  }
  .eligibility-desc {
    @screen xl {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 10rem
      );
    }
    @screen lg {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 10rem
      );
    }
    @screen md {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 10rem
      );
    }
  }
  .examid{
    height: calc(100vh - 20vh);
  }
  
  .state {
    color: #000000;
    border: var(--border);
  }
  .Available {
    color: #000000;
  }
  .time {
    color: var(--text-input);
    border: none !important;
  }
  .elgibilityDet {
    color: var(--text-color1);
  }
  .elgibilityTitle {
    color: var(--text-color3);
  }
  .horzl {
    border: var(--border-hrz);
  }
  .add-new {
    background-color: var(--button-background) !important;
    color: var(--button-color) !important;
    padding: 0rem 2rem;
  }
  
  .line {
    color: #7d7d7d;
  }
  .active {
    color: var(--button-background) !important;
    border: 1px solid var(--button-background) !important;
  }
  .SelectedRange {
    color: var(--button-background) !important;
    border: 1px solid var(--button-background) !important;
    font-weight: bold ;
  }
  
  .highlight{
    color: var(--button-background) !important;
    font-weight: bold;
  }
  .add {
    border: 1px solid var(--button-background) !important;
    color: var(--button-background) !important;
    width: 100px;
    line-height: 26px;
  }
  .img {
    width: 5px;
  }
  .submit {
    width: 100px;
    line-height: 26px;
  }
  .text {
    color: #000000;
  }
  
  [disabled]{
    pointer-events: none;
  }
  
  .select {
    height: 40px;
  }
  .slot {
    display: contents;
  }
  
  .title {
    font-size: 100%;
  }
  .title1 {
    font-size: 10px;
    color: var(--button-background) !important;
  }
  
  .btn1 {
    line-height: 16px !important;
    min-width: unset !important;
    font-size: 12px;
  }
  .buuton2 {
    line-height: 26px !important;
    min-width: unset !important;
    font-size: 12px;
    background-color: var(--button-background) !important;
    color: #ffff;
  }
  .buuton1 {
    line-height: 26px !important;
    min-width: unset !important;
    font-size: 12px;
    color: var(--button-background) !important;
    border: 1px solid var(--button-background) !important;
  }
  
  .button {
    line-height: 20px !important;
    width: 70px !important;
    min-width: unset !important;
    font-size: 12px;
    height: 30px;
  }
  
  .radio {
    color: var(--button-background) !important;
  }
  
  .rdn {
    height: 10%;
    width: 10%;
  }
  .example-action-buttons {
    padding-bottom: 20px;
  }
  .slots2 {
    font-size: 0.63rem !important;
    line-height: 13px !important;
  }
  .slots3 {
    font-size: 8px;
  }
  .t-xss{
    font-size: 0.53rem !important;
  }
  
  .example-headers-align .mat-expansion-panel-header-title,
  .example-headers-align .mat-expansion-panel-header-description {
    flex-basis: 0;
  }
  
  .example-headers-align .mat-expansion-panel-header-description {
    justify-content: space-between;
    align-items: center;
  }
  
  .example-headers-align .mat-form-field + .mat-form-field {
    margin-left: 8px;
  }
  // mat-expansion-panel-header {
  //   border: var(--border);
  //   border-radius: var(--border-radius);
  // }
  
  .text-color {
    color: #c9c9c9;
  }
  
  .text-color1 {
    color: #7d7d7d;
  }
  .exam1 {
    line-height: 27px;
    font-size: 0.65rem;
    color: #000000;
  }
  ::ng-deep .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
    opacity: 0 !important;
  }
  .fontColor1 {
    color: #000000;
    font-weight: 550;
  }
  .imges {
    font-size: 10px;
  }
  
  .eligibility-desc {
    @screen xl {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9rem
      );
    }
    @screen lg {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9rem
      );
    }
    @screen md {
      height: calc(
        100vh - var(--toolbar-height) - var(--navigation-height) -
          var(--footer-height) - 9rem
      );
    }
  }
  .icons {
    font-size: 14px;
    margin-top: 10px;
  }
  .header {
    height: 36px;
  
    .titled {
      color: #11263c;
      font-size: 10px;
    }
  }
  ::ng-deep .mat-select-panel .mat-option-text {
    font-size: 10px;
  }
  ::ng-deep .mat-datepicker-content .mat-calendar {
    width: auto !important;
    height: auto !important;
    font-size: 11px !important;
  }
  ::ng-deep .mat-calendar-body-cell-content.mat-focus-indicator {
    font-size: 8px !important;
  }
  ::ng-deep .mat-calendar-header {
    padding: 0px 0px 0px 0px !important;
  }
  ::ng-deep .mat-calendar-controls {
    margin: 0% calc(33% / 7 - 16px) !important;
  }
  
  .fontColor2 {
    color: #a7a8ac;
    font-size: 10px !important;
  }
  .fontColor {
    color: #a7a8ac;
    // font-size: 16px !important;
  }
  .slots2 {
    font-size: 10px;
    line-height: 20px;
    // color: #7d7d7d;
  }
  .mat-testCenter {
    height: calc(100vh - 85vh);
    -webkit-overflow-scrolling: touch;
    touch-action: auto!important;
  }
  .mat-testCenter1 {
    height: calc(100vh - 75vh);
    touch-action: auto!important;
  }
  .mi {
    padding: 1rem;
    color: var(--button-background);
    cursor: pointer;
  }
  .km {
    padding: 1rem;
    cursor: pointer;
  }
  // .buttom6{
  //   // padding: 0 26px
  // }
  .Limited_Slots {
    color: #ee9400;
    border: 1px solid #ee9400;
  }
  .avaiable {
    color: #000000;
    border: 1px solid #000000;
  }
  .sloted {
    font-size: 15px;
    color: #7d7d7d;
  }
  .sloted1 {
    font-size: 14px;
    color: #7d7d7d;
  }
  .iconSize {
    font-size: 17px;
    margin-top: -30px;
  }
  ::ng-deep .mat-calendar-body-label {
    opacity: 0 !important;
  }
  ::ng-deep .mat-calendar-arrow {
    opacity: 0 !important;
  }
  :host:ng-deep .mat-button .mat-button-wrapper > *,
  .mat-flat-button .mat-button-wrapper > *,
  .mat-stroked-button .mat-button-wrapper > *,
  .mat-raised-button .mat-button-wrapper > *,
  .mat-icon-button .mat-button-wrapper > *,
  .mat-fab .mat-button-wrapper > *,
  .mat-mini-fab .mat-button-wrapper > * {
    vertical-align: middle;
    font-size: 10px;
  }
  ::ng-deep .mat-calendar-table {
    border-spacing: 0;
    border-collapse: collapse;
    width: 100%;
    border-bottom: 1px lightgray solid;
  }
  ::ng-deep .mat-calendar-body-label {
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  ::ng-deep
    .mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
    // border-color: rgba(0, 0, 0, 0.38);
    border-radius: 0px !important;
    border-color: #ccf5e2 !important;
    background-color: #ccf5e2 !important;
  }
  ::ng-deep .mat-calendar-body-cell-content.mat-focus-indicator {
    border-radius: 0px !important;
    // border-color: var(--text-color2) !important;
    // background-color: var(--text-color2) !important;
  }
  .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover
    > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),
  .cdk-keyboard-focused
    .mat-calendar-body-active
    > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),
  .cdk-program-focused
    .mat-calendar-body-active
    > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
    // background-color: rgba(32,158,145) !important;
    background-color: var(--text-color2) !important;
  }
  .select {
    font-size: 10px;
  }
  ::ng-deep .mat-select-value {
    font-size: 11px !important;
    vertical-align: middle !important;
  }
  
  // ::ng-deep input.mat-input-element {
  //   font-size: 10px !important;
  //   width: 100% ;
  // }
  .select-slot-btn {
    color: var(--text-color2);
    border: 1px solid var(--text-color2);
  }
  .example-container {
    display: flex;
    flex-direction: column;
  }
  
  .example-container > * {
    width: 100%;
  }
  
  .example-container form {
    margin-bottom: 20px;
  }
  
  .example-container form > * {
    margin: 5px 0;
  }
  
  .example-container .mat-radio-button {
    margin: 0 12px;
  }
  .textBox{
    border-top: 0.5px solid #C9C9C9;
    border-bottom : 0.5px solid #C9C9C9;
    border-left: 0.5px solid #C9C9C9;
    border-right: 0.5px solid #C9C9C9;
  }
  :host ::ng-deep .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline {
    color: rgba(82, 63, 105, 0.06);
    border: 0.5px solid #C9C9C9 !important;
    font-weight: 900;
    border-radius: 5px !important;
  }
  ::ng-deep .mat-input-element:disabled, .mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after {
    color: rgba(0, 0, 0, 0.38);
    margin-top: -10px !important;
  }
   :host :ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 .75em 0 .75em;
    margin-top: -0.25em;
    position: relative;
    height: 41px !important;
  }
  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // width: 300px;
  }
  ::ng-deep .mat-select-panel {
    border: 0.5px solid lightgray !important;
    box-shadow: none !important;
  }
  .radioGroup{
    border: 0.5px solid #6D6D6D !important;
  }
  
  .height {
    height: 28px;
  }
  :host::ng-deep.mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 5px 0.75em 0 0.75em !important;
    margin-top: -0.25em !important;
    position: relative !important;
  }
  .dot1{
    height: 5px;
    width: 5px;
    background-color:  #000000;;
    border-radius: 50%;
    display: inline-block;
  }
  .dot2{
    height: 5px;
    width: 5px;
    background-color:  #ee9400;;
    border-radius: 50%;
    display: inline-block;
  }
  .dot3{
    height: 5px;
    width: 5px;
    background-color: #0076c1;
    border-radius: 50%;
    display: inline-block;
  }
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgb(0 0 0 / 0.38) ;
  }
  
  .registerCard {
    z-index: 1 !important;
  }

  .examDisablepractice {
    border-color: rgba(0, 0, 0, 0.38) !important;
}


  
  
  // ::ng-deep .mat-radio-label-content {
  //   margin-top: 1px !important;
  // }