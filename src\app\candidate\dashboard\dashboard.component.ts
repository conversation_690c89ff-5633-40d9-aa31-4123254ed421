import { <PERSON><PERSON>iewInit, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild, OnDestroy } from "@angular/core";
import { Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import { LanguageService } from "src/app/core/language.service";
import { PersonForm } from "../application/application.types";
import { FormModel } from "./state/dashboard.models";
import {
  getDashboardData, getShowRegisterExam,
} from "./state/dashboard.actions";
import { upcomingExam } from "./state/dashboard.models/Upcomingexam";
import {
  selectDashboard, selectorShowRegisterExam$,
} from "./state/dashboard.selectors";
import { DashboardState } from "./state/dashboard.state";
import { PersonDetails } from "src/app/core/Dto/persondetail";
import { get_decodeInfo } from "../state/shared/shared.selectors";
import { GlobalUserService } from 'src/app/core/global-user.service';
import { DecodedIdentityToken, UserDetails } from "../candiate.types";
import { ScheduledService } from "../scheduled/scheduled.service";
import { MatDialog } from "@angular/material/dialog";
import { ConfirmPopupComponent } from "./confirm-popup/confirm-popup.component";
import { GrievanceFormService } from "../grievance-form/grievance-form.service";
import * as moment from 'moment'; import 'moment-timezone';
import { GrievanceFormsList } from "../grievance-form/state/grievance.model";
import { FormStatuses, FormTypes } from "src/app/core/Dto/enum";
import { map } from "rxjs/operators";
import { BreakpointObserver } from "@angular/cdk/layout";
import { ShowRegisterExamModel } from "src/app/core/Dto/show-register-exam.model";
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpService } from "src/app/core/http-services/http.service";
import { toUpdateSnn } from "./dashboard-static";
import { getRegisteredExam,getPracticeRegisteredExam } from "../scheduled/state/scheduled.actions";
import { Subscription, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { HttpErrorResponse } from "@angular/common/http";
import { RegisteredExamsModel } from "src/app/core/Dto/registered-exams.model";
import { selectorGetRegisteredexam,selectorPracticeGetRegisteredexam } from "../scheduled/state/scheduled.selectors";
import { TestDirectionsDialogComponent } from "../scheduled/test-directions-dialog/test-directions-dialog.component";
import { DatePipe } from "@angular/common";
// import { StateList } from 'src/app/core/enums/state-list.enum';
import { Excusedlist } from "../excused-absense/state/excused.model";
import { Allowonlyoral } from "src/app/core/examroom-formbuilder/form-builder.types";
import { ExamName } from "../scheduled/state/models/cart";
import { TermsDialogComponent } from "./terms/terms.component";
import { StateList } from "../forms-wrapper/forms-wrapper.types";
import { selectorLoadAllCertificates, selectorLoadAllRequests } from '../registry/state/registry.selectors';
import { loadAllCertificates, loadAllRequests } from '../registry/state/registry.actions';
import { CertificateModel, RequestModel } from '../registry/state/registry.model';

interface ReciprocityTile {
  title: string;
  description: string;
  action: string;
  icon: string;
}

@Component({
  selector: "exai-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
})
export class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  gridColumns = 4;
  listExam: any;
  _isAbsenceallow:boolean
  errors: any;
  exam: boolean = false;
  rescheduleallow=AllowReschedulestatus
  details: boolean = false;
  BeforeFillingApplication: Boolean = false;
  WithoutActiveApplication: Array<FormModel> = []

  showApplication :boolean = false
  applicationMessage:string
  person: PersonDetails = null;
  examList: PersonForm[] = [];
  dashboardDetail: upcomingExam;
  registeredDateTimeN: string;
  examDateTimeN: string;
  upcomingExams: upcomingExam[] = [];
  registeredPracticeexamSub: Subscription
  PracticeExams: RegisteredExamsModel[]
  @ViewChild('systemCheck') systemCheck: TemplateRef<any>;
  helpText:string="Register For Exam"
  examStatus: boolean = false;
  startNewApplicationFlag: boolean =false;
  activeForms: FormModel[] = [];
  userdata: DecodedIdentityToken;
  NotAllowScheduleforCheating:boolean = false
  dateFormat = Intl.DateTimeFormat().resolvedOptions();
  mobile: boolean = false;
  showRegisterExam: ShowRegisterExamModel;
  bothApproved: boolean = false;
  registeredexamSub: Subscription;
  registerExams: RegisteredExamsModel[];
  timeslot = false;
  RescheduleButton = true;
  statusIcon = statusIcon;
  showRegisteredDashboard: boolean = false;
  FirstName:string
  LastName:string
  MiddleName:string
  httpService: any;
  harshDetails={hmacHash:'',expirationDate:'',personId:0}
  listRequest:RequestModel[]=[]
  // Add new properties for PA/SC dashboard logic
paScDashboardTiles: any[] = [];
showPaScDashboard: boolean = false;
candidateHandbookUrl: string = ""; // Add handbook URL
// Add StateList property to make it accessible in template
  StateList = StateList;

  // Registry properties for PA/SC dashboard
  listCertificates: CertificateModel[] = [];
  private _listRequests: RequestModel[] = [];
  hasRegistryApplications: boolean = false;

  // Getter to ensure Registration Reciprocity requests are always filtered out
  get listRequests(): RequestModel[] {
    return this._listRequests.filter(request => {
      const isNotRegistrationReciprocity = request.formTypeId !== FormTypes.ReciporatingSCMA;
      const isNotRegistrationReciprocityByName = !request.name?.toLowerCase().includes('registration reciprocity');
      return isNotRegistrationReciprocity && isNotRegistrationReciprocityByName;
    });
  }

  set listRequests(value: RequestModel[]) {
    this._listRequests = value || [];
  }

  // Reciprocity tiles for PA/SC states
  reciprocityTiles: ReciprocityTile[] = [];

  // Subject for managing subscriptions
  private destroy$ = new Subject<void>();
  
  constructor(
    private router: Router,
    private store: Store<DashboardState>,
    public lngSrvc: LanguageService,
    public global: GlobalUserService,
    private _services: ScheduledService,
    private dialog: MatDialog,
    private service: GrievanceFormService,
    private breakpointObserver: BreakpointObserver,
    private snackbar: SnackbarService,
    private cdr: ChangeDetectorRef,
    private http:HttpService,
    private dp:DatePipe
  ) {
    this.global.userDetails.pipe(map((data) => {
      if (data) {
        this.store.dispatch<Action>(getDashboardData());
        this.store.dispatch<Action>(getShowRegisterExam({ personTenantRoleId: this.global.candidateId }));
        this.store.dispatch<Action>(getRegisteredExam({ candidateId: this.global.candidateId }));
        this.store.dispatch<Action>(getPracticeRegisteredExam({ candidateId: this.global.candidateId }));
        //TempMethod to update SSN Need to remove in later part
        this.getConfirmationSSN();
      }
    })
    ).subscribe();
  }


  ngOnInit(): void {
    this.store.select(get_decodeInfo).subscribe((data: DecodedIdentityToken) => {
      if (data)
        this.userdata = data;
      this.http.getProductFruitsKey(data.email).subscribe((response:harshDetails)=>{
        if(data){
           this.harshDetails.expirationDate = response.expirationDate
           this.harshDetails.hmacHash = response.hmacHash
           this.harshDetails.personId = this.global.userDetails.getValue().personId
        }
      })
    });

    // Check if should show PA/SC specific dashboard
    if (this.global.stateId === StateList.PA || this.global.stateId === StateList.SC) {
      this.determinePaScDashboardTiles();
    }

    this.store.select(selectDashboard).subscribe((data: DashboardState) => {
      if (!data.loading && data.form != null) {
        if (data.form.length > 0) {
          console.log(data.form)
          this.WithoutActiveApplication = data.form
          this.loadFormModels(data.form);

        }
        else {
          this.BeforeFillingApplication = true;
          this.showPaScDashboard = true
          this.http.getProductsLoad(0,this.harshDetails)

        }
      }
      if (data.upcomingExam && data.upcomingExam.length > 0) {
        this.loadUpcomingExams(data.upcomingExam);
      }
      if (data.personForms && data.personForms.length > 0) {
        this.examList = data.personForms;
      }
    });

  //   window.addEventListener('productfruits_button_ext_widget_init', (e) => {
  //         // your function that opens a custom widget
  // });

    this.store.select(selectorShowRegisterExam$).subscribe((data: ShowRegisterExamModel) => {
      if (data ) {
        this.showRegisterExam = data;
        // this.examStatus = !data.showRegister;
      }
    });

    this.breakpointObserver.observe(['(min-width : 500px)']).subscribe((result: { matches: any }) => {
      this.mobile = !result.matches;
    });

    this.registeredexamSub = this.store.select(selectorGetRegisteredexam).subscribe(
      (data: RegisteredExamsModel[]) => {
        this.registerExams =data
        const exams = data;
        if (exams.length > 0) {
          this.exam = true;
          this.details = false;
          this.timeslot = true;

          let n = Intl.DateTimeFormat().resolvedOptions();
          const _exams = exams.map((ele) =>
            Object.assign(
              {},
              ele,
              {
                registeredDateTime: moment(ele.registeredDateTime)
                  .tz(n.timeZone)
                  .format("MMMM Do, YYYY / h:mm A"),
              },
              {
                examDateTimenew: moment(ele.examDateTime).format("MM/DD/YYYY"),
              },
              {
                examDateTimePDT: moment(ele.examDateTimeUtc)
                  .tz(ele.timeZoneAbbreviation)
                  .format("h:mm a z"),
              }
            )
          );

          // Can't cancel and Reschedule the exam within 9 days start
          _exams.forEach((ele, i: number) => {
            if(ele.mode === "Online"){
              ele.examStatus = (ele.examStatusId === 97 || ele.examStatusId === 88 || ele.examStatusId == 89) && ele.isMisconductValidate == false?"Exam Under Review":ele.isMisconductValidate == true && ele.examStatusId !=96 && ele.examStatusId !=8  ?"Confirmed violation of exam rules":ele.examStatus
              function isDateGreaterThan48Hours(inputDate) {
                const currentDate = new Date();
                const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
              
                return diffInHours > 48;
              }
              const inputDate = new Date(ele.examDateTimeUtc);
              const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
              if (isGreaterThan48Hours) {
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }
            }else  if (ele.mode !== "Online") {
              ele.examStatus = (ele.examStatusId === 97 || ele.examStatusId === 88 || ele.examStatusId == 89) && ele.isMisconductValidate == false?"Exam Under Review":ele.isMisconductValidate == true && ele.examStatusId !=96 && ele.examStatusId !=8  ?"Confirmed violation of exam rules":ele.examStatus
              function isDateGreaterThan48Hours(inputDate) {
                const currentDate = new Date();
                const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
                return diffInHours > 216;
              }
              const inputDate = new Date(ele.examDateTimeUtc);
              const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
              if (isGreaterThan48Hours) {
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }
            }
           
          });
          // Can't cancel and Reschedule the exam within 9 days end
          this.listExam = _exams.filter(e => {
            return this.upcomingExams.find(x => (x.id == e.id && x.examMode === 'Online') ||(x.id == e.id && e.mode === "Test Center" && Allowonlyoral.includes(x.examName) || (x.id === e.id && JSON.parse(x.eventDataDetail).ExamCode === e.ExamCode)) ) == undefined
          });

          
        } else if (exams.length == 0) {
          this.details = true;
          this.exam = false;
          this.timeslot = false;
        }
        // showing a Reschedule button when exam cancelled
        let a= this.registerExams.filter(x=>(x.mode !=='Test Center' && x.examStatusId ===this.global.ScheduledStatusId))
        this.RescheduleButton = a.length > 0 ? false : true  
      },
      (err: HttpErrorResponse) => {
        this.errors = err.error;
        this.details = true;
        this.exam = false;
        this.timeslot = false;
      } 
    );

    this.registeredPracticeexamSub = this.store.select(selectorPracticeGetRegisteredexam).subscribe(
      (data: RegisteredExamsModel[]) => {
        this.PracticeExams = data
        const exams = data;
        if (exams.length > 0) {
          this.exam = true;
          this.details = false;
          this.timeslot = true;

          let n = Intl.DateTimeFormat().resolvedOptions();
          const _exams = exams.map((ele) =>
            Object.assign(
              {},
              ele,
              {
                registeredDateTime: moment(ele.registeredDateTime)
                  .tz(n.timeZone)
                  .format("MMMM Do, YYYY / h:mm A"),
              },
              {
                examDateTimenew: moment(ele.examDateTime).format("MM/DD/YYYY"),
              },
              {
                examDateTimePDT: ele.timeZoneAbbreviation != null ? moment(ele.examDateTimeUtc)
                  .tz(ele.timeZoneAbbreviation)
                  .format("h:mm a z") : '',
              },
              {
                examDatetime: this.dp.transform((ele.examDateTimeUtc), "MM/dd/YYYY", "+0000",)
              }
            )
          );

          // Can't cancel and Reschedule the exam within 9 days start
          _exams.forEach((ele, i: number) => {
            if (ele.mode !== "Online") {
              const startDate = moment(ele.examDateTimeUtc)
                .subtract(9, "days")
                .format("MMM-DD-YYYY");
              const endDate = moment(ele.examDateTimeUtc).format("MMM-DD-YYYY");
              const today = moment(new Date()).format("MMM-DD-YYYY");
              if (
                moment(today).isBefore(startDate) ||
                moment(today).isAfter(endDate)
              ) {
                //condition satisfied if today's date is not between 9 of exam
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }
            } else if (ele.mode === "Online") {

              function isDateGreaterThan48Hours(inputDate) {
                const currentDate = new Date();
                const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

                return diffInHours > 48;
              }
              const inputDate = new Date(ele.examDateTimeUtc);
              const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
              if (isGreaterThan48Hours) {
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }
            } else if (ele.examMode !== "Online") {
              function isDateGreaterThan48Hours(inputDate) {
                const currentDate = new Date();
                const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
                const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
                return diffInHours > 216;
              }
              const inputDate = new Date(ele.examDateTimeUtc);
              const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
              if (isGreaterThan48Hours) {
                _exams[i].reschedule = true;
                _exams[i].cancel = true;
              } else {
                _exams[i].reschedule = false;
                _exams[i].cancel = false;
              }
            }

          });
          // Can't cancel and Reschedule the exam within 9 days end
          // this.PracticeExams = _exams.filter(e => {
          //   return this.upcomingExams.find(x => x.id == e.id && e.mode === x.examMode) === undefined
          // });



        } else if (exams.length == 0) {
          this.details = true;
          this.exam = false;
          this.timeslot = false;
        }
        // showing a Reschedule button when exam cancelled
        let a = this.PracticeExams.filter(x => (x.mode !== 'Test Center' && x.examStatusId === this.global.ScheduledStatusId))
        this.RescheduleButton = a.length > 0 ? false : true
      },
      (err: HttpErrorResponse) => {
        this.errors = err.error;
        this.details = true;
        this.exam = false;
        this.timeslot = false;
      }
    );



    this.http.getAllData(this.global.personId).subscribe((data:any)=>{

         if(data){
              data.result.forEach((x,i)=>{
              if(x.RegistryStatusId === FormStatuses.revoked || x.RegistryStatusId === FormStatuses.summary || x.RegistryStatusId === FormStatuses.suspension){
                this.showApplication = true
                this.applicationMessage =x.RegistryStatusId === FormStatuses.revoked?"Certificate as Revoked":x.RegistryStatusId === FormStatuses.summary?"Certificate as Summary Suspensed":x.RegistryStatusId === FormStatuses.suspension?"Certificate is Suspensed":null
               }else{
                this.showApplication = false
                this.applicationMessage =""
               }
            })
           
         }
    })

    this.getIncidentstatus()

    // Load registry data for PA/SC states
    this.loadRegistryDataIfNeeded();

    // Also subscribe to user details changes to handle cases where user data loads after component init
    this.global.userDetails
      .pipe(takeUntil(this.destroy$))
      .subscribe((userDetails) => {
        if (userDetails && userDetails.stateId) {
          this.loadRegistryDataIfNeeded();
        }
      });

    // Retry loading registry data after a short delay if user data wasn't available initially
    setTimeout(() => {
      this.loadRegistryDataIfNeeded();
    }, 1000);
  }

  // Add method to determine PA/SC dashboard tiles
determinePaScDashboardTiles(): void {
  if (this.global.stateId === StateList.PA) {
    this.handlePaDashboard();
  } else if (this.global.stateId === StateList.SC) {
    this.handleScDashboard();
  }
}

handlePaDashboard(): void {
  // Check if PA candidate was ever certified in PA
  const wasEverCertifiedInPA = this.checkIfEverCertifiedInState('PA');
  
  this.paScDashboardTiles = [
    {
      title: 'Start a New Application',
      action: 'addNewApplication',
      description: 'You can apply for a New Application',
      icon: 'assets/img/NoApplication-Blue.svg'
    }
  ];

  if (wasEverCertifiedInPA) {
    this.paScDashboardTiles.push({
      title: 'Submit a Reinstatement Application',
      description: 'You can apply for a Reinstatement Assistant Reciprocity',
      action: 'submitReinstatement',
      icon: 'assets/img/NoApplication-Blue.svg'
    });
  } else {
    this.paScDashboardTiles.push({
      title: 'Submit a Reciprocity Application',
      description: 'You can apply for a Medication Assistant Reciprocity',
      action: 'submitReciprocity',
      icon: 'assets/img/NoApplication-Blue.svg'
    });
  }
  

}

handleScDashboard(): void {
  const certificationStatus = this.getScCertificationStatus();
  
  this.paScDashboardTiles = [
    {
      title: 'Add Application',
      action: 'addNewApplication',
      description: 'You can apply for a New Application',
      icon: 'assets/img/NoApplication-Blue.svg'
    }
  ];

  if (certificationStatus === 'never_certified') {
    this.paScDashboardTiles.push({
      title: 'Medication Assistant Reciprocity',
      description: 'You can apply for a Medication Assistant Reciprocity',
      action: 'submitReciprocity',
      icon: 'assets/img/NoApplication-Blue.svg',
    });
  } else if (certificationStatus === 'inactive') {
    this.paScDashboardTiles.push({
      title: 'Submit a Reinstatement Application',
      description: 'You can apply for a Reinstatement Assistant Application',
      action: 'submitReinstatement',
      icon: 'assets/img/NoApplication-Blue.svg',
    });
  } else if (certificationStatus === 'active') {
    this.paScDashboardTiles.push({
      title: 'Submit a Renewal Application',
      description: 'You can apply for a Renewal Assistant Application',
      action: 'submitRenewal',
      icon: 'assets/img/NoApplication-Blue.svg'
    });
  }
  

}

checkIfEverCertifiedInState(stateCode: string): boolean {
  // Implementation to check certification history
  // This would typically check against registry data
  return false; // Placeholder
}

getScCertificationStatus(): string {
  // Implementation to determine SC certification status
  // Returns: 'never_certified', 'inactive', or 'active'
  return 'never_certified'; // Placeholder
}

handleTileAction(action: string): void {
  switch (action) {
    case 'addNewApplication':
      this.addNewApplication();
      break;
    case 'submitReciprocity':
      this.router.navigate(['/application/reciprocity']);
      break;
    case 'submitNurseAideReciprocity':
      this.submitNurseAideReciprocity();
      break;
    case 'submitMedicationAssistantReciprocity':
      this.submitMedicationAssistantReciprocity();
      break;
    case 'submitReinstatement':
      this.router.navigate(['/application/reinstatement']);
      break;
    case 'submitRenewal':
      this.router.navigate(['/application/renewal']);
      break;
  }
}

  getForms(item){
    this.http.getForms(this.global.candidateId).subscribe((data:any)=>{
      if(data){
       let Value = data.filter((x)=>x.personFormId === item.personFormId)
        this.global.userstatus.next(Value[0])
        this.router.navigate(['application', 'application-form', FormTypes.Application, this.global.candidateId, item.eligibilityRouteId, this.global.stateId, '0', item.personFormId, item.formCode]);

      }
    })
  }

  absence(item: RegisteredExamsModel, personFormId: any): void {
   
    this.global.absensePersonEventId = item.id;
    let absenseForm: Excusedlist = {
      personFormId: personFormId,
      formTypeId: 0,
      formId: 0,
      examName: item.examName,
      name: "",
      state: "",
      eligiblityRoute: "",
      stateName: "",
      eligiblityRouteName: item.eligibilityRouteName,
      iconUrl: item.iconUrl,
      comment: "",
      examDate: item.examDateTime,
      submittedDate: "",
      status: item.examStatus,
      statusId: item.examStatusId,
      mode: item.mode,
      examId: item.examId,
      isExcuseAbsenceSubmitted: false,
      id: item.id,
    };
    this.service.viewabsense = absenseForm;
    
    if (personFormId == 0 || item.isExcuseAbsenceFilled == false) {
      // this.router.navigateByUrl("grievance-form/report-grievance");

      this.router.navigate([
        "absense-form",
        "Absence-form-view",
        FormTypes.ExcusedAbsense,
        this.global.candidateId,
        0,
        this.global.stateId,
        item.id,
        ''
      ]);
    } else {
      if (item.isExcuseAbsenceSubmitted == false) {
        // this.router.navigateByUrl("grievance-form/report-grievance");
        this.router.navigate([
          "absense-form",
          "Absence-form-view",
          FormTypes.ExcusedAbsense,
          this.global.candidateId,
          item.eligibilityRouteId,
          this.global.stateId,
          item.id,
          item.personFormId,
          0
        ]);
        
      } 
      else {
        // this.router.navigateByUrl("grievance-form/view-grievance");
        this.router.navigate([
          "absense-form",
          "Absence-form-view",
          FormTypes.ExcusedAbsense,
          this.global.candidateId,
          0,
          this.global.stateId,
          item.id,
          personFormId,
          0,
        ]);
      }
    }
  }

  private loadFormModels(forms: FormModel[]) {
    forms = forms.map((ele) => (Object.assign({}, ele, { submittedDateN: moment(ele.submittedDate).format("MMMM Do , YYYY / h:mm A") != 'Invalid date' ? moment(ele.submittedDate).format("MMMM Do , YYYY / h:mm A") : '' }, { submittedDateOnly: moment(ele.submittedDate).format('MM/DD/YYYY') }
    )))
    // if(forms.find(ele=>ele.status==="Expired")){
    //   this.hasExpired=true
    // }
    if(forms.filter(x => (x.statusId !== FormStatuses.Rejected && x.statusId !== FormStatuses.Cancelled && x.statusId !== FormStatuses.Expired)).length === 0){
      this.startNewApplicationFlag = (forms[forms.length - 1].statusId !== FormStatuses.Rejected);
    }
    this.BeforeFillingApplication = false;
    this.showPaScDashboard = true
    this.activeForms = forms;
 
    let approvedApplicationCount = forms.filter(x => (x.formTypeId == FormTypes.Application && (x.status == "Approved" || x.status =='Pending')));
    let accomodationFormCount = forms.filter(x => x.formTypeId == FormTypes.Accomodation && x.status == "Pending").length;

      approvedApplicationCount.length > 0?this.http.getProductsLoad(approvedApplicationCount[0].statusId,this.harshDetails):null
 
    // let rejected = forms.filter(
    //   (x) => x.status == "Rejected"
    // ).length;
    // if (accomodationFormCount > 0) {
    //   let approvedAccomodationCount = forms.filter(x => (x.formTypeId == FormTypes.Accomodation && x.status == "Approved")).length;
    //   if (approvedApplicationCount > 0 && approvedAccomodationCount > 0) {
    //     this.bothApproved = true;
    //     this.errors = 'Your application is approved and you may now register for the exam'
    //   }
    //   else {
    //     this.errors = 'Approved Application Required'
    //   }
    // }
    // else {
    //   if (approvedApplicationCount > 0) {
    //     this.bothApproved = true;
    //     this.errors = 'Your application is approved and you may now register for the exam'
    //   }else if (approvedApplicationCount > 0 && rejected > 0){
    //     this.bothApproved = true;
    //     this.errors = 'Your application is approved and you may now register for the exam'
    //   }
    //   else {
    //     this.errors = 'Approved Application Required'
    //   }
    // }

          // }
          this.examStatus = this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false ?false:this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved?false:true
          this.errors = (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodation == false) || (this.showRegisterExam !=undefined && this.showRegisterExam?.isApplicationApproved && this.showRegisterExam?.isAccommodationApproved)? 'Your application is approved and you may now register for the exam': 'Approved Application Required'

  }
  

  private loadUpcomingExams(comingExam: upcomingExam[]): void {
    if (comingExam.length != 0) {
       //9days for cancel and reschedule
      comingExam = comingExam.map((ele) => (Object.assign({}, ele, { registeredDateTime: moment(ele.registeredDateTime).format("MMMM Do , YYYY / h:mm A") },
        // { examDateTime: moment(ele.examDateTime).format('MM/DD/YYYY') },
        // { examDateTimePDT: moment(ele.examDateTime).tz(ele.timeZoneAbbreviation).format('h:mm a z') }
      )))
      var indx = comingExam.findIndex((ele) => {
        debugger
        return ((ele.examMode === 'Online') || (ele.examMode === "Test Center" && Allowonlyoral.includes(ele.examName)) || (ele.examMode ==="Test Center" && JSON.parse(ele.eventDataDetail).ExamCode ==='CBT-WR'));
      });

      this.dashboardDetail = comingExam[indx];
      this.upcomingExams = comingExam

     
      // Can't cancel and Reschedule the exam within 9 days start
      this.upcomingExams.forEach((ele, i: number) => {
        if (ele.examMode !== "Online") {
          function isDateGreaterThan48Hours(inputDate) {
            const currentDate = new Date();
            const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
            const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
            return diffInHours > 216;
          }
          const inputDate = new Date(ele.examDateTimeUtc);
          const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
          if (isGreaterThan48Hours) {
            comingExam[i].reschedule = true;
            comingExam[i].cancel = true;
          } else {
            comingExam[i].reschedule = false;
            comingExam[i].cancel = false;
          }
        } else  if(ele.examMode === "Online"){
          function isDateGreaterThan48Hours(inputDate) {
            const currentDate = new Date();
            const diffInMilliseconds = inputDate.getTime() - currentDate.getTime();
            const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
          
            return diffInHours > 48;
          }
          const inputDate = new Date(ele.examDateTimeUtc);
          const isGreaterThan48Hours = isDateGreaterThan48Hours(inputDate);
          if (isGreaterThan48Hours) {
            comingExam[i].reschedule = true;
            comingExam[i].cancel = true;
          } else {
            comingExam[i].reschedule = false;
            comingExam[i].cancel = false;
          }
        }
       
      })
      this.showRegisteredDashboard = this.upcomingExams &&
                                     this.upcomingExams.length > 0 && 
                                     this.upcomingExams.filter(x => (x.examMode === "Online") || (x.examMode === "Test Center" && Allowonlyoral.includes(x.examName)) || (JSON.parse(x.eventDataDetail).ExamCode ==='CBT-WR' && x.examMode === "Test Center" ) ).length > 0;

      this.listExam = this.upcomingExams.length > 0? this.listExam?.filter(e => {
        return this.upcomingExams.find(x => (x.id == e.id && e.examMode === 'Online') || (x.id == e.id && e.mode === "Test Center" && Allowonlyoral.includes(x.examName) ||  ( x.id == e.id &&  JSON.parse(x.eventDataDetail).ExamCode ==='CBT-WR' && x.examMode === "Test Center" ))) == undefined
      }):this.listExam
    }

  }

  ngAfterViewInit(): void {
  }


  getIncidentstatus() {
    this.http.getIncidentStatus(this.global.personId).subscribe((data: boolean) => {
      if (data) {
        this.NotAllowScheduleforCheating = data
      }
    })
  }

  private loadRegistryDataIfNeeded(): void {
    // Only load if we have the necessary user data and are in PA/SC states
    if (this.global.stateId && this.global.userId && this.global.candidateId &&
        (this.global.stateId === StateList.PA || this.global.stateId === StateList.SC)) {
      this.loadRegistryData();
      this.initializeReciprocityTiles();
    }
  }

  private loadRegistryData(): void {
    // Only proceed if we have the required IDs
    if (!this.global.userId || !this.global.candidateId) {
      return;
    }

    // Subscribe to certificates
    this.store.select(selectorLoadAllCertificates)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        if (data?.result && Array.isArray(data.result)) {
          debugger
          this.listCertificates = data.result;
          this.updateRegistryApplicationsStatus();
        } else if (data && Array.isArray(data)) {
          // Handle case where data is directly an array
          this.listCertificates = data;
          this.updateRegistryApplicationsStatus();
        } else {
          this.listCertificates = [];
          this.updateRegistryApplicationsStatus();
        }
      });

    // Subscribe to requests
    this.store.select(selectorLoadAllRequests)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: RequestModel[]) => {
        if (data && Array.isArray(data)) {
          debugger
          console.log(data)
          // Set the raw data - the getter will handle filtering
          this.listRequest = data;
          this.updateRegistryApplicationsStatus();
        } else {
          this.listRequest = [];
          this.listRequests =[]
          this.updateRegistryApplicationsStatus();
        }
      });

    // Now dispatch the actions to load data
    this.store.dispatch<Action>(loadAllCertificates({
      pageNumber: 1,
      pageSize: 10,
      personId: this.global.userId
    }));
    this.store.dispatch<Action>(loadAllRequests({
      candidateId: this.global.candidateId
    }));
  }

  private updateRegistryApplicationsStatus(): void {
    debugger
    this.hasRegistryApplications = this.listCertificates.length > 0 || this.listRequest.length > 0;
  }

  private initializeReciprocityTiles(): void {
    this.reciprocityTiles = [];

    if (this.global.stateId === StateList.SC) {
      // Add Nurse Aide Reciprocity for SC
      if(this.listRequest.length > 0){
          this.listRequest.filter((x)=>{
             if(NursformTypeId.includes(x.formTypeId)){
              this.reciprocityTiles.push({
                title: 'Medication Assistant Reciprocity',
                description: 'You can apply for a Medication Assistant Reciprocity',
                action: 'submitMedicationAssistantReciprocity',
                icon: 'assets/img/NoApplication-Blue.svg'
              });
             }else if(MedformTypeId.includes(x.formTypeId)){
              this.reciprocityTiles.push({
                title: 'Nurse Aide Reciprocity',
                description: 'You can apply for a Nurse Aide Reciprocity',
                action: 'submitNurseAideReciprocity',
                icon: 'assets/img/NoApplication-Blue.svg'
              });
      
             }else if(NursformTypeId.includes(x.formTypeId) && MedformTypeId.includes(x.formTypeId)){
                 this.reciprocityTiles =[]
             }
          })
      }else{
        this.reciprocityTiles.push({
          title: 'Nurse Aide Reciprocity',
          description: 'You can apply for a Nurse Aide Reciprocity',
          action: 'submitNurseAideReciprocity',
          icon: 'assets/img/NoApplication-Blue.svg'
        });

        this.reciprocityTiles.push({
          title: 'Medication Assistant Reciprocity',
          description: 'You can apply for a Medication Assistant Reciprocity',
          action: 'submitMedicationAssistantReciprocity',
          icon: 'assets/img/NoApplication-Blue.svg'
        });
      }
   

      // Add Medication Assistant Reciprocity for SC
     
    } else if (this.global.stateId === StateList.PA) {
      if(this.listRequest.length > 0){
        this.listRequest.filter((x)=>{
            if(NursformTypeId.includes(x.formTypeId)){
              this.reciprocityTiles =[]
            }
        })
      }else{
        this.reciprocityTiles.push({
          title: 'Nurse Aide Reciprocity',
          description: 'You can apply for a Nurse Aide Reciprocity',
          action: 'submitNurseAideReciprocity',
          icon: 'assets/img/NoApplication-Blue.svg'
        });
      }
      // Add only Nurse Aide Reciprocity for PA
     
    }
  }

  _systemCheck(): void {
    this.dialog.open(this.systemCheck, {
      maxHeight: '90vh',
      maxWidth: '85vw',
      minWidth: '75vh',
      minHeight: '75vh',
      autoFocus: false,
      disableClose: true
    })
  }

  addNewApplication() {
    this.router.navigate(["/application/select-application"]);
  }

  register() {
      this.router.navigate(["/exam-scheduled/register/"]);

  }

  // Handle reciprocity tile actions
  submitNurseAideReciprocity() {
    // Route to Nurse Aide Reciprocity form (FormTypes.CertificateReciprocity = 6)
    this.router.navigate(['registry', 'reciprocity-form', FormTypes.CertificateReciprocity, this.global.candidateId, 0, this.global.stateId, this.global.stateId, '']);
  }

  submitMedicationAssistantReciprocity() {
    // Route to Medication Assistant Reciprocity form (FormTypes.ReciporatingSCMA = 15)
    this.router.navigate(['registry', 'reciprocity-form', FormTypes.ReciporatingSCMA, this.global.candidateId, 0, this.global.stateId, this.global.stateId, '']);
  }

  reSchedule(event: any): void {
    if (!event?.candidateId) event.candidateId = this.global.candidateId;
    if (!event?.mode && event?.examMode) event.mode = event.examMode;
    ExamName.includes(event.eligibilityRouteId) ? this._services.PracticeInformation = event : this._services.rescheduleInformation = event;
    ExamName.includes(event.eligibilityRouteId) ? this.router.navigateByUrl("/practice_exam/register_practice") : this.router.navigateByUrl("/exam-scheduled/register");
  }

  getConfirmation(comingexam: upcomingExam) {
    const dialogRef = this.dialog.open(ConfirmPopupComponent, {
      data:{ message:comingexam,id:3},
    });
  }

  openTestCenterDirections(data) {
    const dialogRef = this.dialog.open(TestDirectionsDialogComponent, { panelClass: ['test-center'], data });
    dialogRef.afterClosed().subscribe((result) => { });
  }

  enableTestCenterDirections(item): boolean {
    if(!item) return false;
    const { mode, examDateTime } = item;
    if(!mode || !examDateTime) return false;
    const today = new Date()
    let newDate = this.dp.transform(today, 'yyyy-MM-dd');
    let ExamDate = this.dp.transform(examDateTime, 'yyyy-MM-dd');
    if(mode == 'Test Center' && ExamDate >= newDate) return true;
    return false;
  }

  getConfirmationSSN() {
    var stateSSNNotMandatory = ['PA']
    this.http.getAccountPersonDetails().subscribe((personDetails:any)=>{
      debugger
      this.FirstName = personDetails.firstName
      this.LastName = personDetails.lastName
      this.MiddleName= personDetails.middleName
      if(personDetails.fullSSN?.length<9 && !(stateSSNNotMandatory.includes(personDetails.clientStateCode))){
      const dialogRef = this.dialog.open(ConfirmPopupComponent, {
        disableClose: true,
        data:{ssn: personDetails},
      });
    }
    else if(this.global.userDetails.getValue().personProcessStatus){
      const dialogRef = this.dialog.open(ConfirmPopupComponent, {
        disableClose: true,
        data: {userdata:this.global.userDetails.getValue().cantest,UserDetails:personDetails,id:2},
        height:"500px",
        width:"700px"
      });
    }
    })
    }

  summary(item: PersonForm, index: number) {
    this.getForms(item)
  }

  editApplication(item: PersonForm, index: number) {
     this.getForms(item)
  
  }



  handleExamChange(ExamDetails){
    this.upcomingExams=ExamDetails;
    if(ExamDetails){
    this.loadUpcomingExams(ExamDetails)
    }
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    // Complete the destroy subject to unsubscribe from all observables
    this.destroy$.next();
    this.destroy$.complete();

    if(this.registeredexamSub) this.registeredexamSub.unsubscribe();
    if (this.registeredPracticeexamSub) this.registeredexamSub.unsubscribe();
  }

  public applyAgain(): void {
    this.router.navigate(["/application/select-application"]);
  }
}

enum statusIcon {
  "Waiting for Proctor" = "assets/img/Group 354.svg",
  "Exam Scheduled" = "assets/img/Group 355.svg",
  "Event Assigned" = "assets/img/Icons/approved.svg",
  "No Exam" = "assets/img/NoExam-Blue.svg",
  "Exam Cancelled" = "assets/img/Icons/approved.svg",
  "Event Rescheduled" = "assets/img/Icons/approved.svg",
  "Payment Pending" = "assets/img/Icons/pending.svg",
  "Event Completed" = "assets/img/Icons/approved.svg",
}
export const AllowReschedulestatus=[79,80,81,84,85,86,87,77,78,96,93]
export interface   harshDetails {hmacHash:string,expirationDate:string}
export const MedformTypeId=[15]
export const NursformTypeId=[6]
