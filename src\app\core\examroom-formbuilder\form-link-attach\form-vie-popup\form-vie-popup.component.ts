import { Component, ElementRef, Inject, OnInit, ViewChild, ViewChildren } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { FormBuilderService } from '../../form-builder.service';
import { sectionName } from '../../form-builder.types';
import { v4 as uuidv4 } from "uuid";
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-form-vie-popup',
  templateUrl: './form-vie-popup.component.html',
  styleUrls: ['./form-vie-popup.component.scss']
})
export class FormViePopupComponent implements OnInit {
  @ViewChildren("hiddenUnnecessaryButton") submitButton:any;
  formJson;
  userResponse;
  flag:Boolean = true;
  sectionName=sectionName.ProblemreportSction4
  isSubmit:boolean;
  permanentlySaveResponse:boolean;
  selectedfromDetails:any;
  constructor( @Inject(MAT_DIALOG_DATA) public data: any,private formservice: FormBuilderService,
  private globalUserService:GlobalUserService,private http:HttpClient, private dialogRef: MatDialogRef<FormViePopupComponent>) {
  }

  disable=[true,true,true,true,true,true]

  ngOnInit(): void {
      //for logic check dynamic-loader-effects line numner 189
      this.formJson=Array.isArray(this.data) ? JSON.parse(this.data[0].formJson) : JSON.parse(this.data);
      if(this.data[1] && this.data[1].length!=0){
        this.selectedfromDetails=this.data[1]
      this.userResponse= Array.isArray(this.data) ? JSON.parse(this.data[1].formResponse) : null
      }
  }

  SaveactionForm($event){
    let response={
        //formTypeID: this.data[0].formTypeId,
         // userResponse: {
            id: this.data[1] && this.data[1].length!=0? this.data[1].id: 0,
            personTenantRoleId: this.globalUserService.userDetails.value.personTenantRoleId,
            formResponse: JSON.stringify( $event.formValue),
            isSubmit: false,
            formId: this.data[0].id,
            code: this.data[1].length>0?this.data[1].code:uuidv4(),
            actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
          //}
        }
      this.http.post(environment.baseUrl +`client/api/form/savepersonform`,response).subscribe((personFormId:number)=>{
        if(personFormId)
        this.userResponse?'':this.formservice.attachedForm=[{personformId:personFormId}];
        this.dialogRef.close({personformId:personFormId});
      })
  }

}
