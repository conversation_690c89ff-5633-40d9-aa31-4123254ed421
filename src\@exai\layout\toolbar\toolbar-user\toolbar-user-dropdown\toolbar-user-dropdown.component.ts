import { environment } from 'src/environments/environment';
import { URL } from 'src/app/core/url';

import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from "@angular/core";
import { trackById } from "../../../../utils/track-by";
import ic<PERSON><PERSON> from "@iconify/icons-ic/twotone-person";
import icSettings from "@iconify/icons-ic/twotone-settings";
import icChevronRight from "@iconify/icons-ic/twotone-chevron-right";
import icArrowDropDown from "@iconify/icons-ic/twotone-arrow-drop-down";
import icBusiness from "@iconify/icons-ic/twotone-business";
import icVerifiedUser from "@iconify/icons-ic/twotone-verified-user";
import icLock from "@iconify/icons-ic/twotone-lock";
import icNotificationsOff from "@iconify/icons-ic/twotone-notifications-off";
import { Icon } from "@visurel/iconify-angular";
import { PopoverRef } from "../../../../components/popover/popover-ref";
import { Router } from "@angular/router";
import { GlobalUserService } from 'src/app/core/global-user.service';
import { NavigationService } from 'src/@exai/services/navigation.service';
import { LayoutService } from 'src/@exai/services/layout.service';
import { DecodedIdentityToken } from 'src/app/candidate/candiate.types';
import { PersonalInfo } from 'src/app/candidate/scheduled/payment/payment.component';
import { HttpClient } from '@angular/common/http';

export interface OnlineStatus {
  id: "online" | "away" | "dnd" | "offline";
  label: string;
  icon: Icon;
  colorClass: string;
}

@Component({
  selector: "exai-toolbar-user-dropdown",
  templateUrl: "./toolbar-user-dropdown.component.html",
  styleUrls: ["./toolbar-user-dropdown.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolbarUserDropdownComponent implements OnInit {

  trackById = trackById;
  icPerson = icPerson;
  icSettings = icSettings;
  icChevronRight = icChevronRight;
  icArrowDropDown = icArrowDropDown;
  icBusiness = icBusiness;
  icVerifiedUser = icVerifiedUser;
  icLock = icLock;
  icNotificationsOff = icNotificationsOff;
  isMobile:boolean;
  userName:string;
  personId:number;
  usersData: DecodedIdentityToken;
  personalInfo: PersonalInfo = new PersonalInfo();
  candidateId:any;
  roleName:string;
  Roles:Array<{roleId:number,roleName:string
  }>=[]
  constructor(
    private cd: ChangeDetectorRef,
    private router: Router,
    private popoverRef: PopoverRef<ToolbarUserDropdownComponent>,
    public global: GlobalUserService,
    private layoutService: LayoutService,
    private http:HttpClient
  ) {}

  ngOnInit() {
    this.getRoles()
    this.userData();
  
    this.layoutService.isMobile$.subscribe((result) => {
      this.isMobile = result;
    });



    this.global.userDetails.subscribe((data) => {
      if(data != null){
        this.personId = data.personId;
        this.candidateId=this.global.personId;
        this.roleName = data.roles[0].roleName;
      }
    })

    this.userName = this.global.family_name + " " +this.global.given_name
  }

  setStatus(status: OnlineStatus) {
    this.cd.markForCheck();
  }

  closeInformation(){
      this.popoverRef.close();
  }


  getRoles(){

    this.http.get(`${environment.baseUrl}login/get-role-assign?emailId=${this.global.emailIdToStartExam}`).subscribe((data:Array<{roleId:number,roleName:string
    }>)=>{
        if(data.length > 0){
         this.Roles = data.filter((x)=>x.roleName !=this.roleName)
        }
    })
 }
  close() {
    this.global.clearSessionStorage();
    window.location.href =  URL.REDIRECT_URL;
  }

  SelectRole(role, idToken, stateId): void {
    this.global.SelectRole(role,idToken,stateId)
  }

  openEdit() {
    this.router.navigate(["/manage-profile"]);
  }

  redirectToCredentialTicketForm(){
    const formValue = {firstName:this.personalInfo.firstname,lastName:this.personalInfo.lastname,email:this.personalInfo.email,candidateId:this.candidateId};
    window.location.href = `assets/img/credentia-ticket-form.html?formValue=${formValue.firstName}&lastName=${formValue.lastName}&email=${formValue.email}&candidateId=${formValue.candidateId}`;
  }

  async userData(): Promise<void>{
    this.usersData = this.global.getUserData();
      this.personalInfo.firstname = this.usersData.given_name;
      this.personalInfo.lastname = this.usersData.family_name;
      this.personalInfo.email = this.usersData.email;
   }


}
