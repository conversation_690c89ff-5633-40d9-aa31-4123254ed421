import { DatePipe } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http-services/http.service';
import { LanguageService } from 'src/app/core/language.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { cancelExam, getupcomingExam } from '../state/dashboard.actions';
import { get_isCancelled, selectUpcommmingExam } from '../state/dashboard.selectors';
import { TermsDialogComponent } from '../terms/terms.component';
import { HttpErrorResponse } from '@angular/common/http';
import { cancelPracticeExam } from '../../scheduled/state/scheduled.actions';
import { ExamName } from '../../scheduled/state/models/cart';
import { selectorGetIsCancelled } from '../../scheduled/state/scheduled.selectors';


@Component({
  selector: 'exai-confirm-popup',
  templateUrl: './confirm-popup.component.html',
  styleUrls: ['./confirm-popup.component.scss']
})


export class ConfirmPopupComponent implements OnInit {
candidateName:string;
ageLimit:number
maxDate: Date;
options = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },

];

state:Array<object>=[]


mode: string
  id: any
  examName: any
  isChecked:boolean = false
  testcenterName:string
  timezoneAbbrevation:string;
  time
  Date
  listExam: any = [];
  personDetails;
  modifyssn: FormGroup;
  form:FormGroup
  stateName:string
  stateId:number
examDate:string

validation_messages = {
  'firstName': [
    { type: 'required', message: this.global.First_name_is_required },
    { type: 'maxlength', message: this.global.Maximum_50_characters_are_allowed },
    { type: 'pattern', message: this.global.Please_enter_valid_first_name }
  ],
  'middleName': [
  ],
  'lastName': [
    { type: 'required', message: this.global.Last_name_is_required },
    { type: 'maxlength', message: this.global.Maximum_50_characters_are_allowed },
    { type: 'pattern', message: this.global.Please_enter_valid_last_name }
  ],
  'orgID': [
    { type: 'required', message: this.global.Please_enter_your_organization_ID }
  ],
  'dob': [
    { type: 'required', message: this.global.Please_select_your_DOB }
  ],
  'gender': [
    { type: 'required', message: this.global.Please_select_your_gender }
  ],
  'address': [
    { type: 'required', message: this.global.Please_enter_your_address },
    { type: 'maxlength', message: this.global.Maximum_225_characters_are_allowed }
  ],
  'city': [
    { type: 'required', message: this.global.Please_enter_your_city },
    { type: 'maxlength', message: this.global.Maximum_25_characters_are_allowed },
    { type: 'pattern', message: this.global.Please_enter_valid_City_name }
  ],
  'zipCode': [
    { type: 'required', message: this.global.Zip_code_is_required },
    { type: 'pattern', message: this.global.Maximum_5_numbers_are_allowed }
  ],
  'state': [
    { type: 'required', message: this.global.Please_enter_your_state },
    { type: 'maxlength', message: this.global.Maximum_25_characters_are_allowed },
    { type: 'pattern', message: this.global.Please_enter_valid_state_name }
  ],
  'ssn': [
    { type: 'required', message: this.global.Please_enter_your_Social_Security_number },
    { type: 'pattern', message: this.global.Please_enter_valid_SSN },
  ],
  'ssnColorado': [
    { type: 'required', message: this.global.Please_enter_your_SSN_or_ITIN },
    { type: 'pattern', message: this.global.Please_enter_valid_SSN_or_ITIN },
  ],
  'phoneNumber': [
    { type: 'required', message: this.global.Phone_number_is_required },
    { type: 'minlength', message: this.global.Enter_a_valid_phone_number },
    { type: 'maxlength', message: this.global.Enter_a_valid_phone_number }
  ],
  '_email': [
    { type: 'required', message: this.global.Email_is_required },
    { type: 'pattern', message: this.global.Enter_a_valid_email }
  ]
};
  constructor(private router: Router,
    private dialogRef: MatDialogRef<ConfirmPopupComponent>, @Inject(MAT_DIALOG_DATA)
    public data: any,
    public lngSrvc: LanguageService,
    private store: Store,
    private _snackbar: MatSnackBar,
    private http:HttpService,
    public global: GlobalUserService,
    private services: SnackbarService,private dialog: MatDialog,private datepipe:DatePipe) {
      console.log(data)
    this.id = data.message?.id
    this.examName = data.message?.examName
    this.personDetails=data.ssn
    this.candidateName = data.message?.candidateName
    this.mode =data.message?.examMode
    this.examDate =data.message?.examDateTime;
    this.timezoneAbbrevation =data.message?.timeZoneAbbreviation
    this.testcenterName =data.message?.testCenterDetails?.testCenterName
  }

  ngOnInit(): void {
    this.modifyssn = new FormGroup({
      ssn: new FormControl("", [Validators.required,Validators.minLength(9),Validators.maxLength(9)]),
    });
    this.loadCandidateDetails()
    debugger
    if (this.global.stateCodestoClearSSNValidation.includes(this.global.userDetails.getValue().stateCode)) {
      // this.form.get('ssn').clearValidators();
      this.form.controls['ssn'].clearValidators();
    }
    this.global.getStates().subscribe((data:Array<{stateName:string,stateCode:string}>)=>{
      if(data){
         this.state = data
         this.stateName =data.filter((x:{stateCode:string})=>x.stateCode === this.data.UserDetails.clientStateCode)[0].stateName
        this.loadCandidateDetails()
        if (this.global.stateCodestoClearSSNValidation.includes(this.global.userDetails.getValue().stateCode)) {
          // this.form.get('ssn').clearValidators();
          this.form.controls['ssn'].clearValidators();
        }
      }
    })

    this.data?.id == 2?this.loadStateAgeLimits(this.global.userDetails.getValue().stateCode):null
 
  
  }


  loadCandidateDetails(){
    this.form = new FormGroup({
      firstName: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.firstName:'', Validators.compose([Validators.required,
      Validators.maxLength(50), Validators.pattern("^[a-zA-Z ]+(?:[\s-][a-zA-Z]+)*$")])),
      middleName: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.middleName:'', []),
      lastName: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.lastName:'', Validators.compose([Validators.required,
      Validators.maxLength(50), Validators.pattern("^[a-zA-Z ]+(?:[\s-][a-zA-Z]+)*$")])),
      // orgID: new FormControl([{ value: this.orgIDValue ? this.orgIDValue : "" }, Validators.compose([Validators.required])]),
      dob: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.dateofBirth:'', Validators.compose([Validators.required])),
      gender: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.gender:'', Validators.compose([Validators.required])),
      address: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.address:'', Validators.compose([Validators.required,
      Validators.maxLength(225)])),
      city: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.city:'', Validators.compose([Validators.required, , Validators.maxLength(25), Validators.pattern("^[a-zA-Z \-]+(?:[\s-][a-zA-Z]+)*$")])),
      Registrationnumber: new FormControl(''),
      state: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.stateName:'', Validators.compose([Validators.required, Validators.maxLength(25), Validators.pattern("^[a-zA-Z \-]+(?:[\s-][a-zA-Z]+)*$")])),
      zipCode: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.zipCode:'', Validators.compose([Validators.required,
      Validators.pattern("^[0-9]{5}(?:-[0-9]{4})?$"),
      ])),
      phoneNumber: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.phoneNumber:'', Validators.compose([Validators.required,
      Validators.minLength(12), Validators.maxLength(14)])),
      _email: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.emailId:'', Validators.compose([Validators.required, Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,10}$')])),
      ssn: new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.fullSSN:'', Validators.compose([Validators.required, Validators.pattern("^[0-9]{9}(?:-[0-9]{9})?$")])),
      confirmSsn:new FormControl(this.data !=null && this.data !=undefined && this.data !=''?this.data.UserDetails.fullSSN:'', []),
      
    },
    [
      matchingTwoFieldsValidator('ssn', 'confirmSsn')]
    );
  }
  cancelExam(item) {
    ExamName.includes(this.data.message.eligibilityRouteId)?this.store.dispatch<Action>(
      cancelPracticeExam({
        examScheduleId: this.id,
        candidateId: this.global.candidateId,
      })
    )  : this.store.dispatch<Action>(cancelExam({ examScheduleId: this.id, candidateId: this.global.candidateId }));
    ExamName.includes(this.data.message.eligibilityRouteId)? this.store.select(selectorGetIsCancelled).subscribe((iscancelled) => {
      if (iscancelled != null) {
        this.Date = moment(this.examDate).format("MM/DD/YYYY")
        var datePipe = new DatePipe("en-US");
        this.time =datePipe.transform(this.examDate, 'shortTime','+0000')
        let body={
          body: `${this.mode} ${this.examName} scheduled ${this.testcenterName? "for" + this.testcenterName:""} on ${this.Date} at ${this.time} ${this.timezoneAbbrevation} was cancelled by ${this.global.roleName}`,
          candidateId:this.global.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: `${this.mode} ${this.examName} Cancelled`,
          userId: this.global.candidateId,
          userName:this.candidateName,
        }
        this.http.getAddnotes(body).subscribe(data=>{
        })
      
        setTimeout(()=>{
          this.store.dispatch<Action>(getupcomingExam({ candidateId: this.global.candidateId }));
          this.services.callSnackbaronSuccess("Exam Cancelled Successfully")
        })
       
      }
    })  : this.store.select(get_isCancelled).subscribe((iscancelled) => {
      if (iscancelled != null) {
        this.Date = moment(this.examDate).format("MM/DD/YYYY")
        var datePipe = new DatePipe("en-US");
        this.time =datePipe.transform(this.examDate, 'shortTime','+0000')
        let body={
          body: `${this.mode} ${this.examName} scheduled ${this.testcenterName? "for" + this.testcenterName:""} on ${this.Date} at ${this.time} ${this.timezoneAbbrevation} was cancelled by ${this.global.roleName}`,
          candidateId:this.global.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: `${this.mode} ${this.examName} Cancelled`,
          userId: this.global.candidateId,
          userName:this.candidateName,
        }
        this.http.getAddnotes(body).subscribe(data=>{
        })
      
        setTimeout(()=>{
          this.store.dispatch<Action>(getupcomingExam({ candidateId: this.global.candidateId }));
          this.services.callSnackbaronSuccess("Exam Cancelled Successfully")
        })
       
      }
    })
  }

  keyPressNumbers(event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    // Only Numbers 0-9
    if ((charCode < 48 || charCode > 57)) {
      event.preventDefault();
      return false;
    } else {
      return true;

    }


  }

  preventDefault(event: Event) {
    event.preventDefault();
  }

  close(){
    this.dialogRef.close(true);
  }

  loadStateAgeLimits(stateCode: string) {
    this.services.getStateAgeLimits(stateCode).subscribe(data => {
      if (data && data.length > 0) {
        this.ageLimit = data[0].ageLimit;
        const today = new Date();
        const years = Math.floor(this.ageLimit); // Extract the whole years
        const monthsDecimal = this.ageLimit - years; // Extract the decimal part representing months
        // Calculate months and days
        const months = Math.floor(monthsDecimal * 12);
        const days = Math.floor((monthsDecimal * 12 - months) * 30); // Assuming an average of 30 days per month
        this.maxDate = new Date(today.getFullYear() - years, today.getMonth() - months, today.getDate() - days);
      } else {
        // Handle case where age limit is not available for the current state code
      }
    });
}

// calculateMaxDate() {
//   const today = new Date();
//   this.maxDate = new Date(today.getFullYear() - this.ageLimit, today.getMonth(), today.getDate());
// }


  showOptions($event){
    if($event.checked === true){
      this.isChecked = true
    }else{
      this.isChecked = false 
    }
  }

  checkStatus(): boolean {
    const controls = this.form.controls;
    const invalid = []
    for (const name in controls) {
      invalid.push({ name: name, valid: !controls[name].invalid, })
    }
    const status = invalid.every((ele: any) => ele.valid == true);
    return  this.isChecked == true?status : false;
  }

  registered(){
  //  console.log(this.form.value) 
  //  this.dialogRef.close();
   const phoneNumber = this.form.value.phoneNumber;
   const countryCodeLength = phoneNumber.length - 10;
   const countryCode = phoneNumber.substr(0, countryCodeLength);
   let dob = this.datepipe.transform(this.form.value.dob, "yyyy-MM-dd");
   let body={
    "id": this.global.userDetails.value.personId,
    "firstName": this.form.value.firstName,
    "middleName": this.form.value.middleName,
    "lastName": this.form.value.lastName,
    "email": this.form.value._email,
    "ssn": this.form.value.ssn,
    "countryCode": countryCode,
    "country": "",
    "state": this.form.value.state,
    "city": this.form.value.city,
    "zipCode": this.form.value.zipCode,
    "accommodation": "",
    "clientCandidateId": "",
    "phoneNumber": this.form.value.phoneNumber,
    "tenantId": this.global.userDetails.value.stateId,
    "profile": "",
    "modifiedBy":this.global.userDetails.value.personId,
    "address":this.form.value.address ,
    "dateofBirth": dob,
    "gender": this.form.value.gender,
    'personProcessStatusId':3
   }
   this.global.UpdateUser(body).subscribe((data)=>{
      if(data){
        if(data){
           this.dialogRef.close();
           this.services.callSnackbaronSuccess('Updated SuccessFully')
        }
      }
   },(err:HttpErrorResponse | any)=>{
      this.services.callSnackbaronError(`${err.message.message.error}`)
   })
  }

  openDialog() {
    this.dialog.open(TermsDialogComponent, {
      data:this.global.roleId,
      disableClose: false,
      width: '650px',
      height: '500px',
      panelClass: 'my-custom-dialog-class'
    });
  }

  submit(){
    this.modifyssn.markAllAsTouched();
    let persondata;
    if (this.modifyssn.valid) {
      persondata={
        id:this.personDetails.id,
        firstName:null,
        middleName:null,
        lastName:null,
        address:null,
        dateofBirth:null,
        gender:null,
        profile:null,
        profileImageUrl:null,
        phoneNumber:null,
        ssn:this.modifyssn.value.ssn,
        fullSSN:this.modifyssn.value.ssn,
        emailId:null,
        zipCode:null,
        city:null,
        emailVerified:null,
        stateCode:null,
        clientStateCode:null,
        personRoleId:1,
        tenantId:3,
        modifiedBy:this.personDetails.id}
      this.http.upadateOnlySSN(persondata).subscribe(data=>{
        if(data){
        this.services.callSnackbaronSuccess('updated Successfully')
        this.dialogRef.close();
        }
      })
    }
  }
}

const matchingTwoFieldsValidator = (controlName: string, matchingControlName: string) => {
  return async (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (matchingControl.errors && !matchingControl.errors.mustMatch)
      return;
    return control.value !== matchingControl.value ? matchingControl.setErrors({ mustMatch: true }) : matchingControl.setErrors(null);
  }
}
