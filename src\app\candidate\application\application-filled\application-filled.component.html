<div class="px-gutter pt-2" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
  <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
    <div fxLayout="column">
      <h5>
        <strong>{{ this.lngSrvc.curLangObj.value.application }}</strong>
      </h5>
      <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
      </app-ng-dynamic-breadcrumb>
    </div>
  </div>
  <div>

    <div>
      <div class="pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
        <div class="justify-start" gdColumn="1 / 1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
          <div class="touch-auto overflow-auto" fxFlex="auto">
            <div class="pt-2" gdGap="12px" exaiContainer>
              <div class="content touch-auto overflow-auto -mt-5">
                <div fxLayout="row wrap" fxLayoutGap="10px grid">
                  
                  <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
                    *ngFor="let item of applicationList; let index = index">
                    <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                      <div class="bg-color px-4 py-3">
                        <div class="flex justify-between" fxLayout="row">
                          <div class="t-xs title-hed">
                            <strong>{{ item.stateName }}&nbsp;{{
                              item.eligiblityRouteName
                              }}</strong>
                          </div>
                        </div>
                        <div class="t-xs state-elig pt-1" fxLayout="row">
                          <strong> {{ item.name }}</strong>
                        </div>
                      </div>
                      <div fxLayout="column">
                        <div class="px-4" fxFlexFill>
                          <span class="status t-xs">{{
                            this.lngSrvc.curLangObj.value.currentStatus
                            }}</span><br />
                          <span><img src="{{ item.iconUrl }}" class="inline iconSize" /></span>
                          <span class="t-xs ml-2 -mt-3" [style.color]="
                            item.status == 'Drafted' || item.status == 'Pending'
                              ? '#EE9400'
                              : item.status == 'Approved'
                              ? '#00AB72'
                              : '#F7685B'
                          ">
                            {{ item.status }}</span>
                          <!-- <div class="italic t-xs state-elig" *ngif="item.status =='Pending' || 'Approved' || 'Rejected'"> -->
                          <div class="italic t-xs state-elig pb-2 pt-1" *ngIf="item.status != 'Drafted'">
                            <!-- {{item.lastUpdatedDate | date:"short"}} -->
                            {{ item.applicationSubmittedDate }}
                          </div>
                          <div class="italic t-xs state-elig pb-3 pt-1" *ngIf="item.status == 'Drafted'">
                            <!-- {{item.lastUpdatedDate | date:"short"}} -->
                           Yet to Submit
                          </div>
                          <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                            gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr"
                            exaiContainer>
                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                              <div class="h4 status t-xs">
                                {{ this.lngSrvc.curLangObj.value.appId }}
                              </div>
                            </div>
                            <div gdColumn="3/6" gdColumn.lt-md="3/6" gdColumn.lt-sm="3/6">
                              <div class="h4 status t-xs" *ngIf="item.status != 'Drafted'">
                                {{ this.lngSrvc.curLangObj.value.submitDate }}
                              </div>
                            </div>
                          </div>
                          <div gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                            gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                              <div class="h4 status1 t-xs">
                                {{ item.personFormId }}
                              </div>
                            </div>
                            <div gdColumn="3/6" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/6">
                              <div class="status1 t-xs" *ngIf="item.status != 'Drafted'">
                                {{item.appSubmittedDate }}
                              </div>

                              
                            </div>
                          </div>
                          <div class="pt-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr"
                            gdColumns.lt-sm="1fr 1fr" exaiContainer *ngIf="item.statusId == 3 || item.status == 'Change Request' || item.statusId == 14 ">
                            <div gdColumn="1/-1" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                        
                              <div class="" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
                                <div class="t-xs status minimise"  *ngIf="item.status == 'Rejected'">
                                  {{ this.lngSrvc.curLangObj.value.reason }}
                                </div>
                                <div class="t-xs status minimise" *ngIf="item.statusId == 4 || item
                                .statusId === 14">
                                  {{ this.lngSrvc.curLangObj.value.reason2 }}
                                </div>
                              </div>


                              <div class="status1 t-xs minimise" matTooltip="{{item.comment}}">{{ item.comment }}</div>
                            </div>
                          </div>
                          <div class="h-8" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr"
                            gdColumns.lt-sm="1fr 1fr" exaiContainer *ngIf="hasAnyRejectedChangeRequestItems && item.status !== 'Change Request' && item.status !== 'Rejected' ">
                            <div gdColumn="1/-1" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                              <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
                                <div class="t-xs status" >
                                
                                </div>
                               <div  class="status1 t-xs"></div>
                              </div> 
                              <div fxLayout="column"> 
                                <div class="t-xs status">
                                </div>
                               <div  class="status1 t-xs"></div>
                              </div> 
                             
                            </div>
                          </div>

                          <div class="flex justify-end py-4" gdColumn="2 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2">
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                              *ngIf="item.status == 'Approved'">
                              <button mat-button color="#209E91" (click)="summary(item, index)" class="btn-4 text-xs">
                                {{ this.lngSrvc.curLangObj.value.summary }}
                              </button>
                            </div>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                              *ngIf="item.status == 'Pending'">
                              <button mat-button color="#209E91" (click)="summary(item, index)" class="btn-4 text-xs">
                                   {{ this.lngSrvc.curLangObj.value.summary }}
                              </button>
                            </div>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                              *ngIf="item.status == 'Rejected' && item.allowApplyAgain">
                              <button mat-button color="#209E91" class="btn-4 font-bold t-xs" (click)='applyAgain()'>
                                {{ this.lngSrvc.curLangObj.value.apply }}
                              </button>
                            </div>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px"
                              *ngIf="item.status == 'Drafted' || item.status == 'Change Request' || item.statusId == 14">
                              <button mat-button color="#209E91" class="btn-4 font-bold t-xs"
                                (click)="editApplication(item, index)">
                                Edit Form
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div *ngIf="startNewApplicationFlag" class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%">
                    <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                      <div class="flex justify-center pt-4">
                        <img class="pt-5 pb-2 w-3/12" src="assets/img/NoApplication-Blue.svg" /><br />
                      </div>
                      <div class="content1 pb-6">
                        <section class="mr-5 ml-5">
                          <div class="bg-color">
                            <span class="welc-note
                            flex
                            justify-center
                            text-center text-xs mx-4 mt-4">
                              <span class="pt-3 pb-6"> Fill Application Form. </span>
                            </span>
                          </div>
                        </section>
                        <div class="flex justify-center mb-4 -mt-5 t-xs"  matTooltip="{{applicationMessage}}">
                          <button class="add-new text-xs" data-application-type="Start application" mat-button color="primary" [ngClass]="{'button-disabled' : showApplication}"   (click)="addNewApplication()">
                            {{ this.lngSrvc.curLangObj.value.addNewApp }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%">
                    <div class="card shadow-none cardBorder h-full">
                      <div class="flex justify-center pt-4">
                        <img class="pt-5 pb-2" src="assets/img/register-exam1.svg" /><br />
                      </div>
                      <div class="content1 pb-6">
                        <section class="mr-5 ml-5 ">
                          <div class="bg-color">
                            <span class="
                              welc-note
                              flex
                              justify-center
                              text-center text-xs mx-4 mt-4
                            ">
                              <span class="pt-3 pb-6">
                                {{errors}}
                              </span>
                            </span>
                          </div>
                        </section>
                        <div class="flex justify-center mb-4 -mt-4 t-xs">
                          <button  class="add-new text-xs " mat-button color="primary" (click)="register()" [matTooltip]="helpText"
                            [ngClass]="{'button-disabled' : examStatus || NotAllowScheduleforCheating}">
                            {{ this.lngSrvc.curLangObj.value.registerExam }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>