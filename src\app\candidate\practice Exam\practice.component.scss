.add-new {
    background-color: var(--button-background) !important;
    color: var(--button-color) !important;
    padding: 0.1rem 0.5rem;
  }
  .title {
    background: #fafafa;
    margin-left: -16px;
    margin-right: -16px;
    margin-top: -16px !important;
  
    .text {
      color: #11263c;
    }
  }
  .status {
    color: #c4c4c4;
  }
  .eliggilibityroutename {
    font-size: 0.57rem;
  }
  .status3 {
    color: var(--text-color1);
  }
  .status1 {
    color: #7d7d7d;
  }
  
  .welc-note {
    color: var(--text-color1);
  }
  .active {
    color: var(--text-color2);
    border: 1px solid var(--text-color2);
  }
  .active2 {
    color: var(--text-color2);
  }
  
  .minimise{
    width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .submit {
    width: 147px;
  }
  .active1 {
    color: #ee9400;
  }
  
  .content {
    padding: 14px;
  }
  
  .content1 > mat-card {
    margin-bottom: 12px;
  }
  .cradHeight {
    height: 300px;
  }
  .example-card {
    max-width: 400px;
  }
  
  .example-header-image {
    background-image: url('https://material.angular.io/assets/img/examples/shiba1.jpg');
    background-size: cover;
  }
  mat-card {
    max-width: 400px;
  }
  .add {
    background-color: var(--text-color2);
    color: #fff;
    // width: 200px;
    height: 33px;
  }
  .iconSize {
    width: 12px;
    height: 12px;
  }
  mat-card-title {
    margin-bottom: 5px;
  }
  
  .bg-color {
    background-color: #fafafa;
  }
  .add-new {
    background-color: var(--button-background) !important;
    color: var(--button-color) !important;
    padding: 0.1rem 0.5rem;
  }
  .fontStyle {
    font-style: italic;
  }
  .addBtn {
    color: var(--text-color2);
    height: 30px;
    background-color: #e9f5f5;
  }
  .textColor {
    color: #6d6d6d;
    font-family: Roboto;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    // line-height: 18.75;
    align-items: center;
  }
  .size {
    height: 70px;
    width: 70px;
  }
  .btnText {
    background: var(--sidenav-item-background-active);
    border-left-color: var(--sidenav-item-border-color-active);
  }
  .material-icons-round {
    font-size: 16px;
  }
  .icon {
    cursor: pointer;
  }
  ::ng-deep .mat-menu-panel {
    min-height: 0px !important;
  }
  ::ng-deep .mat-menu-content:not(:empty) {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }
  .textColor1 {
    color: #d80027;
  }
  
  .title-hed {
    color: var(--text-dropdown);
  }
  
  .state-elig {
    color: var(--text-color1);
  }
  
  ::ng-deep .mat-menu-item .mat-icon {
    margin-right: 0px !important;
  }
  .space {
    line-height: 40px;
  }
  // .cardHeight{
  //   height: 130px;
  // }
  
  ::ng-deep .mat-button.mat-primary.mat-button-disabled, .mat-button.mat-accent.mat-button-disabled, .mat-button.mat-warn.mat-button-disabled, .mat-button.mat-button-disabled.mat-button-disabled, .mat-icon-button.mat-primary.mat-button-disabled, .mat-icon-button.mat-accent.mat-button-disabled, .mat-icon-button.mat-warn.mat-button-disabled, .mat-icon-button.mat-button-disabled.mat-button-disabled, .mat-stroked-button.mat-primary.mat-button-disabled, .mat-stroked-button.mat-accent.mat-button-disabled, .mat-stroked-button.mat-warn.mat-button-disabled, .mat-stroked-button.mat-button-disabled.mat-button-disabled {
    /* color: rgba(0, 0, 0, 0.26); */
    background: #4444 !important;
  }
  ::ng-deep b, strong {
    font-weight: bolder;
    font-size: 0.85em !important;
  }
  
  .button-disabled {
    opacity: 0.4;
    pointer-events: none;
  }
  .titleFont {
    font-size: 1.3em;
    // font-weight: bolder;
    font-family: "Roboto", sans-serif;
  }
  
  .onhover {
    cursor: pointer;
  }