
export enum FormTypes {
  Accomodation = 1,
  Grievance = 2,
  Application = 3,
  Demographic = 4,
  CertificateRenewal = 5,
  CertificateReciprocity = 6,
  CertificateDuplicate = 7,
  Survey = 8,
  ExcusedAbsense=13,
  RenewalSCMA = 14,
  ReciporatingSCMA = 15,
  V2_Reciporating_MACE =9,
  V2_Renewal_MACE = 8,
  Grievance_add = 10,
  SC_reinstate = 17,
  SC_reinstate_id =12
}

export const FormTypesToNames = {
  1:"Accomodation",
  2:"Grievance",
  3:"Application",
  4:"Demographic",
  5: "Certificate Renewal",
  6: "Certificate Reciprocity",
  7: "Certificate Duplicate",
  8: "Survey",
  13:"Excused Absence",
  14:"Registration Renewal",
  15:"Registration Reciprocity",
  17:"Reinstatement"
}
  
export enum FormStatuses {
  Drafted = 1,
  Pending = 2,
  Rejected = 3,
  ChangeRequest = 4,
  YetToRecieve = 5,
  Saved = 6,
  Approved = 7,
  Cancelled = 8,
  Submitted = 9,
  Expired = 10,
  revoked = 6,
  summary = 8,
  suspension =7,
  Inactive = 5,
  withdraw_request = 14

}