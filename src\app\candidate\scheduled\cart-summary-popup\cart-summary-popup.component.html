<div class="py-2" gdColumns="1fr" gdColumns.lt-md="1fr " gdColumns.lt-sm="1fr"
gdGap="12px" exaiContainer>
<div class="" gdColumn="1/-1" gdColumn.lt-md="1" gdColumn.lt-sm="1/2">
    <div class="card shadow-none exam" fxFlex="auto">
        <div class="flex justify-between px-4 pt-2 pb-3" fxLayout="row">
            <div class="">
                <h5 class="-ml-1 text-xs font"><strong>Cart Summary</strong></h5>
            </div>
            <div class="flex text-left text-base pb-2">
                <mat-icon class="-mr-3 ml-2 delete"  [mat-dialog-close]="true"
                    >close</mat-icon>
            </div>
        </div>
            <div  class="mb-1 content1 px-4" exaiContainer>
                <div fxLayoutGap="6px grid">
                    <div fxLayout="row wrap" fxLayoutGap="10px grid">
                        <div [fxFlex]="(100/1) + '%'" fxFlex.xs="100%" fxFlex.sm="100%" fxFlex.md="100%"
                            *ngFor="let item of listExam; let i = index">
                            <div class="card shadow-none cardBorder mb-2" fxFlex="auto">
                                <div class="bg-color -pt-2">
                                    <div class="flex justify-between px-4 pt-2" fxLayout="row">
                                        <div class="" *ngIf="item.examCode !='NA-PAR'">
                                            <h5 class="-ml-1 t-xs font "><strong>{{item.examName}}</strong></h5>
                                        </div>
                                        <div class="" *ngIf="item.examCode =='NA-PAR'">
                                            <h5 class="-ml-1 t-xs font "><strong>{{item.skillTitle}}</strong>
                                            </h5>
                                        </div>
                                        <div class="flex text-left text-base pb-2">
                                            <span class="text-xs">${{item.amount}} </span>
                                            <mat-icon *ngIf="item.examCode !='NA-PAR'" class="-mr-3 ml-2 delete"
                                                (click)="deleteItem(item.personEventCartId, i,'Exams')">delete
                                            </mat-icon>
                                            <mat-icon *ngIf="item.examCode =='NA-PAR'" class="-mr-3 ml-2 delete"
                                                (click)="deleteSkill(item)">delete
                                            </mat-icon>
                                        </div>

                                   



                                    </div>
                                    <div fxLayout="row" class="px-3 -mt-2 "
                                        *ngIf="item.examName!='Renewal Fee' && item.examName!='Reciprocity Fee'&& item.examName!='Medication Assistant Reciprocity Fee' && item.examName!='Medication Assistant Renewal Fee'&& item.cartItemTypeId !='7' && item.cartItemTypeId !='9' && item.examCode !='NA-PAR' ">
                                        <h6 class="t-xs mb-1 status1 ">{{item.eligibilityRouteName}}</h6>
                                    </div>
                                    <div class="px-3 -mt-2 "
                                        *ngIf="item.examName!='Renewal Fee' && item.examName!='Reciprocity Fee'&& item.examName!='Medication Assistant Reciprocity Fee' && item.examName!='Medication Assistant Renewal Fee'&& item.cartItemTypeId !='7' && item.cartItemTypeId !='9' && item.examCode =='NA-PAR' ">
                                        <p class="t-xs mb-1  font">{{item.skillDescription}}</p>
                                        <div class="flex">
                                            <div class="w-1/5">
                                                <div class="meta-label">Duration</div>
                                                <div class="meta-value ml-2">{{ item.skillDuration }}</div>
                                            </div>
                                            <div class="w-1/5">
                                                <div class="meta-label">Steps</div>
                                                <div class="meta-value ml-2">{{ item ?.skillSteps }}</div>
                                            </div>
                                            <div class="w-1/5">
                                                <div class="meta-label">Attempts</div>
                                                <div class="meta-value ml-2 ">{{ item?.skillTotalAttempt }}
                                                </div>
                                            </div>
                                            <div class="w-1/5">
                                                <div class="meta-label">Validity</div>
                                                <div class="meta-value ml-2">{{ item?.validity }}</div>
                                            </div>
                                            <div class="w-1/5">
                                                <div class="meta-label">Price</div>
                                                <div class="meta-value ml-2">{{ item.amount }}</div>
                                            </div>
                                        </div>
                                       


                                    </div>
                                </div>
                                <div fxLayout="column"
                                    *ngIf="item.examName!='Renewal Fee' && item.examName!='Reciprocity Fee'&& item.examName!='Medication Assistant Reciprocity Fee' && item.examName!='Medication Assistant Renewal Fee'&& item.cartItemTypeId !='7' && item.cartItemTypeId !='9' && item.examCode !='NA-PAR' ">
                                    <div class="pt-2 mb-2 px-3"
                                        gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        exaiContainer>
                                        <div gdColumn="1/4" gdColumn.lt-md="1/4" gdColumn.lt-sm="1/4">
                                            <div *ngIf="item.testCenterName==null" class="h4 status t-xs">
                                                ExamMode</div>
                                            <div *ngIf="item.testCenterName!=null" class="h4 status t-xs">Test
                                                Center Name</div>
                                        </div>
                                        <div gdColumn="5/8" gdColumn.lt-md="5/8" gdColumn.lt-sm="5/8">
                                            <div class="h4  status t-xs">Exam Date</div>
                                        </div>
                                        <div gdColumn="8/-1" gdColumn.lt-md="8/-1" gdColumn.lt-sm="8/-1">
                                            <div class="h4 status t-xs">Exam Time</div>
                                        </div>
                                    </div>
                                    <div class="px-3 mb-2"
                                        gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        exaiContainer>
                                        <div gdColumn="1/4" gdColumn.lt-md="1/4" gdColumn.lt-sm="1/4">
                                            <div *ngIf="item.testCenterName==null" class="h4 status1 t-xs">
                                                {{item.examMode}}</div>
                                            <div *ngIf="item.testCenterName!=null" class="h4 status1 t-xs">
                                                {{item.testCenterName}}</div>
                                            <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->
                                        </div>
                                        <div gdColumn="5/8" gdColumn.lt-md="5/8" gdColumn.lt-sm="5/8">
                                            <!-- <div class="h4 status1 t-xs">{{item.eventDate | date}}</div> -->
                                            <div class="h4 status1 t-xs">{{ item.eventDate |date:
                                                "MM/dd/yyyy":'+0000' }}</div>
                                        </div>
                                        <div gdColumn="8/-1" gdColumn.lt-md="8/-1" gdColumn.lt-sm="8/-1">
                                            <!-- <div class="h4  status1 ml-2 t-xs">{{item.eventDate | date:"HH:mm"}} -->
                                            <div class="h4  status1  t-xs">{{item.eventDate
                                                |date:'shortTime':'+0000'}} {{item.timeZoneAbbreviation}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                              
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        

       


        <!-- <div class="px-2 " gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr" exaiContainer>
            <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                <div class="h4 ml-6 font-bold  total text-xs">
                    SubTotal
                </div>
            </div>
            <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                <div class="h4 text-right mr-6 status1 font-bold  text-xs">
                    ${{subtotal}}
                </div>
            </div>


        </div> -->
        <!-- <div class="px-2 pt-3 mb-3" gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
            exaiContainer>
            <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                <div class="h4 ml-6 total text-xs">
                    Tax
                </div>
            </div>
            <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                <div class="h4 text-right mr-6 status1 text-xs">
                    $1.00
                </div>
            </div>


        </div> -->
        <hr class='ml-5 mr-5 status1'>
        <div class="px-2 pt-3 mb-3" gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
            exaiContainer>
            <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                <div *ngIf="listExam?.length > 0 || PracticeListExam?.length > 0" class="h4 ml-6 mb-2 total font-bold  text-xs">
                     Total
                </div>

            </div>
            <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                <div *ngIf="listExam?.length > 0" class="h4 text-right mr-6 mb-2 total font-bold text-xs">
                    ${{ExamTotal}}
                </div>
                <div *ngIf="listExam?.length == 0"  class="h4 text-right mr-6 mb-2 total font-bold text-xs">
                    ${{subtotal}}
                </div>
            </div>
        </div>
        <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="8px">
            <button
              class="buuton2 text-xs"
              mat-button
              type="button"
              *ngIf="registeredExams?.length<2 && listExam?.length < 2 && !NotAllowScheduleforCheating"
              (click)="addExam()"
            >
              Add Exam
            </button>
    
            <button *ngIf="listExam?.length == 0"  class="buuton2  text-xs" mat-button type="button" (click)='practicePay()'
            >Pay Now</button>
            <button *ngIf="listExam?.length > 0 && !NotAllowScheduleforCheating" class="buuton2  text-xs" mat-button type="button" (click)='PayNow()'
            >Pay Now</button>
          </div>

          <div class="p-3">
            Disclaimer: "Please note that your exam slot is not reserved until
            payment is processed and you receive a confirmation email."
          </div>
     
    </div>
</div>
</div>