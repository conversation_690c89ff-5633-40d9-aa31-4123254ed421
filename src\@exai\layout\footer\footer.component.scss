:host {
  bottom: 0;
  display: block;
  width: 100%;
  z-index: var(--footer-z-index);
}

.footer {
  background-color: var(--toolbar-background) !important;
  color: var(--footer-color);
  height: var(--footer-height);
  // padding-left: var(--padding);
  // padding-right: var(--padding);
  position: relative;
  z-index: var(--footer-z-index);
  border-top: var(--border);
  place-content: center !important;
}

img,
video {
  max-width: 85% !important;
  height: auto;
}

.copy-right {
  color: var(--text-copy);
}

.facebook {
  width: 2.1rem;
}

.color {
  color: var(--status-color);
}

.help-btn {
  position: fixed;
  bottom: 0.15rem !important;
  right: 0.65rem;
  z-index: 100000000 !important;
  border: 1px solid var(--text-color2);
  border-radius: 4px;
  background-color: var(--text-color2);
  padding-top: 0.1rem;
  padding-bottom: 0.1rem;
}

.chat {
  color: var(--background-base);
  margin-bottom: -0.15rem !important;
  margin-top: 0.25rem;
  // height: 20px;
}
.chat1 {
  color: var(--background-base);
  margin-bottom: -0.35rem !important;
  // vertical-align: text-top!important;
}

.chatContainer {
  height: fit-content;
  width: 30vw;
  position: fixed;
  bottom: 2.5rem !important;
  right: 1rem;
  border-radius: 4px;
  border: var(--border);
  // @screen xxs {
  //   width: 50vw;
  // }
}

.mat-button {
  line-height: 26px !important;
  min-width: fit-content !important;
}
