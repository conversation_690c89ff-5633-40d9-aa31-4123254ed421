import { createReducer, on } from "@ngrx/store";
import * as RegistryActions from "./registry.actions";
import { store } from "./registry.store";

export const reducer = createReducer(
  store,
  on(RegistryActions.loadedAllCertificates, (state, action) => {
    return {
      ...state,
      certificates: action.data
    }
  }),
  on(RegistryActions.loadedAllRequests, (state, action) => {
    return {
      ...state,
      requests: action.data
    }
  }),

  on(RegistryActions.DO_Check_Registrys, (state, action) => {
    return {
      ...state,
      data: action.data
    }
  }),
  on(RegistryActions.loadedAllStates, (state, action) => {
    return {
      ...state,
      states: action.data
    }
  }),
  on(RegistryActions.gotForm, (state, action) => {
    return {
      ...state,
      form: action.form
    }
  }),
  on(RegistryActions.gotDraftedForm, (state, action) => {
    return {
      ...state,
      draftedForm: action.draftedForm
    }
  }),
  on(RegistryActions.loadedCertificatePath, (state, action) => {
    return {
      ...state,
      certificatePath: action.certificatePath
    }
  }),
  on(RegistryActions.requestSaveForm, (state, action) => {
    return {
      ...state,
      form: action.params
    }
  }),
  on(RegistryActions.setSelectedCertificate, (state, action) => {
    return {
      ...state,
      selectedCertificate: action.data
    }
  }),
  on(RegistryActions.setSelectedRequest, (state, action) => {
    return {
      ...state,
      selectedRequest: action.data
    }
  }),
  on(RegistryActions.setSelectedState, (state, action) => {
    return {
      ...state,
      selectedState: action.data
    }
  }),
  on(RegistryActions.loadedLogs, (state, action) => {
    return {
      ...state,
      logs: action.logs
    }
  }),
  on(RegistryActions.clearDraftedState, (state, action) => {
    return {
      ...state,
      draftedForm: null,
      deletedRenewedForm: false
    }
  }),
  on(RegistryActions.clearForm, (state, action) => {
    return {
      ...state,
      form: null
    }
  }),
  on(RegistryActions.updateDeletedRenewedFormState, (state, action) => {
    return {
      ...state,
      deletedRenewedForm: true
    }
  })
  ,
  on(RegistryActions.madenull, (state, action) => {
    return {
      ...state,
      requests: action.pass
    }
  }),
)