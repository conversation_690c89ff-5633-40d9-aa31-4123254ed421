import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicFileUploadControlComponent } from './dynamic-file-upload-control.component';

describe('DynamicFileUploadControlComponent', () => {
  let component: DynamicFileUploadControlComponent;
  let fixture: ComponentFixture<DynamicFileUploadControlComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DynamicFileUploadControlComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicFileUploadControlComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
