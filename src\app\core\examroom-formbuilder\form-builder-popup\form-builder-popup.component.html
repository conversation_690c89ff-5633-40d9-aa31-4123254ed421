<div id="questionGroupPopup" class="card p-6 fb-ht-fixed" style="position:relative" fxFlex fxFlex.xs="auto">
    <div style="position:absolute; top:10px;right:10px;cursor:pointer;" (click)="this.dialogRef.close()">
        <mat-icon>close</mat-icon>
    </div>
    <h4 class="mt-0 mb-6">{{this.typeName}} Builder</h4>
    <form [formGroup]="basicFormOrSectionDetails">
        <div class="row">
            <!-- <mat-form-field appearance="outline" class="genesis-form-feild">
                <mat-label>{{this.typeName}} Id</mat-label>
                <input matInput formControlName="id" readonly>
            </mat-form-field> -->
            <mat-form-field appearance="outline" class="genesis-form-feild">
                <mat-label>{{this.typeName}} Name</mat-label>
                <input matInput formControlName="name">
            </mat-form-field>
            <mat-form-field appearance="outline" class="genesis-form-feild">
                <mat-label>{{this.typeName}} Description</mat-label>
                <input matInput formControlName="description">
            </mat-form-field>
            <mat-checkbox formControlName="showInstructionInIButton" class="genesis-form-feild">
                Show Instructions on hover of
                <mat-icon>info</mat-icon> Button
            </mat-checkbox>
        </div>
        <div class="row">
            <mat-form-field appearance="outline" class="editor">
                <mat-label>{{this.typeName}} Instructions</mat-label>
                <!-- <textarea *ngIf="basicFormOrSectionDetails.value.showInstructionInIButton" matInput formControlName="instructions" placeholder="Enter Instructions Here" cols="80" rows="10"></textarea> -->
                <input matInput id="wMsg">
                <ngx-editor-menu class="editor" style="overflow:hidden;" [editor]="editor" [toolbar]="toolbar">
                </ngx-editor-menu>
                <ngx-editor class="editor" style="height: 3rem !important;" [editor]="editor" formControlName="instructions">
                </ngx-editor>
            </mat-form-field>
        </div>
        <ng-container *ngIf="typeName == 'Form'">
            <mat-checkbox formControlName="fetchFromApi" class="genesis-form-feild mb-6">
                Fetch Prefill Data from API
            </mat-checkbox>
            <ng-container *ngIf="this.basicFormOrSectionDetails.value.fetchFromApi">
                <mat-form-field appearance="outline" class="genesis-form-feild">
                    <mat-label>Fetch Url</mat-label>
                    <input matInput formControlName="fetchUrl" type="text">
                    <mat-hint>Provide details here to make and api call</mat-hint>
                </mat-form-field>
                <mat-form-field appearance="outline" class="genesis-form-feild">
                    <mat-label>Method</mat-label>
                    <mat-select formControlName="method" required>
                        <mat-option [value]="'post'">Post</mat-option>
                        <mat-option [value]="'get'" selected>Get</mat-option>
                    </mat-select>
                </mat-form-field>
                <div class="row" *ngFor="let group of requestParams.controls; let paramIndex = index">
                    <form [formGroup]="group">
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Param Type</mat-label>
                            <mat-select formControlName="paramType">
                                <mat-option *ngFor="let pt of paramTypesKeys" [value]="paramTypes[pt]">{{pt}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Param Name</mat-label>
                            <input matInput formControlName="paramName" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild">
                            <mat-label>Param Value</mat-label>
                            <input matInput formControlName="paramValue" type="text">
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="group.value.paramType == paramTypes.Required">
                            <mat-label>Required Param Position</mat-label>
                            <input matInput formControlName="position" type="number">
                        </mat-form-field>
                        <mat-checkbox formControlName="extractedFromGlobal" class="genesis-form-feild" *ngIf="!group.value.extractedFromElement">
                            Extract Value of Param From Some Global Property?
                        </mat-checkbox>
                        <mat-checkbox formControlName="extractedFromElement" class="genesis-form-feild" *ngIf="!group.value.extractedFromGlobal">
                            Extract Value of Param From Some Local Property?
                        </mat-checkbox>
                        <mat-form-field appearance="outline" class="genesis-form-feild" *ngIf="group.value.extractedFromGlobal || group.value.extractedFromElement">
                            <mat-label>Property/Key to be Extracted..</mat-label>
                            <input matInput formControlName="elementPropertyToBeExtracted" type="text">
                        </mat-form-field>
                        <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="this.requestParams.removeAt(paramIndex)">
                            Remove Param
                        </button>
                    </form>
                </div>
                <div class="row">
                    <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="addParam()">
                        Add Param
                    </button>
                </div>
            </ng-container>
        </ng-container>
    </form>
    <div class="row">
        <button mat-button class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow" (click)="submit()">
            Create
        </button>
    </div>
</div>